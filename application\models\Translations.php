<?php

	class Model_Translations extends Model_Base {
		
		public function getEnabledLanguages() {
			$select = $this->db->select()
				->from($this->tables['languages'], array('short_name', '*'))
				->where('enabled = 1')
				->order('id ASC');
			return $this->db->fetchAssoc($select);
		}
		
		public function getTranslations($short_name) {
			$select = $this->db->select()
				->from($this->tables['translations'], array('key', 'value'))
				->where('language = ?', $short_name)
				->order('key ASC');
			return $this->db->fetchPairs($select);
		}
		
		public function getAllTranslations() {
			$select = $this->db->select()
				->from($this->tables['translations'])
				->order(array('key ASC', 'language ASC'));
			return $this->db->fetchAll($select);
		}
		
		public function updateFromSr($data) {
			try {
				$this->db->beginTransaction();
				
				$sql = "DELETE FROM " . $this->tables['translations'] . " WHERE 1";
				$this->db->query($sql);
				
				foreach ($data as $item) {
					$this->db->insert(
						$this->tables['translations'],
						$item
					);
				}
				
				$this->db->commit();
				
				$cache = Zend_Registry::get('Cache');
				$cache->clean(Zend_Cache::CLEANING_MODE_MATCHING_ANY_TAG, array('translate'));
			}
			catch (Exception $e) {
				$this->db->rollBack();
				throw $e;
			}
		}
		
	}