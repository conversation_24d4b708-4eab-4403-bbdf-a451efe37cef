<?php

class Model_Cars_Transfers extends Model_Base {
	
        public function contactThanks($data)
        {
            $view = new Zend_View();
            $view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
	    $view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");
            $msg = $view->render('contact_thanks_email.phtml');
            
            $mail = new Zend_Mail($charset="UTF-8");
            $mail->addTo($data['email'])
                    ->setBodyHtml($msg)
                    ->setSubject('Dziękujemy za kontakt z AutoAuto.pl!');
            $mail->send();
            
        }

        public function contactFinancing($data) {

            if (empty($data)) {
                throw new Exception("Incomplete data in contactFinancing");
            }
            try {
                $view = new Zend_View();
                $view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
                $view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");

                $carModel = new Model_Cars_Cars();
                $model = $carModel->getCarModel($data['model']);
                $data['model'] = $model['name'];
                $view->data = $data;



                $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
                $srDomain = $opt['synchronization']['sr']['domain'];
                $wwwDomain = Zend_Registry::get('siteDomain');

                $view->srDomain = $srDomain;
                $view->wwwDomain = $wwwDomain;

                $msg = $view->render('financing_email2.phtml');


                $mail = new Zend_Mail($charset="UTF-8");
                $mail->addTo('<EMAIL>')
                    ->setBodyHtml(nl2br($msg))
                    ->setSubject('Finansowanie - autoauto.pl');
                $mail->send();

                $this->contactThanks($data);

            }
            catch (Exception $e) {
                $logger = Zend_Registry::get('errorLogger');
                $logger->log(
                    PHP_EOL . PHP_EOL
                    . "unexpected error in " . __METHOD__ . ", line " . (__LINE__ - 2)
                    . PHP_EOL . $e->getMessage()
                    . PHP_EOL . $e->getTraceAsString()
                    . PHP_EOL . "carData:" . PHP_EOL . print_r($carData, true)
                    . PHP_EOL . "data:" . PHP_EOL . print_r($data, true)
                    ,
                    Zend_Log::ERR
                );
                throw new Exception("Error in makeOffer; logged to errorLogger");
            }
        }
        
	public function contactComment($carData, $data, $userData) {
		if (empty($carData) || empty($data) || empty($carData['caretaker_email'])) {
			throw new Exception("Incomplete data in contactComment");
		}
		try {
			$view = new Zend_View();
			$view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
			$view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");
			$view->carData = $carData;
			$view->data = $data;
			$view->userData = $userData;
			
			$carText = $carData['make_name'] . " " . $carData['model_name'] . " " . $carData['build_year'] . " r. (id: " . $carData['sr_car_id'] . ")";
			$view->carText = $carText;
			
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$srDomain = $opt['synchronization']['sr']['domain'];
			$wwwDomain = Zend_Registry::get('siteDomain');
			
			$view->srDomain = $srDomain;
			$view->wwwDomain = $wwwDomain;
			
			$msg = $view->render('comment_email.phtml');
			
			$locations = new Model_Locations();
			$location = $locations->getLocation($carData['location_id']);
			
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($location['contact_email'])
				->setBodyHtml(nl2br($msg))
				->setSubject('Komentarz do samochodu
				 ' . $carText);
			$mail->send();
                        
                        $this->contactThanks($data);
                       
		}
		catch (Exception $e) {
			$logger = Zend_Registry::get('errorLogger');
			$logger->log(
					PHP_EOL . PHP_EOL
					. "unexpected error in " . __METHOD__ . ", line " . (__LINE__ - 2)
					. PHP_EOL . $e->getMessage()
					. PHP_EOL . $e->getTraceAsString()
					. PHP_EOL . "carData:" . PHP_EOL . print_r($carData, true)
					. PHP_EOL . "data:" . PHP_EOL . print_r($data, true)
				,
				Zend_Log::ERR
			);
			throw new Exception("Error in makeOffer; logged to errorLogger");
		}
	}
	
	public function contactService($data) {
		try {
			$view = new Zend_View();
			$view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
			$view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");
			
			$view->data = $data;
			
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$recipient = $opt['service']['contact_email'];
			
			$msg = $view->render('contact_service.phtml');
			
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($recipient)
				->setBodyHtml($msg)
				->setSubject('Zlecenie serwisowe ze strony www');
			$mail->send();
                        
                        $this->contactThanks($data);
                        
                         
                        
		}
		catch (Exception $e) {
			$logger = Zend_Registry::get('errorLogger');
			$logger->log(
					PHP_EOL . PHP_EOL
					. "unexpected error in " . __METHOD__ . ", line " . (__LINE__ - 2)
					. PHP_EOL . $e->getMessage()
					. PHP_EOL . $e->getTraceAsString()
					. PHP_EOL . "carData:" . PHP_EOL . print_r($carData, true)
					. PHP_EOL . "data:" . PHP_EOL . print_r($data, true)
				,
				Zend_Log::ERR
			);
			throw new Exception("Error in makeOffer; logged to errorLogger");
		}
	}
	
	public function contactTestDrive($carData, $data, $userData) {
		if (empty($carData) || empty($data) || empty($carData['caretaker_email'])) {
			throw new Exception("Incomplete data in contactTestDrive");
		}
		try {
			$view = new Zend_View();
			$view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
			$view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");
			$view->carData = $carData;
			$view->data = $data;
			$view->userData = $userData;
			
			$carText = $carData['make_name'] . " " . $carData['model_name'] . " " . $carData['build_year'] . " r. (id: " . $carData['sr_car_id'] . ")";
			$view->carText = $carText;

            if(isset($data['is_contact_caretaker'])) {
                $script = 'caretaker_email.phtml';
                $to = $carData['caretaker_email'];
                $subject = 'Zapytanie do opiekuna ' . $carText;
            } else if(isset($data['is_contact_financing'])) {
                $script = 'financing_email.phtml';
                $employeesModel = new Model_Employees();
                $employee = $employeesModel->getById($data['is_contact_financing']);
                $to = $employee['email'];
                $subject = 'Zapytanie o kredyt lub leasing ' . $carText;

            } else if(isset($data['is_contact_language'])) {
                $script = 'caretaker_email.phtml';
                $employeesModel = new Model_Employees();
                $employee = $employeesModel->getById($data['is_contact_language']);
                $to = $employee['email'];
                $subject = 'Zapytanie do opiekuna ' . $carText;

            } else {
                $script = 'test_drive_email.phtml';
                $to = $carData['caretaker_email'];
                $subject = 'Propozycja jazdy próbnej ' . $carText;
            }
			
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$srDomain = $opt['synchronization']['sr']['domain'];
			$wwwDomain = Zend_Registry::get('siteDomain');
			
			$view->srDomain = $srDomain;
			$view->wwwDomain = $wwwDomain;
			
			$msg = $view->render($script);
			
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($to)
                ->setReplyTo($data['email'])   
				->setBodyHtml($msg)
				->setSubject($subject);
			$mail->send();
                        
                        $this->contactThanks($data);
		}
		catch (Exception $e) {
			$logger = Zend_Registry::get('errorLogger');
			$logger->log(
					PHP_EOL . PHP_EOL
					. "unexpected error in " . __METHOD__ . ", line " . (__LINE__ - 2)
					. PHP_EOL . $e->getMessage()
					. PHP_EOL . $e->getTraceAsString()
					. PHP_EOL . "carData:" . PHP_EOL . print_r($carData, true)
					. PHP_EOL . "data:" . PHP_EOL . print_r($data, true)
				,
				Zend_Log::ERR
			);
			throw new Exception("Error in makeOffer; logged to errorLogger");
		}
	}

    public function contactSendOffer($carData, $data) {
        if (empty($carData) || empty($data)) {
            throw new Exception("Incomplete data in contactComment");
        }
        try {
            $view = new Zend_View();
            $view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
            $view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");
            $view->car = $carData;
            $view->data = $data;
            $view->translate = Zend_Registry::get('Zend_Translate');


            $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
            $srDomain = $opt['synchronization']['sr']['domain'];
            $wwwDomain = Zend_Registry::get('siteDomain');

            $view->srDomain = $srDomain;
            $view->domain = $wwwDomain;

            $msg =  $view->render('car_send_offer.phtml');

            $title = ($carData['auction_price'] > 0  && $data['auction'] ? ('- cena Klienta plus '. number_format($carData['auction_price'] * 0.02, 0, '', ' '). ' PLN') : $carData['title']);
            $subject = "autoauto.pl :: " . $carData['make_name'] . " " . $carData['model_name'] . " " . $carData['build_year'] . " " . $title;


            $mail = new Zend_Mail($charset="UTF-8");

            if(isset($data['employee'])) {
                $mail->setFrom(
                    $opt['resources']['mail']['defaultFrom']['email'],
                    $data['employee']['first_name'] . ' ' . $data['employee']['last_name']
                );
            }
            $mail->addTo($data['recipient_email'])
                ->setReplyTo($data['sender_email'])
                ->setBodyHtml($msg)
                ->setSubject($subject);

            $mail->send();



        }
        catch (Exception $e) {
            $logger = Zend_Registry::get('errorLogger');
            $logger->log(
                PHP_EOL . PHP_EOL
                . "unexpected error in " . __METHOD__ . ", line " . (__LINE__ - 2)
                . PHP_EOL . $e->getMessage()
                . PHP_EOL . $e->getTraceAsString()
                . PHP_EOL . "carData:" . PHP_EOL . print_r($carData, true)
                . PHP_EOL . "data:" . PHP_EOL . print_r($data, true)
                ,
                Zend_Log::ERR
            );
            throw new Exception("Error in makeOffer; logged to errorLogger");
        }
    }
	
	public function deleteReservationFromSr($id, $statusDetails) {
		$this->db->update(
			$this->tables['cars_reservations'],
			array(
				'status' => 'CANCELLED'
			) + $statusDetails,
			"id = " . (int)$id
		);
	}
	
	public function expireReservations() {
		$this->db->update(
			$this->tables['cars_reservations'],
			array(
				'status' => 'EXPIRED',
				'status_change_datetime' => new Zend_Db_Expr('NOW()')
			),
			"status NOT IN(" . $this->db->quote(array('EXPIRED', 'CANCELLED')) . ") AND valid_until < NOW()"
		);
	}
	
	public function extendReservationFromSr($id, $statusDetails) {
		$this->db->update(
			$this->tables['cars_reservations'],
			array(
				'status' => 'ACTIVE'
			) + $statusDetails,
			"id = " . (int)$id
		);
	}
	
	public function getPayment($id) {
		$select = $this->db->select()
			->from($this->tables['payments'])
			->where('id = ' . (int)$id);
		return $this->db->fetchRow($select);
	}
	
	public function getUsersReservations($id) {
		$select = $this->db->select()
			->from(array('cr' => $this->tables['cars_reservations']))
			->join(array('c' => $this->tables['cars']),
				'c.sr_car_id = cr.sr_car_id',
				array('car_id', 'price' => 'IFNULL(LEAST(price, promotion_price), price)', 'price_currency', 'price_type_key', 'build_year', 'odometer', 'power', 'power_unit', 'cubic_capacity', 'power_unit', 'position', 'title', 'description','is_new_car' => new Zend_Db_Expr('CASE c.location_id WHEN '.  Model_Cars_Cars::NEW_CAR_LOCATION.' THEN 1 ELSE 0 END'), 'credit_collateral', 'ownership_type', 'is_reserved_hidden', 'first_registration_year', 'leasing', 'is_sold_hidden')
			)
			->joinLeft(array('mk' => $this->tables['car_makes']),
				'mk.id = c.make_id',
				array('make_name' => 'name')
			)
			->joinLeft(array('md' => $this->tables['car_models']),
				'md.id = c.model_id',
				array('model_name' => 'name')
			)
			->joinLeft(array('ph' => $this->tables['cars_photos']),
				'ph.car_id=c.car_id AND ord=1',
				array('filename_base', 'filename_extension'))
			->joinLeft(array('loc' => $this->tables['locations']),
				'c.location_id = loc.location_id',
				array('short_name', 'location_group_id', 'name')
			)
			->joinLeft(array('locgr' => $this->tables['location_groups']),
				'loc.location_group_id = locgr.id',
				array('address')
			)
			->joinLeft(array('em' => $this->tables['employees']),
				'em.sr_id = c.caretaker_sr_id',
				array('caretaker_first_name' => 'first_name', 'caretaker_last_name' => 'last_name', 'caretaker_email' => 'email', 'caretaker_phone' => 'phone', 'caretaker_visible' => 'visible')
			)
			->where('cr.user_id = ?', $id)
			->order('cr.date_added ASC');
		return $select;
	}	
	
	public function makeOffer($carData, $data, $userData) {
		if (empty($carData) || empty($data) || empty($carData['caretaker_email'])) {
			throw new Exception("Incomplete data in makeOffer");
		}
		try {
			$view = new Zend_View();
			$view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
			$view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");
			$view->carData = $carData;
			$view->data = $data;
			$view->userData = $userData;
			
			$carText = $carData['make_name'] . " " . $carData['model_name'] . " " . $carData['build_year'] . " r. (id: " . $carData['sr_car_id'] . ")";
			$view->carText = $carText;
			
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$srDomain = $opt['synchronization']['sr']['domain'];
			$wwwDomain = Zend_Registry::get('siteDomain');
			
			$view->srDomain = $srDomain;
			$view->wwwDomain = $wwwDomain;
			
			$msg = $view->render('offer_email.phtml');
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($carData['caretaker_email'])
				->setBodyHtml($msg)
				->setSubject('Oferta na samochód ' . $carText);
			$mail->send();
                        
            $this->contactThanks($data);
		}
		catch (Exception $e) {
			$logger = Zend_Registry::get('errorLogger');
			$logger->log(
					PHP_EOL . PHP_EOL
					. "unexpected error in " . __METHOD__ . ", line " . (__LINE__ - 2)
					. PHP_EOL . $e->getMessage()
					. PHP_EOL . $e->getTraceAsString()
					. PHP_EOL . "carData:" . PHP_EOL . print_r($carData, true)
					. PHP_EOL . "data:" . PHP_EOL . print_r($data, true)
				,
				Zend_Log::ERR
			);
			throw new Exception("Error in makeOffer; logged to errorLogger");
		}
	}
	
	public function makeReservation($car, $data, $userData) {
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$optPlatnosci = $opt['transfers']['platnoscipl'];
		$platnosci = new My_PlatnosciPl($optPlatnosci);
		
		$ts = time();
		
		try {
			$this->db->beginTransaction();
			
			$paymentData = array(
				'pos_id' => $platnosci->getPosId(),
				'ts' => $ts,
				'amount' => $data['amount']
			);
			
			$paymentId = $this->newPayment($userData['id'], $car, $car['company_id'], $paymentData);
			$sessionId = $paymentId;
			
			$this->db->insert(
				$this->tables['cars_reservations'],
				array(
					'date_added' => new Zend_Db_Expr('NOW()'),
					'valid_until' => new Zend_Db_Expr('NOW() + INTERVAL ' . $opt['transfers']['reservation']['duration']),
					'user_id' => $userData['id'],
					'sr_car_id' => $car['sr_car_id'],
					'amount' => $data['amount'],
					'buy_price' => $data['buy_price'],
					'buy_price_type_key' => $car['price_type_key'],
					'payment_id' => $paymentId,
					'status' => 'WAITING_FOR_PAYMENT',
					'status_change_datetime' => new Zend_Db_Expr('NOW()'),
                    'agreed_with_sr_id' => !empty($data['agreed_with_sr_id']) ? $data['agreed_with_sr_id'] : $car['caretaker_sr_id'],
                    'lang' => $data['lang'],
				)
			);
			
			$platnosciData = array(
				'pos_id' => $platnosci->getPosId(),
				'pos_auth_key' => $platnosci->getPosAuthKey(),
				'session_id' => $sessionId,
				'amount' => (string)(100 * $data['amount']), //kwota w groszach
				'desc' => 'Rezerwacja samochodu: ' . mb_substr($data['email'], 0, 40, "UTF-8") . ' id: ' . $car['car_id'] . "/ sys2: " . $car['sr_car_id'],
				'first_name' => mb_substr($data['first_name'], 0, 100, "UTF-8"),
				'last_name' => mb_substr($data['last_name'], 0, 100, "UTF-8"),
				'email' => mb_substr($data['email'], 0, 100, "UTF-8"),
				'client_ip' => Zend_Controller_Front::getInstance()->getRequest()->getServer('REMOTE_ADDR'),
				'ts' => (string)$ts
			);
			
			if (isset($data['pay_type'])) {
				$platnosciData['pay_type'] = $data['pay_type'];
			}
			
			$sig = $platnosci->getNewPaymentSig($platnosciData);
			$platnosciData['sig'] = $sig;
			
			$this->db->update(
				$this->tables['payments'],
				array('sig' => $sig),
				"id = " . $paymentId
			);
			
			$this->db->update(
				$this->tables['users'],
				array('export_to_sr' => 1),
				"id = " . (int)$userData['id']
			);
			$users = new Model_Users();
			$user = $users->getUser((int)$userData['id']);
			$users->edit(
				$user['id'],
				$userData
			);
			
			$url = $platnosci->getUrlNewPayment();
			
			
			//maile
			$view = new Zend_View();
			$view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
			$view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");
			$view->translate = Zend_Registry::get('Zend_Translate');
			$view->carData = $car;
			$view->data = $data;
			$view->userData = $userData;
			
			$carText = $car['make_name'] . " " . $car['model_name'] . " " . $car['build_year'] . " r. (id: " . $car['sr_car_id'] . ")";
			$carTextForClient = $car['make_name'] . " " . $car['model_name'] . " " . $car['build_year'] . " r. (id: " . $car['car_id'] . ")";
			
			$view->carText = $carText;
			$view->carTextForClient = $carTextForClient;
			
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$srDomain = $opt['synchronization']['sr']['domain'];
			$wwwDomain = Zend_Registry::get('siteDomain');
			
			$view->srDomain = $srDomain;
			$view->wwwDomain = $wwwDomain;
			
			$msg = $view->render('reservation_email_caretaker.phtml');
			
            $carEmail = $car['caretaker_email'];
            
            if(!empty($data['agreed_with_sr_id']))
            {
                $employeesModel = new Model_Employees();
                $employee = $employeesModel->getBySrId($data['agreed_with_sr_id']);
                $carEmail = $employee['email'];
                
            }
                    
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($carEmail)
				->setBodyHtml($msg)
				->setSubject('Rezerwacja samochodu ' . $carText);
			$mail->send();
			
			$msg = $view->render('reservation_email_client.phtml');
			
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($data['email'])
				->setBodyHtml($msg)
				->setSubject($view->translate->_('CAR_RESERVATION_TITLE') . ' ' . $carTextForClient);
			$mail->send();
			
			$this->db->commit();
		}
		catch (Exception $e) {
			$this->db->rollBack();
			throw $e;
		}
		
		return array(
			'url' => $url,
			'data' => $platnosciData
		);
	}
	
	public function newPayment($userId, $car, $companyId, $data) {
		$carId = new Zend_Db_Expr('NULL');
		if (is_array($car) && (int)$car['car_id'] > 0) {
			$carId = (int)$car['car_id'];
		}
		
		if ($companyId === null) {
			$companyId = new Zend_Db_Expr('NULL');
		}
		else {
			$companyId = (int)$companyId;
		}
		
		$insertData = array(
			'date_added' => new Zend_Db_Expr('NOW()'),
			'user_id' => (int)$userId,
			'car_id' => $carId,
			'company_id' => $companyId,
			'pos_id' => (int)$data['pos_id'],
			'ts' => $data['ts'],
			'amount' => $data['amount']
		);
		
		$this->db->insert(
			$this->tables['payments'],
			$insertData
		);
		
		return $this->db->lastInsertId($this->tables['payments'], 'id');
	}
	
	public function sellCarEmail($data) {
		if (empty($data)) {
			throw new Exception("Incomplete data in sellCarEmail");
		}
		try {
			$view = new Zend_View();
			$view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
			$view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");
			$view->data = $data;
			
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$tgtEmail = $opt['transfers']['sellCar']['email'];
			if (empty($tgtEmail)) {
				throw new Exception('Empty target email address in sellCarEmail');
			}
			
            $exportSr = new Model_ImportExport_Export();
			$exportSr->sendClientSell($data);
                
			/*$msg = $view->render('sell_car_email.phtml');
			$subjectData = $view->escape($data['make'] . ' ' . $data['model'] . ', ' . $data['build_year']);
			
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($tgtEmail)
				->setBodyHtml($msg)
				->setSubject('Auto do wyceny: ' . $subjectData);
			$mail->send();*/
            
            $this->contactThanks($data);
		}
		catch (Exception $e) {
			$logger = Zend_Registry::get('errorLogger');
			$logger->log(
					PHP_EOL . PHP_EOL
					. "unexpected error in " . __METHOD__ . ", line " . (__LINE__ - 2)
					. PHP_EOL . $e->getMessage()
					. PHP_EOL . $e->getTraceAsString()
					. PHP_EOL . "data:" . PHP_EOL . print_r($data, true)
				,
				Zend_Log::ERR
			);
			throw new Exception("Error in makeOffer; logged to errorLogger");
		}
	}
	
	public function updatePayment($id, $data) {
		$this->db->update(
			$this->tables['payments'],
			$data,
			"id = " . (int)$id
		);
	}
	
	public function updateReservationsOnPaymentStatusChange($payment, $status) {
		//assume multiple reservations possible for one payment
		$select = $this->db->select()
			->from($this->tables['cars_reservations'])
			->where('payment_id = ' . (int)$payment['id'])
			->where('status IN(?)', array('WAITING_FOR_PAYMENT', 'ACTIVE'));
		$reservations  = $this->db->fetchAll($select);
		
		if (count($reservations) < 1) return;
		
		$cars = new Model_Cars_Cars();
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$optReservations = $opt['transfers']['reservation'];
		
		$export = new Model_ImportExport_Export();
		foreach ($reservations as $reservation) {
			$reservationStatus = "WAITING_FOR_PAYMENT";
			if ($status['status'] == 99 && $status['amount'] == $payment['amount'] * 100) {
				$reservationStatus = "ACTIVE";
			}
			else if (in_array($status['status'], array(2, 3, 888))) {
				$reservationStatus = "CANCELLED";
			}
			
			$statusChanged = ($reservationStatus != $reservation['status']);
            
            //mails
			if ($statusChanged) {
				$carTemp = $cars->getCarBySrCarId($reservation['sr_car_id']);
				$car = $cars->getCarFull($carTemp['car_id']);
				
				$carText = $car['make_name'] . " " . $car['model_name'] . " " . $car['build_year'] . " r. (id: " . $car['sr_car_id'] . ")";
				$carTextForClient = $car['make_name'] . " " . $car['model_name'] . " " . $car['build_year'] . " r. (id: " . $car['car_id'] . ")";
				
				$view = Zend_Layout::getMvcInstance()->getView();
				$view->carText = $carText;
				$view->carTextForClient = $carTextForClient;
				
				$srDomain = $opt['synchronization']['sr']['domain'];
				$wwwDomain = Zend_Registry::get('siteDomain');
				
				$view->carData = $car;
				$view->srDomain = $srDomain;
				$view->wwwDomain = $wwwDomain;
				$view->status = $reservationStatus;
				$view->reservation = $reservation;
				
                $carEmail = $car['caretaker_email'];
            
                if(!empty($reservation['agreed_with_sr_id']))
                {
                    $employeesModel = new Model_Employees();
                    $employee = $employeesModel->getBySrId($reservation['agreed_with_sr_id']);
                    $carEmail = $employee['email'];

                }
                          
				$msg = $view->render('reservation_email_2_caretaker.phtml');
				
				$mail = new Zend_Mail($charset="UTF-8");
				$mail->addTo($carEmail)
					->setBodyHtml($msg)
					->setSubject('Zmiana statusu rezerwacji samochodu ' . $carText);
				$mail->send();
				
				$users = new Model_Users();
				$user = $users->getUser($reservation['user_id']);
				$view->user = $user;
				
				$msg = $view->render('reservation_email_2_client.phtml');
				
				$mail = new Zend_Mail($charset="UTF-8");
				$mail->addTo($user['email'])
					->setBodyHtml($msg)
					->setSubject($view->translate->_('CAR_RESERVATION_STATUS_CHANGE_TITLE') . ' ' . $carTextForClient);
				$mail->send();
                
                
			}
			
			$this->db->update(
				$this->tables['cars_reservations'],
				array(
					'status' => $reservationStatus,
					'status_change_datetime' => new Zend_Db_Expr('NOW()')
				),
				"id = " . (int)$reservation['id']
			);
			
			ob_start();//just in case
			$reservation = $this->db->fetchRow($select = $this->db->select()->from($this->tables['cars_reservations'])->where('id = ?', $reservation['id']));
			$export->sendReservation($reservation);
			ob_get_clean();//just in case
			
			
		}
	}
    
    
    public function newCarEmail($data) {
		if (empty($data)) {
			throw new Exception("Incomplete data in newCarEmail");
		}
		try {
			$view = new Zend_View();
			$view->setScriptPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "scripts");
			$view->setHelperPath(APPLICATION_PATH . DIRECTORY_SEPARATOR . "views" . DIRECTORY_SEPARATOR . "helpers");
			$view->data = $data;
			
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$tgtEmail = $opt['transfers']['newCar']['email'];
			if (empty($tgtEmail)) {
				throw new Exception('Empty target email address in newCarEmail');
			}
			
			$msg = $view->render('new_car_email.phtml');
			$subjectData = $view->escape($data['make'] . ' ' . $data['model']);
			
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($tgtEmail)
				->setBodyHtml($msg)
				->setSubject('Auto nowe ' . $subjectData);
			$mail->send();
            
            $this->contactThanks($data);
		}
		catch (Exception $e) {
			$logger = Zend_Registry::get('errorLogger');
			$logger->log(
					PHP_EOL . PHP_EOL
					. "unexpected error in " . __METHOD__ . ", line " . (__LINE__ - 2)
					. PHP_EOL . $e->getMessage()
					. PHP_EOL . $e->getTraceAsString()
					. PHP_EOL . "carData:" . PHP_EOL . print_r($carData, true)
					. PHP_EOL . "data:" . PHP_EOL . print_r($data, true)
				,
				Zend_Log::ERR
			);
			throw new Exception("Error in makeOffer; logged to errorLogger");
		}
	}
    
    public function makeNewCar($data, $userData) {
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$optPlatnosci = $opt['transfers']['platnoscipl'];
        $amount = $opt['transfers']['newCar']['amount'];
		$platnosci = new My_PlatnosciPl($optPlatnosci);
		
		$ts = time();
		
		try {
			$this->db->beginTransaction();
			
			$paymentData = array(
				'pos_id' => $platnosci->getPosId(),
				'ts' => $ts,
				'amount' => $amount
			);
			
			$paymentId = $this->newPayment($userData['id'], false, null, $paymentData);
			$sessionId = $paymentId;
			
			$this->db->insert(
				$this->tables['cars_new'],
				array(
					'date_added' => new Zend_Db_Expr('NOW()'),
					'user_id' => $userData['id'],
                    'make' => $data['make'],
                    'model' => $data['model'],
                    'additional' => $data['additional'],
					'payment_id' => $paymentId,
					'status' => 'WAITING_FOR_PAYMENT',
					'status_change_datetime' => new Zend_Db_Expr('NOW()')
				)
			);
			
			$platnosciData = array(
				'pos_id' => $platnosci->getPosId(),
				'pos_auth_key' => $platnosci->getPosAuthKey(),
				'session_id' => $sessionId,
				'amount' => (string)(100 * $amount), //kwota w groszach
				'desc' => 'Licytacja dealerów: ' . mb_substr($data['email'], 0, 40, "UTF-8") . ' ' . $data['make'] . " " . $data['model'],
				'first_name' => mb_substr($data['first_name'], 0, 100, "UTF-8"),
				'last_name' => mb_substr($data['last_name'], 0, 100, "UTF-8"),
				'email' => mb_substr($data['email'], 0, 100, "UTF-8"),
				'client_ip' => Zend_Controller_Front::getInstance()->getRequest()->getServer('REMOTE_ADDR'),
				'ts' => (string)$ts
			);
			
			if (isset($data['pay_type'])) {
				$platnosciData['pay_type'] = $data['pay_type'];
			}
			
			$sig = $platnosci->getNewPaymentSig($platnosciData);
			$platnosciData['sig'] = $sig;
			
			$this->db->update(
				$this->tables['payments'],
				array('sig' => $sig),
				"id = " . $paymentId
			);
			
			$this->db->update(
				$this->tables['users'],
				array('export_to_sr' => 1),
				"id = " . (int)$userData['id']
			);
			$users = new Model_Users();
			$user = $users->getUser((int)$userData['id']);
			$users->edit(
				$user['id'],
				array('first_name' => $user['first_name']) //dummy update to trigger export to SR
			);
			
			$url = $platnosci->getUrlNewPayment();

			$this->db->commit();
		}
		catch (Exception $e) {
			$this->db->rollBack();
			throw $e;
		}
		
		return array(
			'url' => $url,
			'data' => $platnosciData
		);
	}
    
    
    public function getLastNewCarPaymant($userId) {
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$optPlatnosci = $opt['transfers']['platnoscipl'];
        $amount = $opt['transfers']['newCar']['amount'];
		$platnosci = new My_PlatnosciPl($optPlatnosci);
		
	
        
        $select = $this->db->select()
			->from(array('cn' => $this->tables['cars_new']))
            ->join(array('u' => $this->tables['users']), 'user_id = u.id', array('first_name', 'last_name', 'email', 'phone'))
            ->join(array('p' => $this->tables['payments']), 'p.id = cn.payment_id')
			->where('cn.user_id = ?', (int)$userId)
			->where('cn.status IN(?)', array('WAITING_FOR_PAYMENT'))
            ->order('cn.id DESC')
            ->limit(1);
        
		$newCar = $this->db->fetchRow($select);
        
        if(!$newCar)
            return false;
        

        $paymentId = $newCar['payment_id'];
        $sessionId = $paymentId;
        $amount = $newCar['amount'];
        $ts = $newCar['ts'];


        $platnosciData = array(
            'pos_id' => $platnosci->getPosId(),
            'pos_auth_key' => $platnosci->getPosAuthKey(),
            'session_id' => $sessionId,
            'amount' => (string)(100 * $amount), //kwota w groszach
            'desc' => 'Licytacja dealerów: ' . mb_substr($newCar['email'], 0, 40, "UTF-8") . ' ' . $newCar['make'] . " " . $newCar['model'],
            'first_name' => mb_substr($newCar['first_name'], 0, 100, "UTF-8"),
            'last_name' => mb_substr($newCar['last_name'], 0, 100, "UTF-8"),
            'email' => mb_substr($newCar['email'], 0, 100, "UTF-8"),
            'client_ip' => Zend_Controller_Front::getInstance()->getRequest()->getServer('REMOTE_ADDR'),
            'ts' => (string)$ts
        );
			
			
			
        $sig = $platnosci->getNewPaymentSig($platnosciData);
        $platnosciData['sig'] = $sig;
			
        $url = $platnosci->getUrlNewPayment();

		return array(
			'url' => $url,
			'data' => $platnosciData
		);
	}
    
    public function updateNewCarOnPaymentStatusChange($payment, $status) {
		//assume multiple reservations possible for one payment
		$select = $this->db->select()
			->from($this->tables['cars_new'])
            ->join(array('u' => $this->tables['users']), 'user_id = u.id', array('first_name', 'last_name', 'email', 'phone'))
			->where('payment_id = ' . (int)$payment['id'])
			->where('status IN(?)', array('WAITING_FOR_PAYMENT', 'ACTIVE'));
		$carsNew  = $this->db->fetchAll($select);
		
		if (count($carsNew ) < 1) return;
		
		$cars = new Model_Cars_Cars();
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$optReservations = $opt['transfers']['reservation'];
		
		$export = new Model_ImportExport_Export();
		foreach ($carsNew  as $new) {
			$newStatus = "WAITING_FOR_PAYMENT";
			if ($status['status'] == 99 && $status['amount'] == $payment['amount'] * 100) {
				$newStatus = "ACTIVE";
			}
			else if (in_array($status['status'], array(2, 3, 888))) {
				$newStatus = "CANCELLED";
			}
			
			$statusChanged = ($newStatus != $new['status']);
			
			$this->db->update(
				$this->tables['cars_new'],
				array(
					'status' => $newStatus,
					'status_change_datetime' => new Zend_Db_Expr('NOW()')
				),
				"id = " . (int)$new['id']
			);
			
			
			//mails
			if ($statusChanged && $newStatus = "ACTIVE") {
                
                $this->newCarEmail($new);
                
			}
		}
	}
	
}