<?php

class Form_Car_Reservation extends My_Form {

	protected $_minimumAmount;
    protected $_buyPrice;

	public function init() {
		if (empty($this->_minimumAmount)) {
			throw new Exception("Minimum amount not set in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}

        $empl = new Model_Employees();
        $tr = Zend_Registry::get('Zend_Translate');

		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
                'attribs' => array('class' => 'form-control'),

				'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8'))
				)
			)),
			new Zend_Form_Element_Text('first_name', array(
				'label'	=>	'FIRST_NAME',
				'required' => true,
                'attribs' => array('class' => 'form-control'),
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('last_name', array(
				'label'	=>	'LAST_NAME',
				'required' => true,
                'attribs' => array('class' => 'form-control'),
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
            new Zend_Form_Element_Text('address', array(
				'label'	=>	'ADDRESS',
                'required' => true,
                'attribs' => array('class' => 'form-control'),
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 255, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('zip_code', array(
				'label'	=>	'ZIP_CODE',
                'attribs' => array('class' => 'form-control'),
                'required' => true,
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 32, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('city', array(
				'label'	=>	'CITY',
                'attribs' => array('class' => 'form-control'),
                'required' => true,
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('company_name', array(
				'label'	=>	'COMPANY_NAME',
                'attribs' => array('class' => 'form-control'),
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 255, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('phone', array(
				'label'	=>	'PHONE',
                'attribs' => array('class' => 'form-control'),
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('amount', array(
				'label'	=>	'RESERVATION_AMOUNT',
                'attribs' => array('class' => 'form-control'),
				'validators' => array(new Zend_Validate_Int(), new My_Validate_GreaterOrEqual($this->_minimumAmount)),
				'value' => $this->_minimumAmount
			)),
            new Zend_Form_Element_Text('buy_price', array(
				'label'	=>	'CAR_PRICE',
                'attribs' => array('class' => 'form-control'),
				'validators' => array(new Zend_Validate_Int()),
				'value' => $this->_buyPrice
			)),
             new Zend_Form_Element_Select('agreed_with_sr_id', array(
                    'label'	=>	'AGREED_WITH',
                    'attribs' => array('class' => 'form-control'),
                    'multiOptions' => array('' => $tr->_('AGREED_WITH')) + $empl->getAll(array('sr_assoc' => true)),
                    'required' => false
            )),
            new Zend_Form_Element_Captcha('captcha', array(
                'label' => 'CAPTCHA',
                'captcha' => new My_Captcha_ReCaptcha(array(
                    'siteKey'  => '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-',
                    'secretKey' => '6Lfx7A0UAAAAAL41t8hFSPCzuh6VQGhcpXttwrJd'
                )),
            )),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('submit', array(
                'value' => 'RESERVATION_SUBMIT',
				'label'	=>	'RESERVATION_SUBMIT',
                'attribs' => array('class' => "btn btn-action btn-action-orange"),
			)),
            new Zend_Form_Element_Submit('preview', array(
                'value' => 'CONTRACT_PREVIEW',
				'label'	=>	'CONTRACT_PREVIEW',
                'attribs' => array('class' => "btn btn-action btn-action-transparent"),

			)),
		));
		parent::init();
		$this->setAttrib('id', 'reservation');
	}

	public function setMinimumAmount($value) {
		$this->_minimumAmount = (int)$value;
	}

    public function setBuyPrice($value) {
		$this->_buyPrice = (int)$value;
	}

}