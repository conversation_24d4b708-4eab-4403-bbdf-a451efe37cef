<?php
class My_Plugin_Translate extends Zend_Controller_Plugin_Abstract {

	public function preDispatch(Zend_Controller_Request_Abstract $request) {
		$translations = new Model_Translations();
		$cache = Zend_Registry::get('Cache');

		$enabled_languages = null;
		if (!$enabled_languages = $cache->load('enabled_languages')) {
			$enabled_languages = $translations->getEnabledLanguages();

			$cache->save(
				$enabled_languages,
				'enabled_languages',
				$tags=array('translate')
			);
		}

        $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
        Zend_Registry::set('siteDomain', $opt['general']['domain']);

        Zend_Registry::set('siteVaraint', 'autoauto.pl');
        $siteTitle = "AutoAuto.pl";

		$language = $request->getParam('language');

        if(isset($_SERVER['HTTP_HOST']))
        {
            if(strpos($_SERVER['HTTP_HOST'], "autoauto.by") !== false)
            {
                if (empty($language)) {
                    $language = 'ru';
                    $request->setParam('language', $language);
                }

                $siteTitle = "AutoAuto.by";
                Zend_Registry::set('siteVaraint', 'autoauto.by');
                Zend_Registry::set('siteDomain',  $opt['general']['domain_by']);
                Zend_Registry::set('carTaker', 190);
                Zend_Registry::set('randomEmploye', 190);
            }

            else if(strpos($_SERVER['HTTP_HOST'], "usa.autoauto.pl") !== false)
            {
                $redirector = Zend_Controller_Action_HelperBroker::getStaticHelper('redirector');
                $redirector->setCode(301)
                    ->goToUrl('http://autoauto.pl/pl/list?addName[]=production&addValue_0[]=24');
            }

            else if(strpos($_SERVER['HTTP_HOST'], "autoautoua.pl") !== false)
            {
                if (empty($language)) {
                    $language = 'ua';
                    $request->setParam('language', $language);
                }
            }


        }

		if (empty($language)) {
			$language = 'pl';
			$request->setParam('language', $language);
		}

		if (!array_key_exists($language, $enabled_languages)) {
			$language = 'pl';
			$request->setParam('language', $language);
		}



		$translate = null;
		$cacheId = 'translate_' . $language;
		if (!$translate = $cache->load($cacheId)) {

			$translate = new Zend_Translate(
				'array',
				$translations->getTranslations($language),
				$language
			);


			$cache->save(
				$translate,
				$cacheId,
				$tags=array('translate', $language)
			);
		}


		Zend_Registry::set('Zend_Translate', $translate);
		Zend_Registry::set('translate_language', $language);
		Zend_Form::setDefaultTranslator($translate);

		$view = Zend_Layout::getMvcInstance()->getView();

		$view->translate = $translate;
		$view->language = $language;
		$view->language_row = $enabled_languages[$language];
        $view->siteVariant =  Zend_Registry::get('siteVaraint');
        $view->siteTitle = $siteTitle;
		//change language links:

		$languageData = array();
		$languageData['queryString'] = $request->getServer('QUERY_STRING');
		$languageData['url'] = $view->url();
		$languageData['postData'] = null;


		if ($request->isPost()) {
			$languageData['postData'] = $request->getPost();
		}
		$view->changeLanguageData = $languageData;
	}

    public function postDispatch(Zend_Controller_Request_Abstract $request) {
        $view = Zend_Layout::getMvcInstance()->getView();

        $view->changeLanguageData['hash'] = null;
        if(!$request->getParam('hash'))
            $view->changeLanguageData['hash'] = $view->hash;
    }
}
