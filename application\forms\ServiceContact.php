<?php

class Form_ServiceContact extends My_Form {

	public function init() {
		$this->addElements(array(
			new Zend_Form_Element_Textarea('content', array(
				'label'	=>	'SRV_FORM_CONTENT',
				'required' => true
			)),
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true
			)),
			new Zend_Form_Element_Text('contact', array(
				'label'	=>	'SRV_FORM_CONTACT',
				'required' => true
			)),
			new Zend_Form_Element_Text('vin', array(
				'label'	=>	'SRV_FORM_VIN'
			)),
			new Zend_Form_Element_Captcha('captcha', array(
				'label' => 'CAPTCHA',
				'captcha' => new My_Captcha_Math(array(
					'timeout' => '180',
				)),
			)),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('send', array(
				'label'	=>	'SEND'
			)),
		));
		
		parent::init();
	}//init

}