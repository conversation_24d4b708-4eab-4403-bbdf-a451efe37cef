<div id="service_content_static">
	<div id="service_wrapper">
		<h1 id="service_title"><?= $this->translate->_('SERVICE_TITLE') ?></h1>
		<div id="service_content" class="<?= $this->containerClass ?>">
			<p><?= $this->translate->_('SERVICE_TEXT_1') ?></p>
			<p><?= $this->translate->_('SERVICE_TEXT_2') ?></p>
		</div>
	</div>
	<div id="service_contact">
		<h2 class="no_padding"><?= $this->translate->_('SERVICE_CONTACT_DATA') ?></h2>
		<div class="larger">
			<?= $this->translate->_('OFFICE') ?>: <?= $this->escape($this->serviceData['contact_office_phone']) ?>
			<br />
			<?= $this->escape($this->serviceData['contact_person']) ?>: <?= $this->escape($this->serviceData['contact_person_phone']) ?>
			<br />
			<a href="mailto:<?= $this->escape($this->serviceData['contact_person_email']) ?>"><?= $this->escape($this->serviceData['contact_person_email']) ?></a>
		</div>
		
		<br />
		
		<h2 class="no_padding"><?= $this->translate->_('SERVICE_CONTACT_HEADER') ?></h2>
		<?= $this->form ?>
	</div>
</div>

<script type="text/javascript">
	$(function(){
		$("#service_contact .input_textarea textarea, #service_contact .input_text input").compactInputs({
			getLabelFn: function(el) {
				return $(el).closest(".form_input").siblings(".form_label").find("label");
			}
		});
		
		$("#service_contact .form_label.for_captcha").hide();
		$("input[name='captcha[input]']").before("<br />");
	});
</script>