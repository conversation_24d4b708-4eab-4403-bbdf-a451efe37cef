<html>
<head>
<title>Docs for page qrcode.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">com-tecnick-tcpdf</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_com-tecnick-tcpdf.html" class="menu">class tree: com-tecnick-tcpdf</a> ]
		  [ <a href="../elementindex_com-tecnick-tcpdf.html" class="menu">index: com-tecnick-tcpdf</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_com-tecnick-tcpdf.html">com-tecnick-tcpdf</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../com-tecnick-tcpdf/_2dbarcodes.php.html">		2dbarcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_barcodes.php.html">		barcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_htmlcolors.php.html">		htmlcolors.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_qrcode.php.html">		qrcode.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_tcpdf.php.html">		tcpdf.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_config---tcpdf_config.php.html">		tcpdf_config.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_unicode_data.php.html">		unicode_data.php
		</a><br>
	  </div><br />
      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../com-tecnick-tcpdf/QRcode.html">QRcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF.html">TCPDF</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF2DBarcode.html">TCPDF2DBarcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDFBarcode.html">TCPDFBarcode</a><br />
	  </div>
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: qrcode.php</h1>
Source Location: /qrcode.php<br /><br />

<br>
<br>

<div class="contents">
<h2>Classes:</h2>
<dt><a href="../com-tecnick-tcpdf/QRcode.html">QRcode</a></dt>
	<dd>Class to create QR-code arrays for TCPDF class.</dd>
</div><br /><br />

<h2>Page Details:</h2>
Class to create QR-code arrays for TCPDF class.<br /><br /><p>QR Code symbol is a 2D barcode that can be scanned by handy terminals such as a mobile phone with CCD.  The capacity of QR Code is up to 7000 digits or 4000 characters, and has high robustness.  This class supports QR Code model 2, described in JIS (Japanese Industrial Standards) X0510:2004 or ISO/IEC 18004.  Currently the following features are not supported: ECI and FNC1 mode, Micro QR Code, QR Code model 1, Structured mode.</p><p>This class is derived from &quot;PHP QR Code encoder&quot; by Dominik Dzienia (http://phpqrcode.sourceforge.net/) based on &quot;libqrencode C library 3.1.1.&quot; by Kentaro Fukuchi (http://megaui.net/fukuchi/works/qrencode/index.en.html), contains Reed-Solomon code written by Phil Karn, KA9Q. QR Code is registered trademark of DENSO WAVE INCORPORATED (http://www.denso-wave.com/qrcode/index-e.html).  Please read comments on this class source file for full copyright and license information.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Nicola Asuni</td>
  </tr>
  <tr>
    <td><b>version:</b>&nbsp;&nbsp;</td><td>1.0.000</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2010 Nicola Asuni - Tecnick.com S.r.l (www.tecnick.com) Via Della Pace, 11 - 09044 - Quartucciu (CA) - ITALY - www.tecnick.com - <EMAIL></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.tcpdf.org">http://www.tcpdf.org</a></td>
  </tr>
  <tr>
    <td><b>abstract:</b>&nbsp;&nbsp;</td><td>Class for generating QR-code array for TCPDF.</td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a></td>
  </tr>
</table>
</div>
<br /><br />
<br /><br />
<br /><br />
  <hr />
	<a name="defineMAX_STRUCTURED_SYMBOLS"></a>
	<h3>MAX_STRUCTURED_SYMBOLS <span class="smalllinenumber">[line 212]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>MAX_STRUCTURED_SYMBOLS = 16</code>
    </td></tr></table>
    </td></tr></table>

    Max number of symbols for structured mode<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineN1"></a>
	<h3>N1 <span class="smalllinenumber">[line 221]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>N1 = 3</code>
    </td></tr></table>
    </td></tr></table>

    Down point base value for case 1 mask pattern (concatenation of same color in a line or a column)<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineN2"></a>
	<h3>N2 <span class="smalllinenumber">[line 226]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>N2 = 3</code>
    </td></tr></table>
    </td></tr></table>

    Down point base value for case 2 mask pattern (module block of same color)<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineN3"></a>
	<h3>N3 <span class="smalllinenumber">[line 231]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>N3 = 40</code>
    </td></tr></table>
    </td></tr></table>

    Down point base value for case 3 mask pattern (1:1:3:1:1(dark:bright:dark:bright:dark)pattern in a line or a column)<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineN4"></a>
	<h3>N4 <span class="smalllinenumber">[line 236]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>N4 = 10</code>
    </td></tr></table>
    </td></tr></table>

    Down point base value for case 4 mask pattern (ration of dark modules in whole)<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQRCAP_EC"></a>
	<h3>QRCAP_EC <span class="smalllinenumber">[line 198]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QRCAP_EC = 3</code>
    </td></tr></table>
    </td></tr></table>

    Matrix index to get error correction level from $capacity array.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQRCAP_REMINDER"></a>
	<h3>QRCAP_REMINDER <span class="smalllinenumber">[line 193]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QRCAP_REMINDER = 2</code>
    </td></tr></table>
    </td></tr></table>

    Matrix index to get remainder from $capacity array.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQRCAP_WIDTH"></a>
	<h3>QRCAP_WIDTH <span class="smalllinenumber">[line 183]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QRCAP_WIDTH = 0</code>
    </td></tr></table>
    </td></tr></table>

    Matrix index to get width from $capacity array.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQRCAP_WORDS"></a>
	<h3>QRCAP_WORDS <span class="smalllinenumber">[line 188]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QRCAP_WORDS = 1</code>
    </td></tr></table>
    </td></tr></table>

    Matrix index to get number of words from $capacity array.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQRCODEDEFS"></a>
	<h3>QRCODEDEFS <span class="smalllinenumber">[line 99]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QRCODEDEFS = true</code>
    </td></tr></table>
    </td></tr></table>

    Indicate that definitions for this class are set<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQRSPEC_VERSION_MAX"></a>
	<h3>QRSPEC_VERSION_MAX <span class="smalllinenumber">[line 171]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QRSPEC_VERSION_MAX = 40</code>
    </td></tr></table>
    </td></tr></table>

    Maximum QR Code version.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQRSPEC_WIDTH_MAX"></a>
	<h3>QRSPEC_WIDTH_MAX <span class="smalllinenumber">[line 176]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QRSPEC_WIDTH_MAX = 177</code>
    </td></tr></table>
    </td></tr></table>

    Maximum matrix size for maximum version (version 40 is 177*177 matrix).<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_DEFAULT_MASK"></a>
	<h3>QR_DEFAULT_MASK <span class="smalllinenumber">[line 255]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_DEFAULT_MASK = 2</code>
    </td></tr></table>
    </td></tr></table>

    when QR_FIND_BEST_MASK === false<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_ECLEVEL_H"></a>
	<h3>QR_ECLEVEL_H <span class="smalllinenumber">[line 159]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_ECLEVEL_H = 3</code>
    </td></tr></table>
    </td></tr></table>

    Error correction level H : About 30% or less errors can be corrected.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_ECLEVEL_L"></a>
	<h3>QR_ECLEVEL_L <span class="smalllinenumber">[line 144]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_ECLEVEL_L = 0</code>
    </td></tr></table>
    </td></tr></table>

    Error correction level L : About 7% or less errors can be corrected.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_ECLEVEL_M"></a>
	<h3>QR_ECLEVEL_M <span class="smalllinenumber">[line 149]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_ECLEVEL_M = 1</code>
    </td></tr></table>
    </td></tr></table>

    Error correction level M : About 15% or less errors can be corrected.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_ECLEVEL_Q"></a>
	<h3>QR_ECLEVEL_Q <span class="smalllinenumber">[line 154]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_ECLEVEL_Q = 2</code>
    </td></tr></table>
    </td></tr></table>

    Error correction level Q : About 25% or less errors can be corrected.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_FIND_BEST_MASK"></a>
	<h3>QR_FIND_BEST_MASK <span class="smalllinenumber">[line 245]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_FIND_BEST_MASK = true</code>
    </td></tr></table>
    </td></tr></table>

    if true, estimates best mask (spec. default, but extremally slow; set to false to significant performance boost but (propably) worst quality code<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_FIND_FROM_RANDOM"></a>
	<h3>QR_FIND_FROM_RANDOM <span class="smalllinenumber">[line 250]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_FIND_FROM_RANDOM = 2</code>
    </td></tr></table>
    </td></tr></table>

    if false, checks all masks available, otherwise value tells count of masks need to be checked, mask id are got randomly<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_MODE_8B"></a>
	<h3>QR_MODE_8B <span class="smalllinenumber">[line 123]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_MODE_8B = 2</code>
    </td></tr></table>
    </td></tr></table>

    Encoding mode 8bit byte data. In theory, 2953 characters or less can be stored in a QRcode.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_MODE_AN"></a>
	<h3>QR_MODE_AN <span class="smalllinenumber">[line 118]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_MODE_AN = 1</code>
    </td></tr></table>
    </td></tr></table>

    Encoding mode alphanumeric (0-9A-Z $%*+-./:) 45characters. 2 characters are encoded to 11bit length. In theory, 4296 characters or less can be stored in a QRcode.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_MODE_KJ"></a>
	<h3>QR_MODE_KJ <span class="smalllinenumber">[line 128]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_MODE_KJ = 3</code>
    </td></tr></table>
    </td></tr></table>

    Encoding mode KANJI. A KANJI character (multibyte character) is encoded to 13bit length. In theory, 1817 characters or less can be stored in a QRcode.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_MODE_NL"></a>
	<h3>QR_MODE_NL <span class="smalllinenumber">[line 108]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_MODE_NL = -1</code>
    </td></tr></table>
    </td></tr></table>

    Encoding mode<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_MODE_NM"></a>
	<h3>QR_MODE_NM <span class="smalllinenumber">[line 113]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_MODE_NM = 0</code>
    </td></tr></table>
    </td></tr></table>

    Encoding mode numeric (0-9). 3 characters are encoded to 10bit length. In theory, 7089 characters or less can be stored in a QRcode.<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineQR_MODE_ST"></a>
	<h3>QR_MODE_ST <span class="smalllinenumber">[line 133]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QR_MODE_ST = 4</code>
    </td></tr></table>
    </td></tr></table>

    Encoding mode STRUCTURED (currently unsupported)<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineSTRUCTURE_HEADER_BITS"></a>
	<h3>STRUCTURE_HEADER_BITS <span class="smalllinenumber">[line 207]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>STRUCTURE_HEADER_BITS = 20</code>
    </td></tr></table>
    </td></tr></table>

    Number of header bits for structured mode<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
<br />
  <hr />
	<a name="functionstr_split"></a>
	<h3>str_split <span class="smalllinenumber">[line 273]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>If str_split(
string $string, [int $split_length = 1])</code>
    </td></tr></table>
    </td></tr></table><br />

		Convert a string to an array (needed for PHP4 compatibility)<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>the optional split_length  parameter is specified, the returned array will be broken down into chunks with each being split_length  in length, otherwise each chunk will be one character in length. FALSE is returned if split_length is less than 1. If the split_length length exceeds the length of string , the entire string is returned as the first (and only) array element.</td>
  </tr>
</table>
</div>
    <br /><br />
	
    		<h4>Parameters</h4>
    <table border="0" cellspacing="0" cellpadding="0">
		      <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$string</b>&nbsp;&nbsp;</td>
        <td>The input string.</td>
      </tr>
		      <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$split_length</b>&nbsp;&nbsp;</td>
        <td>Maximum length of the chunk.</td>
      </tr>
				</table>
    	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
	</div>

        <div class="credit">
		    <hr />
		    Documentation generated on Sun, 28 Mar 2010 22:22:40 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>