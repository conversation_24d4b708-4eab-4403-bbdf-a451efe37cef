</div>

<div id="sell">
    <div class="container">
        <div class="row py-4">
            <div class="col-lg-12">

                <h1 class="title">
                    <?php
                    $text = explode(" ", $this->translate->_('SELL_CAR'));
                    echo $text[0] . ' <span class="orange-color">' . $text[1] . '</span>';
                    ?>
                </h1>

            </div>

            <div class="col-lg-12">
                <?= $this->translate->_('SELL_CAR_INFO_4') ?>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">

                <h2 class="title">Formularz  <span class="orange-color">sprzedaży</span> lub zamiany  <span class="orange-color">auta</span> </h2>

            </div>

            <div class="col-lg-12">
                <?= $this->translate->_('SELL_CAR_HEADER') ?>
            </div>
        </div>
    </div>
    <div class="container my-4">

    <form action="<?php echo $this->form->getAction() ?>"
          enctype="<?php echo $this->form->getEnctype() ?>"
          method="<?php echo $this->form->getMethod() ?>"
          class="sell-form form-style-aa">



            <div class="row gray-background pt-4" style="max-width: 850px">

                <div class="col-lg-12<?= $this->form->no_accident->hasErrors() ? ' has-danger' : '' ?>">
                    <div class="form-check form-check-inline">
                        <?= $this->form->no_accident->renderLabel() ?>
                        <?= $this->form->no_accident->renderViewHelper() ?>
                        <?php if($this->form->no_accident->hasErrors()): ?>
                            <div class="form-control-feedback"><?= $this->form->no_accident->renderErrors() ?></div>
                        <?endif ?>
                    </div>

                </div>

                <div class="col-lg-6">
                    <div class="form-group<?= $this->form->make->hasErrors() ? ' has-danger' : '' ?>">

                            <?= $this->form->make->renderViewHelper() ?>
                            <?php if($this->form->make->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->form->make->renderErrors() ?></div>
                            <?endif ?>
                    </div>
                    <div class="form-group<?= $this->form->model->hasErrors() ? ' has-danger' : '' ?>">

                        <?= $this->form->model->renderViewHelper() ?>
                        <?php if($this->form->model->hasErrors()): ?>
                            <div class="form-control-feedback"><?= $this->form->model->renderErrors() ?></div>
                        <?endif ?>
                    </div>
                    <div class="form-group<?= $this->form->build_year->hasErrors() ? ' has-danger' : '' ?>">

                        <?= $this->form->build_year->renderViewHelper() ?>
                        <?php if($this->form->build_year->hasErrors()): ?>
                            <div class="form-control-feedback"><?= $this->form->build_year->renderErrors() ?></div>
                        <?endif ?>
                    </div>
                    <div class="form-group<?= $this->form->engine->hasErrors() ? ' has-danger' : '' ?>">

                        <?= $this->form->engine->renderViewHelper() ?>
                        <?php if($this->form->engine->hasErrors()): ?>
                            <div class="form-control-feedback"><?= $this->form->engine->renderErrors() ?></div>
                        <?endif ?>
                    </div>
                    <div class="form-group<?= $this->form->color->hasErrors() ? ' has-danger' : '' ?>">

                        <?= $this->form->color->renderViewHelper() ?>
                        <?php if($this->form->color->hasErrors()): ?>
                            <div class="form-control-feedback"><?= $this->form->color->renderErrors() ?></div>
                        <?endif ?>
                    </div>
                    <div class="form-group<?= $this->form->odometer->hasErrors() ? ' has-danger' : '' ?>">

                        <?= $this->form->odometer->renderViewHelper() ?>
                        <?php if($this->form->odometer->hasErrors()): ?>
                            <div class="form-control-feedback"><?= $this->form->odometers->renderErrors() ?></div>
                        <?endif ?>
                    </div>
                    <div class="form-group<?= $this->form->origin->hasErrors() ? ' has-danger' : '' ?>">
                        <?= $this->form->origin->renderViewHelper() ?>
                        <?php if($this->form->origin->hasErrors()): ?>
                            <div class="form-control-feedback"><?= $this->form->origin->renderErrors() ?></div>
                        <?endif ?>
                    </div>
                </div>


                <div class="col-lg-6 d-flex">
                    <div class="form-group<?= $this->form->comments->hasErrors() ? ' has-danger' : '' ?> w-100">
                        <?= $this->form->comments->renderViewHelper() ?>
                        <?php if($this->form->comments->hasErrors()): ?>
                            <div class="form-control-feedback"><?= $this->form->comments->renderErrors() ?></div>
                        <?endif ?>
                    </div>
                </div>
            </div>

        <div class="row gray-background pt-4 mt-4" style="max-width: 850px">

            <div class="col-lg-12<?= $this->form->exchange->hasErrors() ? ' has-danger' : '' ?>">
                <div class="form-check form-check-inline">
                    <?= $this->form->exchange->renderLabel() ?>
                    <?= $this->form->exchange->renderViewHelper() ?>
                    <?php if($this->form->exchange->hasErrors()): ?>
                        <div class="form-control-feedback"><?= $this->form->exchange->renderErrors() ?></div>
                    <?endif ?>
                </div>

            </div>

            <div class="col-lg-6">
                <div class="form-group<?= $this->form->exchange_makes->hasErrors() ? ' has-danger' : '' ?>">

                    <?= $this->form->exchange_makes->renderViewHelper() ?>
                    <?php if($this->form->exchange_makes->hasErrors()): ?>
                        <div class="form-control-feedback"><?= $this->form->exchange_makes->renderErrors() ?></div>
                    <?endif ?>
                </div>
                <div class="form-group<?= $this->form->exchange_models->hasErrors() ? ' has-danger' : '' ?>">

                    <?= $this->form->exchange_models->renderViewHelper() ?>
                    <?php if($this->form->exchange_models->hasErrors()): ?>
                        <div class="form-control-feedback"><?= $this->form->exchange_models->renderErrors() ?></div>
                    <?endif ?>
                </div>
            </div>


            <div class="col-lg-6 d-flex">
                <div class="form-group<?= $this->form->exchange_comments->hasErrors() ? ' has-danger' : '' ?> w-100">
                    <?= $this->form->exchange_comments->renderViewHelper() ?>
                    <?php if($this->form->exchange_comments->hasErrors()): ?>
                        <div class="form-control-feedback"><?= $this->form->exchange_comments->renderErrors() ?></div>
                    <?endif ?>
                </div>
            </div>
        </div>

        <div class="row gray-background pt-4 mt-4" style="max-width: 850px">

            <div class="col-lg-6">
                <div class="form-group<?= $this->form->email->hasErrors() ? ' has-danger' : '' ?>">

                    <?= $this->form->email->renderViewHelper() ?>
                    <?php if($this->form->email->hasErrors()): ?>
                        <div class="form-control-feedback"><?= $this->form->email->renderErrors() ?></div>
                    <?endif ?>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="form-group<?= $this->form->phone->hasErrors() ? ' has-danger' : '' ?>">

                    <?= $this->form->phone->renderViewHelper() ?>
                    <?php if($this->form->phone->hasErrors()): ?>
                        <div class="form-control-feedback"><?= $this->form->phone->renderErrors() ?></div>
                    <?endif ?>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="form-group<?= $this->form->first_name->hasErrors() ? ' has-danger' : '' ?>">

                    <?= $this->form->first_name->renderViewHelper() ?>
                    <?php if($this->form->first_name->hasErrors()): ?>
                        <div class="form-control-feedback"><?= $this->form->first_name->renderErrors() ?></div>
                    <?endif ?>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="form-group<?= $this->form->last_name->hasErrors() ? ' has-danger' : '' ?>">

                    <?= $this->form->last_name->renderViewHelper() ?>
                    <?php if($this->form->last_name->hasErrors()): ?>
                        <div class="form-control-feedback"><?= $this->form->last_name->renderErrors() ?></div>
                    <?endif ?>
                </div>
            </div>
        </div>

        <div class="row t-4 mt-4" style="max-width: 850px">

            <div class="col-lg-7 green-border">
                <p >
                    Każde biuro wycenia niezależnie! tak wiec inne auto - inna wycena.
                    Może być tak ze nawet kolejnego dnia dostaniecie Państwo inną propozycje, bo to zależy bardzo od chwilowych ruchów na rynku i dostępności środków na odkup tego typu auta. Prosimy się nie zrażać i negocjować.
                    Jeśli w jednym biurze nie dojdziemy do porozumienia - inne biuro ma podobne auto i tam wystarczy zadzwonić albo podjechać!
                </p>
            </div>
            <div class="col-lg-5">
                <div class="form-group <?= $this->form->photo->hasErrors() ? ' has-danger' : '' ?>">
                    <button type="button" class="btn btn-action btn-action-gray2 add-photo">Dodaj zdjęcie</button>
                    <div style="display: none">
                        <?= $this->form->photo->renderFile() ?>
                    </div>
                </div>

                <div class="form-group <?= $this->form->captcha->hasErrors() ? ' has-danger' : '' ?>">
                        <?= $this->form->captcha->renderCaptcha() ?>
                        <?php if($this->form->captcha->hasErrors()): ?>
                            <div class="form-control-feedback"><?= $this->form->captcha->renderErrors() ?></div>
                        <?endif ?>
                </div>
                <?= $this->form->csrf->renderViewHelper() ?>
                <div class="form-group">

                    <?= $this->form->submit->renderViewHelper() ?>

                </div>
            </div>

        </div>


    </div>
    </form>

    <div class="container">


        <div id="search_results" class="" style="margin-top: 10px">




            <table id="results_top_paginator" cellspacing="0" cellpadding="0">
                <tr>


                    <td class="middle">
                        <?php if ($this->paginator && $this->paginator->getTotalItemCount() > $this->paginator->getItemCountPerPage()): ?>
                            <?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''), 'carListShowMax' => $this->pagingMaxResults, 'carListShowDefault' => $this->pagingDefaultResults, 'translate' => $this->translate)); ?>
                        <?php else: ?>
                            <div class="paginator"><div class="paginator_inner"></div></div>
                        <?php endif; ?>

                    </td>


            </table>

            <?php if ($this->paginator): ?>
                <?php foreach($this->paginator as $car):


                    ?>
                    <div class="car-list py-4">
                    <div class="cars list pt-3">
                    <div class="car row pb-4<?=  (!empty($car['photo'])) ? '' : ' no-photo'?>">
                        <div class="col-lg-3">
                            <div class="img d-flex justify-content-center align-items-center">

                                <?php if (!empty($car['photo'])):?>

                                    <a href="#">

                                        <?php

                                        $pathinfo = pathinfo($car['photo']);
                                        $filename =$pathinfo['filename'];
                                        $extension = $pathinfo['extension'];

                                        ?>

                                        <img class="img-fluid" src="<?= $this->domain ?>/images/user-sell-exchange/<?= $this->escape($filename) ?>_M.<?= $this->escape($extension) ?>" alt="">
                                    </a>




                                <?php else: ?>

                                    <a class="d-flex justify-content-center align-items-center" href="#">


                                        <img class="img-fluid " src="/files/car.svg" alt="">

                                    </a>

                                <?php endif ?>



                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="d-flex">
                                <div class="location col">
                                    <?= $car['make_name'] . ' '.  $car['model_name'] . ' '.  $car['build_year']?>
                                </div>
                            </div>
                            <div class="title">
                                <?= $this->translate->_('MAKE') ?>, <?= $this->translate->_('MODEL') ?>:</td><td><?= $this->escape($car['make_name']) . " " . $this->escape($car['model_name']) ?>
                            </div>

                            <div class="added">
                                data dodania: <?= $this->escape(date('Y-m-d',strtotime($car['added_datetime']))) ?>        </div>

                            <table class="table">

                                <tr>
                                    <td><?= $this->translate->_('MAKE') ?>, <?= $this->translate->_('MODEL') ?>:</td><td><?= $this->escape($car['make_name']) . " " . $this->escape($car['model_name']) ?></td>
                                </tr>
                                <tr>

                                    <td><?= $this->translate->_('BUILD') ?>:</td><td><?= $this->escape($car['build_year']) ?></td>


                                </tr>
                                <tr>
                                    <td><?= $this->translate->_('ENGINE') ?>:</td><td><?= $this->escape($car['engine']) ?></td>

                                </tr>
                                <tr>
                                    <td><?= $this->translate->_('COLOR') ?>:</td><td><?= $this->escape($car['color']) ?></td>

                                </tr>
                                <tr>
                                    <td><?= $this->translate->_('ODOMETER') ?>:</td><td><?= $this->escape($car['odometer']) ?></td>
                                </tr>
                                <tr>
                                    <td><?= $this->translate->_('SELL_CAR_ORIGIN') ?>:</td><td><?= $this->escape($car['origin']) ?></td>
                                </tr>
                                <tr>
                                    <td><?= $this->translate->_('SELL_CAR_NO_ACCIDENT') ?>:</td><td> <?= $car['no_accident'] == 'y' ? $this->translate->_('YES') : $this->translate->_('NO') ?></td>
                                </tr>


                                <?php if($car['is_for_exchange']): ?>
                                    <tr>
                                        <td><?= $this->translate->_('SELL_CAR_EXCHANGE_FOR') ?></td><td><?= $this->escape($car['exchange_makes']) ?></td>
                                    </tr>
                                <?php endif ?>


                            </table>


                        <div class="description">
                            <div class="title"><?= $this->translate->_('SELL_CAR_DESCRIPTION') ?></div>
                            <?= $this->escape($car['comments']) ?>
                        </div>

                        </div>
                        <div class="col-lg-3">

                            <div class="actions d-flex flex-column">
                                <div class="row">
                                    <div class="col-lg-12">

                                        <div class="caretaker">
                                            <div class="phone"><?= $this->escape($car['caretaker_phone']) ?></div>

                                            <div class="name"><?= $this->escape($car['caretaker_first_name'] . " " . $car['caretaker_last_name']) ?></div>
                                        </div>

                                    </div>
                                </div>


                            </div>
                        </div>


                    </div>
                    </div>





                <?php endforeach; ?>


                <?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''), 'carListShowMax' => $this->pagingMaxResults, 'carListShowDefault' => $this->pagingDefaultResults, 'translate' => $this->translate)); ?>
            <?php endif ?>

        </div>




    </div>


</div>
</div>





<script type="text/javascript">
    <?php $this->inlineScript()->captureStart() ?>
    $(function() {

        $('.add-photo').on('click',function() {
            $('#photo').trigger('click');
        })

        $('#exchange').on('change',function() {
           if($(this).prop( "checked" ))  {

               $('#exchange_makes').removeAttr('disabled');
               $('.add-photo').show();
           } else {
               $('#exchange_makes').attr('disabled', 'disabled');
               $('#exchange_models').attr('disabled', 'disabled');

               $('.add-photo').hide();
           }
        });

        $('#exchange').trigger('change');

        $('#exchange_makes').on("change", function (e) {
            console.log("change");

            var makeIds= 0
            if($(this).data('ids') == 1) {
                makeIds = 1;
            }

            $.ajax({
                type: 'GET',
                url: '<?= $this->url(array(), 'getmodels', true); ?>',
                data: {makes: $('#exchange_makes').val(), 'make_ids': makeIds},
                dataType: 'json',
                async: false,
                success: function (data) {

                    $("#exchange_models").empty();
                    $("#exchange_models").append('<option value=""></option>');
                    for (k in data) {
                        $("#exchange_models").append('<option value="' + k + '">' + data[k] + '</option>');
                    }

                    $("#exchange_models").prop('disabled', false);
                    $("#exchange_models").trigger('change');

                },
                error: function () {
                }
            });


        });

    });
    <?php $this->inlineScript()->captureEnd() ?>
</script>