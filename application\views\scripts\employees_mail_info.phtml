<html>
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8" />

  <title></title>
</head>
<body style="padding: 15px; padding-top: 5px;">
  <p style="text-align: left; font-size: 13px; font-family: Arial, sans-serif; line-height: 18px;">
      
      <?php foreach($this->searchUsers as $user): ?>
      <?= $user['email'] . ' ' . $user['first_name'] . ' '. $user['last_name'] . ' nr tel: '. (!empty($user['phone']) ? $user['phone'] : 'brak' )  . ' '. $user['search_name']   . ' '. $user['added_datetime']?>
                <a href="<?= $this->domain ?><?= $this->url(array('language' => $this->language, 'hash' => $user['hash']), 'editSearchList', true) ?>">Pokaż kryteria</a> <a href="<?= $this->domain ?><?= $this->url(array('language' => $this->language), 'searchList', true).'?set='.$user['hash'].'&date='.date('Y-m-d H:i:s') ?>">Pokaż wysłane</a><br /><br />
      


            <br /><br />
                        
      <?php endforeach ?>
   
  <br /><br /><br />

  <span style="color: #949494">Pozdrawiamy,<br />
  załoga autoauto.pl<br /><br />
  ------------------------------------------------------------------------------------------------------------<br />
  Ten e-mail został‚ wygenerowany automatycznie, prosimy nie odpowiadać na niego.</span>
  </p>
</body>
</html>