<?php

class Form_NewSearch extends My_Form {
	
	protected $_appendForms = array();
	protected $_replaceSubmit = null;
	protected $_variant = "full";
    protected $_deleteSubmit = false;
    protected $_searchParameters = array();
    protected $_searchParametersCacheString = null;
	

	protected function array_merge_keys(){
		$args = func_get_args();
		$result = array();
		foreach($args as &$array){
			foreach($array as $key => &$value){
				$result[$key] = $value;
			}
		}
		return $result;
	}

	public function init() {



		$cars = new Model_Cars_Cars();
		$view = Zend_Layout::getMvcInstance()->getView();
		$this->setAction($view->url(array('language' => $view->language), 'list', true));
		$tr = Zend_Registry::get('Zend_Translate');

		$this->addElements(array(

            new Zend_Form_Element_Select('make', array(
                'label'	=>	'MAKE',
                'validators' => array(),
                'multiOptions' =>  array("" => "" ) + $cars->getCarMakesWithCountsByNames(null, null, false, $forSelect=true,true),
                'attribs' => array('class' => 'form-control', 'data-ids' => 1, 'data-placeholder' => $tr->_('MAKE'))
            )),
            new Zend_Form_Element_Select('model', array(
                'label'	=>	'MODEL',
                'validators' => array(),
                'registerInArrayValidator' => false,
                'attribs' => array( 'disabled' => 'disabled', 'class' => 'select2 form-control' , 'data-placeholder' => $tr->_('MODEL'))
            )),

            new Zend_Form_Element_Text('price_min', array(
                'label'	=>	'MINPRICE',
                'validators' => array(),
                'registerInArrayValidator' => false,
				'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('MINPRICE'))
            )),
            new Zend_Form_Element_Text('price_max', array(
                'label'	=>	'MAXPRICE',
                'validators' => array(),
                'registerInArrayValidator' => false,
				'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('MAXPRICE'))
            )),

            new Zend_Form_Element_Text('build_from', array(
                'label'	=>	'BUILD_FROM',
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('BUILD_FROM'))
            )),
            new Zend_Form_Element_Text('build_to', array(
                'label'	=>	'BUILD_TO',
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('BUILD_TO'))
            )),

            new Zend_Form_Element_Text('odometer_to', array(
                'label'	=>	'ODOMETER_TO',
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('ODOMETER_TO')),
                'validators' => array(),
       )),

            new Zend_Form_Element_Select('engine', array(
                'label'	=>	'ENGINE',
                'multiOptions' => array(
                    "" => "",
                    'fuels petrol' => 'petrol',
                    'fuels diesel' => 'diesel',
                    'fuels petrol_gas' => 'petrol_gas',
                    'fuels hybryd' => 'hybryd',
                    'fuels electric' => 'electric',

                ),
                'attribs' => array('class' => 'select2 form-control', 'data-placeholder' => $tr->_('ENGINE')),
                'registerInArrayValidator' => false //no nie da rady inaczej z shortcutami zrobic
            )),

            new Zend_Form_Element_Select('origin_country', array(
                'label'	=>	'Pochodzenie',
                'value' => '',
                'attribs' => array('class' => 'select2 form-control', 'data-placeholder' => $tr->_('Pochodzenie')),
                'validators' => array(),
                'multiOptions' =>  array("" => "" ) + $cars->getCountries(),
            )),


                new Zend_Form_Element_Multiselect('important_features', array(
                    'label'	=>	'IMPORTANT_FEATURES',
                    'attribs' => array('class' => 'select2 select2-multiselect form-control', 'data-placeholder' => $tr->_('IMPORTANT_FEATURES')),
                    'multiOptions' => array(
                        $tr->_('IFC_OPTIONS') => array(
                            'premium 1' => mb_strtolower($tr->_('SHORTCUT_PREMIUM')),
                            'extras leather_interior' => 'leather_interior',
                            'extras velour' => 'velour_interior',
                            'features v6' => $tr->_('V6'),
                            'features v8' => $tr->_('V8'),
                            'extras awd' => 'awd',
                            'extras air_conditioning' => 'air_conditioning',
                            'extras climatronic' => 'climatronic',
                            'extras sunroof' => 'sunroof',
                            'extras hardtop' => 'hardtop',
                            'extras tempomat' => 'tempomat',
                            'extras electric_seats' => 'electric_seats',
                            'extras navigation' => 'promo_navigation',
                            'extras parking_sensor' => 'promo_parking_sensor',
                            'features warranty' => 'promo_warranty',
                            'features tinted_windows' => 'promo_tinted_windows',
                            'features remove_scratches' => 'promo_remove_scratches'
                        ),
                        $tr->_('IFC_SIZE') => array(
                            'features small_car' => 'small_car',
                            'features medium_car' => 'medium_car',
                            'features big_car' => 'big_car',
                            'seat_count_min 6' => '6 ' . $tr->_('SEAT_COUNT_MIN'),
                            'door_count 2_3' => 'doors_2_3',
                            'door_count 5' => 'doors_5',
                            'consumption_less_than 10' => $tr->_('CONSUMPTION_LESS_THAN') . ' 10 l.',
                            'displacement_max 2' => $tr->_('DISPLACEMENT_VERY_SHORT') . ' < 2 l',
                            'power >300hp' => $tr->_('>300hp'),
                            'power >200hp' => $tr->_('>200hp'),
                        ),

                        $tr->_('IFC_COLOURS') => array(
                            'colour_key 5' => 'color_black',
                            'colour_key_not 5' => 'color_not_black',
                            'colour_key 2' => 'color_white',
                            'colour_key_not 2' => 'color_not_white',
                            'features light_interior' => 'light_interior',
                            'features dark_interior' => 'dark_interior',
                        ),
                        $tr->_('IFC_HISTORY') => array(
                            'features no_accident' => 'no_accident',
                            'origin 21' => 'ORIGIN_21',
                            'production 19' => 'PRODUCTION_19',
                            'production 33' => 'PRODUCTION_33',
                            'production 34' => 'PRODUCTION_34',
                            'production 26' => 'PRODUCTION_26',
                            'production 24' => 'PRODUCTION_24',
                            'origin_not 24' => 'ORIGIN_NOT_24',
                            'production 9' => 'PRODUCTION_9',
                            'production_not 9' => 'PRODUCTION_NOT_9',
                            'production_not 31' => 'PRODUCTION_NOT_31',
                            'for_exchange 1' => 'FOR_EXCHANGE',
                            'last_2_years 1' => $tr->_('SHORTCUT_LAST_2_YEARS'),
                            'on_site 1' => 'CAR_ON_SITE',
                        ),


                        $tr->_('IFC_INVOICE') => array(
                            'leasing 1' => 'VAT_LEASING',
                            'price_type_key brutto' => 'NO_VAT',
                            'leasing_transfer 1' => mb_strtolower($tr->_('SHORTCUT_LEASING_TRANSFER'))
                        )

                        //'features max_2_elements_painted' => 'max_2_elements_painted',











                        //'extras dvd' => 'promo_dvd',

                    ),
                    'registerInArrayValidator' => false //no nie da rady inaczej z shortcutami zrobic
                )),

            new Zend_Form_Element_Select('category', array(
                'label'	=>	'CATEGORY',
                'validators' => array(),
                'multiOptions' => array("" => "") + $cars->getCarCategoriesByName($all=false, $onlySelectable=true),
                'registerInArrayValidator' => false,
                'attribs' => array('class' => 'select2 form-control', 'data-placeholder' => $tr->_('CATEGORY')),
            )),

            new Zend_Form_Element_Select('financing', array(
                'label'	=>	'FINANCING',
                'validators' => array(),
                'multiOptions' => array("" => "") + $cars->getCarCategoriesByName($all=false, $onlySelectable=true),
                'registerInArrayValidator' => false,
                'attribs' => array('class' => 'select2 form-control', 'data-placeholder' => $tr->_('FINANCING')),
            )),

            new Zend_Form_Element_Text('cubic_capacity_from', array(
                'label'	=>	'ENGINE_CAPACITY_FROM',
                'value' => '',
                'multiOptions' => array(
                    "" => "",
                    '<20km' => '<20km',
                    '<50km' => '<50km',
                    '<100km' => '<100km',
                    '<150km' => '<150km',
                    '>=150km' => '>=150km'),
				'attribs' => array('class' => 'form-control','placeholder' => $tr->_('ENGINE_CAPACITY_FROM')),
            )),
            new Zend_Form_Element_Text('cubic_capacity_to', array(
                'label'	=>	'ENGINE_CAPACITY_TO',
                'value' => '',
                'multiOptions' => array(
                    "" => "",
                    '<20km' => '<20km',
                    '<50km' => '<50km',
                    '<100km' => '<100km',
                    '<150km' => '<150km',
                    '>=150km' => '>=150km'),
				 'attribs' => array('class' => 'form-control','placeholder' => $tr->_('ENGINE_CAPACITY_TO')),
            )),


            new Zend_Form_Element_Multiselect('gearboxes', array(
                'label'	=>	'GEARBOX',
                'multiOptions' => array(
                    'automatic' => 'automatic',
                    'manual' => 'manual',
                ),
                'attribs' => array('class' => 'select2 select2-multiselect', 'data-placeholder' => $tr->_('GEARBOX')),
                'registerInArrayValidator' => false //no nie da rady inaczej z shortcutami zrobic
            )),
            new Zend_Form_Element_Multiselect('drives', array(
                'label'	=>	'DRIVE',
                'multiOptions' => array(
                    'front-wheel-drive' => 'front_wheel_drive',
                    'rear-wheel-drive' => 'rear_wheel_drive',
                    '4x4' => '4x4',
                ),
                'attribs' => array('class' => 'select2 select2-multiselect', 'data-placeholder' => $tr->_('DRIVE')),
                'registerInArrayValidator' => false //no nie da rady inaczej z shortcutami zrobic
            )),

                new Zend_Form_Element_Text('query', array(
                    'label'	=>	'QUERY',
                    'value' => '',
                    'validators' => array(),
                    'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('QUERY'))
                )),

        ));




        $this->addElements(array(







			new Zend_Form_Element_Hidden('promotions', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('leasing', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('new', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('last_2_years', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('premium', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('exclusive', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('4x4_pickup', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('after_leasing_vindication', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('location', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('order', array(
				'label'	=>	''
			)),
            new Zend_Form_Element_Hidden('list_view', array(
                'label'	=>	''
            )),
			new Zend_Form_Element_Hidden('search_by_price_gross', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('on_site', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('leasing_transfer', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('credit_collateral', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('rental', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Hidden('price_type_key', array(
				'label'	=>	''
			)),

			new Zend_Form_Element_MultiSelect('types', array(
				'label'	=>	'',
				'validators' => array(),
				'registerInArrayValidator' => false,
				'attribs' => array('style' => 'display: none;')
			)),

            new Zend_Form_Element_Hidden('auction', array(
                'label'	=>	''
            )),
			new Zend_Form_Element_Hidden('hash', array(
				'label'	=>	''
			)),
			new Zend_Form_Element_Button('submit', array(
				'label' => 'FILTER',
				'value' => 'FILTER',
                'type' => 'submit'
			)),
		));

        $this->setSearchParameters();

		
		parent::init();


		$this->loadDefaultDecorators();
		$this->setElementDecorators(array(
			'ViewHelper',
			'Errors',
			array('Description', array('tag' => '', 'escape' => false)),
			'Label',
			array('HtmlTag', array('tag' => ''))
		));

	}
	
	public function isValid($data) {
		if ($this->models && isset($data['makes']) && is_array($data['makes'])) {
			$modelsArr = array();
			$cars = new Model_Cars_Cars();
			foreach($data['makes'] as $make) {
				$models = $cars->getCarModelsWithCountsFromName($make);
				$modelsArr = $this->array_merge_keys($modelsArr, $models);
			}
			$this->models->setMultiOptions($modelsArr);
		}
		return parent::isValid($data);
	}

	public function populate($arg) {

        $cars = new Model_Cars_Cars();

		if ($this->models && isset($arg['makes']) &&  $arg['makes'] && is_array($arg['makes'])) {
            $modelsArr = array();
            $cars = new Model_Cars_Cars();
            foreach ($arg['makes'] as $make) {
                $models = $cars->getCarModelsWithCountsFromName($make);
                $modelsArr = $this->array_merge_keys($modelsArr, $models);
            }
            $this->models->setMultiOptions($modelsArr);
        }


        if ($this->make && isset($arg['make']) &&  $arg['make'] && is_string($arg['make'])) {
            $make = $cars->getCarMakeBySlug($arg['make']);

            if($make) {
                $arg['make']  = $make['id'];
                $models = $cars->getCarModels( $arg['make'] ,true);
                $this->model->setMultiOptions(array("" => "" ) + $models);
                $this->model->setAttrib('disabled', null);
            }

		}

        if ($this->model && isset($arg['model']) &&  $arg['model'] && is_string($arg['model'])) {
            $model = $cars->getCarModelBySlug($arg['model']);

            if($model) {
                $arg['model']  = $model['id'];
            }

        }
		return parent::populate($arg);
	}
	
	public function setAppendForms($arr) {
		if (is_array($arr)) {
			foreach ($arr as $form) {
				if ($form instanceof Zend_Form) {
					$this->_appendForms[] = $form;
				}
			}
		}
	}
	
	public function setReplaceSubmit($form) {
		if ($form instanceof Zend_Form) {
			foreach ($form->getElements() as $el) {
				if ($el instanceof Zend_Form_Element_Submit) {
					$this->_replaceSubmit = $el;
				}
			}
		}
	}
    
    public function setDeleteSubmit($val) {
        if($val === true)
        {
            $this->_deleteSubmit =   new Zend_Form_Element_Submit('delete', array(
				'label' => 'DELETE'
			));
        }
        

	}
	
	public function setVariant($value) {
		if (in_array($value, array("full", "short", "make_only", "have_car_found", "have_car_found_only_favourite_salesman", 'front', 'left'))) {
			$this->_variant = $value;
		}
	}

	public function setModels($modelsArray) {
		$this->getElement('models')->setMultiOptions($modelsArray);
	}

    public function setSearchParameters($searchParameters = array()) {

        $cars = new Model_Cars_Cars();

        $this->_searchParameters = $searchParameters;

//        /$allowedArray = array('price_min', 'price_max', 'build_from', 'build_to', 'cubic_capacity_from', 'cubic_capacity_to', 'gearboxes', 'categories', 'query', 'location', 'makes', 'models', 'types', 'fuels', 'power');

        if(!empty($this->_searchParameters)) {
            foreach($this->_searchParameters as $key => $value) {

                    if(!empty($value)) {
                        if(is_array($value)) {

                            foreach($value as $key2 => $value2) {
                                $this->_searchParametersCacheString = $this->_searchParametersCacheString . preg_replace('/[^a-zA-Z0-9]/s', '', $key).preg_replace('/[^a-zA-Z0-9]/s', '', $value2);
                            }

                        } else {

                            $this->_searchParametersCacheString = $this->_searchParametersCacheString . preg_replace('/[^a-zA-Z0-9]/s', '', $key).preg_replace('/[^a-zA-Z0-9]/s', '', $value);
                        }
                    }


            }
        }



        $cache = Zend_Registry::get('Cache');
        $options = $this->important_features->getMultiOptions();
        $this->important_features->setMultiOptions(array());

        $disabledOptions = array();
        foreach($options as $ckey => $cvalue) {

            foreach($cvalue as $key => $value) {
                $data = array('price_min' => '', 'price_max' =>'' , 'important_features' => array());

                $data['important_features'][] = $key;

                $parameters = $cars->searchPrepareParameters($data,$this->_searchParameters);

                $cacheKey = 'car_important_features_search_count_'.preg_replace('/[^a-zA-Z0-9]/s', '', $key);
                if($this->_searchParametersCacheString) {

                    $cacheKey = $cacheKey.'_'.md5($this->_searchParametersCacheString);


                }

                if(($count = $cache->load($cacheKey )) === false) {
                    $count = $cars->search($parameters, false,false,true,false,false,true);
                }



                if(!$count || $count == 0) {
                    $count = 0;
                    $disabledOptions[] = $key;

                }

                $value = preg_replace('/\(\d+\)/', '',$value);

                $options[$ckey][$key] = $value. ' ('.  $count.')';

                $this->important_features->addMultiOption($key, $value);
                $cache->save($count, $cacheKey, array('cars', 'car_important_features_search_count'));
            }


        }
        $this->important_features->setMultiOptions($options);
        $this->important_features->setAttrib('disable', $disabledOptions);
    }

}