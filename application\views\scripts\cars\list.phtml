<div class="car-list py-4">
    <div class="row">
        <div class="col-lg-6">
            <h1 class="title">Kup <span class="orange-color">auto</span></h1>
        </div>
        <div class="col-lg-6">

            <div class="d-flex pull-right">
                <div class="dropdown dropdown-aa order">
                    <?= $this->translate->_('ORDER_BY') ?>:
                    <button class="btn btn-secondary" type="button" id="dropdownOrder" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <?= $this->translate->_('DATE_ADDED') ?> <i class="fa fa-caret-down" aria-hidden="true"></i>
                    </button>
                    <div class="dropdown-menu" aria-labelledby="dropdownOrder">
                        <a class="dropdown-item" data-value="1"><?= $this->translate->_('DATE_ADDED') ?> <i class="fa fa-caret-down" aria-hidden="true"></i></a>
                        <a class="dropdown-item" data-value="2"><?= $this->translate->_('DATE_ADDED') ?> <i class="fa fa-caret-up" aria-hidden="true"></i></a>
                        <a class="dropdown-item" data-value="3"><?= $this->translate->_('PRICE_SHORT') ?> <i class="fa fa-caret-down" aria-hidden="true"></i></a>
                        <a class="dropdown-item" data-value="4"><?= $this->translate->_('PRICE_SHORT') ?> <i class="fa fa-caret-up" aria-hidden="true"></i></a>
                        <a class="dropdown-item" data-value="5"><?= $this->translate->_('MAKE') ?> <i class="fa fa-caret-down" aria-hidden="true"></i></a>
                        <a class="dropdown-item" data-value="6"><?= $this->translate->_('MAKE') ?> <i class="fa fa-caret-up" aria-hidden="true"></i></a>
                        <a class="dropdown-item" data-value="7"><?= $this->translate->_('PRODUCTION_YEAR') ?> <i class="fa fa-caret-down" aria-hidden="true"></i></a>
                        <a class="dropdown-item" data-value="8"><?= $this->translate->_('PRODUCTION_YEAR') ?> <i class="fa fa-caret-up" aria-hidden="true"></i></a>

                    </div>
                </div>
                <div data-value="block" class="car-list-view  align-self-center">
                    <i class="fa fa-th" aria-hidden="true"></i>
                </div>
                <div data-value="list" class="car-list-view align-self-center">
                    <i class="fa fa-th-list" aria-hidden="true"></i>
                </div>
            </div>


        </div>
    </div>

    <?= $this->partial('car-items.phtml',
        array(
            'paginator' => $this->paginator,
            'translate' => $this->translate,
            'language' => $this->language,
            'language_row' => $this->language_row,
            'hash' => ($this->hash ? '?hash=' . $this->hash . '&i=' . (($this->page - 1) * $this->paginator->getItemCountPerPage() + $index) : ''),
            'page' => $this->page,
            'perPage' => $this->perPage,
            'domain' => $this->domain,
            'searchParameters' => $this->searchParameters,
            'siteVariant' => $this->siteVariant,
            'auctionSearch' => $this->auctionSearch,
            'queryStringArray' => $this->queryStringArray,
            'favouritesIds' => $this->favouritesIds
        ))
?>
</div>


<script type="text/javascript">
<?php $this->inlineScript()->captureStart() ?>

    $(function() {


        $('.dropdown.order .dropdown-item').on('click',function() {
            $('.dropdown.order button').html($(this).html());



            $("#search-form #order").val($(this).data('value'));
            $("#search-form button.btn-search").trigger('click');

        })

        <?php if(isset($this->queryStringArray['order'])) :?>

        $('.dropdown.order button').html($('.dropdown.order .dropdown-item[data-value="<?= $this->queryStringArray['order']?>"]').html());


        <?endif ?>

        $('.car-list-view').on('click',function() {

            $(this).addClass('active');
            $("#search-form #list_view").val($(this).data('value'));
            $("#search-form button.btn-search").trigger('click');

        })

        <?php if(isset($this->queryStringArray['list_view'])) :?>

            $('.car-list-view[data-value="<?= $this->queryStringArray['list_view']?>"]').addClass('active');
        <?endif ?>


    })



<?php $this->inlineScript()->captureEnd() ?>
</script>



