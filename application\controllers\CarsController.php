<?php


class Cars<PERSON>ontroller extends Zend_Controller_Action {


    public function init() {
        $cache = Zend_Registry::get('Cache');
        if(($cache->load('all_count')) === false) {

            $cars = new Model_Cars_Cars();
            $this->view->allCount = count($cars->search());
            $cache->save($this->view->allCount, 'all_count', array('cars'));

        } else {
            $this->view->allCount = $cache->load('all_count');
        }


    }

	public function deleteSearchByHashAction() {
		$this->_helper->viewRenderer->setNoRender(true);
		$hash = $this->_request->getParam('hash');
		$users = new Model_Users();
		$success = $users->deleteSearchByHash($hash);

		if ($success) {
			$this->view->messenger->addMessage($this->_helper->translate('USER_SEARCH_DELETED'));
		}
		else {
			$this->view->messenger->addMessage($this->_helper->translate('USER_SEARCH_NOT_DELETED'));
		}
		$this->_redirect('');

	}

	public function deleteSearchByMailAction() {
		$this->_helper->viewRenderer->setNoRender(true);
		$form = new Form_DeleteNewsletterByMail();
		$users = new Model_Users();



		if ($this->_request->isPost()) {
			if ($form->isValid($this->_request->getPost())) {
				$data = $form->getValues();

				$this->_redirect($this->view->url(array('language' => $this->view->language), 'searchList', true).'?email='.$data['email']);
			}//isValid
			else {
				$this->view->messenger->addMessage($this->_helper->translate('EMAIL_REQUIRED'));
				$this->_redirect($this->view->url(array('language' => $this->view->language), 'haveCarFound', true));
			}
		}//isPost

	}

	public function favouriteListAction() {
		$page = $this->getRequest()->getParam('page');

		$users = new Model_Users();

		if ($this->_request->isGet()) {
			$filterInt = new Zend_Filter_Int();
			$del = $filterInt->filter($this->getRequest()->getParam('del'));
			if ($del) {
				$users->delFavourite($del, $this->view->identity->id);
				$this->view->messenger->addMessage($this->_helper->translate('CAR_REMOVED_FROM_FAVOURITES'));
				$this->_helper->redirectBack('favourite-list-delete');
			}

            $delAll = $filterInt->filter($this->getRequest()->getParam('del-all'));
            if ($delAll) {
                $users->delFavourites($this->view->identity->id);
                $this->_helper->redirectBack('favourite-list-delete');
            }
		}

        $this->view->showActions = false;

		$paginator = Zend_Paginator::factory(array());
		$userFavourites = $users->getUserFavourites($this->view->identity->id, $returnSelect=true);
		if ($userFavourites) {
			$paginator = new Zend_Paginator(new Zend_Paginator_Adapter_DbSelect($userFavourites));
			$paginator->setCurrentPageNumber($page);
		}
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$paginator->setItemCountPerPage($opt['paging']['favourites']);
		$paginator->setPageRange($opt['paging']['defaultPageRange']);

        if($paginator->getCurrentItemCount() > 0) {

            $this->view->showActions = true;
        }

		$this->view->paginator = $paginator;
		$this->view->page = $page;
		$this->view->userFavourites = $userFavourites;

		/* Drukowanie pdfa */
		$doPrint = $this->_request->getParam('print', null) == "true";
		if ($doPrint) {
			$this->_helper->layout->disableLayout();

			$db = Model_Base::getDb();
			$this->view->cars = $db->fetchAll($userFavourites);

			$pdf = new My_Tcpdf();
			$html = $this->view->render("car_list_pdf.phtml");
			//echo $html;
			$pdf->generateCarList($html);

			$this->_helper->viewRenderer->setNoRender(true);
		}
		/* k */

        $nlForm = new Form_FavouritesNewsletter();
        $nlForm->setMethod("post");
        $nlForm->setAction($this->view->url() . "?newsletter=true");
        $nlForm->populate(array('email' => Zend_Auth::getInstance()->getIdentity()->email));
        $nlForm->removeElement('captcha');

        $smForm = new Form_FavouritesNewsletter(array('variant' => 'salesman'));
        $smForm->setMethod("post");
        $smForm->setAction($this->view->url() . "?salesman=true");
        $smForm->removeElement('captcha');

        $this->view->nlForm = $nlForm;
        $this->view->smForm = $smForm;
        /* wysylka ulubionych mailem */
        if ($this->_request->isPost()) {
            if($this->_getParam('salesman')) {
                $checkForm = $smForm;
            } else {
                $checkForm = $nlForm;
            }

            if ($checkForm->isValid($this->_request->getPost())) {
                $data = $checkForm->getValues();

                if ($this->_request->getParam('newsletter', null) || $this->_request->getParam('salesman', null)) {
                    $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
                    $domain = Zend_Registry::get('siteDomain');
                    $searches = array(array(
                        'first_name' => null,
                        'last_name' => null,
                        'search_name' => "",
                        'employee_sr_id' => null,
                        'hash' => null,
                        'cars' => Model_Base::getDb()->fetchAll($userFavourites)
                    ));
                    $cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
                    $cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);

                    if(isset($data['sr_id'])) {
                        $employeesModel = new Model_Employees();
                        $employee = $employeesModel->getBySrId($data['sr_id']);
                        $searches[0]['email'] = $employee['email'];
                    } else {
                        $searches[0]['email'] = $data['email'];
                    }


                    foreach($searches as $searchUser) {
                        $msg = false;
                        if (count($searchUser['cars'])) {
                            $this->view->css = $cssContent;
                            $this->view->user = $searchUser;
                            $this->view->employee = null;
                            $this->view->newsletterGreeting = $data['newsletter_greeting'];
                            $html = $this->view->render('newsletter_content.phtml');

                            $emogrifier = new My_Emogrifier($html);
                            $html = $emogrifier->emogrify();

                            $mail = new Zend_Mail($charset="UTF-8");
                            $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

                            $mail->setFrom(
                                $opt['resources']['mail']['defaultFrom']['email']
                            );

                            $text = $data['newsletter_greeting'] .':'.  $searchUser['search_name']
                                . "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');

                            $firstCar = $searchUser['cars'][0];

                            $subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
                                $firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);

                            $mail->addTo($searchUser['email'])
                                ->setBodyText($text)
                                ->setBodyHtml($html)
                                ->setSubject($subject);

                            $mail->send();
                        }
                    }
                    $this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'send-newsletter'), Zend_Log::INFO);
                    $this->view->messages[] = $this->view->translate("FAVOURITES_EMAIL_SENT");
                }
            }//isValid
            else {
                if($this->_getParam('salesman')) {
                    $this->view->showSmForm = true;

                } else {
                    $this->view->showNlForm = true;
                }

            }
        }//isPost


        /* k */

		if (!$doPrint) {
			$this->_helper->setRedirBack('user_cp_edit');
			$this->_helper->setRedirBack('favourite-list-delete');
		}
	}

	public function favouriteListSessionAction() {
		$page = $this->getRequest()->getParam('page');

		$users = new Model_Users();
		$cars = new Model_Cars_Cars();

		if ($this->_request->isGet()) {
			$filterInt = new Zend_Filter_Int();
			$del = $filterInt->filter($this->getRequest()->getParam('del'));
			if ($del) {
				$favNS = new Zend_Session_Namespace('favourite_cars');
				if (isset($favNS->cars) && is_array($favNS->cars)) {
					$index = array_search($del, $favNS->cars);
					if ($index !== false) {
						unset($favNS->cars[$index]);
						$this->view->messages[] = $this->view->translate->_('CAR_REMOVED_FROM_FAVOURITES');
                        $this->_helper->redirectBack('favourite-list-delete');
					}
				}
			}

            $delAll = $filterInt->filter($this->getRequest()->getParam('del-all'));
            if ($delAll) {
                $favNS = new Zend_Session_Namespace('favourite_cars');
                $favNS->cars = array();
                $this->_helper->redirectBack('favourite-list-delete');
            }
		}

        $this->view->showActions = false;
		$paginator = Zend_Paginator::factory(array());
		$userFavourites = array();
		$favNS = new Zend_Session_Namespace('favourite_cars');
		if (isset($favNS->cars) && is_array($favNS->cars) && count($favNS->cars) > 0) {
			$params = array('ids' => $favNS->cars);
			$userFavourites = $cars->search($params, $retSelect=true);

			$savePermanentlyForm = new Form_GenericSubmit();
			$savePermanentlyForm->submit->setLabel('FAVOURITES_SAVE_PERMANENTLY');
			$savePermanentlyForm->setMethod('get')->setAction($this->view->url(array('language' => $this->view->language), 'login', true));
			;
			$this->view->favouritesSavePermanentlyForm = $savePermanentlyForm;
            $this->view->showActions = true;
		}

		if ($userFavourites) {
			$paginator = new Zend_Paginator(new Zend_Paginator_Adapter_DbSelect($userFavourites));
			$paginator->setCurrentPageNumber($page);
		}
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$paginator->setItemCountPerPage($opt['paging']['favourites']);
		$paginator->setPageRange($opt['paging']['defaultPageRange']);

		$this->view->paginator = $paginator;
		$this->view->page = $page;
		$this->view->userFavourites = $userFavourites;

		if($this->view->isMobile) {
			$form = new Form_Search();
		} else {
			$form = new Form_NewSearch();
		}
		$this->view->form = $form;

		$results = $cars->search(array(), $retSelect=true);
		$this->view->makesWithCounts = $cars->getCarMakesWithCountsByNamesWithFilters($results);

		/* Drukowanie pdfa */
		$doPrint = $this->_request->getParam('print', null) == "true";
		if ($doPrint) {
			$this->_helper->layout->disableLayout();

			$db = Model_Base::getDb();
			$this->view->cars = $db->fetchAll($userFavourites);

			$pdf = new My_Tcpdf();
			$html = $this->view->render("car_list_pdf.phtml");
			//echo $html;
			$pdf->generateCarList($html);

			$this->_helper->viewRenderer->setNoRender(true);
		}
		/* k */

		$nlForm = new Form_FavouritesNewsletter();
		$nlForm->setMethod("post");
		$nlForm->setAction($this->view->url() . "?newsletter=true");
        if(Zend_Auth::getInstance()->hasIdentity()) { //salesman
            $nlForm->populate(array('email' => Zend_Auth::getInstance()->getIdentity()->email));
        }

        $smForm = new Form_FavouritesNewsletter(array('variant' => 'salesman'));
        $smForm->setMethod("post");
        $smForm->setAction($this->view->url() . "?salesman=true");


		$this->view->nlForm = $nlForm;
        $this->view->smForm = $smForm;
		/* wysylka ulubionych mailem */
		if ($this->_request->isPost()) {
            if($this->_getParam('salesman')) {
                $checkForm = $smForm;
            } else {
                $checkForm = $nlForm;
            }

			if ($checkForm->isValid($this->_request->getPost())) {
				$data = $checkForm->getValues();

				if ($this->_request->getParam('newsletter', null) || $this->_request->getParam('salesman', null)) {
					$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
					$domain = Zend_Registry::get('siteDomain');
					$searches = array(array(
						'first_name' => null,
						'last_name' => null,
						'search_name' => "",
						'employee_sr_id' => null,
						'hash' => null,
						'cars' => Model_Base::getDb()->fetchAll($userFavourites)
					));
					$cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
					$cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);

                    if(isset($data['sr_id'])) {
                        $employeesModel = new Model_Employees();
                        $employee = $employeesModel->getBySrId($data['sr_id']);
                        $searches[0]['email'] = $employee['email'];
                    } else {
                        $searches[0]['email'] = $data['email'];
                    }


					foreach($searches as $searchUser) {
						$msg = false;
						if (count($searchUser['cars'])) {
							$this->view->css = $cssContent;
							$this->view->user = $searchUser;
							$this->view->employee = null;
                            $this->view->newsletterGreeting = $data['newsletter_greeting'];
							$html = $this->view->render('newsletter_content.phtml');

                            $emogrifier = new My_Emogrifier($html);
                            $html = $emogrifier->emogrify();

							$mail = new Zend_Mail($charset="UTF-8");
							$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

							$mail->setFrom(
								$opt['resources']['mail']['defaultFrom']['email']
							);

                            $text = $data['newsletter_greeting'] .':'.  $searchUser['search_name']
                                . "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');

                            $firstCar = $searchUser['cars'][0];

                            $subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
                                $firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);

							$mail->addTo($searchUser['email'])
                                ->setBodyText($text)
								->setBodyHtml($html)
								->setSubject($subject);

							$mail->send();
						}
					}
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'send-newsletter'), Zend_Log::INFO);
					$this->view->messages[] = $this->view->translate("FAVOURITES_EMAIL_SENT");
				}
			}//isValid
			else {
                if($this->_getParam('salesman')) {
                    $this->view->showSmForm = true;

                } else {
                    $this->view->showNlForm = true;
                }

			}
		}//isPost


		/* k */

		if (!$doPrint) {
			$this->_helper->setRedirBack('favourite-list-delete');
		}
	}

	public function getInstalmentsAction() {
		if ($this->getRequest()->isXmlHttpRequest()) {
			$carId = $this->_request->getParam('carId');
			$type = $this->_request->getParam('type');
			$contribution = $this->_request->getParam('contribution');
			$instalmentsNo = $this->_request->getParam('instalmentsNo');
			$price = $this->_request->getParam('price');

			$cars = new Model_Cars_Cars();
			if ($carId) {
				$instalment = $cars->getInstalment($carId, $type, $contribution, $instalmentsNo, $price);
				$this->_helper->json($instalment);
			} else {
				$this->_helper->json(false);
			}
		} else {
			die();
		}
	}

	public function getmodelsAction() {
		if  ($this->getRequest()->isXmlHttpRequest()) {
			$makes = $this->_request->getParam('makes');
			$haveCarFound = $this->_request->getParam('haveCarFound');
            $makeIds = $this->_request->getParam('make_ids');
			$cars = new Model_Cars_Cars();
			$return = array();
			if ($makes and is_array($makes)) {
				foreach($makes as $make) {

                    if($makeIds)
                        $models = $cars->getCarModels($make,true);
				    else if($haveCarFound)
					    $models = $cars->getCarModelsByName($make);
				    else
					    $models = $cars->getCarModelsWithCountsFromName($make);

				    $return = $this->array_merge_keys($return, $models);
				}
				$this->_helper->json($return);
			} else {
				$this->_helper->json(array());
			}
		} else {
			die();
		}
	}

	private function array_merge_keys(){
		$args = func_get_args();
		$result = array();
		foreach($args as &$array){
			foreach($array as $key => &$value){
				$result[$key] = $value;
			}
		}
		return $result;
	}

	public function haveCarFoundAction() {
		$isDeleteFromNL = $this->_request->getParam('isDeleteFromNL', false);
		$hash = $this->_request->getParam('hash', null);

		$this->view->isDeleteFromNL = $isDeleteFromNL;
		$this->view->hash = $hash;

		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$domain = Zend_Registry::get('siteDomain');

		$delByMailForm = new Form_DeleteNewsletterByMail();
		$this->view->delByMailForm = $delByMailForm;
		if (Zend_Auth::getInstance()->hasIdentity()) {
			$delByMailForm->email->setValue(Zend_Auth::getInstance()->getIdentity()->email);
		}
		$delByMailForm->setAction($this->view->url(array('language' => $this->view->language), 'deleteSearchByMail', true));

		$cars = new Model_Cars_Cars();

		$saveForm = null;
		if (Zend_Auth::getInstance()->hasIdentity()) {
			if (Zend_Auth::getInstance()->getIdentity()->role == "salesman") {
				$saveForm = new Form_SearchItemBySalesman();
			}
			else {
				$saveForm = new Form_SearchItem();
			}
		}
		else {
			$saveForm = new Form_SearchItemNoIdentity();
		}

		$form = new Form_Search(array('variant' => 'have_car_found', 'appendForms' => array($saveForm), 'replaceSubmit' => $saveForm));
		$form->setAction($this->_request->getServer('REQUEST_URI'));
		$form->title->setRequired(false);
		if ($form->getElement('newsletter')) {
			$form->removeElement('newsletter');
		}

        $identity = null;
        if(Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity() != 'salesman') {
            $identity = Zend_Auth::getInstance()->getIdentity();
        }

        $weFindCarForm = new Form_WeFindCar();

        $this->view->weFindCarForm = $weFindCarForm;

        if ($identity) {
            $weFindCarForm->removeElement('captcha');
            $weFindCarForm->populate(array(
                'first_name' => $identity->first_name,
                'phone' => $identity->phone,
                'email' => $identity->email
            ));
        }

		if ($isDeleteFromNL) {
			if ($form->getElement('email')) {
				$form->removeElement('email');
				$saveForm->removeElement('email');
			}
			$form->hash->setValue($hash);

			$users = new Model_Users();
			$search = $users->getUserStoredSearch($userId=null, $searchId=null, $hash);
			$this->view->search = $search;

			if ($search) {
				$params = $cars->prepareFormDataFromParams(unserialize($search['search_data']));

				$form->populate(array('title' => $search['title']));
				$form->populate($params);
			}
		}

        if(Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == "salesman")
            $form->populate(array('added_by_sr_id' => Zend_Auth::getInstance()->getIdentity()->sr_id));

		$this->view->form = $form;

		if ($this->_request->isPost() && !($isDeleteFromNL && empty($search))) {
			$postData = $this->_request->getPost();

            if(isset($postData['buy_car'])) {
                if ($weFindCarForm->isValid($postData)) {

                    $data = $weFindCarForm->getValues();

                    if($identity) {
                        $data['user_id'] = $identity->user_id;
                    }

                    $ubModel = new Model_UsersBuy();

                    try {

                        $id = $ubModel->add($data);

                        $result = $ubModel->get($id);

                        $exportModel = new Model_ImportExport_Export();
                        $exportModel->sendClientBuy($result);


                        $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $id), Zend_Log::INFO);
                        $this->view->messenger->addMessage($this->_helper->translate('SELL_CAR_EMAIL_SUCCESS'));
                        $this->_helper->redirectBack(null, $this->_request->getServer('REQUEST_URI'));
                    }
                    catch (Exception $e) {
                        $this->view->messages[] = $e->getMessage();
                        $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => print_r($data, true)), Zend_Log::INFO);
                    }
                }
            } else {


                $searchForm = new Form_Search(array('variant' => 'have_car_found_only_favourite_salesman'));
                if ($form->isValid($postData) && $saveForm->isValid($postData)) {
                    $searchForm->populate($form->getValues());
                    $saveData = $saveForm->getValues();
                    $saveData['added_by_sr_id'] = $searchForm->getValue('added_by_sr_id');

                    $searchData = $cars->searchPrepareParameters($searchForm->getValues());

                    if (empty($saveData['title'])) {
                        $filterArray = $cars->prepareFilterArray($searchData, $form);

                        if ($filterArray) {
                            $valueStr = "";
                            $counted = 0;
                            foreach ($filterArray as $key => $value) {
                                if ($counted == 3) break;

                                if ($counted > 0) {
                                    $valueStr .= ", ";
                                }

                                if ($value['value'] == "") {
                                    $valueStr .= $this->view->translate->_($value['title']);
                                }
                                else {
                                    if (in_array($value['title'], array("CATEGORY", "FEATURE", "FUEL", "MAKE", "MODEL"))) {
                                        $valueStr .= $value['value'];
                                    }
                                    else {
                                        $valueStr .= $this->view->translate->_($value['title']) . ": " . $value['value'];
                                    }
                                }
                                $counted++;
                            }
                            $saveData['title'] = $valueStr;
                        }
                        if (empty($saveData['title'])) {
                            $saveData['title'] = $this->view->translate->_('DEFAULT_SEARCH_TITLE') . ' ' . date("Y-m-d H:i:s");
                        }
                    }

                    $users = new Model_Users();
                    /* Kod do zapisania searcha - można go wsadzić gdziekolwiek gdzie są dane posta searcha lub dane pobrane z sesji (hash) */
                    if ($isDeleteFromNL && array_key_exists('save', $postData)) {
                        $users->editSearch(
                            $searchId=null,
                            $form->getValues() + array('newsletter' => 1),
                            $hash
                        );
                        $this->view->messages[] = $this->view->translate->_('HAVE_CAR_FOUND_SAVED');
                    }
                    else if (array_key_exists('save', $postData)) {
                        if ($this->view->identity && $this->view->identity->id) {
                            $result = $users->saveSearch(array_merge($saveData, array('newsletter' => 1)), $searchData, $this->view->identity->id);
                            if ($result) {
                                $this->view->messages[] = $this->view->translate->_('HAVE_CAR_FOUND_SAVED');
                            }
                            else {
                                $this->view->messages[] = $this->view->translate->_('HAVE_CAR_FOUND_NOT_SAVED');
                            }
                        }
                        else {
                            //valid but user not logged in
                            $result = $users->saveSearch(array_merge($saveData, array('newsletter' => 1)), $searchData, null);
                            if ($result) {
                                $this->view->messages[] = $this->view->translate->_('HAVE_CAR_FOUND_SAVED');
                            }
                            else {
                                $this->view->messages[] = $this->view->translate->_('HAVE_CAR_FOUND_NOT_SAVED');
                            }
                        }

                        $searchNamespace = new Zend_Session_Namespace('search');
                        if (!$searchNamespace->searchParameters) {
                            $searchNamespace->searchParameters = array();
                        }

                        $searchHash = null;
                        do {
                            $searchHash = $this->generateHash();
                        } while (array_key_exists($searchHash, $searchNamespace->searchParameters));
                        $exists = false;
                        foreach($searchNamespace->searchParameters as $hash => $sD) {
                            if (serialize($sD) == serialize($searchData)) {
                                $searchHash = $hash;
                                $exists = true;
                            }
                        }

                        if (!$exists) {
                            $searchNamespace->searchParameters[$searchHash] = $searchData;
                        }

                        if ($result) {
                            $searchId = $result;
                            $searches = $users->getSearchesWithCleanup($date=null, $searchId);
                            $cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
                            $cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);

                            foreach($searches as $searchUser) {
                                $msg = false;
                                if (count($searchUser['cars'])) {


                                    //$emogrifier nie radzi sobie ze zbyt duza ilosci maili
                                    $searchUser['cars'] = array_slice($searchUser['cars'],0,200);

                                    $employees = new Model_Employees();
                                    $employee = null;
                                    if ($saveData['added_by_sr_id']) {
                                        $employee = $employees->getBySrId($saveData['added_by_sr_id']);
                                    }

                                    $this->view->css = $cssContent;
                                    $this->view->user = $searchUser;
                                    $this->view->employee = $employee;
                                    $html = $this->view->render('newsletter_content.phtml');

                                    error_log(count($searchUser['cars']));

                                    $emogrifier = new My_Emogrifier($html);
                                    $html = $emogrifier->emogrify();

                                    $mail = new Zend_Mail($charset="UTF-8");
                                    $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

                                    if ($employee) {
                                        $this->view->employee = $employee;


                                        $mail->setFrom(
                                            $opt['resources']['mail']['defaultFrom']['email'],
                                            $employee['first_name'] . ' ' . $employee['last_name']
                                        );


                                    } else {
                                        $mail->setFrom(
                                            $opt['resources']['mail']['defaultFrom']['email'],
                                            'AutoAuto.pl'
                                        );
                                    }

                                    $text = $this->view->translate->_('NEWSLETTER_GREETING') . "\n".
                                        $this->view->translate->_('NEWSLETTER_HEADING') .':'.  $searchUser['search_name']
                                        . "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');



                                    $firstCar = $searchUser['cars'][0];

                                    $subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
                                        $firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);


                                    $mail->addTo($searchUser['email'])
                                        ->setBodyText($text)
                                        ->setBodyHtml($html)
                                        ->setSubject($subject);

                                    $mail->send();
                                }
                            }
                            $this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'send-newsletter'), Zend_Log::INFO);
                            if ($searchHash) {
                                $this->view->messenger->addMessage($this->_helper->translate('HAVE_CAR_FOUND_SAVED'));
                                $this->_redirect($this->view->url(array('language', $this->view->language), 'list', true) . '?hash=' . $searchHash);
                            }
                        }
                        /* k */
                    }

                    /* Kod do zapisania searcha - dla handlowca dodajacego klienta */
                    elseif (array_key_exists('save_by_salesman', $postData)) {
                        if ($this->view->identity && $this->view->identity->role == "salesman") {
                            try {
                                $searchId = $result = $users->saveSearchBySalesman(array_merge($saveData, array('newsletter' => 1)), $searchData);
                                if (!$searchId) {
                                    $this->view->messages[] = "Profil nie został zapisany - prawdopodobnie został już zapisany wcześniej";
                                }
                                else {
                                    $this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'save-search'), Zend_Log::INFO);
                                    //$this->view->messages[] = "Profil wyszukiwania został zapisany";

                                    $searchNamespace = new Zend_Session_Namespace('search');
                                    if (!$searchNamespace->searchParameters) {
                                        $searchNamespace->searchParameters = array();
                                    }

                                    $searchHash = null;
                                    do {
                                        $searchHash = $this->generateHash();
                                    } while (array_key_exists($searchHash, $searchNamespace->searchParameters));
                                    $exists = false;
                                    foreach($searchNamespace->searchParameters as $hash => $sD) {
                                        if (serialize($sD) == serialize($searchData)) {
                                            $searchHash = $hash;
                                            $exists = true;
                                        }
                                    }

                                    if (!$exists) {
                                        $searchNamespace->searchParameters[$searchHash] = $searchData;
                                    }

                                    if ($result) {

                                        $searchId = $result;
                                        $searches = $users->getSearchesWithCleanup($date=null, $searchId);
                                        $cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
                                        $cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);



                                        foreach($searches as $searchUser) {
                                            $msg = false;
                                            if (count($searchUser['cars'])) {

                                                //$emogrifier nie radzi sobie ze zbyt duza ilosci maili
                                                $searchUser['cars'] = array_slice($searchUser['cars'],0,200);

                                                $employees = new Model_Employees();
                                                $employee = null;
                                                if ($saveData['added_by_sr_id']) {
                                                    $employee = $employees->getBySrId($saveData['added_by_sr_id']);
                                                }

                                                $this->view->css = $cssContent;
                                                $this->view->user = $searchUser;
                                                $this->view->employee = $employee;
                                                $html = $this->view->render('newsletter_content.phtml');

                                                $emogrifier = new My_Emogrifier($html);
                                                $html = $emogrifier->emogrify();

                                                $mail = new Zend_Mail($charset="UTF-8");
                                                $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

                                                if ($employee) {
                                                    $this->view->employee = $employee;


                                                    $mail->setFrom(
                                                        $employee['email'],
                                                        $employee['first_name'] . ' ' . $employee['last_name']
                                                    );


                                                } else {
                                                    $mail->setFrom(
                                                        $opt['resources']['mail']['defaultFrom']['email'],
                                                        'AutoAuto.pl'
                                                    );
                                                }

                                                $text = $this->view->translate->_('NEWSLETTER_GREETING') . "\n".
                                                    $this->view->translate->_('NEWSLETTER_HEADING') .':'.  $searchUser['search_name']
                                                    . "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');

                                                $firstCar = $searchUser['cars'][0];

                                                $subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
                                                    $firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);

                                                $mail->addTo($searchUser['email'])
                                                    ->setBodyText($text)
                                                    ->setBodyHtml($html)
                                                    ->setSubject($subject);


                                                $mail->send();


                                            }
                                        }
                            $this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'send-newsletter'), Zend_Log::INFO);
                            if ($searchHash) {
                                $this->view->messenger->addMessage($this->_helper->translate('HAVE_CAR_FOUND_SAVED'));
                                $this->_redirect($this->view->url(array('language', $this->view->language), 'list', true) . '?hash=' . $searchHash);
                            }
                        }
                        /* k */

                                    if ($searchHash) {
                                        $this->view->messenger->addMessage($this->_helper->translate('HAVE_CAR_FOUND_SAVED'));
                                        $this->_redirect($this->view->url(array('language', $this->view->language), 'list', true) . '?hash=' . $searchHash);
                                    }
                                }
                            }
                            catch (Exception $e) {
                                $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage() . ' / ' . $e->getTraceAsString()), Zend_Log::WARN);
                                $this->view->messages[] = $e->getMessage();
                            }
                        }
                        /* k */
                    }
                }//isValid
            }
		}//isPost
	}

	private function generateHash($count = 5) {
		$result = "";


		$charPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!$%^*()_{}:;,';
		for($p = 0; $p<$count; $p++) {
			$result .= $charPool[mt_rand(0,strlen($charPool)-1)];
		}
		return $result;
	}

	public function indexAction() {
	}

	public function listAction() {

        $this->view->menuActive = 'buyCar';

		$page = $this->getRequest()->getParam('page');
		$searchHash = $this->getRequest()->getParam('hash');
		$perPage = $this->_request->getParam('perPage', null);
		$iterator = $this->_request->getParam('i', null);

		$type = $this->_request->getParam('type',null);
		$make = $this->_request->getParam('make',null);
		$model = $this->_request->getParam('model',null);


		$allowedFilters = array ('price_min', 'price_max', 'build_from', 'build_to', 'cubic_capacity_from',
			'cubic_capacity_to', 'engine', 'gearboxes', 'odometer_to', 'odometer', 'important_features', 'categories', 'category',
			'query', 'promotions', 'leasing', 'new', 'last_2_years', 'premium', 'exclusive', '4x4_pickup',
			'after_leasing_vindication', 'location', 'order', 'search_by_price_gross', 'on_site', 'leasing_transfer',
			'credit_collateral', 'rental', 'price_type_key', 'auction', 'invoice', 'drives', 'list_view', 'origin_country' );

		$cars = new Model_Cars_Cars();

		if($this->view->isMobile) {
			$form = new Form_Search();
		} else {
			$form = new Form_NewSearch();
		}

		$form->setAction($this->view->url());

		$post = false;
		$data = false;

		if ($this->_request->isPost()) {
			$post = true;
			$data = $this->_request->getPost();
            $data['view'] = 'block';


			if (array_key_exists('submit', $data) or array_key_exists('submit2', $data) or array_key_exists('query', $data)) {

                $form->isValid($data);

				if ($form->isValid($data)) {
					$data = $form->getValues();
					$queryFilter = array();



					if(isset($data['make'])) {

					    if($carMake = $cars->getCarMake($data['make'])) {
                            $make = $carMake['slug'];
                            $type = 'osobowe';
                        }


                    }

                    $model = null;
                    if(isset($data['model'])) {
                        if($carModel = $cars->getCarModel($data['model'])) {
                            $model = $carModel['slug'];
                        }

                    }

					foreach($data as $key => $value) {
						if(in_array($key,$allowedFilters) && !empty($value)) {
							$queryFilter[$key] = $value;

						}
					}

					if($queryString =  http_build_query($queryFilter)) {
						$this->_redirect($this->view->url(
								array(
									'language' => $this->view->language,
									'type' => $type,
									'make' => $make,
									'model' => $model), 'list', 'true').'?'.http_build_query($queryFilter),array('code' => 301));

					} else {
						$this->_redirect($this->view->url(
								array(
									'language' => $this->view->language,
									'type' => $type,
									'make' => $make,
									'model' => $model), 'list', 'true'),array('code' => 301));
					}




				}
			}
		}

		$searchParameters  = $cars->searchPrepareParameters($this->_request->getParams());

		$queryStringArray = $this->_request->getParams();
		foreach($queryStringArray as $key => $value) {
			if(!in_array($key, array_merge($allowedFilters))) {
				unset($queryStringArray[$key]);
			}
		}

		if(!isset($queryStringArray['list_view']) || !in_array($queryStringArray['list_view'],array('block', 'list'))) {
            $queryStringArray['list_view'] = 'block';
        }


		if($page !== null) {
			$queryStringArray['page'] = $page;
		}

		if($perPage !== null) {
			$queryStringArray['perPage'] = $perPage;
		}


		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$domain = Zend_Registry::get('siteDomain');

		$this->view->showSaveForm = $this->_request->getParam('show_save_form', false);

		$searchNamespace = new Zend_Session_Namespace('search');
		if (!$searchNamespace->searchParameters) {
			$searchNamespace->searchParameters = array();
		}


        $searchNamespace->searchUrl = $this->_request->getServer('REQUEST_URI');


		$saveForm = null;
		if (Zend_Auth::getInstance()->hasIdentity()) {
			if (Zend_Auth::getInstance()->getIdentity()->role == "salesman") {
				$saveForm = new Form_SearchItemBySalesman();
                $saveForm->populate(array('added_by_sr_id' => Zend_Auth::getInstance()->getIdentity()->sr_id));
			}
			else {
				$saveForm = new Form_SearchItem();
			}
		}
		else {
			$saveForm = new Form_SearchItemNoIdentity();
		}

		$results = false;

		$nonArrayFilters = array('price_min', 'price_max', 'promotions', 'leasing', 'leasing_transfer', 'credit_collateral', 'new', 'last_2_years', 'premium', 'exclusive', '4x4_pickup', 'after_leasing_vindication', 'location', 'on_site', 'rental', 'query', 'displacement_max', 'seat_count', 'seat_count_max', 'seat_count_min', 'consumption_less_than', 'build_from', 'build_to', 'for_exchange', 'price_type_key', 'auction');


		/*
		if ($removeName) {
			if (in_array($removeName, $nonArrayFilters)) {
				unset($searchParameters[$removeName]);
			} else {
				unset($searchParameters[$removeName][array_search($removeValue, $searchParameters[$removeName])]);
			}
			$searchNamespace->searchParameters[$searchHash] = $searchParameters;
		}

		if ($addName && is_array($addName)) {
			foreach ($addName as $i => $name) {
				$addValue = $this->_request->getParam('addValue_' . $i);
				$removeOther = $this->_request->getParam('removeOther_' . $i);
				if (in_array($name, $nonArrayFilters)) {
					if (is_array($removeOther)) {
						foreach ($removeOther as $roName) {
							unset($searchParameters[$roName]);
						}
					}
					else {
						unset($searchParameters[$name]);
					}
					$searchParameters[$name] = $addValue;
				} else {
					if (is_array($removeOther)) {
						foreach ($removeOther as $roName) {
							unset($searchParameters[$roName]);
						}
					}
					else {
						if (isset($searchParameters[$name]) && is_array($searchParameters[$name])) {
							unset($searchParameters[$name][array_search($addValue, $searchParameters[$name])]);
						}
					}
					foreach ($addValue as $key => $value) {
						$searchParameters[$name][] = $value;
					}
				}
			}
			$searchNamespace->searchParameters[$searchHash] = $searchParameters;
		}
		*/


        if($this->view->language != 'pl')
            $searchParameters['no_new_car'] = true;


        if(isset($searchParameters['auction']) && $searchParameters['auction'] == 1) {
            $this->view->auctionSearch = true;
        } else {
            $this->view->auctionSearch = true; //zawsze wyswietlaj jako aukcja
        }

        if($this->_request->getParam('100_vat')) {
            $searchParameters['100_vat'] = true;
        }

        $date = $this->_getParam('date', false);
		if ($searchParameters) {

			$results = $cars->search($searchParameters, true,false,true,$date);
		} else {
			$results = $cars->search(array(), true,false,true,$date);
		}

		$filterArray = $cars->prepareFilterArray($searchParameters, $form);
		$this->view->filterArray = $filterArray;
        $this->view->filterArrayIncludeTitle = array('MINPRICE', 'MAXPRICE', 'BUILD_FROM', 'BUILD_TO', 'ENGINE_CAPACITY_FROM', 'ENGINE_CAPACITY_TO');
		$valueStr = '';
		if ($filterArray && $saveForm) {
			$counted = 0;
			foreach ($filterArray as $key => $value) {
				if ($counted == 3) break;

				if ($counted > 0) {
					$valueStr .= ", ";
				}

				if ($value['value'] == "") {
					$valueStr .= $this->view->translate->_($value['title']);
				}
				else {
					if (in_array($value['title'], array("CATEGORY", "FEATURE", "FUEL", "MAKE", "MODEL"))) {
						$valueStr .= $value['value'];
					}
					else {
						$valueStr .= $this->view->translate->_($value['title']) . ": " . $value['value'];
					}
				}
				$counted++;
			}
			$saveForm->title->setValue($valueStr);
		}

		$this->view->searchValueStr = $valueStr;

		$filterInt = new Zend_Filter_Int();
		$users = new Model_Users();

		/* Kod do zapisania searcha - można go wsadzić gdziekolwiek gdzie są dane posta searcha lub dane pobrane z sesji (hash) */
		if ($post && array_key_exists('save', $data) && is_array($searchParameters)) {
			$data = $this->_request->getPost();
			if ($data && $saveForm->isValid($data) && $this->view->identity && $this->view->identity->id) {
				$result = $users->saveSearch($saveForm->getValues(), $searchParameters, $this->view->identity->id);
			}
			elseif ($data && $saveForm->isValid($data)) {
				//valid but user not logged in
				$result = $users->saveSearch($saveForm->getValues(), $searchParameters, null);
			}
			else if (!$saveForm->isValid($data)) {

				if(!isset($data['car_search_item_newsletter'])) {
					$this->view->showSaveForm = true;
				}

			}

			if ($result) {
				$searchId = $result;
				if ($data['newsletter']) {
					$searches = $users->getSearchesWithCleanup($date=null, $searchId);
					$cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
					$cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);

					foreach($searches as $searchUser) {
						$msg = false;
						if (count($searchUser['cars'])) {
							$employees = new Model_Employees();

                            $employee = null;

                            if($searchUser['employee_sr_id'])
							    $employee = $employees->getBySrId($searchUser['employee_sr_id']);

							$this->view->css = $cssContent;
							$this->view->user = $searchUser;
							$this->view->employee = $employee;
							$html = $this->view->render('newsletter_content.phtml');

                            $emogrifier = new My_Emogrifier($html);
                            $html = $emogrifier->emogrify();

							$mail = new Zend_Mail($charset="UTF-8");
							$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

							$mail->setFrom(
								$opt['resources']['mail']['defaultFrom']['email'],
								!empty($employee) ? ($employee['first_name'] . ' ' . $employee['last_name']) : ''
							);

							$mail->setReplyTo(
								(!empty($employee['email']) ? $employee['email'] : $opt['resources']['mail']['defaultFrom']['email']),
                                !empty($employee) ? ($employee['first_name'] . ' ' . $employee['last_name']) : ''
							);

                            $text = $this->view->translate->_('NEWSLETTER_GREETING') . "\n".
                                $this->view->translate->_('NEWSLETTER_HEADING') .':'.  $searchUser['search_name']
                                . "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');

                            $firstCar = $searchUser['cars'][0];

                            $subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
                                $firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);

							$mail->addTo($searchUser['email'])
                                ->setBodyText($text)
								->setBodyHtml($html)
								->setSubject($subject);

							$mail->send();
						}
					}
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'send-newsletter'), Zend_Log::INFO);
					$this->view->messages[] = $this->view->translate->_('NEWSLETTER_SENT');
				}
			}
			/* k */
		}

		/* Kod do zapisania searcha - dla handlowca dodajacego klienta */
		if ($post && array_key_exists('save_by_salesman', $data) && is_array($searchParameters)) {
			$data = $this->_request->getPost();
			if ($data && $saveForm->isValid($data) && $this->view->identity && $this->view->identity->role == "salesman") {
				$data = $saveForm->getValues();
				try {
					$searchId = $users->saveSearchBySalesman($data, $searchParameters);
					if (!$searchId) {
						$this->view->messages[] = "Profil nie został zapisany - prawdopodobnie został już zapisany wcześniej";
					}
					else {
						$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'save-search'), Zend_Log::INFO);
						$this->view->messages[] = "Profil wyszukiwania został zapisany";

						if ($data['newsletter']) {
							$searches = $users->getSearchesWithCleanup($date=null, $searchId);
							$cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
							$cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);

							foreach($searches as $searchUser) {
								$msg = false;
								if (count($searchUser['cars'])) {
									$employees = new Model_Employees();
                                    $employee = null;

                                    if($searchUser['employee_sr_id'])
                                        $employee = $employees->getBySrId($searchUser['employee_sr_id']);

									$this->view->css = $cssContent;
									$this->view->user = $searchUser;
									$this->view->employee = $employee;
									$html = $this->view->render('newsletter_content.phtml');

                                    $emogrifier = new My_Emogrifier($html);
                                    $html = $emogrifier->emogrify();

									$mail = new Zend_Mail($charset="UTF-8");
									$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

                                    $mail->setFrom(
                                        $opt['resources']['mail']['defaultFrom']['email'],
                                        !empty($employee) ? ($employee['first_name'] . ' ' . $employee['last_name']) : ''
                                    );

                                    $mail->setReplyTo(
                                        (!empty($employee['email']) ? $employee['email'] : $opt['resources']['mail']['defaultFrom']['email']),
                                        !empty($employee) ? ($employee['first_name'] . ' ' . $employee['last_name']) : ''
                                    );



                                    $text = $this->view->translate->_('NEWSLETTER_GREETING') . "\n".
                                        $this->view->translate->_('NEWSLETTER_HEADING') .':'.  $searchUser['search_name']
                                        . "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');

                                    $firstCar = $searchUser['cars'][0];

                                    $subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
                                        $firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);

									$mail->addTo($searchUser['email'])
                                        ->setBodyText($text)
										->setBodyHtml($html)
										->setSubject($subject);

									$mail->send();
								}
							}
							$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'send-newsletter'), Zend_Log::INFO);
							$this->view->messages[] = $this->view->translate->_('NEWSLETTER_SENT');
						}
					}
				}
				catch (Exception $e) {
					$this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage() . ' / ' . $e->getTraceAsString()), Zend_Log::WARN);
					$this->view->messages[] = $e->getMessage();
				}
			}
			else if (!$saveForm->isValid($data)) {
				if(!isset($data['car_search_item_newsletter'])) {
					$this->view->showSaveForm = true;
				}
			}
			/* k */
		}

		/* Zapisywanie auta do ulubionych */
		$favourite = $filterInt->filter($this->getRequest()->getParam('favourite'));
		if ($favourite && $this->view->identity && $this->view->identity->id) {
			$users->saveFavourite($favourite, $this->view->identity->id);
			$this->view->messages[] = '<span class="favourites">'.$this->view->translate->_('CAR_ADDED_TO_FAVOURITES').'</span>';
		}
		elseif ($favourite) {
			$favouriteNamespace = new Zend_Session_Namespace('favourite_cars');
			if (!isset($favouriteNamespace->cars) || !is_array($favouriteNamespace->cars)) {
				$favouriteNamespace->cars = array();
			}
			if (!in_array($favourite, $favouriteNamespace->cars)) {
				$favouriteNamespace->cars[] = $favourite;
				$this->view->messages[] = '<span class="favourites">'.$this->view->translate->_('CAR_ADDED_TO_FAVOURITES').'</span>';
			}
			else {
				$this->view->messages[] = '<span class="favourites">'.$this->view->translate->_('CAR_ALREADY_IN_FAVOURITES').'</span>';
			}
		}
		/* k */

		/* Drukowanie pdfa */
		$doPrint = $this->_request->getParam('print', null) == "true";
		if ($doPrint) {
			$this->_helper->layout->disableLayout();

			$db = Model_Base::getDb();
			$results->limit('50');
			$this->view->cars = $db->fetchAll($results);

			$pdf = new My_Tcpdf();
			$html = $this->view->render("car_list_pdf.phtml");
			//echo $html;
			$pdf->generateCarList($html);

			$this->_helper->viewRenderer->setNoRender(true);
		}
		/* k */


        /* lista ulubionych */


		$searchStored = false;
		if ($this->view->identity and $this->view->identity->id and $searchParameters)  {
			$searchStored = $users->isSearchStored($searchParameters, $this->view->identity->id);
		}
		$this->view->searchStored = $searchStored;


		$form->populate($this->_request->getParams() + array('make' => $make));


		if (is_array($searchParameters) && array_key_exists('make', $searchParameters) && !empty($searchParameters['make'])) {
			$this->view->suggestedModels = $cars->getCarModelsWithCountsWithFilters($results);
		}

		$this->view->searchParameters = $searchParameters;
		$this->view->queryStringArray = $queryStringArray;

		$this->view->hash = $searchHash;
        $form->setSearchParameters($searchParameters);
		$this->view->form = $form;
		$this->view->saveForm = $saveForm;
		$paginator = Zend_Paginator::factory(array());

		if ($results) {
			$paginator = new Zend_Paginator(new Zend_Paginator_Adapter_DbSelect($results));
			$paginator->setCurrentPageNumber($page);
		}

		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		if ($perPage == 'max') {
			$paginator->setItemCountPerPage($opt['paging']['resultsMax']);
			$this->view->pagingDefaultResults = $opt['paging']['results'];
		}
		else {
			$paginator->setItemCountPerPage($opt['paging']['results']);
			$this->view->pagingMaxResults = $opt['paging']['resultsMax'];
		}
		$this->view->perPage = $perPage;

		if ($iterator) {
			$page = (floor(($iterator - 1) / $paginator->getItemCountPerPage())) + 1;
			$paginator->setCurrentPageNumber($page);
		}


		$paginator->setPageRange($opt['paging']['defaultPageRange']);
		$this->view->paginator = $paginator;
		$this->view->page = $page;

		$exclusiveArray = array("EXCLUSIVE");
		if (isset($searchParameters['exclusive']) && $searchParameters['exclusive'] == "0") {
			$exclusiveArray = array("NONEXISTENT_CONSTANT_VALUE_THAT_WILL_MAKE_THE_RESULTS_EMPTY");
		}

		$types = null;
		if (isset($searchParameters['exclusive']) && $searchParameters['exclusive'] == 1) {
			$types = array(1);
		}
		else if (isset($searchParameters['types']) && !empty($searchParameters['types'])) {
			$types = $searchParameters['types'];
		}
		$this->view->types = $types;

		$this->view->makesWithCounts = $cars->getCarMakesWithCountsByNamesWithFilters($results, $types, array("NULL", "PREMIUM", "EXCLUSIVE"));
		//$this->view->exclusiveMakesWithCounts = $cars->getCarMakesWithCountsByNamesWithFilters($results, $types, $exclusiveArray);

		$this->view->otherMakesLayout = "bottom";

		if ($searchParameters && isset($searchParameters['categories']) && in_array('over_3.5t',$searchParameters['categories'])) {
			$this->view->otherMakesLayout = "top";
		}

		$locations = new Model_Locations();
		$this->view->locationGroups = $locations->getLocationGroups($assoc=true);

        if(isset($searchParameters['location'])) {

            $this->view->locationGroup = array('id' => $searchParameters['location'], 'name' => $this->view->locationGroups[$searchParameters['location']]);
        } else {

            $this->view->locationGroup =  array('name' => $this->view->translate->_('LOCATION_ANY'));
        }

		if (Zend_Auth::getInstance()->hasIdentity()) {
			$userSearches = $users->getUserStoredSearch($this->view->identity->id);
			$this->view->storedSearches = $userSearches;
		}

		if (!$doPrint) {
			$url = null; //helper will determine correct url by default
			if (!$this->_request->getParam('hash')) {
				$url = $this->_request->getServer('REQUEST_URI');
				$url .= '?hash=' . $searchHash;
			}

			$this->_helper->setRedirBack('register', $url);
			$this->_helper->setRedirBack('login', $url);
			$this->_helper->setRedirBack('logout', $url);
			$this->_helper->setRedirBack('user_cp_edit', $url);
			$this->_helper->setRedirBack('search-list', $url);
			$this->_helper->setRedirBack('favourite-list-delete', $this->_request->getServer('HTTP_REFERER'));
			$this->_helper->setRedirBack('search-list-delete', $url);
		}

        $this->view->favouritesIds = $users->getFavouritesIds();


        $title = $this->generateTitle($searchParameters);

        if(empty($title)) {
            $title = $this->view->translate->_('SEARCH_RESULTS') . ' (' . $paginator->getTotalItemCount() . ')';
        }

		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'list', true) => $title
		);


		if($this->view->language == 'pl' && empty($queryStringArray) && empty($type) && empty($make) && empty($model)) {
			$this->view->showLeftDescription = true;
		}

        $this->view->topSearchForm = $form;
	}

    private function generateTitle($searchParameters) {

        $titleArray = array();
		$cars = new Model_Cars_Cars();
		$tr = Zend_Registry::get('Zend_Translate');

        if(isset($searchParameters['make']) && !empty($searchParameters['make'])) {
			$make = $cars->getCarMakeBySlug($searchParameters['make']);
            $titleArray[]  = $make['name'];
        }
        if(isset($searchParameters['model']) && !empty($searchParameters['model'])) {

            $model = $cars->getCarModelBySlug($searchParameters['model']);
            $titleArray[]  = $model['name'];
        }

        if(isset($searchParameters['build_from']) && !empty($searchParameters['build_from']) &&
            isset($searchParameters['build_to']) && !empty($searchParameters['build_to'])) {

            $titleArray[]  = $searchParameters['build_from'].'-'.$searchParameters['build_to'];

        } else  if(isset($searchParameters['build_from']) && !empty($searchParameters['build_from'])) {

            $titleArray[]  = $searchParameters['build_from'];

        } else  if(isset($searchParameters['build_to']) && !empty($searchParameters['build_to'])) {

            $titleArray[]  = $searchParameters['build_to'];

        }

		if(isset($searchParameters['type']) && !empty($searchParameters['type'])) {

			$carTypesSlug = $cars->getCarTypesSlug(true);
			$titleArray[]  = $tr->_('type_' . $carTypesSlug[$searchParameters['type']]);
		}


        if(isset($searchParameters['price_min']) && !empty($searchParameters['price_min']) &&
            isset($searchParameters['price_max']) && !empty($searchParameters['price_max'])) {

            $titleArray[]  = $searchParameters['price_min'].'-'.$searchParameters['price_max'];

        } else  if(isset($searchParameters['price_min']) && !empty($searchParameters['price_min'])) {

            $titleArray[]  = $searchParameters['price_min'];

        } else  if(isset($searchParameters['price_max']) && !empty($searchParameters['price_max'])) {

            $titleArray[]  = $searchParameters['price_max'];

        }


        if(!empty($titleArray)) {
            return implode(' ',$titleArray);
        } else {
           return false;
        }

    }

	public function oldListAction() {
		$page = $this->getRequest()->getParam('page');
		$searchHash = $this->getRequest()->getParam('hash');
		$removeName = $this->getRequest()->getParam('removeName');
		$removeValue = $this->getRequest()->getParam('removeValue');
		$removeOther = $this->_request->getParam('removeOther', null);
		$addName = $this->_request->getParam('addName');
		$addValue = $this->_request->getParam('addValue');
		$perPage = $this->_request->getParam('perPage', null);
		$iterator = $this->_request->getParam('i', null);

		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$domain = Zend_Registry::get('siteDomain');

		$this->view->showSaveForm = $this->_request->getParam('show_save_form', false);

		$searchNamespace = new Zend_Session_Namespace('search');
		if (!$searchNamespace->searchParameters) {
			$searchNamespace->searchParameters = array();
		}

		$cars = new Model_Cars_Cars();
		$form = new Form_Search();

		$saveForm = null;
		if (Zend_Auth::getInstance()->hasIdentity()) {
			if (Zend_Auth::getInstance()->getIdentity()->role == "salesman") {
				$saveForm = new Form_SearchItemBySalesman();
				$saveForm->populate(array('added_by_sr_id' => Zend_Auth::getInstance()->getIdentity()->sr_id));
			}
			else {
				$saveForm = new Form_SearchItem();
			}
		}
		else {
			$saveForm = new Form_SearchItemNoIdentity();
		}

		$results = false;
		$searchParameters = array();
		$post = false;
		$data = false;
		if ($this->_request->isPost()) {
			$post = true;
			$data = $this->_request->getPost();

			if (array_key_exists('submit', $data) or array_key_exists('submit2', $data) or array_key_exists('query', $data)) {

				if (array_key_exists('query', $data)) {
					$this->view->query = $data['query'];
				}

				if ($form->isValid($data)) {
					$data = $form->getValues();

					do {
						$searchHash = $this->generateHash();
					} while (array_key_exists($searchHash, $searchNamespace->searchParameters));
					$searchParameters = $cars->searchPrepareParameters($data);
					$exists = false;
					foreach($searchNamespace->searchParameters as $hash => $searchData) {
						if (serialize($searchData) == serialize($data)) {
							$searchHash = $hash;
							$exists = true;
						}
					}

					if (!$exists) {
						$searchNamespace->searchParameters[$searchHash] = $searchParameters;
					}
				}
			} elseif ($searchHash && array_key_exists($searchHash, $searchNamespace->searchParameters)) {
				$searchParameters = $searchNamespace->searchParameters[$searchHash];
			}
		} elseif ($searchHash && array_key_exists($searchHash, $searchNamespace->searchParameters)) {
			$searchParameters = $searchNamespace->searchParameters[$searchHash];
		} else {
			//empty parameters - just browsing
			$data = $form->getValues();
			do {
				$searchHash = $this->generateHash();
			} while (array_key_exists($searchHash, $searchNamespace->searchParameters));
			$searchParameters = $cars->searchPrepareParameters($data);
			$searchNamespace->searchParameters[$searchHash] = $searchParameters;
		}

		$nonArrayFilters = array('price_min', 'price_max', 'promotions', 'leasing', 'leasing_transfer', 'credit_collateral', 'new', 'last_2_years', 'premium', 'exclusive', '4x4_pickup', 'after_leasing_vindication', 'location', 'on_site', 'rental', 'query', 'displacement_max', 'seat_count', 'seat_count_max', 'seat_count_min', 'consumption_less_than', 'build_from', 'build_to', 'for_exchange', 'price_type_key', 'auction');


		if ($removeName) {
			if (in_array($removeName, $nonArrayFilters)) {
				unset($searchParameters[$removeName]);
			} else {
				unset($searchParameters[$removeName][array_search($removeValue, $searchParameters[$removeName])]);
			}
			$searchNamespace->searchParameters[$searchHash] = $searchParameters;
		}

		if ($addName && is_array($addName)) {
			foreach ($addName as $i => $name) {
				$addValue = $this->_request->getParam('addValue_' . $i);
				$removeOther = $this->_request->getParam('removeOther_' . $i);
				if (in_array($name, $nonArrayFilters)) {
					if (is_array($removeOther)) {
						foreach ($removeOther as $roName) {
							unset($searchParameters[$roName]);
						}
					}
					else {
						unset($searchParameters[$name]);
					}
					$searchParameters[$name] = $addValue;
				} else {
					if (is_array($removeOther)) {
						foreach ($removeOther as $roName) {
							unset($searchParameters[$roName]);
						}
					}
					else {
						if (isset($searchParameters[$name]) && is_array($searchParameters[$name])) {
							unset($searchParameters[$name][array_search($addValue, $searchParameters[$name])]);
						}
					}
					foreach ($addValue as $key => $value) {
						$searchParameters[$name][] = $value;
					}
				}
			}
			$searchNamespace->searchParameters[$searchHash] = $searchParameters;
		}



		if($this->view->language != 'pl')
			$searchParameters['no_new_car'] = true;


		if(isset($searchParameters['auction']) && $searchParameters['auction'] == 1) {
			$this->view->auctionSearch = true;
		} else {
			$this->view->auctionSearch = true; //zawsze wyswietlaj jako aukcja
		}

		if($this->_request->getParam('100_vat')) {
			$searchParameters['100_vat'] = true;
		}

		$date = $this->_getParam('date', false);
		if ($searchParameters) {
			$results = $cars->search($searchParameters, true,false,true,$date);
		} else {
			$results = $cars->search(array(), true,false,true,$date);
		}

		$filterArray = $cars->prepareFilterArray($searchParameters, $form);
		$this->view->filterArray = $filterArray;
		$this->view->filterArrayIncludeTitle = array('MINPRICE', 'MAXPRICE', 'BUILD_FROM', 'BUILD_TO', 'ENGINE_CAPACITY_FROM', 'ENGINE_CAPACITY_TO');

		if ($filterArray && $saveForm) {
			$valueStr = "";
			$counted = 0;
			foreach ($filterArray as $key => $value) {
				if ($counted == 3) break;

				if ($counted > 0) {
					$valueStr .= ", ";
				}

				if ($value['value'] == "") {
					$valueStr .= $this->view->translate->_($value['title']);
				}
				else {
					if (in_array($value['title'], array("CATEGORY", "FEATURE", "FUEL", "MAKE", "MODEL"))) {
						$valueStr .= $value['value'];
					}
					else {
						$valueStr .= $this->view->translate->_($value['title']) . ": " . $value['value'];
					}
				}
				$counted++;
			}
			$saveForm->title->setValue($valueStr);
		}

		$filterInt = new Zend_Filter_Int();
		$users = new Model_Users();

		/* Kod do zapisania searcha - można go wsadzić gdziekolwiek gdzie są dane posta searcha lub dane pobrane z sesji (hash) */
		if ($post && array_key_exists('save', $data) && is_array($searchParameters)) {
			$data = $this->_request->getPost();
			if ($data && $saveForm->isValid($data) && $this->view->identity && $this->view->identity->id) {
				$result = $users->saveSearch($saveForm->getValues(), $searchParameters, $this->view->identity->id);
			}
			elseif ($data && $saveForm->isValid($data)) {
				//valid but user not logged in
				$result = $users->saveSearch($saveForm->getValues(), $searchParameters, null);
			}
			else if (!$saveForm->isValid($data)) {
				$this->view->showSaveForm = true;
			}

			if ($result) {
				$searchId = $result;
				if ($data['newsletter']) {
					$searches = $users->getSearchesWithCleanup($date=null, $searchId);
					$cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
					$cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);

					foreach($searches as $searchUser) {
						$msg = false;
						if (count($searchUser['cars'])) {
							$employees = new Model_Employees();

							$employee = null;

							if($searchUser['employee_sr_id'])
								$employee = $employees->getBySrId($searchUser['employee_sr_id']);

							$this->view->css = $cssContent;
							$this->view->user = $searchUser;
							$this->view->employee = $employee;
							$html = $this->view->render('newsletter_content.phtml');

							$emogrifier = new My_Emogrifier($html);
							$html = $emogrifier->emogrify();

							$mail = new Zend_Mail($charset="UTF-8");
							$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

							$mail->setFrom(
								$opt['resources']['mail']['defaultFrom']['email'],
								!empty($employee) ? ($employee['first_name'] . ' ' . $employee['last_name']) : ''
							);

							$mail->setReplyTo(
								(!empty($employee['email']) ? $employee['email'] : $opt['resources']['mail']['defaultFrom']['email']),
								!empty($employee) ? ($employee['first_name'] . ' ' . $employee['last_name']) : ''
							);

							$text = $this->view->translate->_('NEWSLETTER_GREETING') . "\n".
								$this->view->translate->_('NEWSLETTER_HEADING') .':'.  $searchUser['search_name']
								. "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');

							$firstCar = $searchUser['cars'][0];

							$subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
								$firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);

							$mail->addTo($searchUser['email'])
                                ->setBodyText($text)
								->setBodyHtml($html)
								->setSubject($subject);

							$mail->send();
						}
					}
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'send-newsletter'), Zend_Log::INFO);
					$this->view->messages[] = $this->view->translate->_('NEWSLETTER_SENT');
				}
			}
			/* k */
		}

		/* Kod do zapisania searcha - dla handlowca dodajacego klienta */
		if ($post && array_key_exists('save_by_salesman', $data) && is_array($searchParameters)) {
			$data = $this->_request->getPost();
			if ($data && $saveForm->isValid($data) && $this->view->identity && $this->view->identity->role == "salesman") {
				$data = $saveForm->getValues();
				try {
					$searchId = $users->saveSearchBySalesman($data, $searchParameters);
					if (!$searchId) {
						$this->view->messages[] = "Profil nie został zapisany - prawdopodobnie został już zapisany wcześniej";
					}
					else {
						$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'save-search'), Zend_Log::INFO);
						$this->view->messages[] = "Profil wyszukiwania został zapisany";

						if ($data['newsletter']) {
							$searches = $users->getSearchesWithCleanup($date=null, $searchId);
							$cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
							$cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);

							foreach($searches as $searchUser) {
								$msg = false;
								if (count($searchUser['cars'])) {
									$employees = new Model_Employees();
									$employee = null;

									if($searchUser['employee_sr_id'])
										$employee = $employees->getBySrId($searchUser['employee_sr_id']);

									$this->view->css = $cssContent;
									$this->view->user = $searchUser;
									$this->view->employee = $employee;
									$html = $this->view->render('newsletter_content.phtml');

									$emogrifier = new My_Emogrifier($html);
									$html = $emogrifier->emogrify();

									$mail = new Zend_Mail($charset="UTF-8");
									$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

									$mail->setFrom(
										$opt['resources']['mail']['defaultFrom']['email'],
										!empty($employee) ? ($employee['first_name'] . ' ' . $employee['last_name']) : ''
									);

									$mail->setReplyTo(
										(!empty($employee['email']) ? $employee['email'] : $opt['resources']['mail']['defaultFrom']['email']),
										!empty($employee) ? ($employee['first_name'] . ' ' . $employee['last_name']) : ''
									);



									$text = $this->view->translate->_('NEWSLETTER_GREETING') . "\n".
										$this->view->translate->_('NEWSLETTER_HEADING') .':'.  $searchUser['search_name']
										. "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');

									$firstCar = $searchUser['cars'][0];

									$subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
										$firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);

									$mail->addTo($searchUser['email'])
										->setBodyText($text)
										->setBodyHtml($html)
										->setSubject($subject);

									$mail->send();
								}
							}
							$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'send-newsletter'), Zend_Log::INFO);
							$this->view->messages[] = $this->view->translate->_('NEWSLETTER_SENT');
						}
					}
				}
				catch (Exception $e) {
					$this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage() . ' / ' . $e->getTraceAsString()), Zend_Log::WARN);
					$this->view->messages[] = $e->getMessage();
				}
			}
			else if (!$saveForm->isValid($data)) {
				$this->view->showSaveForm = true;
			}
			/* k */
		}

		/* Zapisywanie auta do ulubionych */
		$favourite = $filterInt->filter($this->getRequest()->getParam('favourite'));
		if ($favourite && $this->view->identity && $this->view->identity->id) {
			$users->saveFavourite($favourite, $this->view->identity->id);
			$this->view->messages[] = '<span class="favourites">'.$this->view->translate->_('CAR_ADDED_TO_FAVOURITES').'</span>';
		}
		elseif ($favourite) {
			$favouriteNamespace = new Zend_Session_Namespace('favourite_cars');
			if (!isset($favouriteNamespace->cars) || !is_array($favouriteNamespace->cars)) {
				$favouriteNamespace->cars = array();
			}
			if (!in_array($favourite, $favouriteNamespace->cars)) {
				$favouriteNamespace->cars[] = $favourite;
				$this->view->messages[] = '<span class="favourites">'.$this->view->translate->_('CAR_ADDED_TO_FAVOURITES').'</span>';
			}
			else {
				$this->view->messages[] = '<span class="favourites">'.$this->view->translate->_('CAR_ALREADY_IN_FAVOURITES').'</span>';
			}
		}
		/* k */

		/* Drukowanie pdfa */
		$doPrint = $this->_request->getParam('print', null) == "true";
		if ($doPrint) {
			$this->_helper->layout->disableLayout();

			$db = Model_Base::getDb();
			$this->view->cars = $db->fetchAll($results);

			$pdf = new My_Tcpdf();
			$html = $this->view->render("car_list_pdf.phtml");
			//echo $html;
			$pdf->generateCarList($html);

			$this->_helper->viewRenderer->setNoRender(true);
		}
		/* k */


		/* lista ulubionych */


		$searchStored = false;
		if ($this->view->identity and $this->view->identity->id and $searchParameters)  {
			$searchStored = $users->isSearchStored($searchParameters, $this->view->identity->id);
		}
		$this->view->searchStored = $searchStored;

		if (!$post) {
			$form->populate($cars->prepareFormDataFromParams($searchParameters));
		}

		if (is_array($searchParameters) && array_key_exists('makes', $searchParameters) && !empty($searchParameters['makes'])) {
			$this->view->suggestedModels = $cars->getCarModelsWithCountsWithFilters($results);
		}

		$this->view->searchParameters = $searchParameters;

		$this->view->hash = $searchHash;
		$form->setSearchParameters($searchParameters);
		$this->view->form = $form;
		$this->view->saveForm = $saveForm;
		$paginator = Zend_Paginator::factory(array());

		if ($results) {
			$paginator = new Zend_Paginator(new Zend_Paginator_Adapter_DbSelect($results));
			$paginator->setCurrentPageNumber($page);
		}

		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		if ($perPage == 'max') {
			$paginator->setItemCountPerPage($opt['paging']['resultsMax']);
			$this->view->pagingDefaultResults = $opt['paging']['results'];
		}
		else {
			$paginator->setItemCountPerPage($opt['paging']['results']);
			$this->view->pagingMaxResults = $opt['paging']['resultsMax'];
		}
		$this->view->perPage = $perPage;

		if ($iterator) {
			$page = (floor(($iterator - 1) / $paginator->getItemCountPerPage())) + 1;
			$paginator->setCurrentPageNumber($page);
		}


		$paginator->setPageRange($opt['paging']['defaultPageRange']);
		$this->view->paginator = $paginator;
		$this->view->page = $page;

		$exclusiveArray = array("EXCLUSIVE");
		if (isset($searchParameters['exclusive']) && $searchParameters['exclusive'] == "0") {
			$exclusiveArray = array("NONEXISTENT_CONSTANT_VALUE_THAT_WILL_MAKE_THE_RESULTS_EMPTY");
		}

		$types = null;
		if (isset($searchParameters['exclusive']) && $searchParameters['exclusive'] == 1) {
			$types = array(1);
		}
		else if (isset($searchParameters['types']) && !empty($searchParameters['types'])) {
			$types = $searchParameters['types'];
		}
		$this->view->types = $types;

		$this->view->makesWithCounts = $cars->getCarMakesWithCountsByNamesWithFilters($results, $types, array("NULL", "PREMIUM", "EXCLUSIVE"));
		//$this->view->exclusiveMakesWithCounts = $cars->getCarMakesWithCountsByNamesWithFilters($results, $types, $exclusiveArray);

		$this->view->otherMakesLayout = "bottom";

		if ($searchParameters && isset($searchParameters['categories']) && in_array('over_3.5t',$searchParameters['categories'])) {
			$this->view->otherMakesLayout = "top";
		}

		$locations = new Model_Locations();
		$this->view->locationGroups = $locations->getLocationGroups($assoc=true);

		if(isset($searchParameters['location'])) {

			$this->view->locationGroup = array('id' => $searchParameters['location'], 'name' => $this->view->locationGroups[$searchParameters['location']]);
		} else {

			$this->view->locationGroup =  array('id' => $searchParameters['location'], 'name' => $this->view->translate->_('LOCATION_ANY'));
		}

		if (Zend_Auth::getInstance()->hasIdentity()) {
			$userSearches = $users->getUserStoredSearch($this->view->identity->id);
			$this->view->storedSearches = $userSearches;
		}

		if (!$doPrint) {
			$url = null; //helper will determine correct url by default
			if (!$this->_request->getParam('hash')) {
				$url = $this->_request->getServer('REQUEST_URI');
				$url .= '?hash=' . $searchHash;
			}

			$this->_helper->setRedirBack('register', $url);
			$this->_helper->setRedirBack('login', $url);
			$this->_helper->setRedirBack('logout', $url);
			$this->_helper->setRedirBack('user_cp_edit', $url);
			$this->_helper->setRedirBack('search-list', $url);
			$this->_helper->setRedirBack('favourite-list-delete', $this->_request->getServer('HTTP_REFERER'));
			$this->_helper->setRedirBack('search-list-delete', $url);
		}

		$this->view->favouritesIds = $users->getFavouritesIds();


		$title = $this->generateTitle($searchParameters);

		if(empty($title)) {
			$title = $this->view->translate->_('SEARCH_RESULTS') . ' (' . $paginator->getTotalItemCount() . ')';
		}

		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'list', true) => $title
		);
	}

	public function myCarsAction() {
		$this->_helper->setRedirBack('logout');
		$this->_helper->setRedirBack('user_cp_edit');

		$cars = new Model_Cars_Cars();
		$myCars = $cars->search(array('owner_email' => Zend_Auth::getInstance()->getIdentity()->email), $retSel=true);
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$this->view->paginator = $this->_helper->paginator($myCars, $opt['paging']['mycars']);

	}

	public function myReservationsAction() {
		$this->_helper->setRedirBack('logout');
		$this->_helper->setRedirBack('user_cp_edit');

		$ct = new Model_Cars_Transfers();
		$cars = new Model_Cars_Cars();

		$myRes = $ct->getUsersReservations(Zend_Auth::getInstance()->getIdentity()->id);
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$this->view->paginator = $this->_helper->paginator($myRes, $opt['paging']['reservations']);

		$this->view->form = new Form_Search();
		$this->view->makesWithCounts = $cars->getCarMakesWithCountsByNames();

        $users = new Model_Users();
        $this->view->favouritesIds =  $users->getFavouritesIds();
	}

	public function paymentFailedAction() {
		$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $this->_request->getParam('error')), Zend_Log::WARN);
	}

	public function paymentOkAction() {
		$this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);
	}

	public function paymentStatusChangedAction() {
		$this->_helper->viewRenderer->setNoRender();
		$this->_helper->layout()->disableLayout();
		if ($this->_request->isPost()) {
			$data = $this->_request->getPost();

			ob_start();
			try {
				$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
				$optPlatnosci = $opt['transfers']['platnoscipl'];
				$platnosci = new My_PlatnosciPl($optPlatnosci);
				$transfers = new Model_Cars_Transfers();

				$payment = $transfers->getPayment((int)$data['session_id']);
				if (empty($payment)) {
					throw new Exception("No payment found in " . __METHOD__ . ", line " . (__LINE__ - 1) . "; id=" . $data['session_id']);
				}

				$status = $platnosci->checkStatus($data);
				$transfers->updatePayment($payment['id'], array('status' => $status['status']));

				if ($status['status'] == 99 && $status['amount'] == $payment['amount'] * 100) {
					$transfers->updatePayment($payment['id'], array('date_completed' => new Zend_Db_Expr('NOW()')));
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'make-reservation', 'additional' => 'payment complete ok; id:' . $payment['id']), Zend_Log::INFO);
				}
				else {
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => 'status or amount not ok; status=' . $status['status'] . "; amount=" . $status['amount']), Zend_Log::INFO);
				}
                if(empty($payment['car_id']))
                    $transfers->updateNewCarOnPaymentStatusChange($payment, $status);
                else
                    $transfers->updateReservationsOnPaymentStatusChange($payment, $status);
				$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'updateReservationsOnStatusChange', 'additional' => 'status: ' . $status['status'] . "; amount=" . $status['amount']), Zend_Log::INFO);
			}
			catch (Exception $e) {
				$this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage() . " " . $e->getTraceAsString()), Zend_Log::WARN);
			}
			ob_get_clean();

			echo "OK";
			exit;
		}//isPost
	}

	public function searchEditAction() {
		$hash = $this->getRequest()->getParam('hash');

		$users = new Model_Users();
		$userSearch = $users->getUserStoredSearch(null,null,$hash);

		if (!$userSearch || !is_array($userSearch)) {
			$this->_helper->redirectBack('search-list');
		}

        if(Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == "user")
            $logged = true;
        else
            $logged = false;


        $this->view->logged = $logged;

		$saveForm = new Form_SearchItem();
		$form = new Form_Search(array('variant' => 'have_car_found', 'appendForms' => array($saveForm), 'replaceSubmit' => $saveForm, 'deleteSubmit' => true));
		$form->setAction('')->setAttrib('id', 'edit_form');

		if ($this->_request->isPost()) {
			$data = $this->_request->getPost();

            if(!empty($data['delete']))
            {
                $users->deleteSearchByHash($hash);
                $this->view->messenger->addMessage($this->_helper->translate('USER_SEARCH_DELETED'));
                $this->_redirect($this->view->url(array('language' => $this->view->language), 'searchList', true).'?set='.$userSearch['hash']);

            }
			if ($form->isValid($data)) {
				$data = $form->getValues();
				unset($data['csrf']);

				$users->editSearch($userSearch['id'], $data);

                $this->_redirect($this->view->url(array('language' => $this->view->language), 'searchList', true).'?set='.$userSearch['hash']);

			}
		}

		$cars = new Model_Cars_Cars();
		$form->populate($userSearch);
		$form->populate($cars->prepareFormDataFromParams(unserialize($userSearch['search_data'])));

		$this->view->form = $form;
	}

	public function searchListAction() {
		$users = new Model_Users();

        if(Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == "user")
            $logged = true;
        else
            $logged = false;

        $this->view->logged = $logged;

		if ($this->_request->isGet()) {
			$del = $this->getRequest()->getParam('del');
			$set = $this->getRequest()->getParam('set');
            $email = $this->getRequest()->getParam('email');
			if ($del) {
				$users->deleteSearchByHash($del);
                 $this->view->messenger->addMessage($this->_helper->translate('USER_SEARCH_DELETED'));
				$this->_helper->redirectBack('search-list-delete');
			}
			if ($set) {
                $date = $this->_getParam('date');
				//Przywrócenie wyników wyszukiwania do sesji i przeniesienie do 1 strony listy
				$searchData = $users->getUserStoredSearch(null, null, $set);
				if (!$searchData || !is_array($searchData)) {
					echo 'Wyszukiwanie nie może być przywrócone';
				} else {
					$searchNamespace = new Zend_Session_Namespace('search');
					if (!$searchNamespace->searchParameters) {
						$searchNamespace->searchParameters = array();
					}
					$searchHash = false;
					foreach($searchNamespace->searchParameters as $hash => $searchParameters) {
						if (serialize($searchParameters) == $searchData['search_data']) {
							$searchHash = $hash;
						}
					}
					if (!$searchHash) {
						do {
							$searchHash = $this->generateHash();
						} while (array_key_exists($searchHash, $searchNamespace->searchParameters));
						$searchNamespace->searchParameters[$searchHash] = unserialize($searchData['search_data']);
					}
					$this->_redirect($this->view->url(array(), 'list') . '?hash=' . $searchHash.'&date='. $date);
				}
			}
		}
        if($email)
            $userSearches = $users->getUserStoredSearch(null,null,null,$email);
        elseif(Zend_Auth::getInstance()->hasIdentity())
            $userSearches = $users->getUserStoredSearch($this->view->identity->id);
        else
            $this->_redirect($this->view->url(array('language' => $this->view->language), 'home', true));

		$this->view->storedSearches = $userSearches;
		$this->_helper->setRedirBack('search-list-delete');
	}

	public function showAction() {

        $this->view->menuActive = 'buyCar';

		$this->_helper->setRedirBack('register');
		$this->_helper->setRedirBack('login');
		$this->_helper->setRedirBack('logout');
		$this->_helper->setRedirBack('user_cp_edit');


		$id = (int)$this->_request->getParam('id');
		$searchHash = $this->getRequest()->getParam('hash');
		$iterator = $this->getRequest()->getParam('i');

		$this->view->perPage = $this->_request->getParam('perPage'); // back to results needs this
        $this->view->auction =  $this->_request->getParam('auction');

		$cars = new Model_Cars_Cars();

        if($cars->isNewCar($id))
            $this->_redirect($this->view->url(array('language' => $this->view->language, 'carId' => $id), 'newCars', true));




		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

		if($this->view->isMobile) {
			$form = new Form_Search();
		} else {
			$form = new Form_NewSearch();
		}
		$this->view->form = $form;

		$viewsNamespace = new Zend_Session_Namespace('car_views');
		if (!isset($viewsNamespace->carIds) || !is_array($viewsNamespace->carIds)) {
			$viewsNamespace->carIds = array();
		}
		if (!in_array($id, $viewsNamespace->carIds)) {
			$cars->incrementViews($id);
			$viewsNamespace->carIds[] = $id;
		}

		$searchNamespace = new Zend_Session_Namespace('search');
		if (!$searchNamespace->searchParameters) {
			$searchNamespace->searchParameters = array();
		}

		if($searchNamespace->searchUrl) {
            $this->view->searchUrl = $searchNamespace->searchUrl;
        }


		$saveForm = null;
		if (Zend_Auth::getInstance()->hasIdentity()) {
			if (Zend_Auth::getInstance()->getIdentity()->role == "salesman") {
				$saveForm = new Form_SearchItemBySalesman();
			}
			else {
				$saveForm = new Form_SearchItem();
			}
		}
		else {
			$saveForm = new Form_SearchItemNoIdentity();
		}
		$this->view->saveForm = $saveForm;

		$nextPrev = false;
		$searchParameters = array();
		if ($searchHash && array_key_exists($searchHash, $searchNamespace->searchParameters) && $iterator) {
			$searchParameters = $searchNamespace->searchParameters[$searchHash];

			$nextPrev = $cars->search($searchParameters, true, $iterator);
            $form->setSearchParameters($searchParameters);
			$form->populate($cars->prepareFormDataFromParams($searchParameters));
		}

		$filterArray = $cars->prepareFilterArray($searchParameters, $form);
		//$this->view->filterArray = $filterArray;

		if ($filterArray && $saveForm) {
			$valueStr = "";
			$counted = 0;
			foreach ($filterArray as $key => $value) {
				if ($counted == 3) break;

				if ($counted > 0) {
					$valueStr .= ", ";
				}

				if ($value['value'] == "") {
					$valueStr .= $this->view->translate->_($value['title']);
				}
				else {
					if (in_array($value['title'], array("CATEGORY", "FEATURE", "FUEL", "MAKE", "MODEL"))) {
						$valueStr .= $value['value'];
					}
					else {
						$valueStr .= $this->view->translate->_($value['title']) . ": " . $value['value'];
					}
				}
				$counted++;
			}
			$saveForm->title->setValue($valueStr);
		}

		$results = $cars->search($searchParameters, true);

		$this->view->makesWithCounts = $cars->getCarMakesWithCountsByNamesWithFilters($results, $types=array(1), array("NULL", "PREMIUM"));
		$this->view->exclusiveMakesWithCounts = $cars->getCarMakesWithCountsByNamesWithFilters($results, $types=array(1), array("EXCLUSIVE"));

        $this->view->otherMakesLayout = "bottom";

        if ($searchParameters && isset($searchParameters['categories']) && in_array('over_3.5t',$searchParameters['categories'])) {
            $this->view->otherMakesLayout = "top";
        }

		if ($results) {
			$paginator = new Zend_Paginator(new Zend_Paginator_Adapter_DbSelect($results));
			//this is just to show total number of items in view:
			$paginator->setItemCountPerPage(1);
			$paginator->setCurrentPageNumber(1);
			$this->view->paginator = $paginator;
		}

		$car = $cars->getCarFull($id);
		if (empty($car)) {
			$this->view->messenger->addMessage($this->_helper->translate('CAR_NOT_FOUND'));
			$this->_helper->redirectBack('searchList');
		}


		$this->view->canonicalUrl = $this->view->domain . $this->view->carLink($car, null, true);

		$featuresBlackList = array(
    		32, 33, 34, 19, 31, 30
    	);

    	foreach ($car['features'] as $index => $feature) {
    		if (in_array($index, $featuresBlackList)) {
    			unset($car['features'][$index]);
    		}
    	}

		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'list', true) => $this->view->translate->_('CARS'),
			$this->view->carLink($car, null, $onlyHref=true) => $car['make_name'] . ' ' . $car['model_name'] . ' ' . $car['build_year'] . ' ' . ($this->view->language == 'pl' ? $car['title'] : '')
		);

		$config = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$this->view->similarMakesWithCounts = $cars->getSimilarCarMakesWithCounts($car);
		$this->view->similarCars = $cars->getSimilarCars($car, $config['similar']);

		$getString = "?hash=" . $searchHash . "&i=" . $iterator . "&perPage=" . $this->view->perPage;
        if($this->view->auction) {
            $getString .= '&auction=1';
        }
        $getString .= "#car_id_" . $car['car_id'];

        $this->view->getString = $getString;

		$testDriveForm = new Form_Car_TestDrive();
		$testDriveForm2 = new Form_Car_TestDrive();
        $testDriveForm3 = new Form_Car_TestDrive();
        $testDriveForm4 = new Form_Car_TestDrive();
		if (Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == "user") {
			$testDriveForm->populate(get_object_vars(Zend_Auth::getInstance()->getIdentity()));
			$testDriveForm2->populate(get_object_vars(Zend_Auth::getInstance()->getIdentity()));
            $testDriveForm3->populate(get_object_vars(Zend_Auth::getInstance()->getIdentity()));
            $testDriveForm4->populate(get_object_vars(Zend_Auth::getInstance()->getIdentity()));
		}
		$testDriveForm->setAction(
			$this->view->url(
				array(
					'language' => $this->view->language,
					'id' => $car['car_id'],
					'description' => $this->view->carPermalink($car)
				),
				'showCarTestDrive',
				true
			)
			. $getString
		);
		$testDriveForm->addElement('hash', 'csrf', array('salt' => get_class($testDriveForm) . "_{$id}"));

		$testDriveForm2->setAction(
			$this->view->url(
				array(
					'language' => $this->view->language,
					'id' => $car['car_id'],
					'description' => $this->view->carPermalink($car)
				),
				'showCarTestDrive',
				true
			)
			. $getString
		);
        $testDriveForm2->addElement(new Zend_Form_Element_Hidden('is_contact_caretaker', array('value' => 1)));
		$testDriveForm2->addElement('hash', 'csrf', array('salt' => get_class($testDriveForm3) . "_2_{$id}"));
        $testDriveForm2->removeElement('date');

        $testDriveForm3->setAction(
            $this->view->url(
                array(
                    'language' => $this->view->language,
                    'id' => $car['car_id'],
                    'description' => $this->view->carPermalink($car)
                ),
                'showCarTestDrive',
                true
            )
            . $getString
        );


        $testDriveForm3->addElement('hash', 'csrf', array('salt' => get_class($testDriveForm3) . "_3_{$id}"));
        $testDriveForm3->removeElement('date');

        $testDriveForm4->setAction(
            $this->view->url(
                array(
                    'language' => $this->view->language,
                    'id' => $car['car_id'],
                    'description' => $this->view->carPermalink($car)
                ),
                'showCarTestDrive',
                true
            )
            . $getString
        );


        $testDriveForm4->addElement('hash', 'csrf', array('salt' => get_class($testDriveForm4) . "_4_{$id}"));
        $testDriveForm4->removeElement('date');

		$this->view->testDriveForm = $testDriveForm;
		$this->view->testDriveForm2 = $testDriveForm2;
        $this->view->testDriveForm3 = $testDriveForm3;
        $this->view->testDriveForm4 = $testDriveForm4;


        $employeesModel = new Model_Employees();
        $caretakerLanguages = $employeesModel->getEmployeesLanguages($car['caretaker_id']);

        $this->view->caretakerLanguages = $caretakerLanguages;


        // spec od finansowania
        $employeesModel = new Model_Employees();
        if($this->_request->getPost('is_contact_financing', false)) {
            $financingEmployee = $employeesModel->getById($this->_request->getPost('is_contact_financing'));

        } else {
            $financingEmployee = $employeesModel->getFinancing($car['location_id'],true,array($car['caretaker_id']));
        }



        if($this->view->language == 'pl' && $financingEmployee) {
            $this->view->financingEmployee = $financingEmployee;
            $testDriveForm3->addElement(new Zend_Form_Element_Hidden('is_contact_financing', array('value' => $financingEmployee['id'])));

        } else {
            $this->view->financingEmployee = false;
        }


        if($this->_request->getPost('is_contact_language', false)) {
            $languageEmployee[] = $employeesModel->getById($this->_request->getPost('is_contact_language'));

        } else {
            if(in_array($this->view->language, $caretakerLanguages))
                $languageEmployee = false;
            else
                $languageEmployee = $employeesModel->getRandom($withPhoto=true, $financing=null, $count=1, $ignoreLanguage=false, $exclude=array($car['caretaker_id']));
        }

        if($this->view->language != 'pl' && $languageEmployee) {
            $this->view->languageEmployee = $languageEmployee[0];
            $testDriveForm4->addElement(new Zend_Form_Element_Hidden('is_contact_language', array('value' => $languageEmployee[0]['id'])));

        } else {
            $this->view->languageEmployee = false;
        }

        if($this->_request->getParam('forceShowTestDrive', "0") == "1") {

            echo 'aaa';
            $this->view->showTestDriveForm = true;

        }

        else if($this->_request->getParam('forceShowContactCartaker', "0") == "1") {

            $this->view->showContactCaretakerForm = true;

        }


		if ($this->_request->isPost() && $this->_request->getParam('testDrive', "0") == "1") {
			$checkForm = $testDriveForm;
			if ($this->_request->getPost('is_contact_caretaker', false) == "1") {
				$checkForm = $testDriveForm2;
			}
            else if ($this->_request->getPost('is_contact_financing', false)) {
                $checkForm = $testDriveForm3;
            } else if ($this->_request->getPost('is_contact_language', false)) {
                $checkForm = $testDriveForm4;
            }
			if ($checkForm->isValid($this->_request->getPost())) {
				$data = $checkForm->getValues();
				try {
					$carsTr = new Model_Cars_Transfers();
					$identity = Zend_Auth::getInstance()->getIdentity();
					$userData = null;
					if ($identity !== null) {
						$userData = get_object_vars($identity);
					}
					$carsTr->contactTestDrive($car, $data, $userData);
                    $this->view->showTestDriveForm = false;
					$this->view->messages[] = $this->_helper->translate('MESSAGE_SENT');
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'testDrive', 'additional' => 'test drive / ' . $car['car_id']), Zend_Log::INFO);
				}
				catch (Exception $e) {
                    $this->view->showTestDriveForm = false;
					$this->view->messages[] = $this->_helper->translate('MESSAGE_SEND_ERROR');
					$this->_helper->eventLog->log(array('outcome' => 'fail', 'operation' => 'testDrive', 'additional' => 'test drive / ' . $car['car_id']  . ' ' . $e->getMessage()), Zend_Log::WARN);
				}
			}//isValid
			else {
				if ($this->_request->getPost('is_contact_caretaker', false) == "1") {
					$this->view->showContactCaretakerForm = true;
				}
                else if ($this->_request->getPost('is_contact_financing', false)) {
                    $this->view->showContactFinancingForm = true;
                }
                else if ($this->_request->getPost('is_contact_language', false)) {
                    $this->view->showContactLanguageForm = true;

                }
				else {
					$this->view->showTestDriveForm = true;
				}
			}
		}//isPost

		//offer (send to aa.pl)
		$offerForm = new Form_Car_Offer();
		if (Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == "user") {
			$offerForm->populate(get_object_vars(Zend_Auth::getInstance()->getIdentity()));
			$offerForm->populate(array('price' => (int)$car['price']));
		}
		$offerForm->setAction(
			$this->view->url(
				array(
					'language' => $this->view->language,
					'id' => $car['car_id'],
					'description' => $this->view->carPermalink($car)
				),
				'showCarMakeOffer',
				true
			)
			. $getString
		);
		$this->view->offerForm = $offerForm;

        if($this->_request->getParam('forceShowMakeOffer', "0") == "1")
            $this->view->showOfferForm = true;

		if ($this->_request->isPost() && $this->_request->getParam('makeOffer', "0") == "1") {
			if ($offerForm->isValid($this->_request->getPost())) {
				$data = $offerForm->getValues();
				try {
					$carsTr = new Model_Cars_Transfers();
					$identity = Zend_Auth::getInstance()->getIdentity();
					$userData = null;
					if ($identity !== null) {
						$userData = get_object_vars($identity);
					}
					$carsTr->makeOffer($car, $data, $userData);
					$this->view->messages[] = $this->_helper->translate('OFFER_SENT');
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'makeOffer', 'additional' => 'send offer / ' . $car['car_id'] . ' / ' . $data['price']), Zend_Log::INFO);
				}
				catch (Exception $e) {
					$this->view->messages[] = $this->_helper->translate('OFFER_SEND_ERROR');
					$this->_helper->eventLog->log(array('outcome' => 'fail', 'operation' => 'makeOffer', 'additional' => 'send offer / ' . $car['car_id'] . ' / ' . $data['price'] . ' ' . $e->getMessage()), Zend_Log::WARN);
				}
			}//isValid
			else {
				$this->view->showOfferForm = true;
			}
		}//isPost

		//if (Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == "user") {
			$minAmount = 1000;//max((int)$opt['transfers']['reservation']['min_price'], (int)round($opt['transfers']['reservation']['min_price_factor'] * $car['price']));

			$reservationForm = new Form_Car_Reservation(array('minimumAmount' => $minAmount, 'buyPrice' => $car['price']));
			if(Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role != 'salesman' &&  Zend_Auth::getInstance()->getIdentity()->passwordPassed)
                $reservationUserData = get_object_vars(Zend_Auth::getInstance()->getIdentity());
            else
                $reservationUserData = array();

            $reservationForm->populate($reservationUserData);
			$reservationForm->setAction($this->view->url(array('language' => $this->view->language, 'id' => $car['car_id'], 'description' => $this->view->carPermalink($car)), 'showCarMakeReservation', true). $getString);
			$this->view->reservationForm = $reservationForm;

			if ($this->_request->isPost() && $this->_request->getParam('makeReservation', "0") == "1") {
				$this->_helper->layout->disableLayout();
				$this->_helper->viewRenderer->setNoRender(true);
				if ($reservationForm->isValid($this->_request->getPost())) {


                    $preview = $this->_getParam('preview',false);

					$data = $reservationForm->getValues();

					try {
                            if($preview)
                            {

                                $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
                                $this->_helper->json(array(

                                    'ok' => true,
                                    'preview' => true,
                                    'url' => $opt['general']['domain'].'/cars/contract-preview/language/'.$this->view->language.'/id/'.$car['car_id']
                                ));
                                //$this->_redirect($platnosciUrl);
                                exit;
                            }
                            else
                            {
                                $usersModel = new Model_Users();
                                $reservationUser = $usersModel->getUserByEmail($data['email']);

                                if(!$reservationUser)
                                {
                                    $reservationUserId = $usersModel->addQuick($data);
                                    $reservationUser =  $usersModel->getUser($reservationUserId);
                                }
                                else
                                {
                                    foreach($data as $i => $v)
                                    {
                                        if(isset($reservationUser[$i]) && !empty($data[$i]))
                                        {
                                            $reservationUser[$i] = $data[$i];
                                        }
                                    }
                                }
                                $carsTr = new Model_Cars_Transfers();
                                $userData = $reservationUser;
                                $data['lang'] = ($this->view->language != 'pl') ? 'en' : 'pl';
                                $platnosciData = $carsTr->makeReservation($car, $data, $userData);
                                $this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'makeReservation', 'additional' => 'reservation step1 / ' . $car['car_id'] . ' / ' . $data['amount']), Zend_Log::INFO);

                                $this->_helper->json(array(

                                    'ok' => true,
                                    'url' => $platnosciData['url'],
                                    'preview' => false,
                                    'data' => $platnosciData['data']
                                ));
                                //$this->_redirect($platnosciUrl);
                                exit;
                        }
					}
					catch (Exception $e) {
						//$this->view->messenger->addMessage($this->_helper->translate('RESERVATION_ERROR'));
						$this->_helper->eventLog->log(array('outcome' => 'fail', 'operation' => 'makeReservation', 'additional' => 'reservation step1 / ' . $car['car_id'] . ' / ' . $data['price'] . ' ' . $e->getMessage()), Zend_Log::WARN);
						$this->_helper->json(array(
							'ok' => false
						));
					}
				}//isValid
			else {

                $formHtml =  $this->view->partial('car-form-reservation-modal.phtml',
                    array(
                        'car' => $car,
                        'title' => $this->_helper->translate('RESERVATION_FORM_TITLE'),
                        'form' => $reservationForm,
                        'id' => 'contactReservation',
                        'translate' => $this->view->translate,
                        'ajax' => 1
                    ))   ;
				$this->_helper->json(array(
					'ok' => false,
					'formHtml' => $formHtml
				));
			}
			}//isPost
		//}

		$commentForm = new Form_Car_Comment();
		if (Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == "user") {
			$commentForm->populate(get_object_vars(Zend_Auth::getInstance()->getIdentity()));
		}
		$commentForm->setAction(
			$this->view->url(
				array(
					'language' => $this->view->language,
					'id' => $car['car_id'],
					'description' => $this->view->carPermalink($car)
				),
				'showCarComment',
				true
			)
			. $getString
		);
		$this->view->commentForm = $commentForm;

		if ($this->_request->isPost() && $this->_request->getParam('comment', "0") == "1") {
			if ($commentForm->isValid($this->_request->getPost())) {
				$data = $commentForm->getValues();
				try {
					$carsTr = new Model_Cars_Transfers();
					$identity = Zend_Auth::getInstance()->getIdentity();
					$userData = null;
					if ($identity !== null) {
						$userData = get_object_vars($identity);
					}
					$carsTr->contactComment($car, $data, $userData);
					$this->view->messages[] = $this->_helper->translate('MESSAGE_SENT');
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'comment', 'additional' => 'comment / ' . $car['car_id']), Zend_Log::INFO);
				}
				catch (Exception $e) {
					$this->view->messages[] = $this->_helper->translate('MESSAGE_SEND_ERROR');
					$this->_helper->eventLog->log(array('outcome' => 'fail', 'operation' => 'comment', 'additional' => 'comment / ' . $car['car_id'] . ' ' . $e->getMessage()), Zend_Log::WARN);
				}
			}//isValid
			else {
				$this->view->showCommentForm = true;
			}
		}//isPost

		$leasingCreditInstalment = $cars->getCarMinimumInstalment($car);
		$this->view->leasingCreditInstalment = $leasingCreditInstalment;

		$priceGross = $car['price'];
		if ($car['price_type_key'] == "netto") {
			$priceGross = (1 + $opt['instalments']['VAT']) * $car['price'];
		}
		$this->view->priceGross = $priceGross;

		if ($car['price_type_key'] == "netto") {
			$leasingForm = new Form_Car_LeasingCredit(array('type' => 'leasing'));
			$leasingForm->populate(array('type' => 'leasing', 'carId' => $id));
			$leasingForm->setAttrib('id', 'leasing-form');
			$leasingForm->setAttrib('onsubmit', 'return calculate(this);');
			$leasingForm->contribution->setValue(round(0.2 * $car['price']));
			$contribs = $cars->getInstalmentContributions('leasing');
			$leasingForm->removeElement('contribution');

			$priceCurrencyRecalculate = round($car['price']);
			$priceCurrencyRecalculate = Zend_Filter::filterStatic($this->view->price($priceCurrencyRecalculate, $this->view->language_row), 'Digits'); //recalculate for other currencies

			foreach ($contribs as $val) {
				$leasingForm->getElement('contribution_select')->addMultiOption(
					$val,
					($val * 100) . '% = ' . $this->view->price(round($val * $car['price']), $this->view->language_row)
				);
				$leasingForm->getElement('price')->setValue((int)$car['price']);
			}
			$leasingForm->getElement('contribution_select')->setName('contribution');

			if ($leasingCreditInstalment['type'] == 'leasing') {
				$leasingForm->populate($leasingCreditInstalment['form_params']);
			}

			$this->view->leasingForm = $leasingForm;
		}

		$creditForm = new Form_Car_LeasingCredit(array('type' => 'credit'));
		$creditForm->populate(array('type' => 'credit', 'carId' => $id, 'price' => (int)$car['price']));
		$creditForm->removeElement('contribution_select');

		$priceGrossCurrencyRecalculate = round($priceGross);
		$priceGrossCurrencyRecalculate = Zend_Filter::filterStatic($this->view->price($priceGross, $this->view->language_row), 'Digits'); //recalculate for other currencies

		$creditForm->contribution->setValue(round(0.2 * $priceGrossCurrencyRecalculate));
		$creditForm->setAttrib('id', 'credit-form');
		$creditForm->setAttrib('onsubmit', 'return calculate(this);');

		if ($leasingCreditInstalment['type'] == 'credit') {
			$creditForm->populate($leasingCreditInstalment['form_params']);
		}

		$this->view->creditForm = $creditForm;

		$this->view->car = $car;
		$this->view->nextPrev = $nextPrev;
		$this->view->iterator = $iterator;
		$this->view->hash = $searchHash;
		$this->view->searchParameters = $searchParameters;

		if ($searchParameters && isset($searchParameters['rental']) && $searchParameters['rental'] == 1) {
			$this->view->isRental = true;
		}

		if ($searchParameters) {
			$results = $cars->search($searchParameters, true);
		} else {
			$results = $cars->search(array(), true);
		}

		$exclusiveArray = array("EXCLUSIVE");
		if (isset($searchParameters['exclusive']) && $searchParameters['exclusive'] == "0") {
			$exclusiveArray = array("NONEXISTENT_CONSTANT_VALUE_THAT_WILL_MAKE_THE_RESULTS_EMPTY");
		}

		$types = null;
		if (isset($searchParameters['exclusive']) && $searchParameters['exclusive'] == 1) {
			$types = array(1);
		}
		else if (isset($searchParameters['types']) && !empty($searchParameters['types'])) {
			$types = explode("_", $searchParameters['types']);
		}
		$this->view->types = $types;

		$this->view->makesWithCounts = $cars->getCarMakesWithCountsByNamesWithFilters($results, $types, array("NULL", "PREMIUM"));
		$this->view->exclusiveMakesWithCounts = $cars->getCarMakesWithCountsByNamesWithFilters($results, $types, $exclusiveArray);

        $this->view->otherMakesLayout = "bottom";

        if ($searchParameters && isset($searchParameters['categories']) && in_array('over_3.5t',$searchParameters['categories'])) {
            $this->view->otherMakesLayout = "top";
        }




		$employees = new Model_Employees();
		$this->view->financingEmployees = $employees->getFinancing($car['location_id']);

		$this->view->showReservationForm = $this->_request->getParam('show_reservation_form', false);

		/* Zapisywanie auta do ulubionych */

        $url = null; //helper will determine correct url by default
        if (!$this->_request->getParam('hash')) {
            $url = $this->_request->getServer('REQUEST_URI');
            $url .= '?hash=' . $searchHash;
        }


        $users = new Model_Users();
		$filterInt = new Zend_Filter_Int();
		$favourite = $filterInt->filter($this->getRequest()->getParam('favourite'));
		if ($favourite && $this->view->identity && $this->view->identity->id) {
			$users->saveFavourite($favourite, $this->view->identity->id);
			$this->view->messages[] = '<span class="favourites">'.$this->view->translate->_('CAR_ADDED_TO_FAVOURITES').'</span>';

            $url = $this->_request->getServer('HTTP_REFERER');

		}
		elseif ($favourite) {
			$favouriteNamespace = new Zend_Session_Namespace('favourite_cars');
			if (!isset($favouriteNamespace->cars) || !is_array($favouriteNamespace->cars)) {
				$favouriteNamespace->cars = array();
			}
			if (!in_array($favourite, $favouriteNamespace->cars)) {
				$favouriteNamespace->cars[] = $favourite;
				$this->view->messages[] = '<span class="favourites">'.$this->view->translate->_('CAR_ADDED_TO_FAVOURITES').'</span>';
			}
			else {
				$this->view->messages[] = '<span class="favourites">'.$this->view->translate->_('CAR_ALREADY_IN_FAVOURITES').'</span>';
			}

            $url = $this->_request->getServer('HTTP_REFERER');

        }

        $this->_helper->setRedirBack('favourite-list-delete',$url);

		/* k */

		/* Drukowanie pdfa */
		$doPrint = $this->_request->getParam('print', null) == "true";
		if ($doPrint) {
			$this->_helper->layout->disableLayout();

			$pdf = new My_Tcpdf();
			$html = $this->view->render("car_details_pdf.phtml");
			//echo $html;exit;
			$pdf->generateCarDetails($html);

			$this->_helper->viewRenderer->setNoRender(true);
		}
		/* k */

		//send offer (send to arbitrary email)

        $isSalesman = (Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == 'salesman');
		$sendOfferForm = new Form_Car_SendOffer(array('isSalesman' => $isSalesman) );
		$sendOfferForm->setAction($this->view->url(array('language' => $this->view->language, 'id' => $car['car_id'], 'description' => $this->view->carPermalink($car)), 'showCarSendOffer', true). $getString);
		if(Zend_Auth::getInstance()->hasIdentity())
        {

            if($isSalesman) {
                $employees = new Model_Employees();
                $employee = $employees->getBySrId(Zend_Auth::getInstance()->getIdentity()->sr_id);
                $email = $employee['email'];

            } else {
                $email = Zend_Auth::getInstance()->getIdentity()->email;
            }

            $sendOfferForm->populate(array('sender_email' => $email));
        }
        $this->view->sendOfferForm = $sendOfferForm;

        if($this->_request->getParam('forceShowCarSendOffer', "0") == "1")
            $this->view->showSendOfferForm = true;


		if ($this->_request->isPost() && $this->_request->getParam('sendOffer', "0") == "1") {
			if ($sendOfferForm->isValid($this->_request->getPost())) {
				$data = $sendOfferForm->getValues();
				try {

                    $data['auction'] = $this->view->auction;
                    $data['getString'] = $this->view->getString;
                    $carsTr = new Model_Cars_Transfers();
                    $carsTr->contactSendOffer($car, $data);



                    if($isSalesman) { // dodajemy do mailingu +- 10000 i te same nadwozie

                        $employees = new Model_Employees();
                        $employee = $employees->getBySrId(Zend_Auth::getInstance()->getIdentity()->sr_id);

                        $saveData = array(
                            'email' => $data['recipient_email'],
                            'added_by_sr_id' => $employee['sr_id'],
                            'title' => $data['comment'],
                            'newsletter' => 1
                        );

                        $searchData = array(
                            'price_min' => $car['price'] - 10000,
                            'price_max' => $car['price']  + 10000,
                            'categories' => array($car['vc_key'])
                        );

                        $users = new Model_Users();
                        $result = $users->saveSearch($saveData, $searchData,  null);

                        if ($result) {
                            $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
                            $domain = Zend_Registry::get('siteDomain');
                            $searchId = $result;
                            $searches = $users->getSearchesWithCleanup($date=null, $searchId);
                            $cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
                            $cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);

                            foreach($searches as $searchUser) {
                                $msg = false;
                                if (count($searchUser['cars'])) {


                                    $this->view->css = $cssContent;
                                    $this->view->user = $searchUser;
                                    $this->view->employee = $employee;
                                    $html = $this->view->render('newsletter_content.phtml');

                                    $emogrifier = new My_Emogrifier($html);
                                    $html = $emogrifier->emogrify();

                                    $mail = new Zend_Mail($charset="UTF-8");
                                    $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

                                    $mail->setFrom(
                                        $opt['resources']['mail']['defaultFrom']['email'],
                                        $employee['first_name'] . ' ' . $employee['last_name']
                                    );

                                    $mail->setReplyTo(
                                        $employee['email'],
                                        $employee['first_name'] . ' ' . $employee['last_name']
                                    );

                                    $mail->addCc( $employee['email'],
                                        $employee['first_name'] . ' ' . $employee['last_name']);

                                    $text = $this->view->translate->_('NEWSLETTER_GREETING') . "\n".
                                        $this->view->translate->_('NEWSLETTER_HEADING') .':'.  $searchUser['search_name']
                                        . "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');

                                    $firstCar = $searchUser['cars'][0];

                                    $subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
                                        $firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);

                                    $mail->addTo($searchUser['email'])
                                        ->setBodyText($text)
                                        ->setBodyHtml($html)
                                        ->setSubject($subject);

                                    $mail->send();
                                }
                            }
                            $this->_helper->eventLog->log(array('outcome' => 'ok', 'operation' => 'send-newsletter'), Zend_Log::INFO);

                        }
                    }

				}
				catch (Exception $e) {
					$this->view->messenger->addMessage($this->_helper->translate('MESSAGE_SEND_ERROR'));
					$this->_helper->eventLog->log(array('outcome' => 'fail', 'operation' => 'sendOffer', 'additional' => 'send offer / ' . $car['car_id'] . ' / ' . print_r($data, true) . ' ' . $e->getMessage()), Zend_Log::WARN);
				}
				$this->_redirect($this->view->url(array('language' => $this->view->language, 'id' => $car['car_id'], 'description' => $this->view->carPermalink($car)), 'showCarSendOffer', true).$getString);
			}//isValid
			else {
				$this->view->showSendOfferForm = true;
			}
		}//isPost



        $this->view->favouritesIds =  $users->getFavouritesIds();
        $this->view->isFavourite = in_array($car['car_id'], $users->getFavouritesIds());
	}

	public function showBySrAction() {
		$this->_helper->layout->disableLayout();
		$this->_helper->viewRenderer->setNoRender(true);

		$id = (int)$this->_request->getParam('id');
		if (empty($id)) {
			$this->view->messenger->addMessage($this->_helper->translate('CAR_NOT_FOUND'));
			$this->_redirect('');
		}
		else {
			$cars = new Model_Cars_Cars();
			$car = $cars->getCarBySrCarId($id);
            if($car) {

                $url = $this->view->url(array('language' => $this->view->language, 'id' => $car['car_id']), 'showCar', true);
                $this->_redirect($url);
            } else {

                $this->view->messenger->addMessage($this->_helper->translate('CAR_NOT_FOUND'));
                $this->_redirect('');
            }
		}
	}

    public function showByOfferIdAction() {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        $id = (int)$this->_request->getParam('id');
        if (empty($id)) {
            $this->view->messenger->addMessage($this->_helper->translate('CAR_NOT_FOUND'));
            $this->_redirect('');
        }
        else {
            $cars = new Model_Cars_Cars();
            $car = $cars->getCarByOfferCarId($id);
            if($car) {

                $url = $this->view->url(array('language' => $this->view->language, 'id' => $car['car_id']), 'showCar', true);
                $this->_redirect($url);
            } else {

                $this->view->messenger->addMessage($this->_helper->translate('CAR_NOT_FOUND'));
                $this->_redirect('');
            }
        }
    }

    public function contractPreviewAction() {
		$this->_helper->layout->disableLayout();
		$this->_helper->viewRenderer->setNoRender(true);

		$id = (int)$this->_request->getParam('id');
		if (empty($id)) {
			$this->view->messenger->addMessage($this->_helper->translate('CAR_NOT_FOUND'));
			$this->_redirect('');
		}
		else {
            $view = Zend_Layout::getMvcInstance()->getView();

			$cars = new Model_Cars_Cars();
			$car = $cars->getCarFull($id);
            $view->car = $car;

            $companies  = new Model_Companies();
            $view->account = $companies->getCompany($car['company_id']);
            $view->lang = $this->view->language;
            $data  = $this->_getAllParams();
            $data['contract_date'] = date('Y-m-d');
            $data['contract_salesman'] = !empty($data['agreed_with_sr_id']) ? $data['agreed_with_sr_id'] : $car['caretaker_sr_id'];

            $pdf = new My_Tcpdf();
			if (is_array($data)) {
                if (array_key_exists('contract_date', $data)) {
                    $view->contract_date = $data['contract_date'];
                    $date_stripped = str_replace('-', '', $view->contract_date);
                    $contract_number = $date_stripped . '/' . $car['car_id'] . '/';
                }
                if (array_key_exists('contract_salesman', $data)) {
                    $employees = new Model_Employees();
                    $user = $employees->getBySrId($data['contract_salesman']);

                    $view->user = $user;
                }
                $this->view->price = $this->view->price($data['buy_price'], $this->view->language_row);

                $view->reservation_amount = $this->view->price($data['amount'], $this->view->language_row);


                $view->client = $data;

            }

            $lang = ($this->view->language != 'pl') ? '_en' : '';
            $contractContents = $view->render('cars/reservation_www'.$lang.'.phtml');



            $file_name = 'pus.pdf';


            $pdf->generateReservationFromWww($contractContents, $file_name, 1, null);


		}
	}

}