<?php

class Model_ImportExport_Import extends Model_Base {
	
	const CHECKSUM_SALT = 'aPO4$Mvf09uHOF)MsKL';
	const ENCRYPTION_KEY = 'a8F4%hGn*';
	
	protected $_fuelTypes = array(
		//with gas
		1 => array(
			1 => array(
				'GASOLINE' => 3,
				'DIESEL' => 8,
				'HYBRID' => 5,
				'UNKNOWN' => 6,
			),
			2 => array(
				'GASOLINE' => 12,
				'DIESEL' => 17,
				'HYBRID' => 14,
				'UNKNOWN' => 15
			),
			3 => array(
				'GASOLINE' => 21,
				'DIESEL' => 26,
				'HYBRDID' => 23,
				'UNKNOWN' => 24
			),
			4 => array(
				'GASOLINE' => 30,
				'DIESEL' => 35,
				'HYBRID' => 32,
				'UNKNOWN' => 33,
			),
		),
		//no gas
		2 => array(
			1 => array(
				'GASOLINE' => 1,
				'DIESEL' => 8,
				'HYBRID' => 5,
				'UNKNOWN' => 6,
                'ELECTRIC' => 7,
			),
			2 => array(
				'GASOLINE' => 10,
				'DIESEL' => 8,
				'HYBRID' => 14,
				'UNKNOWN' => 6,
                'ELECTRIC' => 16,
			),
			3 => array(
				'GASOLINE' => 19,
				'DIESEL' => 8,
				'HYBRDID' => 23,
				'UNKNOWN' => 6,
                'ELECTRIC' => 25,
			),
			4 => array(
				'GASOLINE' => 28,
				'DIESEL' => 35,
				'HYBRID' => 32,
				'UNKNOWN' => 33,
                'ELECTRIC' => 34,
			)
			
		)
	);
	
	protected $_priceTypes = array(
		'NET' => 'netto',
		'GROSS' => 'brutto'
	);
	
	protected $_vehicleCategories = array(
		1 => 6,
		2 => 24,
		3 => 37,
		4 => 39
	);
	
	protected function _calculateChecksum($type, $data) {
		switch ($type) {
			case 'addCarQuick':
				return sha1(self::CHECKSUM_SALT . $data['c_vin'] . $data['c_production_year'] . $data['c_company_id'] . self::CHECKSUM_SALT . $data['c_make_id'] . $data['c_model_id']);
				break;
				
			case 'carPhotos':
				return sha1(self::CHECKSUM_SALT . serialize($data) . (count($data) - 13));
				break;
			
			case 'carViews':
				return sha1(self::CHECKSUM_SALT . $data['c_id'] . $data['timestamp']);
				break;
				
			case 'changeCarPatronBulk':
				return sha1(self::CHECKSUM_SALT . serialize($data));
				break;
				
			case 'cleanCache':
				return sha1(self::CHECKSUM_SALT . $data);
				break;
				
			case 'client':
				return sha1(self::CHECKSUM_SALT . $data['c_id'] . $data['c_email'] . self::CHECKSUM_SALT . $data['c_address']);
				break;
				
			case 'clients':
				return sha1(self::CHECKSUM_SALT . $data['added_by_sr_id']);
				break;
				
			case 'deleteCar':
				return sha1(self::CHECKSUM_SALT . $data['c_vin'] . $data['c_production_year'] . $data['c_company_id'] . self::CHECKSUM_SALT . $data['c_make_id'] . $data['c_model_id']);
				break;
			
			case 'employee':
				return sha1(self::CHECKSUM_SALT . $data['u_user_id'] . $data['photos'] . $data['u_www_visible']);
				break;
				
			case 'fuelConsumption':
				return sha1(self::CHECKSUM_SALT . serialize($data));
				break;
				
			case 'generic':
				return sha1(self::CHECKSUM_SALT . serialize($data));
				break;
				
			case 'reservation':
				return sha1(self::CHECKSUM_SALT . $data['id'] . $data['sr_car_id'] . self::CHECKSUM_SALT . $data['user_id']);
				break;
				
			case 'reservationDeleted':
				return sha1(self::CHECKSUM_SALT . $data['cr_www_id'] . $data['cr_deleted_by'] . self::CHECKSUM_SALT . $data['cr_date_deleted']);
				break;
				
			case 'reservationExtended':
				return sha1(self::CHECKSUM_SALT . $data['cr_www_id'] . $data['date'] . self::CHECKSUM_SALT . $data['user_id']);
				break;
			
			case 'translationRequest':
				return sha1(date("Y-m-d H:i", $data['timestamp']) . self::CHECKSUM_SALT . date(":s", $data['timestamp']) . (abs($data['user_id'])+2) * 7);
				break;
			
			case 'translations':
				return sha1($data['data'] . self::CHECKSUM_SALT . $data['user_id'] . self::CHECKSUM_SALT);
				break;
			
			default:
				throw new Exception("Unknown data type in " . __METHOD__ . ", line " . (__LINE__ - 1));
				break;
		}
	}
	
	protected function _colourId($value) {
		$cars = new Model_Cars_Cars();
		return $cars->getColourId($value);
	}
	
	protected function _fuelType($fuelType, $hasAutogasEnum, $carType, $returnIfNotFound=null) {
		$hasAutogasEnum = (int)$hasAutogasEnum;
		$carType = (int)$carType;
		
		if (!isset($this->_fuelTypes[$hasAutogasEnum])) {
			if ($returnIfNotFound !== null) {
				//allow returning default value (0?) if proper index not found
				return $returnIfNotFound;
			}
			throw new Exception("Index not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		if (!isset($this->_fuelTypes[$hasAutogasEnum][$carType])) {
			if ($returnIfNotFound !== null) {
				return $returnIfNotFound;
			}
			throw new Exception("Index not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		if (!isset($this->_fuelTypes[$hasAutogasEnum][$carType][$fuelType])) {
			if ($returnIfNotFound !== null) {
				return $returnIfNotFound;
			}
			throw new Exception("Index not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		
		return $this->_fuelTypes[$hasAutogasEnum][$carType][$fuelType];
	}
	
	protected function _makeModelData($car) {
		$cars = new Model_Cars_Cars();
                
                $srModelId = (int)$car['c_model_id'];
                
                $model = $cars->getCarModelBySrModelId($srModelId);
                
                if (!empty($model)) {
			return array(
				'make_id' => $model['make_id'],
				'model_id' => $model['id']
			);
		}
                
		$modelOmId = (int)$car['cm_model_om_id'];
		$model = $cars->getCarModelByOmId($modelOmId);
		
		if (!empty($model)) {
			return array(
				'make_id' => $model['make_id'],
				'model_id' => $model['id']
			);
		}
		
		$makeId = null;
		$modelId = null;
		
		$make = $cars->getCarMakeByOmId($car['cm_om_id']);
		if (!empty($make)) {
			$makeId = $make['id'];
		}
		else {
			$make = $cars->getCarMakeByString($car['cm_make_name'], $car['c_type_id']);
			if (!empty($make)) {
				$makeId = $make['id'];
			}
			else {
				$makeId = $cars->addCarMake(
					$car['c_type_id'],
					(empty($car['cm_om_id'])) ? null : $car['cm_om_id'],
					$car['cm_make_name']
				);
			}
		}
		
		if (empty($makeId)) {
			throw new Exception("makeId empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		
		$model = $cars->getCarModelByString($car['cm_model_name'], $makeId);
		if (!empty($model)) {
			$modelId = $model['id'];
		}
		else {
			$modelId = $cars->addCarModel(
				$makeId,
				(empty($car['cm_model_om_id'])) ? null : $car['cm_model_om_id'],
				$car['cm_model_name']
			);
		}
		
		return array(
			'make_id' => $makeId,
			'model_id' => $modelId,
			'version_id' => 0
		);
		
	}
	
	protected function _mileage($amount, $unit) {
		if ($unit == 'MIL') {
			return (int)round((int)$amount * 1.6);
		}
		else {
			return (int)$amount;
		}
	}
	
	protected function _priceType($key, $direction) {
		$srcArray = $this->_priceTypes;
		if ($direction == 'wwwToSr') {
			$srcArray = array_flip($this->_priceTypes);
		}
		elseif ($direction != 'srToWww') {
			throw new Exception("Invalid direction in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		
		if (array_key_exists($key, $srcArray)) {
			return $srcArray[$key];
		}
		else {
			throw new Exception("No key found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
	}
	
	protected function _vehicleCategory($typeId) {
		return $this->_vehicleCategories[(int)$typeId];
	}
	
	protected function _isValidCarFull(&$data) {
		if (strlen($data['c_vin']) != 17) return "Nieprawidłowa długość VINu: " . strlen($data['vin']) . "; vin:" . $data['vin'] . "; id:" . $data['id'];
		if (empty($data['c_id']) || !is_numeric($data['c_id'])) return "Brak pola lub puste c_id";
		if (empty($data['c_type_id']) || !is_numeric($data['c_type_id'])) return "Brak pola lub puste c_type_id";
		if (empty($data['c_make_id']) || !is_numeric($data['c_make_id'])) return "Brak pola lub puste c_make_id";
		if (empty($data['cm_make_name'])) return "Brak pola lub puste cm_make_name";
		//if (!array_key_exists('cm_om_id', $data) || !is_numeric($data['cm_om_id'])) return "Brak pola cm_om_id";
		if (empty($data['c_model_id']) || !is_numeric($data['c_model_id'])) return "Brak pola lub puste c_model_id";
		if (!array_key_exists('cm_model_name', $data)) return "Brak pola cm_model_name";
		if (empty($data['c_engine_id']) || !is_numeric($data['c_engine_id'])) return "Brak pola lub puste ";
		if (!isset($data['ce_displacement']) || !is_numeric($data['ce_displacement'])) return "Brak pola ce_displacement";
		if (empty($data['ce_fuel_type'])) return "Brak pola lub puste ce_fuel_type";
		if (!array_key_exists('ce_horsepower', $data)) return "Brak pola ce_horsepower";
		if (empty($data['c_has_autogas'])) return "Brak pola lub puste c_has_autogas";
		if (empty($data['c_production_year']) || !is_numeric($data['c_production_year'])) return "Brak pola lub puste c_production_year";
		if (empty($data['c_mileage']) && !is_numeric($data['c_mileage'])) return "Brak pola lub puste c_mileage: " . $data['c_mileage'];
		if (empty($data['c_mileage_unit'])) return "Brak pola lub puste c_mileage_unit";
		if (!array_key_exists('c_registration_plate', $data)) return "Brak pola c_registration_plate";
		if (empty($data['c_color'])) return "Brak pola lub puste c_color";
		if (empty($data['c_location_id']) || !is_numeric($data['c_location_id'])) return "Brak pola lub puste c_location_id";
		if (empty($data['c_company_id']) || !is_numeric($data['c_company_id'])) return "Brak pola lub puste c_company_id";
		if (empty($data['c_patron_id']) || !is_numeric($data['c_patron_id'])) return "Brak pola lub puste c_patron_id";
		if (empty($data['c_current_price']) && !is_numeric($data['c_current_price'])) return "Brak pola lub puste c_current_price";
		if (empty($data['c_price_type'])) return "Brak pola lub puste c_price_type";
		if (empty($data['c_status'])) return "Brak pola lub puste c_status";
		if (empty($data['c_ownership_type']) || !in_array($data['c_ownership_type'], array("CLIENT", "OWN"))) return "Brak pola lub puste lub nieprawidłowa wartość c_ownership_type";
		if (empty($data['c_is_archived'])) return "Brak pola lub puste c_is_archived";
		if (!(int)$data['c_vehicle_category_id'] > 0) return "Brak pola lub puste c_vehicle_category_id";
		if (!array_key_exists('c_seat_count', $data) || (int)$data['c_seat_count'] <= 0) return "Brak pola c_seat_count";
		if (!array_key_exists('c_title', $data)) return "Brak pola c_title";
		if (!array_key_exists('c_description', $data)) return "Brak pola c_description";
		if (!array_key_exists('extras', $data)) return "Brak pola extras";
		if (!array_key_exists('features', $data)) return "Brak pola features";
		if (!array_key_exists('fuel_consumption', $data)) return "Brak pola fuel_consumption";
		//if (!array_key_exists('c_last_service_year', $data)) return "Brak pola lub puste c_last_service_year";
		//if (!array_key_exists('c_last_service_month', $data)) return "Brak pola lub puste c_last_service_month";
		//powyzsze zluzowane, zeby przechodzily samochody zaciagniete ze starej strony obrazkowej, ktore nie mialy tych pol
		//powyzsze dodatkowo niewazne z powodu usuniecie obligatoryjnosci w sys2 - support #810
		
		return true;
	}
	
	protected function _isValidCarQuick(&$data) {
		//if (strlen($data['c_vin']) != 17) return "Nieprawidłowa długość VINu: " . strlen($data['vin']) . "; vin:" . $data['vin'] . "; id:" . $data['id'];
		if (empty($data['c_id']) || !is_numeric($data['c_id'])) return "Brak pola lub puste c_id";
		if (empty($data['c_type_id']) || !is_numeric($data['c_type_id'])) return "Brak pola lub puste c_type_id";
		if (empty($data['c_make_id']) || !is_numeric($data['c_make_id'])) return "Brak pola lub puste c_make_id";
		if (empty($data['cm_make_name'])) return "Brak pola lub puste cm_make_name";
		//if (!isset($data['cm_om_id']) || !is_numeric($data['cm_om_id'])) return "Brak pola cm_om_id";
		if (empty($data['c_model_id']) || !is_numeric($data['c_model_id'])) return "Brak pola lub puste c_model_id";
		if (!array_key_exists('cm_model_name', $data)) return "Brak pola cm_model_name";
		//if ($data['c_grace'] != "1" && (empty($data['c_engine_id']) || !is_numeric($data['c_engine_id']))) return "Brak pola lub puste ";
		//if ($data['c_grace'] != "1" && (!isset($data['ce_displacement']) || !is_numeric($data['ce_displacement']))) return "Brak pola ce_displacement";
		//if ($data['c_grace'] != "1" && (empty($data['ce_fuel_type']))) return "Brak pola lub puste ce_fuel_type";
		//if (!array_key_exists('ce_horsepower', $data)) return "Brak pola ce_horsepower";
		if (empty($data['c_has_autogas'])) return "Brak pola lub puste c_has_autogas";
		if ($data['c_grace'] != "1" && (empty($data['c_production_year']) || !is_numeric($data['c_production_year']))) return "Brak pola lub puste c_production_year";
		if (empty($data['c_mileage_unit'])) return "Brak pola lub puste c_mileage_unit";
		if (!array_key_exists('c_registration_plate', $data)) return "Brak pola c_registration_plate";
		//if (empty($data['c_color'])) return "Brak pola lub puste c_color";
		if (empty($data['c_location_id']) || !is_numeric($data['c_location_id'])) return "Brak pola lub puste c_location_id";
		if (empty($data['c_company_id']) || !is_numeric($data['c_company_id'])) return "Brak pola lub puste c_company_id";
		if (empty($data['c_patron_id']) || !is_numeric($data['c_patron_id'])) return "Brak pola lub puste c_patron_id";
		if ($data['c_grace'] != "1" && (empty($data['c_current_price']) && !is_numeric($data['c_current_price']))) return "Brak pola lub puste c_current_price";
		if (empty($data['c_price_type'])) return "Brak pola lub puste c_price_type";
		if (empty($data['c_status'])) return "Brak pola lub puste c_status";
		
		return true;
	}
	
	protected function _isValidCarPhotos($data) {
		if (empty($data['car_id'])) return "Brak lub puste car_id";
		foreach ($data['photos'] as $key => $value) {
			if (empty($value['cp_id'])) return "Brak lub puste cp_id";
			if (empty($value['cp_filename_base'])) return "Brak lub puste cp_filename_base";
			if (empty($value['cp_filename_extension'])) return "Brak lub puste cp_filename_extension";
			if (empty($value['cp_ord'])) return "Brak lub puste cp_ord";
		}
				
		return true;
	}
    
    protected function _isValidCarVipautoPhotos($data) {
		if (empty($data['car_id'])) return "Brak lub puste car_id";
		foreach ($data['photos'] as $key => $value) {
			if (empty($value['cvp_id'])) return "Brak lub puste cvp_id";
			if (empty($value['cvp_filename_base'])) return "Brak lub puste cvp_filename_base";
			if (empty($value['cvp_filename_extension'])) return "Brak lub puste cvp_filename_extension";
			if (empty($value['cvp_ord'])) return "Brak lub puste cvp_ord";
		}
				
		return true;
	}
	
	protected function _isValidClient($data) {
		if (empty($data['c_id'])) return "Brak sr-id klienta"; //not really needed, but just in case
		if (empty($data['www_client_id'])) return "Brak id klienta";
		if (empty($data['c_address'])) return "Pusty adres";
		if (empty($data['c_zip_code'])) return "Pusty kod pocztowy";
		if (empty($data['c_city'])) return "Puste miasto";
		
		return true;
	}
	
	protected function _isValidEmployee($data) {
		if (empty($data['u_user_id'])) return "Brak id pracownika";
		if ((empty($data['u_www_photo_filename_base']) || empty($data['u_www_photo_filename_extension'])) && count($data['photos']) > 0) return "Pusta nazwa pliku, gdy dane pliku przesłane";
		if (empty($data['location_id'])) return "Brak lub puste location_id";
		
		return true;
	}
	
	protected function _isValidMakeEdit($data) {
		if (empty($data['cm_type_id'])) return "Brak cm_type_id";
		if (empty($data['cm_make_name'])) return "Brak cm_make_name";
		
		return true;
	}
	
	protected function _isValidModelEdit($data) {
		if (empty($data['cm_type_id'])) return "Brak cm_type_id";
		if (empty($data['cm_make_name'])) return "Brak cm_make_name";
		if (empty($data['cm_make_name'])) return "Brak cm_make_name";
		if (empty($data['cm_make_id'])) return "Brak cm_make_id";
		if (empty($data['cm_model_name'])) return "Brak cm_model_name";
		
		
		return true;
	}
	
	protected function _isValidReservationDeleted($data) {
		if (empty($data['cr_www_id'])) return "Brak cr_www_id";
		if (empty($data['cr_deleted_by'])) return "Brak cr_deleted_by";
		if (empty($data['cr_date_deleted'])) return "Brak cr_date_deleted";
		
		return true;
	}
	
	protected function _isValidReservationExtended($data) {
		if (empty($data['cr_www_id'])) return "Brak cr_www_id";
		if (empty($data['date'])) return "Brak date";
		if (empty($data['user_id'])) return "Brak user_id";
		if (empty($data['date_extended'])) return "Brak date_extended";
		
		return true;
	}
	
	protected function _encrypt(&$data, $iv=null) {
		//by reference!
		
		$td = mcrypt_module_open(MCRYPT_DES, '', MCRYPT_MODE_ECB, '');
		$key = substr(self::ENCRYPTION_KEY, 0, mcrypt_enc_get_key_size($td));
		if ($iv === null) {
			$iv_size = mcrypt_enc_get_iv_size($td);
			$iv = mcrypt_create_iv($iv_size, MCRYPT_RAND);
		}
		$iv = str_replace("/", "]", $iv); //some data exploded using "/" - avoid this char
		
		if (mcrypt_generic_init($td, $key, $iv) == -1) {
			throw new Exception("Inicjalizacja enkrypcji nie powiodla sie");
		}
		$data = mcrypt_generic($td, $data);
		
		return $iv;
	}
	
	protected function _decrypt($data, $iv) {
		$td = mcrypt_module_open(MCRYPT_DES, '', MCRYPT_MODE_ECB, '');
		$key = substr(self::ENCRYPTION_KEY, 0, mcrypt_enc_get_key_size($td));
		
		if (mcrypt_generic_init($td, $key, $iv) == -1) {
			throw new Exception("Inicjalizacja dekrypcji nie powiodla sie");
		}
		
		$data = mdecrypt_generic($td, $data);
		
		return $data;
	}
	
	protected function _receiveData($data, $iv) {
		$data = $this->_decrypt(My_Utils::urlSafeB64Decode($data), My_Utils::urlSafeB64Decode($iv));
		
		return unserialize($data);
	}
	
	protected function _sendData($action, $data, $checksum, $returnBool=true) {
		$data = serialize($data);
		
		$iv = $this->_encrypt($data);
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		
		$url = $opt['synchronization']['sr']['domain'];
		$url .= "/" . $action;
		
		$postData = array(
			'checksum' => $checksum,
			'iv' => My_Utils::urlSafeB64Encode($iv),
			'data' => My_Utils::urlSafeB64Encode($data)
		);
		
		$ch = curl_init($url);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);

        $result = curl_exec($ch); 
		curl_close($ch);
		
		if ($returnBool) {
			if ($result == "OK") return true;
			else return $result;
		}
		else {
			return $result;
		}
	}
	
	public function processCarEdit($car) {
		try {
			$data = array(
				'is_archived' => ($car['c_is_archived'] == 2) ? 0 : 1,
				'sr_car_id' => (int)$car['c_id'],
				'car_type_id' => (int)$car['c_type_id'],
				'price' => (float)$car['c_current_price'],
                'vipauto_price' => $car['c_vipauto_current_price'],
				'promotion_price' => empty($car['c_promotion_price']) ? new Zend_Db_Expr('NULL') : (float)$car['c_promotion_price'],
				'price_currency' => 'PLN',
				'price_type_key' => $this->_priceType($car['c_price_type'], 'srToWww'),
				'leasing' => 'n',
				'gearbox_type_id' => (int)$car['c_gearbox_type_id'] > 0 ? (int)$car['c_gearbox_type_id'] : new Zend_Db_Expr('NULL'),
				'build_year' => (int)$car['c_production_year'],
				'first_registration_year' => (($yearSecondary = (int)$car['c_production_year_secondary']) > 0) ? $yearSecondary : new Zend_Db_Expr('NULL'),
				'odometer' => (int)$car['c_mileage'],
				'odometer_unit' => $car['c_mileage_unit'],
				'power' => (int)$car['ce_horsepower'],
				'power_unit' => 'PS',
				'fuel_type_id' => $this->_fuelType($car['ce_fuel_type'], $car['c_has_autogas'], $car['c_type_id'], $returnIfNotFound=0),
				'colour_key' => (($cId = $this->_colourId($car['c_color'])) != null) ? $cId : new Zend_Db_Expr('NULL'),
				'colour' => ((int)$cId != 0) ? new Zend_Db_Expr('NULL') : $car['c_color'],
				'colour_metallic' => ((int)$car['c_color_is_metallic'] == 1) ? "y" : "n",
				'cubic_capacity' => (int)($car['ce_displacement'] * 1000),
				'damaged' => 'n',
				'vehicle_category_id' => $this->_vehicleCategory($car['c_type_id']),
				'vin' => $car['c_vin'],
				'registration_plate' => $car['c_registration_plate'],
				'is_reserved' => (int)$car['c_is_reserved'] == 1 ? 1 : 0,
				'is_reserved_hidden' => (int)$car['is_reserved_hidden'] ? 1 : 0,
				'is_sold_hidden' => (int)$car['is_sold_hidden'] ? 1 : 0,
				'status' => $car['c_status'],
				'ownership_type' => $car['c_ownership_type'],
				'caretaker_sr_id' => (int)$car['c_patron_id'],
				'company_id' => (int)$car['c_company_id'],
				'location_id' => (int)$car['c_location_id'],
				'position' => !((int)$car['c_position_id'] > 0) ? str_replace(array("myjnia", "w przygotowaniu", "warsztat", "OS: Archive", "Zg", "-Zgłoszony d"), array("", "", "", ""), $car['c_position']) : $car['c_position'],
				'position_id' => !((int)$car['c_position_id'] > 0) ? new Zend_Db_Expr('NULL') : (int)$car['c_position_id'],
				'position_status_id' => !((int)$car['c_position_status_id'] > 0) ? new Zend_Db_Expr('NULL') : (int)$car['c_position_status_id'],
				'position_for_search' => $car['c_position_for_search'],
				'alley_id' => !((int)$car['c_alley_id'] > 0) ? new Zend_Db_Expr('NULL') : (int)$car['c_alley_id'],
				'is_visible' => $car['c_www_visible'] == 1 ? 1 : 0,
                'is_vipauto_visible' => $car['c_www_vipauto_visible'] == 1 ? 1 : 0,
				'vehicle_category_id' => (int)$car['c_vehicle_category_id'],
				'seat_count' => (int)$car['c_seat_count'],
				'door_count' => (int)$car['c_door_count'],
				'no_vat' => (int)$car['c_no_vat'],
				'after_leasing' => (int)$car['c_after_leasing'],
				'after_vindication' => (int)$car['c_after_vindication'],
				'credit_collateral' => (int)$car['c_credit_collateral'],
				'rental' => (int)$car['c_rental'],
				'new_car_price' => ((int)$car['c_new_car_price'] > 0) ? (int)$car['c_new_car_price'] : new Zend_Db_Expr('NULL'),
				'last_service_year' => ((int)$car['c_last_service_year']) ? (int)$car['c_last_service_year'] : new Zend_Db_Expr('NULL'),
				'last_service_month' => ((int)$car['c_last_service_month']) ? (int)$car['c_last_service_month'] : new Zend_Db_Expr('NULL'),
				'leasing' => ((int)$car['c_leasing'] == 1) ? "y" : "n",
				'leasing_compensation_amount' => ((int)$car['c_leasing_compensation_amount'] > 0 ? (int)$car['c_leasing_compensation_amount'] : new Zend_Db_Expr('NULL')),
				'leasing_instalment_amount' => ((int)$car['c_leasing_instalment_amount'] > 0 ? (int)$car['c_leasing_instalment_amount'] : new Zend_Db_Expr('NULL')),
				'leasing_instalment_number' => ((int)$car['c_leasing_instalment_number'] > 0 ? (int)$car['c_leasing_instalment_number'] : new Zend_Db_Expr('NULL')),
				'leasing_residual_value' => ((int)$car['c_leasing_residual_value'] > 0 ? (int)$car['c_leasing_residual_value'] : new Zend_Db_Expr('NULL')),
				'production_country_id' => (int)$car['c_production_country_id'] > 0 ? (int)$car['c_production_country_id'] : new Zend_Db_Expr('NULL'),
				'origin_country' => (int)$car['c_origin_country_id'] > 0 ? (int)$car['c_origin_country_id'] : new Zend_Db_Expr('NULL'),
				'title' => empty($car['c_title']) ? new Zend_Db_Expr('NULL') : $car['c_title'],
                'vipauto_title' => empty($car['c_vipauto_title']) ? new Zend_Db_Expr('NULL') : $car['c_vipauto_title'],
				'description' => empty($car['c_description']) ? new Zend_Db_Expr('NULL') : $car['c_description'],
                'description2' => empty($car['c_description2']) ? new Zend_Db_Expr('NULL') : $car['c_description2'],
                'description3' => empty($car['c_description3']) ? new Zend_Db_Expr('NULL') : $car['c_description3'],
                'vipauto_description' => empty($car['c_vipauto_description']) ? new Zend_Db_Expr('NULL') : $car['c_vipauto_description'],
				'owner_email' => empty($car['owner_email']) ? new Zend_Db_Expr('NULL') : $car['owner_email'],
				'consumption_city' => new Zend_Db_Expr('NULL'),
				'consumption_motorway' => new Zend_Db_Expr('NULL'),
                'ocassion' => (int)$car['c_ocassion'],
                'high_commission' => (int)$car['c_high_commission'],
                'auction_price' =>  empty($car['c_auction_price']) ? new Zend_Db_Expr('NULL') : $car['c_auction_price'],
                'offer_id' => $car['co_offer_name']
			);
			
			if (is_numeric($car['fuel_consumption']['cfc_consumption_city']) && $car['fuel_consumption']['cfc_consumption_city'] > 0 && is_numeric($car['fuel_consumption']['cfc_consumption_motorway']) && $car['fuel_consumption']['cfc_consumption_motorway'] > 0) {
				$data['consumption_city'] = $car['fuel_consumption']['cfc_consumption_city'];
				$data['consumption_motorway'] = $car['fuel_consumption']['cfc_consumption_motorway'];
			}
			
			foreach ($data as $key => $value) {
				if (empty($data[$key]) && !is_numeric($data[$key])) {
					$data[$key] = new Zend_Db_Expr('NULL');
				}
			}
			
			$extras = array();
			foreach ($car['extras'] as $key => $value) {
				$extras[] = $value['extra_id'];
			}
			
			$features = array();
			foreach ($car['features'] as $key => $value) {
				$features[] = $value['feature_id'];
			}
			
			$videos = array();
			foreach ($car['videos'] as $key => $value) {
				$videos[] = $value;
			}

            $carsModel = new Model_Cars_Cars();

            $exchangeModels = array();
            foreach ($car['exchange_models'] as $key => $value) {

                $model = $carsModel->getCarModelBySrModelId( $value['cem_model_id']);
                $exchangeModels[] = $model['id'];
            }


			
			$data += array('extras' => $extras, 'features' => $features, 'videos' => $videos,
                'descriptions' => $car['descriptions'], 'exchange_models' => $exchangeModels);
			
			$makeModelData = $this->_makeModelData($car);
			$data += $makeModelData;
			
			//Zend_Debug::dump($data,'<h2>processed</h2>'); exit;
			return $data;
		}
		catch (Exception $e) {
			throw new Exception($e->getMessage() . "\n" . Zend_Debug::dump($car, '', $echo=false));
		}
		
		return $data;
	}
	
	public function processCarFull($car) {
		try {
			$data = array(
				'is_archived' => ($car['c_is_archived'] == 2) ? 0 : 1,
				'sr_car_id' => (int)$car['c_id'],
				'car_type_id' => (int)$car['c_type_id'],
				'price' => (float)$car['c_current_price'],
                'vipauto_price' => $car['c_vipauto_current_price'] ? (float)$car['c_vipauto_current_price'] : new Zend_Db_Expr('NULL'),
				'promotion_price' => empty($car['c_promotion_price']) ? new Zend_Db_Expr('NULL') : (float)$car['c_promotion_price'],
				'price_currency' => 'PLN',
				'price_type_key' => $this->_priceType($car['c_price_type'], 'srToWww'),
				'leasing' => 'n',
				'gearbox_type_id' => (int)$car['c_gearbox_type_id'] > 0 ? (int)$car['c_gearbox_type_id'] : new Zend_Db_Expr('NULL'),
				'build_year' => (int)$car['c_production_year'],
				'first_registration_year' => (($yearSecondary = (int)$car['c_production_year_secondary']) > 0) ? $yearSecondary : new Zend_Db_Expr('NULL'),
				'odometer' => (int)$car['c_mileage'],
				'odometer_unit' => $car['c_mileage_unit'],
				'power' => (int)$car['ce_horsepower'],
				'power_unit' => 'PS',
				'fuel_type_id' => $this->_fuelType($car['ce_fuel_type'], $car['c_has_autogas'], $car['c_type_id']),
				'colour_key' => (($cId = $this->_colourId($car['c_color'])) != null) ? $cId : new Zend_Db_Expr('NULL'),
				'colour' => ((int)$cId != 0) ? new Zend_Db_Expr('NULL') : $car['c_color'],
				'colour_metallic' => ($car['c_color_is_metallic'] == "1") ? "y" : "n",
				'cubic_capacity' => (int)($car['ce_displacement'] * 1000),
				'damaged' => 'n',
				'vehicle_category_id' => $this->_vehicleCategory($car['c_type_id']),
				'vin' => $car['c_vin'],
				'registration_plate' => $car['c_registration_plate'],
				'is_reserved' => (int)$car['c_is_reserved'] == 1 ? 1 : 0,
				'status' => $car['c_status'],
				'ownership_type' => $car['c_ownership_type'],
				'caretaker_sr_id' => (int)$car['c_patron_id'],
				'company_id' => (int)$car['c_company_id'],
				'location_id' => (int)$car['c_location_id'],
				'position' => !((int)$car['c_position_id'] > 0) ? str_replace(array("myjnia", "w przygotowaniu", "warsztat". "OS: Archive"), array("", "", "", ""), $car['c_position']) : new Zend_Db_Expr('NULL'),
				'position_id' => !((int)$car['c_position_id'] > 0) ? new Zend_Db_Expr('NULL') : (int)$car['c_position_id'],
				'position_status_id' => !((int)$car['c_position_status_id'] > 0) ? new Zend_Db_Expr('NULL') : (int)$car['c_position_status_id'],
				'position_for_search' => $car['c_position_for_search'],
				'alley_id' => !((int)$car['c_alley_id'] > 0) ? new Zend_Db_Expr('NULL') : (int)$car['c_alley_id'],
				'is_visible' => $car['c_www_visible'] == 1 ? 1 : 0,
                'is_visible_datetime' => $car['c_www_visible'] == 1 ? new Zend_Db_Expr('NOW()')  : new Zend_Db_Expr('NULL'),
                'is_vipauto_visible' => $car['c_www_vipauto_visible'] == 1 ? 1 : 0,
				'vehicle_category_id' => (int)$car['c_vehicle_category_id'],
				'seat_count' => (int)$car['c_seat_count'],
				'door_count' => (int)$car['c_door_count'],
				'no_vat' => (int)$car['c_no_vat'],
				'after_leasing' => (int)$car['c_after_leasing'],
				'after_vindication' => (int)$car['c_after_vindication'],
				'credit_collateral' => (int)$car['c_credit_collateral'],
				'rental' => (int)$car['c_rental'],
				'new_car_price' => ((int)$car['c_new_car_price'] > 0) ? (int)$car['c_new_car_price'] : new Zend_Db_Expr('NULL'),
				'last_service_year' => ((int)$car['c_last_service_year']) ? (int)$car['c_last_service_year'] : new Zend_Db_Expr('NULL'),
				'last_service_month' => ((int)$car['c_last_service_month']) ? (int)$car['c_last_service_month'] : new Zend_Db_Expr('NULL'),
				'leasing' => ((int)$car['c_leasing'] == 1) ? "y" : "n",
				'leasing_compensation_amount' => ((int)$car['c_leasing_compensation_amount'] > 0 ? (int)$car['c_leasing_compensation_amount'] : new Zend_Db_Expr('NULL')),
				'leasing_instalment_amount' => ((int)$car['c_leasing_instalment_amount'] > 0 ? (int)$car['c_leasing_instalment_amount'] : new Zend_Db_Expr('NULL')),
				'leasing_instalment_number' => ((int)$car['c_leasing_instalment_number'] > 0 ? (int)$car['c_leasing_instalment_number'] : new Zend_Db_Expr('NULL')),
				'leasing_residual_value' => ((int)$car['c_leasing_residual_value'] > 0 ? (int)$car['c_leasing_residual_value'] : new Zend_Db_Expr('NULL')),
				'production_country_id' => (int)$car['c_production_country_id'] > 0 ? (int)$car['c_production_country_id'] : new Zend_Db_Expr('NULL'),
				'origin_country' => (int)$car['c_origin_country_id'] > 0 ? (int)$car['c_origin_country_id'] : new Zend_Db_Expr('NULL'),
				'title' => empty($car['c_title']) ? new Zend_Db_Expr('NULL') : $car['c_title'],
                'vipauto_title' => empty($car['c_vipauto_title']) ? new Zend_Db_Expr('NULL') : $car['c_vipauto_title'],
				'description' => empty($car['c_description']) ? new Zend_Db_Expr('NULL') : $car['c_description'],
                'description2' => empty($car['c_description2']) ? new Zend_Db_Expr('NULL') : $car['c_description2'],
                'description3' => empty($car['c_description3']) ? new Zend_Db_Expr('NULL') : $car['c_description3'],
                'vipauto_description' => empty($car['c_vipauto_description']) ? new Zend_Db_Expr('NULL') : $car['c_vipauto_description'],
				'owner_email' => empty($car['owner_email']) ? new Zend_Db_Expr('NULL') : $car['owner_email'],
				'consumption_city' => new Zend_Db_Expr('NULL'),
				'consumption_motorway' => new Zend_Db_Expr('NULL'),
                'auction_price' =>  empty($car['c_auction_price']) ? new Zend_Db_Expr('NULL') : $car['c_auction_price'],
			);
			
			if (is_numeric($car['fuel_consumption']['cfc_consumption_city']) && $car['fuel_consumption']['cfc_consumption_city'] > 0 && is_numeric($car['fuel_consumption']['cfc_consumption_motorway']) && $car['fuel_consumption']['cfc_consumption_motorway'] > 0) {
				$data['consumption_city'] = $car['fuel_consumption']['cfc_consumption_city'];
				$data['consumption_motorway'] = $car['fuel_consumption']['cfc_consumption_motorway'];
			}
			
			$extras = array();
			foreach ($car['extras'] as $key => $value) {
				$extras[] = $value['extra_id'];
			}
			
			$features = array();
			foreach ($car['features'] as $key => $value) {
				$features[] = $value['feature_id'];
			}
			
			$videos = array();
			foreach ($car['videos'] as $key => $value) {
				$videos[] = $value;
			}

            $carsModel = new Model_Cars_Cars();

            $exchangeModels = array();
            foreach ($car['exchange_models'] as $key => $value) {

                $model = $carsModel->getCarModelBySrModelId( $value['cem_model_id']);
                $exchangeModels[] = $model['id'];
            }

            $data += array('extras' => $extras, 'features' => $features, 'videos' => $videos,
                'descriptions' => $car['descriptions'], 'exchange_models' => $exchangeModels);
			
			$makeModelData = $this->_makeModelData($car);
			$data += $makeModelData;
			
			//Zend_Debug::dump($data,'<h2>processed</h2>'); exit;
			return $data;
		}
		catch (Exception $e) {
			throw new Exception($e->getMessage() . "\n" . Zend_Debug::dump($car, '', $echo=false));
		}
		
		return $data;
	}
	
	public function processCarQuick($car) {
		try {
			$data = array(
				'sr_car_id' => (int)$car['c_id'],
				'car_type_id' => (int)$car['c_type_id'],
				'price' => (float)$car['c_current_price'],
                                'vipauto_price' => $car['c_vipauto_current_price'] ? (float)$car['c_vipauto_current_price'] : new Zend_Db_Expr('NULL'),
				'price_currency' => 'PLN',
				'price_type_key' => $this->_priceType($car['c_price_type'], 'srToWww'),
				'leasing' => 'n',
				'build_year' => (int)$car['c_production_year'],
				'first_registration_year' => (($yearSecondary = (int)$car['c_production_year_secondary']) > 0) ? $yearSecondary : new Zend_Db_Expr('NULL'),
				'odometer' => $this->_mileage($car['c_mileage'], $car['c_mileage_unit']),
				'power' => (int)$car['ce_horsepower'],
				'power_unit' => 'PS',
				'fuel_type_id' => $this->_fuelType($car['ce_fuel_type'], $car['c_has_autogas'], $car['c_type_id'], $returnIfNotFound=0),
				'colour_key' => (($cId = $this->_colourId($car['c_color'])) != null) ? $cId : new Zend_Db_Expr('NULL'),
				'colour' => ((int)$cId != 0) ? new Zend_Db_Expr('NULL') : $car['c_color'],
				'colour_metallic' => ($car['c_color_is_metallic'] == "1") ? "y" : "n",
				'cubic_capacity' => (int)($car['ce_displacement'] * 1000),
				'damaged' => 'n',
				'vehicle_category_id' => $this->_vehicleCategory($car['c_type_id']),
				'vin' => $car['c_vin'],
				'registration_plate' => $car['c_registration_plate'],
				'status' => $car['c_status'],
				'caretaker_sr_id' => (int)$car['c_patron_id'],
				'company_id' => (int)$car['c_company_id'],
				'location_id' => (int)$car['c_location_id'],
				'is_visible' => 0,
                                'is_vipauto_visible' => 0
			);
			
			$makeModelData = $this->_makeModelData($car);
			$data += $makeModelData;
			
			//Zend_Debug::dump($data,'<h2>processed</h2>'); exit;
			return $data;
		}
		catch (Exception $e) {
			throw new Exception($e->getMessage() . "\n" . Zend_Debug::dump($car, '', $echo=false));
		}
		
		return $data;
	}
	
	public function processCarPhotos($data) {
		try {
			$cars = new Model_Cars_Cars();
			$car = $cars->getCarBySrCarId((int)$data['car_id']);
			$isArchived = false;
			if (empty($car)) {
				$car = $cars->getArchiveCarBySrCarId((int)$data['car_id']);
				if (empty($car)) {
					throw new Exception("Car not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
				}
				$isArchived = true;
			}
			
			$photos = array(
			);
			
			foreach ($data['photos'] as $item) {
				$photos[] = array(
					'car_id' => $car['car_id'],
					'filename_base' => $item['cp_filename_base'],
					'filename_extension' => $item['cp_filename_extension'],
					'sr_photo_id' => $item['cp_id'],
					'ord' => $item['cp_ord'],
					'status' => 'NO_FILE',
                    'description' => $item['cp_description'],
                    'force_sync' => $item['force_sync'],
				);
			}
			$data['photos'] = $photos;
			
			$cars->synchronizePhotos((int)$car['car_id'], $data['photos'], $isArchived);
			
			return $data;
		}
		catch (Exception $e) {
			throw new Exception($e->getMessage() . "\n" . Zend_Debug::dump($car, '', $echo=false));
		}
		
		return $data;
	}
    
    public function processCarVipautoPhotos($data) {
		try {
			$cars = new Model_Cars_Cars();
			$car = $cars->getCarBySrCarId((int)$data['car_id']);
			$isArchived = false;
			if (empty($car)) {
				$car = $cars->getArchiveCarBySrCarId((int)$data['car_id']);
				if (empty($car)) {
					throw new Exception("Car not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
				}
				$isArchived = true;
			}
			
			$photos = array(
			);
			
			foreach ($data['photos'] as $item) {
				$photos[] = array(
					'car_id' => $car['car_id'],
					'filename_base' => $item['cvp_filename_base'],
					'filename_extension' => $item['cvp_filename_extension'],
					'sr_photo_id' => $item['cvp_id'],
					'ord' => $item['cvp_ord'],
					'status' => 'NO_FILE'
				);
			}
			$data['photos'] = $photos;
			
			$cars->synchronizeVipautoPhotos((int)$car['car_id'], $data['photos'], $isArchived);
			
			return $data;
		}
		catch (Exception $e) {
			throw new Exception($e->getMessage() . "\n" . Zend_Debug::dump($car, '', $echo=false));
		}
		
		return $data;
	}
	
	public function receiveChangeCarPatronBulk($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'from' => '',
			'to' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('changeCarPatronBulk', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		return $template;
		
	}
	
	public function processClient($client) {
		try {
			$data = array(
				'id' => $client['www_client_id'],
				'company_name' => $client['c_company_name'],
				'first_name' => $client['c_first_name'],
				'last_name' => $client['c_last_name'],
				'nip_or_pesel' => $client['c_nip_or_pesel'],
				'regon' => $client['c_regon'],
				'id_document' => $client['c_id_document'],
				'id_type' => $client['c_id_type'],
				'address' => $client['c_address'],
				'zip_code' => $client['c_zip_code'],
				'city' => $client['c_city'],
				'country' => $client['c_country'],
				'phone' => $client['c_phone'],
				'is_deleted' => ($client['c_is_deleted'] == 1) ? 1 : 0
			);
			
			return $data;
		}
		catch (Exception $e) {
			throw new Exception($e->getMessage() . "\n" . Zend_Debug::dump($car, '', $echo=false));
		}
		
		return $data;
	}
	
	public function processEmployee($data) {
		$processed = array(
			'sr_id' => (int)$data['u_user_id'],
			'company_id' => (int)$data['u_account_id'],
			'first_name' => $data['u_first_name'],
			'last_name' => $data['u_last_name'],
			'position' => $data['u_www_position'],
			'financing' => ($data['u_www_financing'] == "1" ? 1 : 0),
			'email' => $data['u_www_email'],
			'phone' => $data['u_www_phone'],
			'skype' => $data['u_www_skype'],
			'display_location_id' => (((int)$data['u_www_location_id'] > 0) ? (int)$data['u_www_location_id'] : new Zend_Db_Expr('NULL')),
			'photo_basename' => basename($data['u_www_photo_filename_base']),
			'photo_extension' => basename($data['u_www_photo_filename_extension']),
			'visible' => ($data['u_www_visible'] == '1') ? 1 : 0,
			'location_id' => (int)$data['location_id'],
			'languages' => $data['languages'],
            'financing_locations' => $data['financing_locations'],
			'photos' => array()
		);
		
		if (is_array($data['photos'])) {
			foreach ($data['photos'] as $sizeName => $b64Data) {
				$processed['photos'][$sizeName] = My_Utils::urlSafeB64Decode($b64Data);
			}
		}
		
		return $processed;
		
	}
	
	public function processMakeEdit($data) {
		$existing = $data[0];
		$afterEdit = $data[1];

		

                $select = $this->db->select()
                        ->from(array('cm' => $this->tables['car_makes']), array('id'))
                        ->where('sr_make_id = ' . (int)$existing['cm_make_id']);
                $localMakeId = $this->db->fetchOne($select);
			
		if (empty($localMakeId)) {
                    throw new Exception("Nie znaleziono pasującej marki na stronie www");
		
		}
		
		$ret = array(
			'id' => $localMakeId,
			'type_id' => $afterEdit['cm_type_id'],
			'name' => $afterEdit['cm_make_name'],
			'special_flag' => !empty($afterEdit['cm_special_flag']) ? $afterEdit['cm_special_flag'] : new Zend_Db_Expr('NULL')
		);
		

		return $ret;
	}
	
	public function processMakeDelete($data) {
	
		
            $select = $this->db->select()
                    ->from(array('cm' => $this->tables['car_makes']), array('id'))
                    ->where('sr_make_id = ' . (int)$data['cm_make_id']);
            $localMakeId = $this->db->fetchOne($select);

            if (empty($localMakeId)) {
                throw new Exception("Nie znaleziono pasującej marki na stronie www");

            }
		
	    
		$ret = array(
			'id' => $localMakeId,
			'type_id' => $data['cm_type_id'],
			'name' => $data['cm_make_name'],
			'special_flag' => !empty($data['cm_special_flag']) ? $data['cm_special_flag'] : new Zend_Db_Expr('NULL')
		);
		

		return $ret;
	}
	
	public function processModelEdit($data) {
	    
		$afterMakeEdit = $this->processMakeEdit(array($data[1],$data[0]));
		$existing = $data[0];
		$afterEdit = $data[1];
		
                
		$localModelId = null;
			
        $select = $this->db->select()
            ->from(array('cm' => $this->tables['car_models']), array('id'))
            ->where('sr_model_id = ' . (int)$existing['cm_model_id']);
        $localModelId = $this->db->fetchOne($select);

		if (empty($localModelId)) {

                    throw new Exception("Nie znaleziono pasującego modelu na stronie www");
        }


		
		$ret = array(
			'id' => $localModelId,
			'make_id' => $afterMakeEdit['id'],
			'name' => $afterEdit['cm_model_name'],
            'sr_model_id' => $afterEdit['cm_model_id'],
		);

        if(isset($afterEdit['merge_model_id']) && !empty($afterEdit['merge_model_id'])) {

            $select = $this->db->select()
                ->from(array('cm' => $this->tables['car_models']), array('id'))
                ->where('sr_model_id = ' . (int)$afterEdit['merge_model_id']);
            $localMergeModelId = $this->db->fetchOne($select);

            if (empty($localMergeModelId)) {

                throw new Exception("Nie znaleziono pasującego modelu merge na stronie www");
            }

            $ret['merge'] = $afterEdit['merge'];
            $ret['merge_model_id'] = $localMergeModelId;
            $ret['sr_merge_model_id'] = $afterEdit['merge_model_id'];
        }



        return $ret;
	}
	
	public function processModelAdd($data) {
	    
		$make = $this->processMakeEdit(array($data,$data));
	
		$ret = array(
			'make_id' => $make['id'],
			'name' => $data['cm_model_name'],
                        'sr_model_id' => $data['cm_model_id'],
		);
		

		return $ret;
	}
	
	public function processModelDelete($data) {
	    
		$make = $this->processMakeEdit(array($data,$data));
		
		$localModelId = null;
                $select = $this->db->select()
				->from(array('cm' => $this->tables['car_models']), array('id'))
				->where('sr_model_id = ' . (int)$data['cm_model_id']);
                $localModelId = $this->db->fetchOne($select);
			
		if (empty($localModelId)) {
			
			
                    throw new Exception("Nie znaleziono pasującego modelu na stronie www");
			
		}
		
		
		$ret = array(
			'id' => $localModelId,
			'make_id' => $make['id'],
                        'name' => $data['cm_model_name'],
                        'sr_model_id' => $data['cm_model_id'],
		);
		

		return $ret;
	}
	
	
	
	
	public function processReservationDeleted($data) {
		$processed = array(
			'id' => (int)$data['cr_www_id'],
			'status_change_employee_sr_id' => (int)$data['cr_deleted_by'],
			'status_change_datetime' => $data['cr_date_deleted']
		);
		
		return $processed;
		
	}
	
	public function processReservationExtended($data) {
		$processed = array(
			'id' => (int)$data['cr_www_id'],
			'status_change_employee_sr_id' => (int)$data['user_id'],
			'status_change_datetime' => $data['date_extended'],
			'valid_until' => $data['date'] . " 00:00:00"
		);
		
		return $processed;
		
	}
	
	public function receiveCarDelete($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'c_id' => '',
			'c_vin' => '',
			'c_make_id' => '',
			'c_model_id' => '',
			'c_production_year' => '',
			'c_company_id' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('deleteCar', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$template['sr_car_id'] = $template['c_id'];
		
		return $template;
		
	}
	
	public function receiveCarEdit($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'c_id' => '',
			'c_type_id' => '',
			'c_vin' => '',
			'c_make_id' => '',
			'cm_make_name' => '',
			'cm_om_id' => '',
			'c_model_id' => '',
			'cm_model_name' => '',
			'cm_model_om_id' => '',
			'c_engine_id' => '',
			'ce_displacement' => '',
			'ce_fuel_type' => '',
			'ce_horsepower' => '',
			'c_has_autogas' => '',
			'c_gearbox_type_id' => '',
			'c_production_year' => '',
			'c_production_year_secondary' => '',
			'c_mileage' => '',
			'c_mileage_unit' => '',
			'c_registration_plate' => '',
			'c_color' => '',
			'c_color_is_metallic' => '',
			'c_location_id' => '',
			'c_position' => '',
			'c_position_id' => '',
			'c_position_status_id' => '',
			'c_position_for_search' => '',
			'c_alley_id' => '',
			'c_company_id' => '',
			'c_patron_id' => '',
			'c_current_price' => '',
            'c_vipauto_current_price' => '',
			'c_price_type' => '',
			'c_promotion_price' => '',
			'c_is_reserved' => '',
			'is_reserved_hidden' => '',
			'is_sold_hidden' => '',
			'c_ownership_type' => '',
			'c_status' => '',
			'c_is_archived' => '',
			'c_vehicle_category_id' => '',
			'c_seat_count' => '',
			'c_door_count' => '',
			'c_no_vat' => '',
			'c_after_leasing' => '',
			'c_after_vindication' => '',
			'c_credit_collateral' => '',
			'c_rental' => '',
			'c_new_car_price' => '',
			'c_last_service_year' => '',
			'c_last_service_month' => '',
			'c_leasing' => '',
			'c_leasing_compensation_amount' => '',
			'c_leasing_instalment_amount' => '',
			'c_leasing_instalment_number' => '',
			'c_leasing_residual_value' => '',
			'c_production_country_id' => '',
			'c_origin_country_id' => '',
			'c_title' => '',
            'c_vipauto_title' => '',
			'c_description' => '',
            'c_description2' => '',
            'c_description3' => '',
            'c_vipauto_description' => '',
			'c_www_visible' => '',
            'c_www_vipauto_visible' => '',
			'descriptions' => '',
			'extras' => '',
			'features' => '',
			'videos' => '',
            'exchange_models' => '',
			'fuel_consumption' => '',
			'owner_email' => '',
			'c_grace' => '',
            'c_ocassion'  => '',
            'c_high_commission'  => '',
            'c_auction_price' => '',
            'co_offer_name' => '',
		);
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('addCarQuick', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidCarQuick($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane samochodu: " . $isValid . "\n");
		}
		
		return $template;
	}
	
	public function receiveCarFull($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'c_id' => '',
			'c_type_id' => '',
			'c_vin' => '',
			'c_make_id' => '',
			'cm_make_name' => '',
			'cm_om_id' => '',
			'c_model_id' => '',
			'cm_model_name' => '',
			'cm_model_om_id' => '',
			'c_engine_id' => '',
			'ce_displacement' => '',
			'ce_fuel_type' => '',
			'ce_horsepower' => '',
			'c_has_autogas' => '',
			'c_gearbox_type_id' => '',
			'c_production_year' => '',
			'c_production_year_secondary' => '',
			'c_mileage' => '',
			'c_mileage_unit' => '',
			'c_registration_plate' => '',
			'c_color' => '',
			'c_color_is_metallic' => '',
			'c_location_id' => '',
			'c_position' => '',
			'c_position_id' => '',
			'c_position_status_id' => '',
			'c_position_for_search' => '',
			'c_alley_id' => '',
			'c_company_id' => '',
			'c_patron_id' => '',
			'c_current_price' => '',
            'c_vipauto_current_price' => '',
			'c_price_type' => '',
			'c_promotion_price' => '',
			'c_is_reserved' => '',
			'c_ownership_type' => '',
			'c_status' => '',
			'c_is_archived' => '',
			'c_vehicle_category_id' => '',
			'c_seat_count' => '',
			'c_door_count' => '',
			'c_no_vat' => '',
			'c_after_leasing' => '',
			'c_after_vindication' => '',
			'c_credit_collateral' => '',
			'c_premium' => '',
			'c_rental' => '',
			'c_new_car_price' => '',
			'c_last_service_year' => '',
			'c_last_service_month' => '',
			'c_leasing' => '',
			'c_leasing_compensation_amount' => '',
			'c_leasing_instalment_amount' => '',
			'c_leasing_instalment_number' => '',
			'c_leasing_residual_value' => '',
			'c_production_country_id' => '',
			'c_origin_country_id' => '',
			'c_title' => '',
            'c_vipauto_title' => '',
			'c_description' => '',
            'c_description2' => '',
            'c_description3' => '',
            'c_vipauto_description' => '',
			'c_www_visible' => '',
            'c_www_vipauto_visible' => '',
			'descriptions' => '',
			'extras' => '',
			'features' => '',
			'videos' => '',
            'exchange_models' => '',
			'fuel_consumption' => '',
			'owner_email' => '',
			'c_grace' => '',
            'c_auction_price' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('addCarQuick', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidCarFull($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane samochodu: " . $isValid . "\n");
		}
		
		return $template;
	}
	
	public function receiveCarQuick($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'c_id' => '',
			'c_type_id' => '',
			'c_vin' => '',
			'c_make_id' => '',
			'cm_make_name' => '',
			'cm_om_id' => '',
			'c_model_id' => '',
			'cm_model_name' => '',
			'cm_model_om_id' => '',
			'c_engine_id' => '',
			'ce_displacement' => '',
			'ce_fuel_type' => '',
			'ce_horsepower' => '',
			'c_has_autogas' => '',
			'c_production_year' => '',
			'c_production_year_secondary' => '',
			'c_mileage' => '',
			'c_mileage_unit' => '',
			'c_registration_plate' => '',
			'c_color' => '',
			'c_color_is_metallic' => '',
			'c_location_id' => '',
			'c_company_id' => '',
			'c_patron_id' => '',
			'c_current_price' => '',
            'c_vipauto_current_price' => '',
			'c_price_type' => '',
			'c_status' => '',
			'c_grace' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('addCarQuick', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidCarQuick($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane samochodu: " . $isValid . "\n");
		}
		
		return $template;
		
	}
	
	public function receiveCarPhotos($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$items = array();
		
		$data = $this->_receiveData($data, $iv);
		foreach ($data['photos'] as $item) {
			$template = array(
				'cp_id' => '',
				'cp_car_id' => '',
				'cp_filename_base' => '',
				'cp_filename_extension' => '',
				'cp_ord' => '',
                'cp_description' => '',
                'force_sync' => '',
			);
			
			foreach ($item as $key => $value) {
				if (array_key_exists($key, $template)) $template[$key] = $value;
			}
			$items[] = $template;
		}
		$data['photos'] = $items;
		
		$checksumValid = $this->_calculateChecksum('carPhotos', $data);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidCarPhotos($data);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane zdjęć: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>tpl - nieprawidlowe dane zdjec</h2>', $echo=false));
		}
		
		return $data;
		
	}
    
    
    public function receiveCarVipautoPhotos($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$items = array();
		
		$data = $this->_receiveData($data, $iv);
		
		foreach ($data['photos'] as $item) {
			$template = array(
				'cvp_id' => '',
				'cvp_car_id' => '',
				'cvp_filename_base' => '',
				'cvp_filename_extension' => '',
				'cvp_ord' => ''
			);
			
			foreach ($item as $key => $value) {
				if (array_key_exists($key, $template)) $template[$key] = $value;
			}
			$items[] = $template;
		}
		$data['photos'] = $items;
		
		$checksumValid = $this->_calculateChecksum('carPhotos', $data);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidCarVipautoPhotos($data);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane zdjęć: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>tpl - nieprawidlowe dane zdjec</h2>', $echo=false));
		}
		
		return $data;
		
	}
	
	public function receiveCarViewsRequest($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'timestamp' => '',
			'c_id' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('carViews', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		return $template;
		
	}
	
	public function receiveCleanCache($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$finalData = $this->_receiveData($data, $iv);
		
		$checksumValid = $this->_calculateChecksum('cleanCache', $finalData);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
				
		return $finalData;
		
	}
	
	public function receiveClient($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'c_id' => '',
			'www_client_id' => '',
			'c_company_name' => '',
			'c_first_name' => '',
			'c_last_name' => '',
			'c_nip_or_pesel' => '',
			'c_regon' => '',
			'c_id_document' => '',
			'c_id_type' => '',
			'c_address' => '',
			'c_zip_code' => '',
			'c_city' => '',
			'c_country' => '',
			'c_phone' => '',
			'c_email' => '',
			'c_is_deleted' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('client', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidClient($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane klienta: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>tpl - nieprawidlowe dane klienta</h2>', $echo=false));
		}
		
		return $template;
		
	}
	
	public function receiveClientsRequest($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'added_by_sr_id' => $data['added_by_sr_id'],
			'for_salesman_sr_id' => $data['for_salesman_sr_id']
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('clients', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		return $template;
		
	}
	
	public function receiveEmployee($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}


		
		$template = array(
			'u_user_id' => '',
			'u_first_name' => '',
			'u_last_name' => '',
			'u_www_visible' => '',
			'u_www_visible' => '',
			'u_www_phone' => '',
			'u_www_skype' => '',
			'u_www_email' => '',
			'u_www_position' => '',
			'u_www_financing' => '',
			'u_www_location_id' => '',
			'u_www_photo_filename_base' => '',
			'u_www_photo_filename_extension' => '',
			'u_account_id' => '',
			'location_id' => '',
			'languages' => '',
            'financing_locations' => '',
			'photos' => ''
		);


		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
			if (in_array($key, array('u_www_photo_filename_base', 'u_www_photo_filename_extension'))) $template[$key] = basename($template[$key]);
		}
		
		$checksumValid = $this->_calculateChecksum('employee', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidEmployee($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane pracownika: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>tpl - nieprawidlowe dane pracownika</h2>', $echo=false));
		}

		return $template;
		
	}
	
	public function receiveFuelConsumption($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$finalData = $this->_receiveData($data, $iv);
		
		$checksumValid = $this->_calculateChecksum('fuelConsumption', $finalData);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$correct = true;
		foreach ($finalData as $gearboxId => $values) {
			if (!array_key_exists('ids', $values) || !array_key_exists('consumption_city', $values) || !array_key_exists('consumption_motorway', $values)) {
				$correct = false;
				break;
			}
		}
		
		if (!$correct) {
			throw new Exception("Incorrect incoming data in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
				
		return $finalData;
		
	}
	
	public function receiveMakeEdit($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
                        'cm_make_id' => '',
                    'cm_make_name' => '',
                    'cm_type_id' => '',
                    'cm_special_flag' => '',
                        'cm_om_id' => '',  
                    
		);
		
		$values = $this->_receiveData($data, $iv);
		if (count($values) != 2) {
			throw new Exception("Invalid array count in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		
		foreach ($values[1] as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		$checksumValid = $this->_calculateChecksum('generic', array($values[0], $values[1]));
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidMakeEdit($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane samochodu: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>nieprawidlowe dane w receive-edit</h2>', $echo=false));
		}
		
		return array($values[0], $template);
	}
	
	public function receiveMakeAdd($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
                        'cm_make_id' => '',
			'cm_make_name' => '',
			'cm_type_id' => '',
			'cm_special_flag' => '',
                        'cm_om_id' => '',          
		);
		
		$values = $this->_receiveData($data, $iv);
	
		
		foreach ($values as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		$checksumValid = $this->_calculateChecksum('generic', $values);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidMakeEdit($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane samochodu: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>nieprawidlowe dane w receive-edit</h2>', $echo=false));
		}
		
		return $template;
	}
	
	
	public function receiveMakeDelete($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
                        'cm_make_id' => '',
			'cm_make_name' => '',
			'cm_type_id' => '',
			'cm_special_flag' => '',
                        'cm_om_id' => '',  
                    
		);
		
		$values = $this->_receiveData($data, $iv);
	
		
		foreach ($values as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		$checksumValid = $this->_calculateChecksum('generic', $values);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidMakeEdit($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane samochodu: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>nieprawidlowe dane w receive-edit</h2>', $echo=false));
		}
		
		return $template;
	}
	
	public function receiveModelEdit($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
            'cm_model_id'   =>  '',
			'cm_make_id'	=>	'',
			'cm_type_id'	=>	'',
			'cm_make_name'	=>	'',
			'cm_model_name'	=>	'',
            'merge'         =>  '',
            'merge_model_id' => ''
		);
		
		$values = $this->_receiveData($data, $iv);
		if (count($values) != 2) {
			throw new Exception("Invalid array count in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		
		foreach ($values[1] as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		$checksumValid = $this->_calculateChecksum('generic', array($values[0], $values[1]));
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidModelEdit($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane samochodu: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>nieprawidlowe dane w receive-edit</h2>', $echo=false));
		}
		
		return array($values[0], $template);
	}
	
		public function receiveModelAdd($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
               
                
		$template = array(
                        'cm_model_id'   =>      '',
			'cm_make_id'	=>	'',
			'cm_type_id'	=>	'',
			'cm_make_name'	=>	'',
			'cm_model_name'	=>	''
		);
		
		$values = $this->_receiveData($data, $iv);
	
		
		foreach ($values as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		$checksumValid = $this->_calculateChecksum('generic', $values);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidMakeEdit($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane samochodu: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>nieprawidlowe dane w receive-edit</h2>', $echo=false));
		}
		
		return $template;
	}
	
	public function receiveModelDelete($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
                        'cm_model_id' =>        '',
			'cm_make_id'	=>	'',
			'cm_type_id'	=>	'',
			'cm_make_name'	=>	'',
			'cm_model_name'	=>	''
		);
		
		$values = $this->_receiveData($data, $iv);
	
		
		foreach ($values as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		$checksumValid = $this->_calculateChecksum('generic', $values);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidMakeEdit($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane samochodu: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>nieprawidlowe dane w receive-edit</h2>', $echo=false));
		}
		
		return $template;
	}
	
	public function receiveReservationDeleted($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'cr_www_id' => '',
			'cr_deleted_by' => '',
			'cr_date_deleted' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('reservationDeleted', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidReservationDeleted($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane usuniętej rezerwacji: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>tpl - nieprawidlowe dane rezerwacji</h2>', $echo=false));
		}
		
		return $template;
		
	}
	
	public function receiveReservationExtended($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'cr_www_id' => '',
			'date' => '',
			'user_id' => '',
			'date_extended' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('reservationExtended', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		$isValid = $this->_isValidReservationExtended($template);
		if ($isValid !== true) {
			throw new Exception("Nieprawidłowe dane przedłużonej rezerwacji: " . $isValid . "\n" . Zend_Debug::dump($template,'<h2>tpl - nieprawidlowe dane rezerwacji</h2>', $echo=false));
		}
		
		return $template;
		
	}
	
	public function receiveTranslationRequest($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'timestamp' => '',
			'user_id' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('translationRequest', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		return true;
		
	}
	
	public function receiveTranslations($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
			'data' => '',
			'user_id' => ''
		);
		
		foreach ($this->_receiveData($data, $iv) as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksumValid = $this->_calculateChecksum('translations', $template);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		return $template;
	}
	
	public function serializeAndEncrypt($data, $iv=null) {
		$data = serialize($data);
		$iv = $this->_encrypt($data, $iv); //by reference
		return $data;
	}
    
    public function receiveAdvertisingBarEdit($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		return $this->_receiveData($data, $iv);
        
	}
        
        
        
        public function receiveMakeSync($data, $checksum, $iv) {

		
		$template = array(
                        'cm_make_id' => '',
			'cm_make_name' => '',
			'cm_type_id' => '',
			'cm_special_flag' => ''
		);
		
		$values = $this->_receiveData($data, $iv);
	
		
		foreach ($values as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		$checksumValid = $this->_calculateChecksum('generic', $values);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		

                
		return  $template;
	}
        
        public function processMakeSync($data) {

		$localMakeId = null;
		$error = "";
		if (!empty($data['cm_om_id'])) {
			$select = $this->db->select()
				->from(array('cm' => $this->tables['car_makes']), array('id'))
				->where('om_id = ' . (int)$data['cm_om_id']);
			$localMakeId = $this->db->fetchOne($select);
		}	
		if (empty($localMakeId)) {
			$select = $this->db->select()
				->from(array('cm' => $this->tables['car_makes']))
				->where('name = ?', $data['cm_make_name'])
				->where('type_id = ' . (int)$data['cm_type_id']);
			$matches = $this->db->fetchAll($select);
			if (count($matches) < 1) {
				$error = "Nie znaleziono pasującej marki na stronie www";
			}
			elseif (count($matches) > 1) {
				$error = "Znaleziono więcej niż jedną pasującą markę na www";
			}
		
		}
		
                if($error == "")
                    $matches = $matches[0];
                
                $matches['error'] = $error;
		

		return $matches;
	}
        
        public function processModelSync($data) {
	    
	
		
		$localModelId = null;
		$error = "";	
		if (empty($localModelId)) {
			$select = $this->db->select()
				->from(array('cm' => $this->tables['car_models']))
                                ->join(array('cma' => $this->tables['car_makes']), 'cm.make_id = cma.id', null)
				->where('cm.name = ?', $data['cm_model_name'])
                                ->where('cma.sr_make_id = ?', $data['cm_make_id']);
			$matches = $this->db->fetchAll($select);
			if (count($matches) < 1) {
				$error = "Nie znaleziono pasującego modelu na stronie www";
			}
			elseif (count($matches) > 1) {
				$error = "Znaleziono więcej niż jeden pasujący model na www";
			}
		
		}
		
		if($error == "")
                    $matches = $matches[0];
                
                $matches['error'] = $error;
		

		return $matches;
	}
        
        
        public function receiveModelSync($data, $checksum, $iv) {
		if (empty($data) || empty($checksum) || empty($iv)) {
			throw new Exception("Empty data or empty checksum or empty iv");
		}
		
		$template = array(
                        'cm_model_id'	=>	'',
			'cm_make_id'	=>	'',
			'cm_type_id'	=>	'',
			'cm_make_name'	=>	'',
			'cm_model_name'	=>	''
		);
		
		$values = $this->_receiveData($data, $iv);
		
		foreach ($values as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		$checksumValid = $this->_calculateChecksum('generic', $values);
		if ($checksumValid != $checksum) {
			throw new Exception("Niezgodność sum kontrolnych na danych");
		}
		
		
		
		return $template;
	}

    public function receiveCarSearch($data, $checksum, $iv) {
        if (empty($data) || empty($checksum) || empty($iv)) {
            throw new Exception("Empty data or empty checksum or empty iv");
        }

        $template = array(
            'price_min' => '',
            'price_max' => '',
            'categories' => '',
            'email' => '',
            'added_by_sr_id' => '',
            'sr_car_id' => '',
        );

        foreach ($this->_receiveData($data, $iv) as $key => $value) {
            if (array_key_exists($key, $template)) $template[$key] = $value;
        }

        $checksumValid = $this->_calculateChecksum('generic', $template);
        if ($checksumValid != $checksum) {
            throw new Exception("Niezgodność sum kontrolnych na danych");
        }

        return $template;

    }

    public function processCarSearch($search) {

        $saveData = array(
            'email' => $search['email'],
            'added_by_sr_id' => $search['added_by_sr_id'],
            'newsletter' => 1
        );

        $searchData = array(
            'price_min' => $search['price_min'],
            'price_max' => $search['price_max'],
            'categories' => !empty($search['categories']) ? array($search['categories']) : '',
        );

        $cars = new Model_Cars_Cars();

        $searchData = $cars->searchPrepareParameters($searchData);

        $view = Zend_Layout::getMvcInstance()->getView();
        if (empty($saveData['title'])) {
            $filterArray = $cars->prepareFilterArray($searchData);

            if ($filterArray) {
                $valueStr = "";
                $counted = 0;
                foreach ($filterArray as $key => $value) {
                    if ($counted == 3) break;

                    if ($counted > 0) {
                        $valueStr .= ", ";
                    }

                    if ($value['value'] == "") {
                        $valueStr .= $view->translate->_($value['title']);
                    }
                    else {
                        if (in_array($value['title'], array("CATEGORY", "FEATURE", "FUEL", "MAKE", "MODEL"))) {
                            $valueStr .= $value['value'];
                        }
                        else {
                            $valueStr .= $view->translate->_($value['title']) . ": " . $value['value'];
                        }
                    }
                    $counted++;
                }
                $saveData['title'] = $valueStr;
            }
            if (empty($saveData['title'])) {
                $saveData['title'] = $view->translate->_('DEFAULT_SEARCH_TITLE') . ' ' . date("Y-m-d H:i:s");
            }
        }

        return array('saveData' => $saveData, 'searchData' => $searchData, 'sr_car_id' => $search['sr_car_id']);
    }

    public function receiveUserSellExchange($data, $checksum, $iv) {
        if (empty($data) || empty($checksum) || empty($iv)) {
            throw new Exception("Empty data or empty checksum or empty iv");
        }

        $template = array(
            'www_id' => '',
            'www_user_id' => '',
            'added_datetime' => '' ,
            'updated_datetime' => '',
            'model_id' => '',
            'build_year' => '',
            'engine' => '',
            'odometer' => '',
            'color' => '',
            'origin' => '',
            'no_accident' => '',
            'repaired_elements' => '',
            'comments' => '',
            'caretaker_sr_id' => '',
            'photo' => '',
            'email' => '',
            'phone' => '',
            'first_name' => '',
            'last_name' => '',
            'is_for_exchange' => '',
            'is_visible' => '',
            'exchange_models' => ''
        );

        foreach ($this->_receiveData($data, $iv) as $key => $value) {
            if (array_key_exists($key, $template)) $template[$key] = $value;
        }

        $checksumValid = $this->_calculateChecksum('generic', $template);




        if ($checksumValid != $checksum) {
            throw new Exception("Niezgodność sum kontrolnych na danych");
        }

        return $template;

    }

    public function processUserSellExchange($data) {


        $carsModel = new Model_Cars_Cars();
        $model = $carsModel->getCarModelBySrModelId($data['model_id']);

        $data['model_id'] = $model['id'];
        $data['id'] = $data['www_id'];
        unset($data['www_id']);
        $data['user_id'] = $data['www_user_id'];
        unset($data['www_user_id']);

        return $data;
    }


    public function receiveAccountEdit($data, $checksum, $iv) {
        if (empty($data) || empty($checksum) || empty($iv)) {
            throw new Exception("Empty data or empty checksum or empty iv");
        }

        $template = array(
            'account_id'                => '',
            'account_name' 	            => '',
            'account_address'	        => '',
            'account_zip_code'	        => '',
            'account_city'	            => '',
            'account_nip'	            => '',
            'account_regon'	            => '',
            'account_invoice_issuer'	=> '',
            'account_represented_by'	=> '',
            'account_location_id'	    => '',
        );

        $values = $this->_receiveData($data, $iv);
        if (count($values) != 2) {
            throw new Exception("Invalid array count in " . __METHOD__ . ", line " . (__LINE__ - 1));
        }

        foreach ($values[1] as $key => $value) {
            if (array_key_exists($key, $template)) $template[$key] = $value;
        }
        $checksumValid = $this->_calculateChecksum('generic', array($values[0], $values[1]));
        if ($checksumValid != $checksum) {
            throw new Exception("Niezgodność sum kontrolnych na danych");
        }



        return array($values[0], $template);
    }


    public function processAccountEdit($data) {
        $existing = $data[0];
        $afterEdit = $data[1];



        $select = $this->db->select()
            ->from(array('c' => $this->tables['companies']), array('id'))
            ->where('id = ' . (int)$existing['account_id']);
        $localAccountId = $this->db->fetchOne($select);

        if (empty($localAccountId)) {
            throw new Exception("Nie znaleziono pasującej firmy na stronie www");

        }

        $ret = array(
            'id' => $localAccountId,
            'name' => $afterEdit['account_name'],
            'address' => $afterEdit['account_address'],
            'zip_code' => $afterEdit['account_zip_code'],
            'city' => $afterEdit['account_city'],
            'nip' => $afterEdit['account_nip'],
            'regon' => $afterEdit['account_regon'],
            'invoice_issuer' => $afterEdit['account_invoice_issuer'],
            'represented_by' => $afterEdit['account_represented_by'],
            'location_id' => $afterEdit['account_location_id'],


        );


        return $ret;
    }

    public function receiveAccountAdd($data, $checksum, $iv) {
        if (empty($data) || empty($checksum) || empty($iv)) {
            throw new Exception("Empty data or empty checksum or empty iv");
        }

        $template = array(
            'account_id'                => '',
            'account_name' 	            => '',
            'account_address'	        => '',
            'account_zip_code'	        => '',
            'account_city'	            => '',
            'account_nip'	            => '',
            'account_regon'	            => '',
            'account_invoice_issuer'	=> '',
            'account_represented_by'	=> '',
            'account_location_id'	    => '',
        );

        $values = $this->_receiveData($data, $iv);


        foreach ($values as $key => $value) {
            if (array_key_exists($key, $template)) $template[$key] = $value;
        }


        $checksumValid = $this->_calculateChecksum('generic', $values);
        if ($checksumValid != $checksum) {
            throw new Exception("Niezgodność sum kontrolnych na danych");
        }


        return $template;
    }

    public function processAccountAdd($data) {


        $ret = array(
            'id' => $data['account_id'],
            'name' => $data['account_name'],
            'address' => $data['account_address'],
            'zip_code' => $data['account_zip_code'],
            'city' => $data['account_city'],
            'nip' => $data['account_nip'],
            'regon' => $data['account_regon'],
            'invoice_issuer' => $data['account_invoice_issuer'],
            'represented_by' => $data['account_represented_by'],
            'location_id' => $data['account_location_id'],


        );

        return $ret;
    }

    public function receiveLocationEdit($data, $checksum, $iv) {
        if (empty($data) || empty($checksum) || empty($iv)) {
            throw new Exception("Empty data or empty checksum or empty iv");
        }

        $template = array(
            'location_id'           => '',
            'location_alias_id'     => '',
            'name' 	                => '',
            'name_display'	        => '',
            'short_name' 	        => '',
            'short_name_display'	=> '',
            'location_group_id'	    => '',
            'phone'	                => '',
            'fax'	                => '',
            'email'	                => '',
            'active'	            => '',
        );

        $values = $this->_receiveData($data, $iv);
        if (count($values) != 2) {
            throw new Exception("Invalid array count in " . __METHOD__ . ", line " . (__LINE__ - 1));
        }

        foreach ($values[1] as $key => $value) {
            if (array_key_exists($key, $template)) $template[$key] = $value;
        }
        $checksumValid = $this->_calculateChecksum('generic', array($values[0], $values[1]));
        if ($checksumValid != $checksum) {
            throw new Exception("Niezgodność sum kontrolnych na danych");
        }



        return array($values[0], $template);
    }


    public function processLocationEdit($data) {
        $existing = $data[0];
        $afterEdit = $data[1];



        $select = $this->db->select()
            ->from(array('l' => $this->tables['locations']), array('location_id'))
            ->where('location_id = ' . (int)$existing['location_id']);
        $localLocationId = $this->db->fetchOne($select);

        if (empty($localLocationId)) {
            throw new Exception("Nie znaleziono pasującej lokalizacji na stronie www");

        }

        $ret = array(
            'location_id'           => $localLocationId,
            'location_alias_id'     => $afterEdit['location_alias_id'],
            'name' 	                => $afterEdit['name_display'],
            'short_name' 	        => $afterEdit['short_name_display'],
            'location_group_id'	    => $afterEdit['location_group_id'],
            'phone'	                => $afterEdit['phone'],
            'fax'	                => $afterEdit['fax'],
            'email'	                => $afterEdit['email'],
            'contact_email'	        => $afterEdit['email'],
            'is_active'	        	=> $afterEdit['active'],


        );


        return $ret;
    }

    public function receiveLocationAdd($data, $checksum, $iv) {
        if (empty($data) || empty($checksum) || empty($iv)) {
            throw new Exception("Empty data or empty checksum or empty iv");
        }

        $template = array(
            'location_id'           => '',
            'location_alias_id'     => '',
            'name' 	                => '',
            'name_display'	        => '',
            'short_name' 	        => '',
            'short_name_display'	=> '',
            'location_group_id'	    => '',
            'phone'	                => '',
            'fax'	                => '',
            'email'	                => '',
            'active'	            => '',
        );

        $values = $this->_receiveData($data, $iv);


        foreach ($values as $key => $value) {
            if (array_key_exists($key, $template)) $template[$key] = $value;
        }


        $checksumValid = $this->_calculateChecksum('generic', $values);
        if ($checksumValid != $checksum) {
            throw new Exception("Niezgodność sum kontrolnych na danych");
        }


        return $template;
    }

    public function processLocationAdd($data) {


        $ret = array(
            'location_id'           => $data['location_id'],
            'location_alias_id'     => $data['location_alias_id'],
            'name' 	                => $data['name_display'],
            'short_name' 	        => $data['short_name_display'],
            'location_group_id'	    => $data['location_group_id'],
            'phone'	                => $data['phone'],
            'fax'	                => $data['fax'],
            'email'	                => $data['email'],
            'contact_email'	        => $data['email'],
            'is_active'	        	=> $data['active'],


        );

        return $ret;
    }
		
	
}