<div id="content_have_car_found">
	<div id="expander">

		<div class="haveCarFound--newsletter">
      <form method="post" action="">
        <h2 style="font-size: 18px;"><span class="big">System automatycznie</span><br> b<PERSON><PERSON><PERSON> co dzie<PERSON> szukał nowych ofert<br><i class="fa fa-arrow-circle-down" style="font-size: 40px; margin: 10px auto 5px auto;"></i></h2>
        <div class="haveCarFound--newsletter__container">
              <?php
              echo $this->form->renderIfExist(array(
                  'makes', 'models'
              ));
              ?>

              <?php
              echo $this->form->renderIfExist(array(
                            'price_min', 'price_max', 'build_from',  'build_to','cubic_capacity_from', 'cubic_capacity_to', 'engine', 'gearboxes', 'important_features', 'categories','added_by_sr_id',
              ));
              ?>
              <div class="clear"></div>
            
        </div>
        
        <h3><?= $this->translate->_('HAVE_CAR_FOUND_SEND_DATA') ?></h3>
        <?php
        echo $this->form->renderIfExist(array(
            'email', 'first_name', 'last_name', 'phone', 'title', 'captcha', 'csrf', 'save', 'save_by_salesman'
        ));
        ?>
      </form>
    </div>
    <div class="haveCarFound--or">
		<h2 class="centerText">
			<a href="<?= $this->url(array('language' => $this->language, 'type' => 'osobowe'), 'list', 'true') ?>">Lista wszystkich aut!</a>
		</h2>
		<div class="elipse">
			<div class="centerText"><i class="fa fa-arrow-circle-up"></i></div>
			<div class="centerText">ALBO</div>
			<div style="float: left;"> <i class="fa fa-arrow-circle-left"></i> ALBO</div>
			<div style="float: right;">ALBO <i class="fa fa-arrow-circle-right"></i></div>
		</div>


      <?php if (!$this->isDeleteFromNL): ?>
				<div id="del_nl_by_mail">
					<h3>
						
						<?= $this->translate->_('DELETE_BY_HASH_SUGGEST_EDIT') ?>
					</h3>
					<?php echo $this->delByMailForm ?>
          <div class="clear"></div>
				</div>
        
        <h3 class="title"><?= $this->translate->_('HAVE_CAR_FOUND_HEADER') ?></h3>
				<div id="have_car_found_content_right">
					<p><?= str_replace("%>%", '</p><p><span class="for_icon"></span>', $this->translate->_('HAVE_CAR_FOUND_CONTENT')) ?></p>
				</div>
				
			<?php else: ?>
				<h3 class="title"><?= $this->translate->_('CONFIRM_DELETE_BY_HASH_HEADER') ?></h3>
				<div id="confirm_delete_newsletter">
					<p>
						<?= str_replace("%profile_name%", "&quot;" . $this->escape($this->search['title']) . "&quot;", $this->translate->_('DELETE_NEWSLETTER_CONFIRM')) ?>
					</p>
				</div>
			<?php endif ?>
			
			<?php if ($this->isDeleteFromNL):
				echo '<div id="delete_newsletter_yes_no">';
					$yesForm = new Form_GenericSubmit();
					$yesForm->setMethod('get');
					$yesForm->setAction($this->url(array('language' => $this->language, 'hash' => $this->hash), 'deleteSearchByHash', true));
					$yesForm->setAttrib('class', 'submit_left');
					$yesForm->submit->setLabel('DISABLE');
					echo $yesForm;
                    echo '<div class="clear"></div>';
                    echo '<p style="padding-left: 15px;">'.$this->translate->_('STILL_DIDNT_FIND').'</p>';
                    echo '<div class="clear"></div>';
                    $modForm = new Form_GenericSubmit();
					$modForm->setMethod('get');
					$modForm->setAction($this->url(array('language' => $this->language, 'hash' => $this->hash), 'editSearchList', true));
					$modForm->setAttrib('class', 'submit_right');
					$modForm->submit->setLabel('MODIFY_YOUR_SEARCH_CRITERIA');
					echo $modForm;
				echo '</div>';
			?>
			<?php endif ?>
    </div>
    <div class="haveCarFound--sendMail">
      <h2 style="font-size: 18px;"><span class="big">Handlowiec</span><br> poszuka dla Panśtwa auta!<br><i class="fa fa-arrow-circle-down" style="font-size: 40px; margin: 10px auto 5px auto;"></i></h2>
      <div id="buyForm">
          <?= $this->weFindCarForm  ?>

	
      </div>
    </div>
    <div class="clear"></div>
	</div>
</div>

<script type="text/javascript" charset="utf-8">
	$(document).ready(function(){		
		$("#content_have_car_found h2 .form_item")
			.click(function(){
				$("form input[type='submit']").click();
			})
			.show();
		
		var autoautoLang = "<?= $this->language ?>";
		
		var confirmDeleteText = "<?= $this->escape($this->translate->_('CONFIRM_DELETE')) ?>";
		$(".confirm-delete").click(function(){
			return confirm(confirmDeleteText);
		});
	
		var multiselectOptions = {
			checkAllText: "<?= $this->escape($this->translate->_('MULTISELECT_CHECK_ALL')) ?>",
			uncheckAllText: "<?= $this->escape($this->translate->_('MULTISELECT_UNCHECK_ALL')) ?>",
			selectedText: "<?= $this->escape($this->translate->_('MULTISELECT_SELECTED_TEXT')) ?>",
			minWidth: 270
		};
		
		$("#content_have_car_found .form_label, #content_have_car_found #make_form .form_label").hide();
		$('#categories').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('CATEGORY')) ?>"}));
		$('#makes').multiselect($.extend({}, multiselectOptions, {multiple: true, selectedList: 1,minWidth: 110, noneSelectedText: "<?= $this->escape($this->translate->_('MAKE')) ?>  - <?= $this->escape(strtolower($this->translate->_('ANY2'))) ?>"}));
		$('#models').multiselect($.extend({}, multiselectOptions, {multiple: true, selectedList: 1, minWidth: 110, noneSelectedText: "<?= $this->escape($this->translate->_('MODEL')) ?> - <?= $this->escape(strtolower($this->translate->_('ANY'))) ?> "}));
		$('#engine').multiselect($.extend({}, multiselectOptions, {minWidth: 110, classes: 'engine', noneSelectedText: "<?= $this->escape($this->translate->_('ENGINE')) ?>"}));
		$('#gearboxes').multiselect($.extend({}, multiselectOptions, {minWidth: 110, noneSelectedText: "<?= $this->escape($this->translate->_('GEARBOX')) ?>"}));
		$('#important_features').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('IMPORTANT_FEATURES')) ?>"}));
		$('#build').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('PRODUCTION_YEAR')) ?>"}));
		$('#odometer').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('ODOMETER')) ?>"}));
		$('#added_by_sr_id').multiselect($.extend({}, multiselectOptions, {multiple: false, selectedList: 1, noneSelectedText: "<?= $this->escape($this->translate->_('FAVOURITE_SALESMAN')) ?>  - <?= $this->escape(strtolower($this->translate->_('ANY'))) ?> "}));

		$("#makes").bind("multiselectclick", function(event, ui){
			var selectedArray = new Array();
			$('#makes :selected').each(function(i, selected) { 
				if ($(this).attr('value') != ui.value) {
					selectedArray[selectedArray.length] = $(this).attr('value');
				}
			});
			if (ui.checked) {
				selectedArray[selectedArray.length] = ui.value;
			}
			var modelsArray;
			$.ajax({
				type: 'GET',
				url: '<?= $this->url(array(), 'getmodels', true); ?>',
				data: {makes: selectedArray, haveCarFound: true},
				dataType: 'json',
				async: false,
				success: function(data) {
					modelsArray = data;
				},
				error: function() {
				}
			});
			$('#models').find('option').remove();
			var models = $('#models');
			$.each(modelsArray, function(index, value) { 
				models.append('<option value="' + index + '">' + value + '</option>');
			});
			$('#models').multiselect("refresh");
		});

        var fixedDisabledFeaturesIndexStart = 13;
        var fixedDisabledFeaturesIndexStop = 18;
        var divideModulo = [18, 18, 19];
        $(".for_important_features ul.ui-multiselect-checkboxes").append('<div class="clear" />');
        var lastModulo = 0;
        for (var i in divideModulo) {
            $(".for_important_features ul.ui-multiselect-checkboxes>li:lt("+divideModulo[i]+")").wrapAll('<div class="features_column" />');
        }
        $(".for_important_features ul.ui-multiselect-checkboxes li").slice(fixedDisabledFeaturesIndexStart, fixedDisabledFeaturesIndexStop).find('input').attr('checked', 'checked').attr('disabled', 'disabled').siblings('span').css('color', '#f67812');

		/*var buildFrom = $(".for_build_from").outerHtml();
		var buildTo =  $(".for_build_to").outerHtml();
		
		$(".for_build_from, .for_build_to").remove();
		$(".for_build div.ui-multiselect-menu").append(buildFrom, buildTo);
		$(".for_build_from input, .for_build_to input").css({'color': '#333', 'background-color': '#fafafa'});
		$(".for_build_from, .for_build_to").css({'width': '115px', 'float': 'left'});
		$(".for_build_from").css({'padding-right': '27px'});
		$(".for_build_from input, .for_build_to input").css({'width': '105px'});
        
        var cubicCapacityFrom = $(".for_cubic_capacity_from").outerHtml();
        var cubicCapacityTo =  $(".for_cubic_capacity_to").outerHtml();

        $(".for_cubic_capacity_from, .for_cubic_capacity_to").remove();
        $(".for_engine div.ui-multiselect-menu").append(cubicCapacityFrom, cubicCapacityTo);
        $(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css('color', '#333');
        $(".for_cubic_capacity_from, .for_cubic_capacity_to").css({'width': '115px', 'float': 'left'});
        $(".for_cubic_capacity_from").css({'padding-right': '27px'});
        $(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css({'width': '105px'});*/

		$("#content_have_car_found .input_text input").compactInputs({
			getLabelFn: function(el) {
				return $(el).closest(".form_input").siblings(".form_label").find("label");
			}
		});
        $("#buyForm .form_item_text input, #buyForm textarea").compactInputs({});

        console.log($("#buyForm form"));

        $("#buyForm form").submit(function(){


            if($("#comments").val() == "<?= $this->translate->_('BUY_CAR_DESCRIPTION') ?> *")
                $("#comments").val("");

            return true;
        });

    $("#buyForm .form_item_captcha .form_label").hide();
		
		<?php if ($this->showSaveForm): ?>
			$("#search_save").click();
		<?php endif; ?>
	
	});
</script>