<html>
	<head>
		
	</head>
	<body>
		<div><b>Oferta na samochód <?= $this->escape($this->carText) ?></b></div>
		<div>
			<a target="_blank" href="<?= $this->wwwDomain . $this->carLink($this->carData, "", $onlyHref=true) ?>">&raquo; Strona samochodu w serwisie www (z obrazkami)</a><br />
			<a target="_blank" href="<?= $this->srDomain .  "/cars/show-car/id/" . $this->carData['sr_car_id']?>">&raquo; Zobacz samochód w systemie rozliczeniowym (sys2)</a>
		</div>
		
		<br />
		
		<div>
			<b>Oferowana cena</b>: <?= $this->escape($this->data['price']) ?><br />
			<b><PERSON><PERSON><PERSON><PERSON> wiadom<PERSON>ci</b>: <?= $this->escape($this->data['message']) ?>
		</div>
		
		<br />
		
		<div>
			<b><PERSON> klienta wpisane w formularzu</b><br />
			<b><PERSON><PERSON><PERSON>, nazwi<PERSON></b>: <?= $this->escape($this->data['first_name'] . ' ' . $this->data['last_name']) ?><br />
			<b>E-mail</b>: <a href="mailto:<?= $this->escape($this->data['email']) ?>?subject=Odp: Oferta na samochód <?= $this->escape($this->carText) ?>"><?= $this->escape($this->data['email']) ?></a><br />
			<?php if (!empty($this->data['company_name'])): ?>
				<b>Nazwa firmy</b>: <?= $this->escape($this->data['company_name']) ?><br />
			<?php endif ?>
			<?php if (!empty($this->data['phone'])): ?>
				<b>Telefon</b>: <?= $this->escape($this->data['phone']) ?><br />
			<?php endif ?>
		</div>
		
		<?php if (!empty($this->userData)): ?>
			<br />
			<div>
				<b>Powyższe dane mogą się różnić od danych użytkownika podanych w profilu:</b><br />
				<b>Imię, nazwisko</b>: <?= $this->escape($this->userData['first_name'] . ' ' . $this->userData['last_name']) ?><br />
				<b>E-mail</b>: <a href="mailto:<?= $this->escape($this->userData['email']) ?>?subject=Odp: Oferta na samochód <?= $this->escape($this->carText) ?>"><?= $this->escape($this->userData['email']) ?></a><br />
				<?php if (!empty($this->userData['company_name'])): ?>
					<b>Nazwa firmy</b>: <?= $this->escape($this->userData['company_name']) ?><br />
				<?php endif ?>
				<?php if (!empty($this->userData['phone'])): ?>
					<b>Telefon</b>: <?= $this->escape($this->userData['phone']) ?><br />
				<?php endif ?>
			</div>
		<?php endif ?>
	</body>
</html>