<?php

class My_Validate_Client extends Zend_Validate_Abstract {
	
	const VALUE_FORMAT_INVALID = 'valueFormatInvalid';
	const VALUE_NOT_IN_WHITELIST = 'valueNotInWhitelist';
	
	protected $_messageTemplates = array(
		self::VALUE_FORMAT_INVALID => 'Nieprawidłowy format danych',
		self::VALUE_NOT_IN_WHITELIST => 'Klient nie jest poprawnym wyborem w tym kontekście'
	);
	
	protected $_chooseFrom;
	
	public function __construct($chooseFrom=array()) {
		if (!is_array($chooseFrom)) {
			throw new Exception("Optional argument 'chooseFrom' must be array if present in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		$this->_chooseFrom = $chooseFrom;
	}
	
	public function isValid($value, $context=null) {
		$xpld = explode(' ', $value);
		if (count($xpld) < 1) {
			$this->_error(self::VALUE_FORMAT_INVALID);
			return false;
		}
		
		$xpld = explode(':', $xpld[0]);
		if (strtolower($xpld[0]) != 'id') {
			$this->_error(self::VALUE_FORMAT_INVALID);
			return false;
		}
		
		$val = (int)$xpld[1];
		if (!($val > 0)) {
			$this->_error(self::VALUE_FORMAT_INVALID);
			return false;
		}
		
		if (!in_array($val, $this->_chooseFrom)) {
			$this->_error(self::VALUE_NOT_IN_WHITELIST);
			return false;
		}
		
		return true;
	}
	
	public static function extractClientId($value) {
		$xpld = explode(' ', $value);
		if (count($xpld) < 1) {
			return null;
		}
		
		$xpld = explode(':', $xpld[0]);
		if (strtolower($xpld[0]) != 'id') {
			return null;
		}
		
		return (int)$xpld[1];
	}
	
}