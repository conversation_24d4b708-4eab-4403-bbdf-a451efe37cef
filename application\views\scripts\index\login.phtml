<div id="login_container">
	
	<div id="login_login">
		<h2 class="red"><?= $this->translate->_('LOGIN_PAGE_HEADER') ?></h2>
		<?= $this->form ?>
                
		<div id="login_forgot_password">
			<a href="#remindForm" rel="prettyPhoto" title="">
				<span class="for_icon"></span>
				<?= $this->translate->_('REMIND_PASSWORD') ?>
			</a>
		</div>
              
	</div>
	<?php /*
	<div id="login_why">
		<h2><span class="red"><?= $this->translate->_('REGISTER_WHY_NO_ACCOUNT') ?></span> <?= $this->translate->_('REGISTER_WHY_REGISTER') ?></h2>
		<table cellspacing="0" cellpadding="0">
			<tr>
				<td>
					<span class="for_icon"></span><?= $this->translate->_('REGISTER_WHY_1') ?><br />
					<span class="for_icon"></span><?= $this->translate->_('REGISTER_WHY_2') ?><br />
					<span class="for_icon"></span><?= $this->translate->_('REGISTER_WHY_3') ?><br />
					<span class="for_icon"></span><?= $this->translate->_('REGISTER_WHY_4') ?>
				</td>
			</tr>
		</table>
	</div>
	
	<div id="login_register">
		<?= $this->regForm ?>
	</div>
	  */ ?>
	<div class="clear"></div>
	
</div>

<div id="remindForm">
	<div class="pp_html">
		<br />
		<h2><?= $this->translate->_('RESET_PASSWORD') ?></h2>
		
		<?= $this->translate->_('RESET_PASSWORD_INSTRUCTION_1') ?>
		<br /><br />
		
		<?= $this->remindForm ?>
	</div>	
</div>

<div id="remindInstruction">
	<div class="pp_html">
		<br />
		<h2><?= $this->translate->_('RESET_PASSWORD') ?></h2>
		
		<?= $this->translate->_('RESET_PASSWORD_INSTRUCTION_2') ?>
	</div>
</div>

<?php if ($this->showConfirmRemindResult): ?>
	<div id="remindConfirm">
		<div class="pp_html">
			<h1><?= $this->translate->_('RESET_PASSWORD') ?></h1>
			
			<?php if ($this->success): ?>
				<div>
					<?= $this->translate->_('PASSWORD_WAS_RESET') ?>
				</div>
			<?php else: ?>
				<div>
					<?= $this->translate->_('PASSWORD_NOT_RESET') ?>
				</div>
			<?php endif ?>
		</div>
	</div>
<?php endif ?>

<script type="text/javascript">
	$(function(){
		$("#login_container .input_text input, #login_container .input_password input").compactInputs({
			getLabelFn: function(el) {
				return $(el).closest(".form_input").siblings(".form_label").find("label");
			},
			getLabelContainerFn: function(el) {
				return $(el).closest(".form_input").siblings(".form_label");
			}
		});
		$(".form_label.for_captcha").hide();
		
		<?php if ($this->showRemindInstruction) : ?>
			$.prettyPhoto.open('#remindInstruction');
		<?php endif; ?>
		
		<?php if ($this->showConfirmRemindResult) : ?>
			$.prettyPhoto.open('#remindConfirm');
		<?php endif; ?>
	});
</script>