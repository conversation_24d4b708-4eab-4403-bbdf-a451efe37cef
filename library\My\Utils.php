<?php

class My_Utils {
	
	public static function extractYoutubeVideoId($url) {
		//rudimentary checks only
		if (stripos($url, "youtube") === false) {
			return null;
		}
		
		$matches = array();
		$pattern = "/(\?|&)v=([A-Za-z0-9\-_]+)(&|$)/";
		$id = null;
		if (preg_match($pattern, $url, $matches)) {
			$id = $matches[2];
		}
		else {
			$matches = array();
			$pattern = "/\/embed\/([A-Za-z0-9\-_]+)([\/?\"$])/";
			if (preg_match($pattern, $url, $matches)) {
				$id = $matches[1];
			}
			else {
				return null;
			} 
		}
		
		return $id;
	}
	
	public static function replacePseudoVars($subject, $vars) {
		/*
			subject: "lorem ipsum %var1% sit %var2%"
			vars: array("var1" => "dolor", "var2" => "amet")
			returns: "lorem ipsum dolor sit amet"
		*/
		$pattern = "/%[A-z0-9]+%/i";
		$matches = array();
		if (preg_match_all($pattern, $subject, $matches)) {
			foreach ($matches[0] as $matchedText) {
				$key = trim($matchedText, "%");
				if (array_key_exists($key, $vars)) {
					$subject = preg_replace("/%{$key}%/i", $vars[$key], $subject);
				}
			}
		}
		
		return $subject;
	}
	
	public static function rrmdir($path) {
		return is_file($path) ? @unlink($path) : array_map(array($this, 'rrmdir'),glob($path.'/*'))==@rmdir($path);
	}
	
	public static function urlSafeB64Encode($string) {
		$data = base64_encode($string);
		$data = str_replace(array('+','/','='),array('-','_','.'),$data);
		return $data;
	}

	public static function urlSafeB64Decode($string) {
		$data = str_replace(array('-','_','.'),array('+','/','='),$string);
		$mod4 = strlen($data) % 4;
		if ($mod4) {
			$data .= substr('====', $mod4);
		}
		return base64_decode($data);
	}
	
}