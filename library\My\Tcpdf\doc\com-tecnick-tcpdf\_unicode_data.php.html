<html>
<head>
<title>Docs for page unicode_data.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">com-tecnick-tcpdf</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_com-tecnick-tcpdf.html" class="menu">class tree: com-tecnick-tcpdf</a> ]
		  [ <a href="../elementindex_com-tecnick-tcpdf.html" class="menu">index: com-tecnick-tcpdf</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_com-tecnick-tcpdf.html">com-tecnick-tcpdf</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../com-tecnick-tcpdf/_2dbarcodes.php.html">		2dbarcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_barcodes.php.html">		barcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_htmlcolors.php.html">		htmlcolors.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_qrcode.php.html">		qrcode.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_tcpdf.php.html">		tcpdf.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_config---tcpdf_config.php.html">		tcpdf_config.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_unicode_data.php.html">		unicode_data.php
		</a><br>
	  </div><br />
      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../com-tecnick-tcpdf/QRcode.html">QRcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF.html">TCPDF</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF2DBarcode.html">TCPDF2DBarcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDFBarcode.html">TCPDFBarcode</a><br />
	  </div>
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: unicode_data.php</h1>
Source Location: /unicode_data.php<br /><br />

<br>
<br>


<h2>Page Details:</h2>
Unicode Include file for TCPDF.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Nicola Asuni</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2004-2009 Nicola Asuni - Tecnick.com S.r.l (www.tecnick.com) Via Della Pace, 11 - 09044 - Quartucciu (CA) - ITALY - www.tecnick.com - <EMAIL></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.tcpdf.org">http://www.tcpdf.org</a></td>
  </tr>
  <tr>
    <td><b>since:</b>&nbsp;&nbsp;</td><td>2.1.000 (2008-01-08)</td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a></td>
  </tr>
</table>
</div>
<br /><br />
<br /><br />
<br /><br />
  <hr />
	<a name="defineK_LRE"></a>
	<h3>K_LRE <span class="smalllinenumber">[line 64]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_LRE = 8234</code>
    </td></tr></table>
    </td></tr></table>

    Left-to-Right Embedding<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_LRM"></a>
	<h3>K_LRM <span class="smalllinenumber">[line 56]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_LRM = 8206</code>
    </td></tr></table>
    </td></tr></table>

    Left-to-Right Mark<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_LRO"></a>
	<h3>K_LRO <span class="smalllinenumber">[line 76]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_LRO = 8237</code>
    </td></tr></table>
    </td></tr></table>

    Left-to-Right Override<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_PDF"></a>
	<h3>K_PDF <span class="smalllinenumber">[line 72]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_PDF = 8236</code>
    </td></tr></table>
    </td></tr></table>

    Pop Directional Format<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_RE_PATTERN_ARABIC"></a>
	<h3>K_RE_PATTERN_ARABIC <span class="smalllinenumber">[line 103]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_RE_PATTERN_ARABIC = &quot;/(
		  \xD8[\x80-\x83\x8B\x8D\x9B\x9E\x9F\xA1-\xBA]  # AL
		| \xD9[\x80-\x8A\xAD-\xAF\xB1-\xBF]             # AL
		| \xDA[\x80-\xBF]                               # AL
		| \xDB[\x80-\x95\x9D\xA5\xA6\xAE\xAF\xBA-\xBF]  # AL
		| \xDC[\x80-\x8D\x90\x92-\xAF]                  # AL
		| \xDD[\x8D-\xAD]                               # AL
		| \xDE[\x80-\xA5\xB1]                           # AL
		| \xEF\xAD[\x90-\xBF]                           # AL
		| \xEF\xAE[\x80-\xB1]                           # AL
		| \xEF\xAF[\x93-\xBF]                           # AL
		| \xEF[\xB0-\xB3][\x80-\xBF]                    # AL
		| \xEF\xB4[\x80-\xBD]                           # AL
		| \xEF\xB5[\x90-\xBF]                           # AL
		| \xEF\xB6[\x80-\x8F\x92-\xBF]                  # AL
		| \xEF\xB7[\x80-\x87\xB0-\xBC]                  # AL
		| \xEF\xB9[\xB0-\xB4\xB6-\xBF]                  # AL
		| \xEF\xBA[\x80-\xBF]                           # AL
		| \xEF\xBB[\x80-\xBC]                           # AL
		| \xD9[\xA0-\xA9\xAB\xAC]                       # AN
		)/x&quot;</code>
    </td></tr></table>
    </td></tr></table>

        <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_RE_PATTERN_RTL"></a>
	<h3>K_RE_PATTERN_RTL <span class="smalllinenumber">[line 85]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_RE_PATTERN_RTL = &quot;/(
	  \xD6\xBE                                             # R
	| \xD7[\x80\x83\x86\x90-\xAA\xB0-\xB4]                 # R
	| \xDF[\x80-\xAA\xB4\xB5\xBA]                          # R
	| \xE2\x80\x8F                                         # R
	| \xEF\xAC[\x9D\x9F\xA0-\xA8\xAA-\xB6\xB8-\xBC\xBE]    # R
	| \xEF\xAD[\x80\x81\x83\x84\x86-\x8F]                  # R
	| \xF0\x90\xA0[\x80-\x85\x88\x8A-\xB5\xB7\xB8\xBC\xBF] # R
	| \xF0\x90\xA4[\x80-\x99]                              # R
	| \xF0\x90\xA8[\x80\x90-\x93\x95-\x97\x99-\xB3]        # R
	| \xF0\x90\xA9[\x80-\x87\x90-\x98]                     # R
	| \xE2\x80[\xAB\xAE]                                   # RLE &amp; RLO
	)/x&quot;</code>
    </td></tr></table>
    </td></tr></table>

        <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_RLE"></a>
	<h3>K_RLE <span class="smalllinenumber">[line 68]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_RLE = 8235</code>
    </td></tr></table>
    </td></tr></table>

    Right-to-Left Embedding<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_RLM"></a>
	<h3>K_RLM <span class="smalllinenumber">[line 60]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_RLM = 8207</code>
    </td></tr></table>
    </td></tr></table>

    Right-to-Left Mark<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_RLO"></a>
	<h3>K_RLO <span class="smalllinenumber">[line 80]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_RLO = 8238</code>
    </td></tr></table>
    </td></tr></table>

    Right-to-Left Override<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
<br />

        <div class="credit">
		    <hr />
		    Documentation generated on Sun, 28 Mar 2010 22:22:43 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>