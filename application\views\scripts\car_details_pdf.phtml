<table width="100%" border="1" bgcolor="#f3f3f3" cellpadding="5" cellspacing="0" margin="0">
	<tr>
		<td align="center">	
			<?= $this->translate->_('ASK_FOR_POSITION') ?>: <b><?= (($this->car['status'] == "NOT_ON_SITE") ? "<strong>" . $this->translate->_('NOT_ON_SITE') . "</strong>" : $this->escape($this->cat['short_name'] . " " . $this->car['position'])) ?></b><br />
			<b><?= $this->car['make_name'] . ' ' . $this->car['model_name'] . ($this->language == "pl" ? " " . $this->escape($this->car['title']) : "") ?><br />
			<?= $this->engineData($this->car) ?></b><br />
			<?= $this->car['build_year'] ?>, <?= mb_strtolower($this->translate->_('ODOMETER')) ?>: <b><?= $this->mileage($this->car['odometer']) ?> km</b>
		</td>
	</tr>
</table>

<p align="center" style="margin: 0; padding: 0;">
	<?php if ($this->language == "pl"): ?>
		<font size="14"><b><?= $this->carDescription($this->car['description'], $stripNotReplace=true) ?></b></font>
		<br /><br />
	<?php endif ?>
	<font size="12">
		<b>
			<?php
				$extrasStr = array();
				foreach ($this->car['extras'] as $key => $value) {
					$extrasStr[$key] = $this->translate->_($value);
				}
			?>
			<?= implode("; ", $extrasStr) ?>
		</b>
		
		<b>
			<?php
				$featuresStr = array();
				foreach ($this->car['features'] as $key => $value) {
					$featuresStr[$key] = $this->translate->_($value);
				}
			?>
			<?= implode("; ", $featuresStr) ?>
		</b>
		<br /><br />
	</font>
	
	<table cellspacing="0" cellpadding="0" width="100%" style="padding: 0; margin: 0; margin-left: -30px; width: 100%;">
		<tr style="padding: 0;">
			<td style="font-size: 60px; text-align: center; padding: 0;" width="66%">
				<?= $this->translate->_('PRICE') ?>: 
				<b style="font-size: 80px;"><?= $this->escape($this->carPrice($this->car, $this->language_row)) ?></b>
				
				<br /><br />
				<font size="18"><?= $this->translate->_('CARETAKER') ?>: <b><? if ($this->car['caretaker_visible']) : ?><?= $this->escape($this->car['caretaker_first_name'] . " " . $this->car['caretaker_last_name']) ?><? endif; ?></b><? if ($this->car['caretaker_visible']) : ?><br /><?= $this->escape($this->car['caretaker_phone']) ?><? endif; ?>
				</font>
				<br/><br />
				<?php echo $this->escape($this->car['address'] . ', ' . $this->translate->_('LOCATION_POINT') . ' ' . $this->car['name']) ?>
			</td>
			<td width="33%">
				<?php foreach ($this->car['photos'] as $photo): ?>
					<img title="" src="/images/cars/<?= $this->car['car_id'] ?>/<?= $photo['filename_base'] . "_M." . $photo['filename_extension'] ?>" width="150" height="109" />
				<?php break; endforeach ?>
			</td>
		</tr>
	</table>
</p>
