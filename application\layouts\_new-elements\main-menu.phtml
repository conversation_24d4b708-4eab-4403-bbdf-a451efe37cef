<div class="header--container__languages">
  <?php foreach ($this->getEnabledLanguages() as $key => $value): ?>
  <a href="<?= $this->url(array('language' => $key), 'home', true) ?><?php echo (!empty($this->changeLanguageData['queryString']) ? (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash']."&" : "?" ) . $this->changeLanguageData['queryString'] : (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash'] : "" ) ) ?>" class="<?= $key ?><? if ($key == $this->language):?> active<? endif; ?>"></a>
  <?php endforeach ?>
</div>

<div class="header--container unselectable effect--3d">
  <div class="header--container__inside">
    <a class="header--container__logo" href="/"></a>

    <ul class="header--container__menu">
      <li>
        <a href="<?= $this->url(array('language' => $this->language), 'haveCarFound', true) ?>" class="menu--buy" title="">
            <?php
                $buyCarTr = explode(" ", strtolower($this->translate->_('BUY_CAR')));
                echo $buyCarTr[0] .   ' <span class="orange-font">'.$buyCarTr[1].'</span>';
            ?>

        </a>
      </li>
      <li>
        <a href="<?= $this->url(array('language' => $this->language), 'sellCar', true) ?>" class="menu--sell" title="">

            <?php
            $sellCarTr = explode(" ", strtolower($this->translate->_('SELL_CAR')));
            echo $sellCarTr[0] .   ' <span class="orange-font">'.$sellCarTr[1].'</span>';
            ?>

        </a>
      </li>

        <li>
            <a href="<?= $this->url(array('language' => $this->language), 'exchangeCar', true) ?>" class="menu--sell" title="">

                <?php
                $exchangeCarTr = explode(" ", strtolower($this->translate->_('EXCHANGE_CAR')));
                echo $exchangeCarTr[0] .   ' <span class="orange-font">'.$exchangeCarTr[1].'</span>';
                ?>

            </a>
        </li>

    </ul>
    
    <ul class="header--container__menu-icons">
      <li>
        <?php $specificListName = (!empty($this->identity) && $this->identity->role == "user") ? 'favouriteList' : 'favouriteListSession'; ?>
        <a href="<?= $this->url(array('language' => $this->language), $specificListName, true) ?>" title="Ulubione" class="menuIcon--favourite" data-counter="<?= $this->favouritesCount ?>"></a>
      </li>
      <li>
        <a title="" class="menuIcon--search akaLink" data-js="menuIcon--search"></a>
      </li>
    </ul>
  </div>
  <div class="header--container__additional" data-js="header--container__additional">
    <div class="header--container__additional-inside selectable">
        <ul class="headerItems">
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('rental' => 1)) ?>" title="" >
              <?= $this->translate->_('RENTAL') ?>
            </a>
          </li>

          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('price_min' => '',  'price_max' => 10000)) ?>"  class="red-font bold larger" title="" >
              <?= $this->translate->_('SHORTCUT_UP_TO_10K') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('categories' => array('sedan'))) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_SEDAN') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('important_features' => array('origin 24'))) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_IMPORT_USA') ?>
            </a>
          </li>
          <li style="clear:both;">
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('promotions' => '1')) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_PROMOTIONS') ?>
            </a>

          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('price_min' => 10000,  'price_max' => 30000)) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_10K_TO_30K') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('categories' => array('combi','hatchback'))) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_COMBI_HATCHBACK') ?>
            </a>

          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('important_features' => array('origin 21'))) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_SALON_POLSKA') ?>
            </a>
          </li>
          <li>
            <a class="green-font bold" href="<?= $this->url(array('language' => $this->language), 'list', true) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_ALL_OFFERS') ?>
            </a>

          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('price_min' => 30000,  'price_max' => 50000)) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_30K_TO_50K') ?>
            </a>

          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('categories' => array('suv'))) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_OFFROAD_SUV') ?>
            </a>

          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('exclusive' => '1')) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_EXCLUSIVE') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('new' => '1')) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_NEW_CARS') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('price_min' => 50000,  'price_max' => 80000)) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_50K_TO_80K') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('categories' =>array('van'))) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_VAN') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('leasing_transfer' => '1')) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_LEASING_TRANSFER') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('leasing' => '1')) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_LEASING') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('price_min' => 80000,  'price_max' => 120000)) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_80K_TO_120K') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('categories' => array('cabrio'))) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_CABRIO') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('leasing' => '1')) ?>" title="" >
              <?= $this->translate->_('CARS_WITH_VAT') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('gearboxes' => array('automatic'))) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_AUTOMATIC_GEARBOX') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('price_min' => 200000,  'price_max' => '')) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_80K_TO_120K') ?>
            </a>
          </li>

          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('categories' => array('pickup'))) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_PICKUP') ?>
            </a>
          </li>
            <li>
                <a href="<?= $this->url(array('language' => $this->language), 'financing', true) ?>" class="red-font bold larger" title="">
                    <?= $this->translate->_('FINANCING') ?>
                </a>
            </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('premium' => '1')) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_PREMIUM') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'list', true).'?'.http_build_query(array('engine' => 'fuels diesel')) ?>" title="" >
              <?= $this->translate->_('SHORTCUT_DIESEL') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'service', true) ?>" class="blue-font bold larger">
              <?= $this->translate->_('SERVICE') ?>
            </a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'locations', true) ?>" class="green-font bold larger"><?= $this->translate->_('CONTACT') ?></a>
          </li>
          <li>
            <a href="<?= $this->url(array('language' => $this->language), 'login', true) ?>" class="orange-font bold larger"><?= $this->translate->_('LOG_IN') ?></a>
          </li>
        </ul>
    </div>
  </div>
</div>
<div class="additional--container">

  <form action="<?= $this->url(array('language' => $this->language), 'list', true) ?>" method="post" class="search--container unselectable effect--3d">
    <label for="search-input">szukaj <span class="orange">auto</span></label>
    <input name="query" type="text" id="search-input" class="selectable" placeholder="np. audi a4 automat 2011" autocomplete="off" data-js="searchInput">
    <a class="akaLink submit-search" data-js="search-submit">OK</a>
    <a class="akaLink advanced-search" data-js="advanced-search">zaawansowane</a>
  </form>
  <div class="banner--container">
      <ul class="bxslider">
          <li>
              <p>
                <span class="orange-color"><span data-js="cars-counter"><?= $this->allCount ?></span> aut <span class="blue-color">Miasteczko Samochodowe Okęcie</span><br>
                     <span class="orange-color">czynne codziennie</span>
              </p>
          </li>
      </ul>
<script type="text/javascript">

    $('.bxslider').bxSlider({
        auto: ($(".bxslider li").length > 1) ? true: false,
        autoControls: true,
        speed: 1000,
        controls: false,
        pager: false
    });
</script>

  </div>
</div>
<div class="advanced--container" data-js="advanced--container">
  <div id="front_search_content">
    <?php
      $form = new Form_Search(array('variant' => 'front'));
                      $form->getElement('submit')->setLabel('SEARCH');
      echo $form;
    ?>

    <script type="text/javascript" charset="utf-8">
      var autoautoLang = "<?= $this->language ?>";

      var multiselectOptions = {
        checkAllText: "<?= $this->escape($this->translate->_('MULTISELECT_CHECK_ALL')) ?>",
        uncheckAllText: "<?= $this->escape($this->translate->_('MULTISELECT_UNCHECK_ALL')) ?>",
        selectedText: "<?= $this->escape($this->translate->_('MULTISELECT_SELECTED_TEXT')) ?>",
        minWidth: 270
      };

                      var multiselectOptions2 = {
                          checkAllText: "<?= $this->escape($this->translate->_('MULTISELECT_CHECK_ALL')) ?>",
                          uncheckAllText: "<?= $this->escape($this->translate->_('MULTISELECT_UNCHECK_ALL')) ?>",
                          selectedText: function(selectedCount,length,selected){
                              if(selectedCount == length) {
                                  return "<?= $this->escape($this->translate->_('ANY2')) ?>";
                              } else if(selectedCount == 1) {
                                  return $(selected[0]).attr('title');
                              } else {
                                  return "<?= $this->escape($this->translate->_('MULTISELECT_SELECTED_TEXT')) ?>".replace("#",selectedCount);
                              }
                          },

                          minWidth: 270
                      };

      $(document).ready(function(){
        $("#front_search .form_label").hide();

        $('#categories').multiselect($.extend({}, multiselectOptions, {classes: "for_categories", noneSelectedText: "<?= $this->escape($this->translate->_('CATEGORY')) ?>"}));
        $('#important_features').multiselect($.extend({}, multiselectOptions, {minWidth: 110,noneSelectedText: "<?= $this->escape($this->translate->_('IMPORTANT_FEATURES')) ?>"}));
        $('#engine').multiselect($.extend({}, multiselectOptions, {minWidth: 110, classes: 'engine', noneSelectedText: "<?= $this->escape($this->translate->_('ENGINE')) ?>"}));
        $('#gearboxes').multiselect($.extend({}, multiselectOptions2, {minWidth: 110, noneSelectedText: "<?= $this->escape($this->translate->_('GEARBOX')) ?>"}));
        $('#build').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('PRODUCTION_YEAR')) ?>"}));
        $('#odometer').multiselect($.extend({}, multiselectOptions, {minWidth: 110,noneSelectedText: "<?= $this->escape($this->translate->_('ODOMETER')) ?>"}));

        $("#front_search form").submit(function(){
          $(this).find("option[selected='']").removeAttr('selected');
        });

                          var fixedDisabledFeaturesIndexStart = 13;
                          var fixedDisabledFeaturesIndexStop = 18;
        var divideModulo = [18, 18, 19];
        $(".for_important_features ul.ui-multiselect-checkboxes").append('<div class="clear" />');
        var lastModulo = 0;
        for (var i in divideModulo) {
          $(".for_important_features ul.ui-multiselect-checkboxes>li:lt("+divideModulo[i]+")").wrapAll('<div class="features_column" />');
        }
                          $(".for_important_features ul.ui-multiselect-checkboxes li").slice(fixedDisabledFeaturesIndexStart, fixedDisabledFeaturesIndexStop).find('input').attr('checked', 'checked').attr('disabled', 'disabled').siblings('span').css('color', '#f67812');


        /*var buildFrom = $(".for_build_from").outerHtml();
        var buildTo =  $(".for_build_to").outerHtml();

        $(".for_build_from, .for_build_to").remove();
        $(".for_build div.ui-multiselect-menu").append(buildFrom, buildTo);
        $(".for_build_from input, .for_build_to input").css('color', '#333');
        $(".for_build_from, .for_build_to").css({'width': '115px', 'float': 'left'});
        $(".for_build_from").css({'padding-right': '27px'});
        $(".for_build_from input, .for_build_to input").css({'width': '105px'});*/


                          /*var cubicCapacityFrom = $(".for_cubic_capacity_from").outerHtml();
        var cubicCapacityTo =  $(".for_cubic_capacity_to").outerHtml();

        $(".for_cubic_capacity_from, .for_cubic_capacity_to").remove();
        $(".for_engine div.ui-multiselect-menu").append(cubicCapacityFrom, cubicCapacityTo);
        $(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css('color', '#333');
        $(".for_cubic_capacity_from, .for_cubic_capacity_to").css({'width': '115px', 'float': 'left'});
        $(".for_cubic_capacity_from").css({'padding-right': '27px'});
        $(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css({'width': '105px'});*/


        $("#front_search .input_text input").compactInputs({
          getLabelFn: function(el) {
            return $(el).closest(".form_input").siblings(".form_label").find("label");
          }
        });

      });
      </script>
  </div>
</div>

<div id="top_menu" data-layout="el/top_menu" style="display: none !important;">

    <div id="top_menu_left">
        <div class="item" id="top_menu_search">
            <form method="post" action="<?= $this->url(array('language' => $this->language), 'list', true); ?>">
                <input type="submit" name="" value="" id="find_car_quick_submit">
                <input type="text" name="query" value="" id="find_car_quick_value" />

            </form>
        </div>
        <div id="top_slogan">
            <?php
            $str = $this->translate->_('TOP_SLOGAN');
            $strParts = explode("%", $str);
            $i = 1;
            if (count($strParts) == 3) { ?>
                <span class="blue">1500</span>
                <?= $strParts[0] ?>
                <span class="red">7</span>
                <?= $strParts[1] ?>
                <span class="green">3</span>
                <?= $strParts[2] ?>

            <?php }	?>
        </div>
    </div>
    <div id="top_menu_right">
        <?php $cache = Zend_Registry::get('Cache'); ?>





        <div id="top_menu_languages">
            <?php foreach ($this->getEnabledLanguages() as $key => $value): ?>
                <div class="language language_<?= $key ?><? if ($key == $this->language):?> active<? endif; ?>">

                        <a href="<?= $this->url(array('language' => $key), null, false) ?><?php echo (!empty($this->changeLanguageData['queryString']) ? (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash']."&" : "?" ) . $this->changeLanguageData['queryString'] : (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash'] : "" ) ) ?>">&nbsp;</a>


                </div>
            <?php endforeach ?>
        </div>
        <div class="float_right">
            <?php if (!empty($this->identity)): ?>
                <?php if ($this->identity->role == "user"): ?>
                    <div class="item favourites">
                        <a href="<?= $this->url(array('language' => $this->language), 'favouriteList', true) ?>">
                            <img src="/images/heart3.png?ver=3" />
                            <span id="favourites-count"><?= $this->favouritesCount ?></span>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="item favourites">
                        <a href="<?= $this->url(array('language' => $this->language), 'favouriteListSession', true) ?>">
                            <img src="/images/heart3.png?ver=3" />
                            <span id="favourites-count"><?= $this->favouritesCount ?></span>
                        </a>
                    </div>
                <?php endif ?>
                <?php if ($this->identity->role == "user"): ?>
                    <div class="item"  id="top_menu_account">
                        <a href="<?= $this->url(array('language' => $this->language, 'controller' => 'user-cp'), 'general', true) ?>"><?= $this->translate->_('USER_CP') ?></a>
                    </div>
                <?php endif ?>
                <div class="item"  id="top_menu_logout">
                    <a href="<?= $this->url(array('language' => $this->language), 'logout', true) ?>"><?= $this->translate->_('LOG_OUT') ?></a>
                </div>
            <?php else: ?>
                <div class="item favourites">
                    <a href="<?= $this->url(array('language' => $this->language), 'favouriteListSession', true) ?>">
                        <img src="/images/heart3.png?ver=2" />
                        <span id="favourites-count"><?= $this->favouritesCount ?></span>
                    </a>
                </div>

            <?php endif ?>
            <?php if (empty($this->identity)): ?>
                <div class="item" id="top_menu_login">
                    <a href="<?= $this->url(array('language' => $this->language), 'login', true) ?>"><?= $this->translate->_('LOG_IN') ?></a>
                </div>
            <?endif;?>
        </div>
     </div>

</div>







<div style="display: none !important">
  <div id="logo">
              <a href="<?= $this->url(array('language' => $this->language), 'home', true) ?>"></a>
            </div>
  <div id="main_menu">

    <?php $cache = Zend_Registry::get('Cache'); ?>
    
    <?php if ($menuHtml = $cache->load('main_menu_' . $this->language)) : ?>
      <?= $menuHtml ?>
    <?php else: ?>
      <?php ob_start(); ?>

          <span class="item expandable" id="main_menu_shortcut">
        <a href="javascript:;" title="" ><?= $this->translate->_('SHORTCUT') ?><span class="for_icon">&nbsp;</span></a>
      </span>
      <span class="item">
        <a href="<?= $this->url(array('language' => $this->language), 'haveCarFound', true) ?>" title="" ><?= $this->translate->_('BUY2') ?></a>
      </span>
          <span class="item">
        <a href="<?= $this->url(array('language' => $this->language), 'sellCar', true) ?>" title="" ><?= $this->translate->_('SELL2') ?></a>
      </span>
          <span class="item">
        <a href="<?= $this->url(array('language' => $this->language), 'exchangeCar', true) ?>" title="" ><?= $this->translate->_('EXCHANGE2') ?></a>
      </span>
      <?php if(false && $this->language == 'pl'): ?>
      <span class="item">
        <a href="<?= $this->url(array('language' => $this->language), 'newCars', true) ?>" title="" ><?= $this->translate->_('NEW_CARS') ?></a>
      </span>
      <?php endif;?>
      <span class="item">
        <a href="<?= $this->url(array('language' => $this->language), 'financing', true) ?>" title="" ><?= $this->translate->_('FINANCING') ?></a>
      </span>
      <span class="item">
        <a href="<?= $this->url(array('language' => $this->language), 'service', true) ?>" title="" ><?= $this->translate->_('SERVICE') ?></a>
      </span>

      <span class="item last">
        <a href="<?= $this->url(array('language' => $this->language), 'locations', true) ?>" title="" ><?= $this->translate->_('CONTACT') ?></a>
      </span>
      
      
      <span id="main_menu_breaker"></span>
      
      <div id="main_menu_shortcut_content">
        <table cellspacing="0" cellpadding="0">
          <tr>
            <td class="bg_1">
               <div>
                <a href="<?= $this->url(array('language' => $this->language), 'list', true) ?>?addName[]=rental&amp;addValue_0=1" title="" >
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('RENTAL') ?>
                </a>
              </div>
              <div>
                <form>
                  <input type="hidden" name="promotions" value="1">
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_PROMOTIONS') ?>
                </a>
              </div>
                         
              <div>
                <form>
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_ALL_OFFERS') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="new" value="1" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_NEW_CARS') ?>
                </a>
              </div>
              
              <div>
                <form>
                  <input type="hidden" name="leasing" value="1" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_LEASING') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="last_2_years" value="1" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_LAST_2_YEARS') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="gearboxes[]" value="automatic" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_AUTOMATIC_GEARBOX') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="engine[]" value="fuels diesel" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_DIESEL') ?>
                </a>
              </div>
            </td>
            <td class="bg_2">
                
              <div>
                <form>
                  <input type="hidden" name="price_min" value="" />
                  <input type="hidden" name="price_max" value="15000" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_UP_TO_15K') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="price_min" value="15000" />
                  <input type="hidden" name="price_max" value="30000" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_15K_TO_30K') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="price_min" value="30000" />
                  <input type="hidden" name="price_max" value="50000" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_30K_TO_50K') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="price_min" value="50000" />
                  <input type="hidden" name="price_max" value="80000" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_50K_TO_80K') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="price_min" value="80000" />
                  <input type="hidden" name="price_max" value="120000" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_80K_TO_120K') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="price_min" value="120000" />
                  <input type="hidden" name="price_max" value="200000" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_120K_TO_200K') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="price_min" value="200000" />
                  <input type="hidden" name="price_max" value="" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_OVER_200K') ?>
                </a>
              </div>
            </td>
            <td class="bg_3">
                
              <div>
                <form>
                  <input type="hidden" name="categories[]" value="sedan" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_SEDAN') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="categories[]" value="combi" />
                  <input type="hidden" name="categories[]" value="hatchback" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_COMBI_HATCHBACK') ?>
                </a>
              </div>
              
              <div>
                <form>
                  <input type="hidden" name="categories[]" value="suv" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_OFFROAD_SUV') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="categories[]" value="van" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_VAN') ?>
                </a>
              </div>
              
              <div>
                <form>
                  <input type="hidden" name="categories[]" value="cabrio" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_CABRIO') ?>
                </a>
              </div>
              
              <div>
                <form>
                  <input type="hidden" name="categories[]" value="coupe" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_COUPE') ?>
                </a>
              </div>
              
              <div>
                <form>
                  <input type="hidden" name="categories[]" value="pickup" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_PICKUP') ?>
                </a>
              </div>
            </td>
            <td class="bg_4">
                
              <div>
                <form>
                  <input type="hidden" name="important_features[]" value="origin 24" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_IMPORT_USA') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="important_features[]" value="origin 21" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_SALON_POLSKA') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="exclusive" value="1" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_EXCLUSIVE') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="leasing_transfer" value="1" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_LEASING_TRANSFER') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="leasing" value="1" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('CARS_WITH_VAT') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="after_leasing_vindication" value="1" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_AFTER_LEASING_VINDICATION') ?>
                </a>
              </div>
                
              <div>
                <form>
                  <input type="hidden" name="premium" value="1" />
                </form>
                <a class="js">
                  <span class="for_icon">&nbsp;</span>
                  <?= $this->translate->_('SHORTCUT_PREMIUM') ?>
                </a>
              </div>
              
            </td>
          </tr>
        </table>
      </div>
      
    
      
      <script type="text/javascript" charset="utf-8">
        $(function(){
          jQuery('.headerItems > li > a.js').click(function() {
            var form = $(this).parent().find('form');
            form.attr({
              enctype: 'application/x-www-form-urlencoded',
              action: '/pl/list',
              method: 'post'
            });

            form.attr('action', "<?= $this->url(array('language' => $this->language), 'list', true) ?>").attr('method', 'post');
            $('<input type="submit" name="submit" value="" style="display: none;" />').appendTo(form).click();
            return false;

          });
          
          var quickSearchInput = $("#find_car_quick_value");
          quickSearchInput
            .data('oldValue', ''/*quickSearchInput.val()*/)
            .focus(function(){
              var qs = $(this);
              qs.select();
            })
            .blur(function(){
              var qs = $(this);
            })
          ;

                  $("#main_menu_shortcut").click(function() {
                      if($(this).hasClass('hover')) {

                          $("#main_menu_shortcut").removeClass('hover');
                          $("#main_menu_shortcut_content").hide();

                      } else {
                          $(this).addClass('hover');
                          $("#main_menu_shortcut_content").show();
                      }

                  });

          $("#main_menu_shortcut").hover(
            function(){
              $(this).addClass('hover');
              $("#main_menu_shortcut_content").show();
            },
            function(){
              var timeoutId = setTimeout(
                function(){
                  $("#main_menu_shortcut").removeClass('hover');
                  $("#main_menu_shortcut_content").hide();
                },
                50
              );
              $("#main_menu_shortcut_content").data('hide_me_timeout', timeoutId);
            }
          );
          
          $("#main_menu_shortcut_content").hover(
            function(){
              clearTimeout($(this).data('hide_me_timeout'));
            },
            function(){
              $(this).hide();
              $("#main_menu_shortcut").removeClass('hover');
            }
          );
          
          $("#main_menu_car").hover(
            function(){
              $(this).addClass('hover');
              $("#main_menu_car_content").css('left', $(this).offset().left - $(this).parent().offset().left).show();
            },
            function(){
              var timeoutId = setTimeout(
                function(){
                  $("#main_menu_car").removeClass('hover');
                  $("#main_menu_car_content").hide();
                },
                50
              );
              $("#main_menu_car_content").data('hide_me_timeout', timeoutId);
            }
          );
          
          $("#main_menu_car_content").hover(
            function(){
              clearTimeout($(this).data('hide_me_timeout'));
            },
            function(){
              $(this).hide();
              $("#main_menu_car").removeClass('hover');
            }
          );
          
          $("#main_menu .item").not(".expandable").hover(
            function(){
              $(this).addClass('hover');
            },
            function(){
              $(this).removeClass('hover');
            }
          );
          
          <?php if ($this->query): ?>
            quickSearchInput.focus().blur();
          <?php endif; ?>
        });
      </script>
      <?php
        $html = ob_get_flush();
        $cache->save($html, 'main_menu_' . $this->language, $tags=array('main_menu', $this->language, 'translate'));
      ?>
    <?php endif ?>

  </div>
  <div class="clear"></div>
</div>


<script type="text/javascript">
    
var menuItem = 1;
$(function () {
      $("#main_menu .item a").eq(menuItem).css('color', 'rgb(246, 120, 18)');
      menuItem++;
      setInterval(function(){
      
           
            $("#main_menu .item a").css('color', ' #E0E0E0');  
            $("#main_menu .item a").eq(menuItem).css('color', 'rgb(246, 120, 18)');
            menuItem++;
            if(menuItem > 3)
                menuItem = 1;
        }
    ,2000);
      
      
});

</script>
