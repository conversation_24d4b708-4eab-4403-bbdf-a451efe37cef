<?php if ($this->pageCount >= 2):
	?>
	<div class="paginator">
		<div class="paginator_inner">
			<?php if (isset($this->previous)): ?> 
			<div class="prev"><a href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => $this->previous))); ?>"></a></div>
			<?php endif; ?>

			<?php if (isset($this->next)): ?>
				<div class="next"><a href="<?php echo $this->url() .  '?'. http_build_query(array_merge($this->queryStringArray,array('page' => $this->next))); ?>"></a></div>
			<?php endif; ?>

			<div class="paginator_content">
				<?php if (!in_array(1, $this->pagesInRange)): ?>
					<span><a href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => 1))); ?>">1</a></span>
					<span>...</span>
				<?php endif ?>
				<?php foreach ($this->pagesInRange as $page): ?>
					<?php if ($page != $this->current): ?>
						<span><a href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => $page))) ?>"><?php echo $page;?></a></span>
					<?php else: ?>
						<span class="active"><?php echo $page;?></span>
				    <?php endif;?>
				<?php endforeach;?>
				<?php if (!in_array($this->pageCount, $this->pagesInRange)): ?>
					<span>...</span>
					<span><a href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => $this->pageCount))); ?>"><?= $this->pageCount ?></a></span>
				<?php endif ?>
				<?php if (isset($this->carListShowMax) && $this->carListShowMax): ?>
					<span><a href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => 1, 'perPage' => 'max'))); ?>"><?= str_replace("%items%", $this->carListShowMax, $this->translate->_('PAGINATOR_SHOW_ITEM_COUNT_PER_PAGE')) ?></a></span>
				<?php elseif (isset($this->carListShowDefault) && $this->carListShowDefault): ?>
					<span><a href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => 1, 'perPage' => 'default'))) ?>"><?= str_replace("%items%", $this->carListShowDefault, $this->translate->_('PAGINATOR_SHOW_ITEM_COUNT_PER_PAGE')) ?></a></span>
				<?php endif ?>
				
			</div>
		</div>
	</div>
<?php endif;?>
