<?php
//@ini_set('zlib.output_compression', 0);
//if (function_exists('apache_setenv')) @apache_setenv('no-gzip', '1');
//if (function_exists('header_remove')) @header_remove('Content-Length');
//while (ob_get_level() > 0) @ob_end_clean();
@ini_set('zlib.output_compression', 0);   // wyłącz kompresję zlib (PHP 5.5)
@header_remove('Content-Length');
//if (function_exists('apache_setenv')) { @apache_setenv('no-gzip', '1'); } // zgaś mod_deflate
//if (function_exists('header_remove')) { @header_remove('Content-Length'); } // usuń ręczny CL, jeśli ustawiany
//while (ob_get_level() > 0) { @ob_end_clean(); } // wyczyść wszelkie buffery

//@ini_set('zlib.output_compression', 0);      // wyłącz kompresję
//if (function_exists('header_remove')) {
//    header_remove('Content-Length');         // usuń ręczny CL jeśli jest
//}
//header('Content-Type: text/plain; charset=utf-8');
//echo "MOBILE TEST OK\n";
//exit;


/* ADDED BY MATEUSZ 2009-12-30 - Specific for this server ?? */
$path = "C:\Program Files\Zend\ZendServer\share\ZendFramework\library";

ini_set('session.save_path', '/data/www/beta.autoauto.pl/tmp/session/');

date_default_timezone_set("Europe/Warsaw");
setlocale(LC_CTYPE, 'pl_PL.utf8');
mb_internal_encoding("UTF-8");

set_include_path(get_include_path() . PATH_SEPARATOR . $path);

/* END */

// Define path to application directory
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../application'));

$application_env = 'production';

if(isset($_SERVER['SERVER_NAME']) && (strpos($_SERVER['SERVER_NAME'], '.weby.pl') !== false || strpos($_SERVER['SERVER_NAME'], 'localhost') !== false) )
	define('APPLICATION_ENV', 'development');

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'production'));


// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/../library'),
    get_include_path(),
)));

/** Zend_Application */
require_once 'Zend/Application.php';  


try {
	// Create application, bootstrap, and run
	$application = new Zend_Application(
	    APPLICATION_ENV, 
	    APPLICATION_PATH . '/configs/application.ini'
	);

	$application->bootstrap()
    	        ->run();
}
catch (Exception $e) {
	file_put_contents(APPLICATION_PATH . "/../logs/startup.txt", date("Y-m-d H:i:s", time()) . "\n" . $e->getMessage() . "\n\n" . $e->getTraceAsString());
}
