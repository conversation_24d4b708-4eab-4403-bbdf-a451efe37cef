<?php

class Model_UsersBuy extends Model_Base {
	

	public function add($data, $doTransaction=true) {

        $usersModel = new Model_Users();

        if (!isset($data['user_id'])) {
            //user not logged in - check existence and add new if necessary
            $select = $this->db->select()
                ->from($this->tables['users'], array('id'))
                ->where('email = ?', $data['email']);
            $data['user_id'] = $this->db->fetchOne($select);

            if (!$data['user_id']) {

                $data['user_id']= $usersModel->addQuick($data, $trans=false);
            }
        }

        // dla pewnosci export do sys2
        $user = $usersModel->getUser($data['user_id']);
        $usersModel->edit($data['user_id'], array('email' => $user['email']));

		try {
			if ($doTransaction) $this->db->beginTransaction();

			$addData = array(
				'user_id' => $data['user_id'],
				'added_datetime' => new Zend_Db_Expr('NOW()'),
				'updated_datetime' => new Zend_Db_Expr('NOW()'),
                'comments' => $data['comments'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'first_name' => $data['first_name']

			);

			$this->db->insert(
				$this->tables['users_cars_buy'],
				$addData
			);

			
			$id = $this->db->lastInsertId($this->tables['users_cars_buy'], 'id');



			if ($doTransaction) $this->db->commit();
			
			return $id;
		}
		catch (Exception $e) {
			if ($doTransaction) $this->db->rollBack();


			throw $e;
		}
	}

    public function get($id) {

        $select = $this->db->select()
            ->from(array('ucb' => $this->tables['users_cars_buy']))
            ->where('ucb.id = ?', $id);

        $result = $this->db->fetchRow($select);

        return $result;


    }

    public function update($id, $data, $doTransaction=true) {

        try {
            if ($doTransaction) $this->db->beginTransaction();

            $newData = array();

            $fields = array(
                'user_id',
                'added_datetime' ,
                'updated_datetime',
                'comments',
                'caretaker_sr_id',
                'email',
                'phone',
                'first_name',
                'is_visible',
            );


            foreach ($fields as $field) {
                if (isset($data[$field]))
                    $newData[$field] = $data[$field];
            }


            $this->db->update(
                $this->tables['users_cars_buy'],
                $newData, 'id = '. $id
            );




            if ($doTransaction) $this->db->commit();

            return $id;
        }
        catch (Exception $e) {
            if ($doTransaction) $this->db->rollBack();

            throw $e;
        }
    }

}