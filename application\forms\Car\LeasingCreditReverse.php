<?php

class Form_Car_LeasingCreditReverse extends My_Form {
	
	protected $_type = 'credit';
	
	public function init() {

		$view = Zend_Layout::getMvcInstance()->getView();
		$cars = new Model_Cars_Cars();
		$instalmentsNo = $cars->getInstalmentNoOptions($this->_type);
		
		$this->addElements(array(
			new Zend_Form_Element_Text('instalment', array(
				'label'	=>	$view->translate->_('INSTALLMENT_VALUE') . ' [' . $view->language_row['currency'] . ' ' . $view->translate->_('brutto') . ']',
				'required' => true,
                'attribs' => array('class' => 'form-control'),
				'validators' => array(new Zend_Validate_GreaterThan(0)),
				'value' => 3000
			)),
			new Zend_Form_Element_Select('type', array(
				'label' => 'FINANCING_TYPE',
                'attribs' => array('class' => 'form-control'),
				'multiOptions' => array(
					'credit' => 'LOAN',
					'leasing' => 'LEASING'
				),
				'value' => $this->_type
			)),
			new Zend_Form_Element_Text('contribution', array(
				'label'	=>	$view->translate->_('CONTRIBUTION') . ' [' . $view->language_row['currency'] . ' ' . $view->translate->_('brutto') . ']',
                'attribs' => array('class' => 'form-control'),
				'required' => false,
				'value' => 3000,
				'validators' => array(new Zend_Validate_GreaterThan(0))
			)),
			new Zend_Form_Element_Select('contribution_select', array(
				'label'	=>	'CONTRIBUTION',
                'attribs' => array('class' => 'form-control'),
				'required' => false,
				'value' => 0.2
			)),
			new Zend_Form_Element_Select('instalmentsNo', array(
				'label'	=>	'INSTALLMENTSNO',
                'attribs' => array('class' => 'form-control'),
				'required' => false,
				'multiOptions' => $instalmentsNo,
				'value' => 36
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label'	=>	'LEASINGCREDIT_CALCULATE',
        'attribs' => array('class' => ' btn btn-action btn-action-gray2'),
			)),
		));
		parent::init();
	}
	
	public function setType($value) {
		$this->_type = $value;
	}
	
}