<?php

class Model_Dates {
	
	public function dayDifference($later, $earlier) {
		if (!is_numeric($later)) $later = strtotime($later);
		if (!is_numeric($earlier)) $earlier = strtotime($earlier);
		
		return ($later - $earlier) / (60 * 60 *24);
	}
	
	public function getMonthNow() {
		return (int)date("m", time());
	}
	
	public function getMonthPrevious($intMonth=null) {
		if ($intMonth <= 12 && $intMonth >= 1) {
			return (($intMonth + 10) % 12) + 1;
		}
		else {
			$month = $this->getMonthNow();
			return (($month + 10) % 12) + 1;
		}
	}
	
	public function getMonths() {
		return array(
			'1'		=>	'Styczeń',
			'2'		=>	'<PERSON>ty',
			'3'		=>	'Marzec',
			'4'		=>	'Kwiecień',
			'5'		=>	'Maj',
			'6'		=>	'Czerwiec',
			'7'		=>	'Lipiec',
			'8'		=>	'Sierpień',
			'9'		=>	'<PERSON><PERSON><PERSON><PERSON><PERSON>',
			'10'	=>	'<PERSON><PERSON><PERSON><PERSON><PERSON>',
			'11'	=>	'Listopad',
			'12'	=>	'<PERSON><PERSON>zie<PERSON>'
		);
	}
	
	public function getYearNow() {
		return (int)date("Y", time());
	}
	
	public function getYearPreviousMonth($intYear=null, $intMonth=null) {
		//returns the year that was month ago... it's the same unless it's january now
		if ($intMonth == null) $intMonth = $this->getMonthNow();
		if ($intYear == null) $intYear = $this->getYearNow();
		if ($intMonth > 1) return $intYear;
		else return $intYear - 1;
	}
	
	public function getYearsForTransaction() {
		$now = $this->getYearNow();
		$ret = array();
		for ($y = 2010; $y <= $now; $y++) {
			$ret[$y] = $y;
		}
		return $ret;
	}
	
	public function getYearsForTransactionEmployee($monthCurrent, $monthsAhead=0) {
		if ($monthCurrent === null) (int)$monthCurrent = date("m", time());
		$yearsAhead = 0;
		if ($monthsAhead > 0) {
			$yearsAhead = $monthsAhead / 12;
			if ($monthsAhead > 12 - $monthCurrent) $yearsAhead++;
		}
		
		$now = $this->getYearNow();
		$limit = $now + $yearsAhead;
		$ret = array();
		for ($y = 2010; $y <= $limit; $y++) {
			$ret[$y] = $y;
		}
		return $ret;
	}
	
	public function isInFuture($year, $month) {
		$y = $this->getYearNow();
		$m = $this->getMonthNow();
		if ((int)$year > $y) return true;
		if ((int)$year == $y && (int)$month > $m) return true;
		return false;
	}
	
	public function isInPast($year, $month) {
		$y = $this->getYearNow();
		$m = $this->getMonthNow();
		if ((int)$year < $y) return true;
		if ((int)$year == $y && (int)$month < $m) return true;
		return false;
	}
	
	public function isInPastOrNow($year, $month) {
		$y = $this->getYearNow();
		$m = $this->getMonthNow();
		if ((int)$year < $y) return true;
		if ((int)$year == $y && (int)$month <= $m) return true;
		return false;
	}
	
	public function isNow($year, $month) {
		$y = $this->getYearNow();
		$m = $this->getMonthNow();
		if ((int)$year == $y && (int)$month == $m) {
			return true;
		}
		else return false;
	}
	
	public function monthDifference($later, $earlier) {
		if (!is_numeric($later)) $later = strtotime($later);
		if (!is_numeric($earlier)) $earlier = strtotime($earlier);
		
		$yearLater = date("Y", $later);
		$monthLater = date("m", $later);
		
		$yearEarlier = date("Y", $later);
		$monthEarlier = date("m", $earlier);
		
		$monthDiff = ($yearLater - $yearEarlier) * 12 + $monthLater - $monthEarlier;
		return $monthDiff;
	}
	
}