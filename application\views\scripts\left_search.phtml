<div id="save-form" style="display: none">
	<?
		if ($this->saveForm) {
			//$this->saveForm->setAction($this->url(array('language' => $this->language), 'list', false) . "?hash=" . $this->hash);
			echo '<div class="pp_html"><br /><h2>' . $this->translate->_('SAVE_FORM_TITLE') . '</h2>';
			echo $this->translate->_('SAVE_FORM_INSTRUCTION') . "<br /><br />";
			echo $this->saveForm;
			echo '</div>';
		}
 	?>
</div>
<div id="full_form">
	
	<?php if (Zend_Auth::getInstance()->hasIdentity() && !$this->searchStored && $this->saveForm): ?>
		<?php if (Zend_Auth::getInstance()->getIdentity()->role == "user"): ?>
			<a id="search_save" href="#save-form" rel="prettyPhoto" title=""><span class="for_icon">&nbsp;</span><?= $this->translate->_('SAVE_SEARCH') ?></a>
		<?php elseif (Zend_Auth::getInstance()->getIdentity()->role == "salesman"): ?>
			<a id="search_save" href="#save-form" rel="prettyPhoto" title=""><span class="for_icon">&nbsp;</span><?= $this->translate->_('SAVE_SEARCH') ?></a>
		<?php endif ?>		
	<?php elseif ($this->saveForm): ?>
		<a id="search_save" href="#save-form" rel="prettyPhoto" title=""><span class="for_icon">&nbsp;</span><?= $this->translate->_('SAVE_SEARCH') ?></a>
	<?php endif ?>
	
	<h2>
		&nbsp;
		<div class="form_item form_item_submit for_submit ">
			<div class="form_input input_submit for_submit ">
				<input type="submit" helper="formSubmit" value="<?= $this->translate->_('FILTER') ?>" id="submit" name="submit">
			</div>
		</div>
	</h2>
	
	<?php if ($this->storedSearches): ?>
		<div id="stored_searches">
			<?php foreach ($this->storedSearches as $searchItem): ?>
				<div class="item">
					<a class="item<?= $searchItem['id'] == $this->searchStored ? ' active' : '' ?>" href="<?= $this->url(array('language' => $this->language), 'searchList', true); ?>?set=<?= $searchItem['id']; ?>">
						<span class="for_icon">&nbsp;</span>
						<?= $this->escape($searchItem['title']) ?>
					</a>
					<a class="confirm-delete action" href="<?= $this->url(array('language' => $this->language), 'searchList', true); ?>?del=<?= $searchItem['id']; ?>">
						<span class="for_icon">&nbsp;</span>
						<?= $this->translate->_('DELETE') ?>
					</a>
					<a class="action" href="<?= $this->url(array('id' => $searchItem['id']), 'editSearchList'); ?>">
						<span class="for_icon">&nbsp;</span>
						<?= $this->translate->_('EDIT') ?>
					</a>
				</div>
			<?php endforeach ?>
		</div>
	<?php endif ?>


    <h2 class="red" style="font-size: 13px;"><?php // $this->translate->_('FILTER')?> <?= $this->paginator->getTotalItemCount() ?> wybranych aut</h2>
	<?php if ($this->otherMakesLayout == "top"): ?>

			
		<?php if ($this->makesWithCounts && count($this->makesWithCounts) > 0): ?>
			<div id="left_makes">
				<?= $this->partial(
					'make_boxes.phtml',
					array(
						'view' => $this,
						'makesWithCounts' => $this->makesWithCounts,
						'linkToAddParam' => true,
						'types' => $this->types,
						'queryStringArray' => $this->queryStringArray
					)
				) ?>
			</div>	
		<?php endif ?>
		
		<?php if ($this->exclusiveMakesWithCounts && count($this->exclusiveMakesWithCounts) > 0): ?>		
			<div id="left_exclusive">
				<?= $this->partial(
					'make_boxes.phtml',
					array(
						'view' => $this,
						'makesWithCounts' => $this->exclusiveMakesWithCounts,
						'linkToAddParam' => true,
						'types' => $this->types
					)
				) ?>
			</div>	
		<?php endif ?>
		
		<?php if ($this->otherCategoriesWithCounts && count($this->otherCategoriesWithCounts) > 0): ?>		
			<div id="left_other_categories">
				<?= $this->partial(
					'category_boxes.phtml',
					array(
						'view' => $this,
						'catsWithCounts' => $this->otherCategoriesWithCounts,
						'linkToAddParam' => true,
						'types' => $this->types,
					)
				) ?>
			</div>	
		<?php endif ?>
		
	<?php endif ?>

	<div id="left_form">

		<?php echo $this->form->renderForm(false);  ?>

		<?php foreach($this->form->getElements() as $element) :
				$element->getDecorator('Label')->removeOption('tag');
				$element->getDecorator('HtmlTag')->removeOption('tag');

		?>

		<? endforeach ;?>
			<div class="row">
				<h3 class="orange-color"><?= $this->translate->_('PRICE') ?></h3>
			</div>
			<div class="row">
				<div class="col-50"><?= $this->form->price_min->renderViewHelper() ?></div>
				<div class="col-50"><?= $this->form->price_max->renderViewHelper() ?></div>
			</div>
		<div class="row">


			<div class="col-50">
				<div class='form-label'><?= $this->form->invoice->getLabel() ?>
					<span class="question-mark tooltip" data-tooltip="<h3>3 możliwości sprzedaży:</h3>
					<ol>
					<li>Faktura VAT potrzebna jest do leasingu operacyjnego i odliczeń VAT</li>
					<li>Faktura VAT Marża umożliwia leasing operacyjny, Klient nie płaci 2% PCC</li>
					<li>Umowa kupna-sprzedaży, sprzedaż bezpośrednia 2% PCC</li>
					</ol>">?</span>
				</div>
			</div>
			<div class="col-50">
				<?= $this->form->invoice->renderViewHelper() ?><label for="invoice" class="optional"></label>
			</div>

		</div>
		<div class="row">
			<h3 class="blue-color">Wiek</h3>
		</div>
		<div class="row">
			<div class="col-50"><?= $this->form->build_from->renderViewHelper() ?></div>
			<div class="col-50"><?= $this->form->build_to->renderViewHelper() ?></div>
		</div>

		<div class="row">
			<div class="col-100"><?= $this->form->odometer_to->renderViewHelper() ?></div>
		</div>
		<div class="row">
			<h3 class="green-color">Silnik/Skrzynia/Napęd</h3>
		</div>
		<div class="row">
			<div class="col-30"><?= $this->form->engine->renderLabel() ?></div>
			<div class="col-70"><?= $this->form->engine->renderViewHelper() ?></div>
		</div>
		<div class="row">
			<div class="col-50"><?= $this->form->cubic_capacity_from->renderViewHelper() ?></div>
			<div class="col-50"><?= $this->form->cubic_capacity_to->renderViewHelper() ?></div>
		</div>
		<div class="row">
			<div class="col-30"><?= $this->form->gearboxes->renderLabel() ?></div>
			<div class="col-70"><?= $this->form->gearboxes->renderViewHelper() ?></div>
		</div>
		<div class="row">
			<div class="col-30"><?= $this->form->drives->renderLabel() ?></div>
			<div class="col-70"><?= $this->form->drives->renderViewHelper() ?></div>
		</div>
		<div class="row">
			<h3>Komfort</h3>
		</div>
		<div class="row list">
			<ul>
				<?php
				$elem = $this->form->important_features;
				$elemName = $elem->getName();
				$values = $elem->getValue();
				foreach($elem->getMultiOptions() as $option=>$value){ ?>
					<li class="row">
						<div class="col-70">
							<div class='form-label'><?= $value ?></div>
						</div>
						<div class="col-30">
							<input type="checkbox" class="cmn-toggle cmn-toggle-round" name="<?php echo $elemName; ?>[]" id="<?php echo $elemName; ?>-<?php echo $option; ?>" value="<?php echo $option; ?>" <?php if($values && in_array($option, $values)){ echo ' checked="checked"'; }?> />

							<label for="<?php echo $elemName; ?>-<?php echo $option; ?>" ></label>

						</div>
					</li>
				<?php } ?>
			</ul>

		</div>
		<div class="row">
			<h3>Nadwozie</h3>
		</div>
		<div class="row list categories">
			<ul>
				<?php
				$elem = $this->form->categories;
				$elemName = $elem->getName();
				$values = $elem->getValue();
				foreach($elem->getMultiOptions() as $option=>$value){ ?>
					<li class="row">
						<div class="col-70">
							<div class='form-label'><?= $value ?></div>
						</div>
						<div class="col-30">
							<input type="checkbox" class="cmn-toggle cmn-toggle-round" name="<?php echo $elemName; ?>[]" id="<?php echo $elemName; ?>-<?php echo $option; ?>" value="<?php echo $option; ?>" <?php if($values && in_array($option, $values)){ echo ' checked="checked"'; }?> />

							<label for="<?php echo $elemName; ?>-<?php echo $option; ?>" ></label>
							<span></span>
						</div>
					</li>
				<?php } ?>
			</ul>

		</div>


		<?= $this->form->order->renderViewHelper() ?>

	<div class="form_item form_item_submit for_submit "><div class="form_input input_submit for_submit "><input type="submit" name="submit" id="submit" value="Filtruj" helper="formSubmit"></div></div>


		</form>
	</div>
	
	<?php if ($this->otherMakesLayout == "bottom"): ?>
		
		<?php
			if ($this->paginator) {
				$count = $this->paginator->getTotalItemCount();
		?>
			<h2 class="red"><?= $this->translate->_('OTHER_MAKES') ?></h2>
		<?
			}
		?>
			
		<?php if ($this->makesWithCounts && count($this->makesWithCounts) > 0): ?>
			<div id="left_makes">
				<?= $this->partial(
					'make_boxes.phtml',
					array(
						'view' => $this,
						'makesWithCounts' => $this->makesWithCounts,
						'linkToAddParam' => true,
						'types' => $this->types,
						'queryStringArray' => $this->queryStringArray
					)
				) ?>
			</div>
		<?php endif ?>
		
		<?php if ($this->exclusiveMakesWithCounts && count($this->exclusiveMakesWithCounts) > 0): ?>
			<div id="left_exclusive">
				<?= $this->partial(
					'make_boxes.phtml',
					array(
						'view' => $this,
						'makesWithCounts' => $this->exclusiveMakesWithCounts,
						'linkToAddParam' => true,
						'types' => $this->types
					)
				) ?>
			</div>
		<?php endif ?>

		<?php if ($this->otherCategoriesWithCounts && count($this->otherCategoriesWithCounts) > 0): ?>
			<div id="left_other_categories">
				<?= $this->partial(
					'category_boxes.phtml',
					array(
						'view' => $this,
						'catsWithCounts' => $this->otherCategoriesWithCounts,
						'linkToAddParam' => true,
						'types' => $this->types
					)
				) ?>
			</div>
		<?php endif ?>

	<?php endif ?>

	<?php if(isset($this->showLeftDescription) && $this->showLeftDescription): ?>

		<div class="left-description">
			<p>W ofercie naszego komisu samochodowego zainteresowani kierowcy znajdą też terenowe auta używane. Warszawa
			-&nbsp;na&nbsp;terenie tego miasta znajdą się aż trzy punkty naszego komisu, proponujące najwyższej klasy, pochodzące
			od&nbsp;czołowych producentów używane samochody z salonu oraz&nbsp;auta terenowe używane, w bardzo dobrym stanie
			technicznym, z przejrzystą sytuacją prawną. Polecane samochody używane (osobowe i terenowe) znajdują
			się&nbsp;w&nbsp;atrakcyjnych i niewygórowanych cenach, a każdego dnia w&nbsp;naszej ofercie pojawia się przynajmniej dziesięć
			nowych pojazdów. W przypadku niezdecydowania co do konkretnego modelu pracujący w naszych komisach
			specjaliści, z&nbsp;pewnością pomogą Państwu wybrać najlepsze i&nbsp;spełniające postawione kryteria samochody używane.</p>

			<p>Sprzedaż odbywa się na podstawie prostych i klarownych procedur, bez zbędnych formalności, w zgodzie
			z obowiązującymi przepisami prawa, zapraszamy siedem dni w tygodniu. Dostępne w naszych komisach samochody
			osobowe używane to szansa na zakup bezpiecznego i bogato wyposażonego auta, w przystępnej cenie. Dostępne
			w naszej propozycji samochody używane (sprzedaż -&nbsp;Warszawa i cały region - stąd zgłasza się największa
			liczba kierowców) oferowane są na dogodnych warunkach finansowania.</p>

		</div>
	<?php endif;?>
</div>

<script type="text/javascript" charset="utf-8">
	$(document).ready(function(){
		$("#left_search h2 .form_item")
			.click(function(){
				$("#full_form form input[type='submit']").click();
			})
			.show();
		
		var autoautoLang = "<?= $this->language ?>";
		
		var confirmDeleteText = "<?= $this->escape($this->translate->_('CONFIRM_DELETE')) ?>";
		$(".confirm-delete").click(function(){
			return confirm(confirmDeleteText);
		});




		
		<?php if ($this->showSaveForm): ?>
			$("#search_save").click();
		<?php endif; ?>
	
	});
</script>