<?php

class IndexController extends Zend_Controller_Action
{

	public function init() {
		$this->_helper->layout->setLayout('layout');

        $cache = Zend_Registry::get('Cache');
        if(($cache->load('all_count')) === false) {

            $cars = new Model_Cars_Cars();
            $this->view->allCount = count($cars->search());
            $cache->save($this->view->allCount, 'all_count', array('cars'));

        } else {
            $this->view->allCount = $cache->load('all_count');
        }


    }
	
	public function aboutUsAction() {
		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'aboutUs', true) => $this->view->translate->_('ABOUT_US')
		);
		
		$this->view->containerClass = "with_photo";
	}
	
	public function aboutUsWorkAction() {
		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'aboutUs', true) => $this->view->translate->_('ABOUT_US'),
			$this->view->url(array('language' => $this->view->language), 'aboutUsWork', true) => $this->view->translate->_('ABOUT_US_WORK')
		);
		
		$this->view->containerClass = "with_photo";
	}
	
	public function activateUserAction() {
		$userId = (int)$this->_request->getParam('uid');
		$hash = $this->_request->getParam('hash');
		
		$users = new Model_Users();
		$success = $users->activate($userId, $hash);
		
		if ($success) {
			$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $userId), Zend_Log::INFO);
			$this->view->messenger->addMessage($this->_helper->translate('USER_ACTIVATION_OK'));
		}
		else {
			$this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $userId), Zend_Log::WARN);
			$this->view->messenger->addMessage($this->_helper->translate('USER_ACTIVATION_ERROR'));
		}
		$this->_helper->redirectBack('');
	}
	
	public function carAction() {
		$this->view->containerClass = "with_photo";
	}
	
	public function exportAction() {
		if ($this->view->language == "pl") {
			$this->_redirect('/pl');
		}
		
		$this->view->containerClass = "with_photo";
		
		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'export', true) => $this->view->translate->_('EXPORT')
		);
	}

	public function financingAction() {

        $this->view->menuActive = 'financeCar';

		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'financing', true) => $this->view->translate->_('FINANCING')
		);
		
		$cache = Zend_Registry::get('Cache');
		$this->view->cache = $cache;
		if ($html = $cache->load('financing_specialists_' . $this->_request->getParam('language'))) {
			$this->view->html = $html;
		}
		else {
			$employeesModel = new Model_Employees();
			$employees = $employeesModel->getFinancing();
			$this->view->employees = $employees;
		}
		
		$cars = new Model_Cars_Cars();
		$form = new Form_Car_LeasingCreditReverse(array('type' => 'credit'));
		$form->removeElement('contribution_select');

		$financingForm = new Form_Financing();
		
		if ($this->_request->isPost()) {
            $financing = $this->_request->getParam('financing', 0);
		    if($financing) {

                if ($financingForm->isValid($this->_request->getPost())) {

                    $data = $financingForm->getValues();
                    $transfersModel = new Model_Cars_Transfers();
                    $transfersModel->contactFinancing($data);

                    $this->_helper->redirectBack('', $this->view->url(array('language' => $this->_request->getParam('language')), 'financing', true));


                }

            } else {
                $type = $this->_request->getParam('type', 'credit');
                $form = new Form_Car_LeasingCreditReverse(array('type' => $type));
                if ($type == 'credit') {
                    $form->removeElement('contribution_select');
                } else {
                    $contribs = $cars->getInstalmentContributions('leasing');
                    $form->removeElement('contribution');
                    foreach ($contribs as $val) {
                        $form->getElement('contribution_select')->addMultiOption(
                            $val,
                            ($val * 100) . '%'
                        );
                    }
                    //some form element magic thanks to setName() bug :]
                    $form->contribution_select->setOrder($form->contribution_select->getOrder() + 2);
                    $form->addElement($form->contribution_select, 'contribution');
                    $form->contribution->setName('contribution');
                    $form->contribution->setOrder($form->contribution_select->getOrder());
                    $form->removeElement('contribution_select');
                }

                if ($form->isValid($this->_request->getPost())) {
                    $data = $form->getValues();

                    $calc = $cars->getAffordableCarPrice($data);
                    $this->view->calc = $calc;
                    $this->view->calcType = $data['type'];

                    $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
                    $spreads = $opt['instalments']['suggestedCars']['spread'];

                    $searchForm = new My_Form();
                    $searchForm->setAction($this->view->url(array('language' => $this->view->language), 'list', true));
                    $spreadUp = $calc['gross'] * (1 + $spreads['up']);
                    $spreadDown = $calc['gross'] * (1 - $spreads['down']);
                    $searchForm->addElements(array(
                        new Zend_Form_Element_Hidden('price_max', array(
                            'value' => round($spreadUp / 100) * 100
                        )),
                        new Zend_Form_Element_Hidden('price_min', array(
                            'value' => round($spreadDown / 100) * 100
                        )),
                        new Zend_Form_Element_Hidden('search_by_price_gross', array(
                            'value' => '1'
                        )),
                        new Zend_Form_Element_Submit('submit', array(
                            'label' => 'CALCULATION_SHOW_CARS',
                            'attribs' => array('class' => ' btn btn-action btn-action-transparent'),
                        ))
                    ));
                    $searchForm->init();
                    $this->view->searchForm = $searchForm;

                }//isValid
                else {
                }
            }
		}//isPost
		$this->view->form = $form;
		$this->view->financingForm = $financingForm;
	}
	
	public function financingGetReverseFormAction() {
		if ($this->_request->isXmlHttpRequest()) {
			$params = $this->_request->getParams();
			$form = new Form_Car_LeasingCreditReverse(array('type' => $params['type']));
			if (!array_key_exists('type', $params)) {
				$this->_helper->json();
				return;
			}
			
			$form->populate($params);
				
			if ($params['type'] == 'credit') {
				$form->removeElement('contribution_select');
				$form->populate(array('contribution' => 3000));
			}
			else {
				$cars = new Model_Cars_Cars();
				$contribs = $cars->getInstalmentContributions('leasing');
				$form->removeElement('contribution');
				foreach ($contribs as $val) {
					$form->getElement('contribution_select')->addMultiOption(
						$val,
						($val * 100) . '%'
					);
				}
				//some form element magic thanks to setName() bug :]
				$form->contribution_select->setOrder($form->contribution_select->getOrder() + 2);
				$form->addElement($form->contribution_select, 'contribution');
				$form->contribution->setName('contribution');
				$form->contribution->setOrder($form->contribution_select->getOrder());
				$form->removeElement('contribution_select');
			}
			
			$this->_helper->json(array('html' => $form->render()));
		}
		else {
			exit;
		}
	}

	public function indexAction() {
		$this->_helper->setRedirBack('register');
		$this->_helper->setRedirBack('login');
		$this->_helper->setRedirBack('logout');
		$this->_helper->setRedirBack('user_cp_edit');
		$this->view->htmlBodyClass = "front";
		
		$locations = new Model_Locations;
		$this->view->locations = $locations->getLocationGroups($assoc=false);
		
		$employees = new Model_Employees();
		$randomEmployees = $employees->getRandom($withPhoto=true, $financing=false, $count=1);
		$this->view->randomEmployees = $randomEmployees;
		
		$cars = new Model_Cars_Cars();
		$this->view->makesWithCounts = $cars->getCarMakesWithCountsByNames($types=array(1), array("NULL", "PREMIUM", "EXCLUSIVE"));
		$this->view->makesWithCountsTotalCount = $cars->getCarMakesWithCountsByNames($types=array(1), array("NULL", "PREMIUM", "EXCLUSIVE"), $getCount=true);
		//$this->view->exclusiveMakesWithCounts = $cars->getCarMakesWithCountsByNames($types=array(1), array("EXCLUSIVE"));
		$this->view->exclusiveMakesWithCountsTotalCount = $cars->getCarMakesWithCountsByNames($types=array(1), array("EXCLUSIVE"), $getCount=true);
		$this->view->otherCategoriesWithCounts = $cars->getCarCategoriesWithCounts(array(1,2, 3, 4),false,true);
		$this->view->otherCategoriesWithCountsTotalCount = $cars->getCarCategoriesWithCounts(array(1,2, 3, 4), $getCount=true);

		$this->view->carTypesWithCounts = $cars->getCarTypesWithCounts();


		$randomCars = $cars->getLatestCarsWithPhotos();
		$this->view->randomCars = $randomCars;

        $cache = Zend_Registry::get('Cache');


		
        $advertisingBar = new Model_AdvertisingBar();      
        $this->view->advertisingBarText = $advertisingBar->getAdvertisingText($this->view->language);
        
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$this->view->domain =Zend_Registry::get('siteDomain');

        $this->view->topSearchForm  = new Form_NewSearch();
        $this->view->topSearchForm->getElement('submit')->setLabel('SEARCH');

		if($this->view->language == 'pl') {
			$this->view->siteTitle = "Komisy samochodowe Warszawa, luksusowe auta używane | Autoauto.pl";
			$this->view->metaDescription = "Komis samochodowy Warszawa. U nas znajdziesz nie tylko samochody osobowe, ale i auta terenowe, czy luksusowe auta używane. Zapraszamy do zapoznania sie z naszą ofertą.";
		}
	}
	
	public function locationDetailsAction() {

        $this->view->menuActive = 'contactCar';

		$id = (int)$this->_request->getParam('id');
		$this->view->locationId = $id;

		$locations = new Model_Locations();
		$location = $locations->getLocation($id, $withLocationGroup=true);

		if(!$location) {
			throw new Zend_Controller_Action_Exception('This page does not exist', 404);
		}


		$cache = Zend_Registry::get('Cache');
		
		//breadcrumb set in cached html!
		
		if ($html = $cache->load('location_' . $id . '_' . $this->_request->getParam('language')) && $breadcrumb = $cache->load('breadcrumb_location_' . $id . '_' . $this->_request->getParam('language'))) {
			$this->view->html = $html;
			$this->view->breadcrumb = $breadcrumb;
		}
		else {

			
			$employees = new Model_Employees();
			$this->view->employees = $employees->getByLocation($id, $withLanguages=true);
			
			$this->view->location = $location;
			$this->view->cache = $cache;
			
			$this->view->breadcrumb = array(
				$this->view->url(array('language' => $this->view->language), 'locations', true) => $this->view->translate->_('CONTACT'),
				$this->view->url(array('language' => $this->view->language, 'id' => $location['location_id'], 'description' => My_FilterPermalink::filter($location['location_group']['address'] . ' ' . $location['name'])), 'locationDetails', true) => $location['location_group']['business_name'] . ', ' . $this->view->translate->_('LOCATION_POINT') . ' ' . $location['name']
			);
			$cache->save($this->view->breadcrumb, 'breadcrumb_location_' . $id, $tags=array('route', 'breadcrumb', 'location', $this->view->language, 'translate'));
		}
		
		if ($locationGroupsHtml = $cache->load('location_groups_with_locations_' . $this->_request->getParam('language'))) {
			$this->view->locationGroupsHtml = $locationGroupsHtml;
		}
		else {
			$locations = new Model_Locations();
			$this->view->locationGroups = $locations->getLocationGroupsWithLocations();
			$this->view->cache = $cache; //cache output in view
		}
	}
	
	public function locationsAction() {

        $this->view->menuActive = 'contactCar';

		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'locations', true) => $this->view->translate->_('CONTACT')
		);
		
		$cache = Zend_Registry::get('Cache');
		$this->view->cache = $cache;
		
		$language = $this->_request->getParam('language');

		if ($language != "pl") {
			if ($languageEmployeesHtml = $cache->load('language_emlpoyees_' . $language)) {
				$this->view->languageEmployeesHtml = $languageEmployeesHtml;
			}
			else {
				$employees = new Model_Employees();
				$this->view->languageEmployees = $employees->getByLanguage($language);
			}
		}
		
		if (
			($locationGroupsHtml = $cache->load('location_groups_with_locations_' . $language))
			&& ($locationGroupsContent = $cache->load('location_groups_with_locations_content' . $language))
		) {
			$this->view->locationGroupsHtml = $locationGroupsHtml;
			$this->view->locationGroupsContent = $locationGroupsContent;
		}
		else {
			$locations = new Model_Locations();
			$this->view->locationGroups = $locations->getLocationGroupsWithLocations();
		}
	}
	
	public function loginAction() {
		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'login', true) => $this->view->translate->_('LOGIN_PAGE_HEADER')
		);
		
		if (!$this->_request->isPost() && in_array($this->_request->getParam('set_redir_back'), array("list_save_form", "show_reservation_form", "user_cp", "new_car_pay"))) {
			if($this->_request->getParam('set_redir_back') == 'user_cp')
                $url = $this->view->url(array('language' => $this->view->language, 'controller' => 'user-cp', 'action' => 'edit'), 'general', true);
            else    
                $url = $this->_request->getServer('HTTP_REFERER');
			
			if (stripos($url, 'hash=') === false) {
				$url .= '?hash=' . $this->_request->getParam('hash');
			}
			$url .= '&show_' . $this->_request->getParam('show') . '=true';
			
			$this->_helper->setRedirBack(
				'login',
				$url
			);
			$this->_helper->setRedirBack(
				'register',
				$url
			);
		}
		
		$action = ($this->_request->isPost() ? $this->_request->getPost('action') : null);
		$showPassword = $this->_getParam('show_password',false);
		$form = new Form_Login(array('showPassword' => $showPassword));
        if($showPassword)
        {
            $form->setAction($form->getAction().'?show_password=1');
            $form->populate(array('email' => Zend_Auth::getInstance()->getIdentity()->email));
        }
		$this->view->form = $form;
		
		$regForm = new Form_Register();
		$this->view->regForm = $regForm;
		
		$remindForm = new Form_RemindPassword();
		$this->view->remindForm = $remindForm;
		
		if ($this->_request->isPost() && $action == "login") {
			if ($form->isValid($this->_request->getPost())) {
				$data = $form->getValues();
				
				$users = new Model_Users();
				$success = $users->login($data);
				
				if (!$success) {
					$this->view->success = false;
					$this->view->messages[] = $this->view->translate->_('LOGIN_FAILED');
					return;
				}
				else {
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $data['email']), Zend_Log::INFO);
					
					//save favourite cars from session
					try {
						$favNS = new Zend_Session_Namespace('favourite_cars');
						if (isset($favNS->cars) && is_array($favNS->cars)) {
							$identity = Zend_Auth::getInstance()->getIdentity();
							foreach ($favNS->cars as $carId) {
								$users->saveFavourite($carId, $identity->id);
							}
							$favNS->cars = array();
						}
					}
					catch (Exception $e) {
						$this->_helper->eventLog->log(array('outcome' => 'fail', 'operation' => 'saveFavourite', 'additional' => $data['email']), Zend_Log::INFO);
					}
				}
				$this->_helper->redirectBack('login');
			}//isValid
			else {
				$this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $this->_request->getPost('email')), Zend_Log::INFO);
			}
		}//isPost
				
		if ($this->_request->isPost() && $action == "register") {
			if ($regForm->isValid($this->_request->getPost())) {
				$data = $regForm->getValues();
				unset($data['action']);
				unset($data['csrf']);
				
				$users = new Model_Users();
				
				$users->register($data);
				$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $data['email']), Zend_Log::INFO);
				$users->login($data);
				
				$this->view->messenger->addMessage($this->_helper->translate('REGISTRATION_OK'));
				$this->_helper->redirectBack('register');
			}//isValid
		}//isPost
		
		if ($this->_request->isPost() && $action == "remind") {
			if ($remindForm->isValid($this->_request->getPost())) {
				$data = $remindForm->getValues();
				
				$users = new Model_Users();
				$users->resetPasswordStart($data);
				$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $data['email']), Zend_Log::INFO);
				
				$this->_helper->redirectBack('', $this->view->url(array('language' => $this->_request->getParam('language')), 'remindPasswordInstruction', true).'?show_password=1');
			}//isValid
			else {
			}
		}//isPost
		
		if ($this->_request->getParam('showRemindInstruction') == "true") {
			$this->view->showRemindInstruction = true;
		}
		
		if ($this->_request->getParam('confirmRemind') == "true") {
			$this->view->showConfirmRemindResult = true;
			
			$userId = (int)$this->_request->getParam('uid');
			$hash = $this->_request->getParam('hash');
			
			$users = new Model_Users();
			if ($users->resetPassword($userId, $hash)) {
				$this->view->success = true;
				$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $userId), Zend_Log::INFO);
			}
			else {
				$this->view->success = false;
				$this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $userId), Zend_Log::WARN);
			}
		}
	}
	
	public function logoutAction() {
		$users = new Model_Users();
		$users->logout();
		
		$where = $this->_helper->redirectBack->getRedirectBack('logout');
		$this->_helper->setRedirBack->clearData();
		
		$this->_helper->redirectBack($key=null, $where);
	}
	
	public function sellCarAction() {

        $this->view->menuActive = 'sellCar';
        $identity = null;
        if(Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity() != 'salesman') {
            $identity = Zend_Auth::getInstance()->getIdentity();
        }

		$exchange = $this->_request->getParam('exchange', null);
		$form = new Form_SellCar();
		if ($exchange) {
            $form->getElement('exchange_makes')->setRequired(true);
            $form->getElement('exchange_models')->setRequired(true);
		}

		if ($identity) {
			$form->removeElement('captcha');
			$form->populate(array(
				'first_name' => $identity->first_name,
                'last_name' => $identity->last_name,
				'phone' => $identity->phone,
				'email' => $identity->email
			));
		}
		
		$this->view->form = $form;
		
		if ($this->_request->isPost()) {
			if ($form->isValid($this->_request->getPost())) {
				$data = $form->getValues();

                if($identity) {
                    $data['user_id'] = $identity->user_id;
                }
				
				$useModel = new Model_UsersSellExchange();

				try {

                    $data['photo_tmp_file_path'] = $form->photo->getFileName();
                    $data['is_for_exchange'] = $exchange ? 1 : 0;

                    $id = $useModel->add($data);

                    $result = $useModel->get($id);

                    $exportModel = new Model_ImportExport_Export();
                    $exportModel->sendClientSell($result);


					$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $id), Zend_Log::INFO);
					$this->view->messenger->addMessage($this->_helper->translate('SELL_CAR_EMAIL_SUCCESS'));
					$this->_helper->redirectBack(null, $this->_request->getServer('REQUEST_URI'));
				}
				catch (Exception $e) {
					$this->view->messages[] = $e->getMessage(). ' '. $e->getTraceAsString();
					$this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => print_r($data, true)), Zend_Log::INFO);
				}
			}//isValid
			else {
			}
		}//isPost

        $page = $this->getRequest()->getParam('page');

        $useModel = new Model_UsersSellExchange();

        $results = $useModel->search(array('is_for_exchange' => ($exchange == 'true' ? 1 : 0) ));

        $paginator = Zend_Paginator::factory(array());

        if ($results) {
            $paginator = new Zend_Paginator(new Zend_Paginator_Adapter_DbSelect($results));
            $paginator->setCurrentPageNumber($page);
            $paginator->setItemCountPerPage(20);
        }

        $this->view->paginator = $paginator;
        $this->view->page = $page;
	}

    public function sellExchangeCarListAction() {


        $page = $this->getRequest()->getParam('page');

        $useModel = new Model_UsersSellExchange();

        $results = $useModel->search();

        $paginator = Zend_Paginator::factory(array());



        if ($results) {
            $paginator = new Zend_Paginator(new Zend_Paginator_Adapter_DbSelect($results));
            $paginator->setCurrentPageNumber($page);
            $paginator->setItemCountPerPage(20);

        }



        $this->view->paginator = $paginator;
        $this->view->page = $page;

    }
	
	public function serviceAction() {
		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'service', true) => $this->view->translate->_('SERVICE')
		);
		
		$this->view->containerClass = "with_photo";
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$this->view->serviceData = $opt['service'];
		
		$form = new Form_ServiceContact();
		$identity = Zend_Auth::getInstance()->hasIdentity() ? Zend_Auth::getInstance()->getIdentity() : null;
		
		if ($identity) {
			$form->populate(array(
				'email' => $identity->email,
				'contact' => $identity->first_name . ' ' . $identity->last_name . ', ' . $identity->phone
			));
			$form->removeElement('captcha');
		}
		$this->view->form = $form;
		
		if ($this->_request->isPost()) {
			if ($form->isValid($this->_request->getPost())) {
				$data = $form->getValues();
				
				$ct = new Model_Cars_Transfers();
				$ct->contactService($data);
				
				$this->view->messenger->addMessage($this->_helper->translate('SERVICE_CONTACT_THANK_YOU'));
				$this->_redirect($this->view->url(array('language' => $this->view->language), 'service', true));
			}//isValid
		}//isPost
	}
    
    public function newCarAction() {
        
        
        $id = (int)$this->_request->getParam('carId');
         
        $passwordPassed = false;
        if(Zend_Auth::getInstance()->hasIdentity())
            $passwordPassed = Zend_Auth::getInstance()->getIdentity()->passwordPassed;
		
        $ct = new Model_Cars_Transfers();
        
        $this->view->passwordPassed = $passwordPassed;
        
        if($passwordPassed)
        {
            $platnosciData = $ct->getLastNewCarPaymant(Zend_Auth::getInstance()->getIdentity()->id);
            $this->view->platnosciData = $platnosciData;
            
        }
        
		$form = new Form_NewCar();
		if ($passwordPassed) {
			$identity = Zend_Auth::getInstance()->getIdentity();
			$form->removeElement('captcha');
			$form->populate(array(
				'first_name' => $identity->first_name,
                'last_name' => $identity->last_name,
				'phone' => $identity->phone,
				'email' => $identity->email
			));
            
           
		}
		
        
        if($id != 0)
        {
            $cars = new Model_Cars_Cars();
            $car = $cars->getCarFull($id);
            if (empty($car)) {
                $this->view->messenger->addMessage($this->_helper->translate('CAR_NOT_FOUND'));
                $this->_helper->redirectBack('searchList');
            }

            $cars = new Model_Cars_Cars();

            if(!$cars->isNewCar($id))
            {
                $$this->view->messenger->addMessage($this->_helper->translate('CAR_NOT_FOUND'));
                $this->_helper->redirectBack('searchList');
            }
            $form->populate(array(
                    'make' => $car['make_name'],
                    'model' => $car['model_name'],
                ));
        }
		$this->view->form = $form;
        
        $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
        $this->view->newCarAmount = $opt['transfers']['newCar']['amount'];
		
		if ($this->_request->isPost()) {
			if ($form->isValid($this->_request->getPost())) {
				$data = $form->getValues();
				$identity = Zend_Auth::getInstance()->getIdentity();
                if(!$identity)
                {
         
                    $usersModel = new Model_Users();
                    
                    $userData = $usersModel->getUserByEmail($data['email']);

                    if (!$userData) {
                        $userId = $usersModel->addQuick($data, $trans=false);
                        $userData = $usersModel->getUser($userId);
                    }              
                    
                }
                else
                {
                    
                    $userData = get_object_vars($identity);
                }
				$ct = new Model_Cars_Transfers();
				try {
                    
                    //$platnosciData = $ct->makeNewCar($data, $userData);
						
                    //$this->view->platnosciData = $platnosciData;
                    
                  
                    
					$ct->newCarEmail($data);
					$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $userData['id']), Zend_Log::INFO);
					$this->view->messenger->addMessage($this->_helper->translate('SELL_CAR_EMAIL_SUCCESS'));
					$this->_helper->redirectBack(null, $this->_request->getServer('REQUEST_URI'));
				}
				catch (Exception $e) {
					$this->view->messages[] = $this->view->translate->_('SELL_CAR_EMAIL_FAILURE');
					$this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => print_r($data, true)), Zend_Log::INFO);
				}
			}//isValid
			else {
			}
		}//isPost
	}
    
    public function cookiesPolicyAction() {
		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'cookiesPolicy', true) => 'Polityka cookies'
		);
		
		$this->view->containerClass = "with_photo";
	}

    public function jsTranslateAction() {

        $this->_helper->viewRenderer->setNoRender(true);
        $this->_helper->layout->disableLayout();

        $this->view->language = 'en';
        echo $this->_helper->translate_($this->_getParam('text'));

    }

	public function sitemapAction()
	{

		$container = new Zend_Navigation();
		$container->addPage(new Zend_Config(array(
			'uri' => '/',
		)));

		$container->addPage(new Zend_Config(array(
			'uri' => '/pl/have-car-found',
		)));

		$container->addPage(new Zend_Config(array(
			'uri' => '/pl/sell',
		)));

		$container->addPage(new Zend_Config(array(
			'uri' => '/pl/sell/exchange',
		)));

		$container->addPage(new Zend_Config(array(
			'uri' => '/pl/list',
		)));


		$container->addPage(new Zend_Config(array(
			'uri' => '/pl/list/osobowe',
		)));

		$container->addPage(new Zend_Config(array(
			'uri' => '/pl/list/dostawcze',
		)));

		$container->addPage(new Zend_Config(array(
			'uri' => '/pl/list/inne',
		)));




		$this->view->language = 'pl';

		$carsModel = new Model_Cars_Cars();

		$makesWithCounts = $carsModel->getCarMakesWithCountsByNames($types=array(1), array("NULL", "PREMIUM", "EXCLUSIVE"));

		foreach($makesWithCounts as $make) {

			$container->addPage(new Zend_Config(array(
				'uri' => $this->view->url(array('language' => $this->view->language, 'type' => $make['type_slug'], 'make' => $make['make_slug']), 'list', 'true'),
			)));
		}

		$cars = $carsModel->search();

		foreach($cars as $car) {

			$container->addPage(new Zend_Config(array(
				'uri' => $this->view->carLink($car, null, $noMarkup=true),
			)));
		}





		$this->view->navigation($container);


		$this->view->layout()->disableLayout();
		$this->_helper->viewRenderer->setNoRender(true);
		echo $this->view->navigation()->sitemap();

	}

}

