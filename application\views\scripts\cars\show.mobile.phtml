

<div class="column_right_1">
<div id="neighbour_offers">
    <?php if ($this->nextPrev):
        if ($this->nextPrev['prev']): ?>
            <a id="previous_car" href="<?= $this->carLink($this->nextPrev['prev'], null, $noMarkup=true) . '?hash=' . $this->hash . '&amp;i=' . ($this->iterator - 1) . "&amp;perPage=" . $this->perPage ; ?>"><?= $this->translate->_('CAR_PREVIOUS') ?></a>
        <?php endif;
        if ($this->nextPrev['next']): ?>
            <a id="next_car" href="<?= $this->carLink($this->nextPrev['next'], null, $noMarkup=true) . '?hash=' . $this->hash . '&amp;i=' . ($this->iterator + 1) . "&amp;perPage=" . $this->perPage ; ?>"><?= $this->translate->_('CAR_NEXT') ?></a>
        <?php endif;
    endif; ?>
    <span class="noprint"><a href="<?= $this->url(array(), 'list', true) . "?hash=" . $this->hash . "&amp;i=" . $this->iterator . "&amp;perPage=" . $this->perPage ?>#car_id_<?= $this->car['car_id'] ?>"><?= $this->translate->_('BACK_TO_SEARCH_RESULTS') ?></a></span>
</div>

<div id="car_data">
<h1 id="page_title">

    <?= $this->escape($this->car['make_name'] . " " . $this->car['model_name'] . " " . $this->car['build_year'] .' '. $this->translate->_('PROD.'). ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year'] .' '. $this->translate->_('REG.') : "") . ($this->language == "pl" ? " " . $this->car['title'] : "")) ?>

    <?php if($this->car['ownership_type'] == 'OWN') :?>
        - <span class="certain-car"><?= $this->translate->_('CERTIFIED_GUARANTEED')?></span>
    <?php endif ?>

</h1>

<div id="car_data_top" class="location_group_<?= $this->car['location_group_id'] ?>">
<div id="car_location">

    <a target="_new" href="http://mapy.google.pl/maps?daddr=<?=$this->escape($this->car['address'])?>,+Warszawa" class="location_address"> <?= $this->escape($this->car['address'] . " - " . $this->car['name']) ?> </a> | <?= $this->escape($this->translate->_('CAR_POSITION') . ": ") . (($this->car['status'] == "NOT_ON_SITE" || $this->car['status'] == 'WITHDRAWN') ? "<strong>" . $this->translate->_('NOT_ON_SITE') . "</strong>" : ('<span class="position">'.$this->escape($this->car['position']).'</span>')) ?>

    <span class='car_sr_id'>id: <?= $this->car['sr_car_id'] ?></span>

</div>






<div id="car_photo" class="<?php if (count($this->car['photos']) < 1 && count($this->car['videos']) < 1): ?>no_photo<?php endif ?>">
    <?php if (count($this->car['photos']) > 0): ?>
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <?php foreach ($this->car['photos'] as $photo): ?>
                <div class="swiper-slide photo">
                    <img src="/images/cars/<?= $this->car['car_id'] ?>/<?= $photo['filename_base'] . "_L." . $photo['filename_extension'] ?>" alt="">
                    <?php if($photo['description']): ?>
                        <span><?= $photo['description'] ?></span>
                    <?php endif ;?>
                </div>
            <?php endforeach ?>

        </div>
    </div>
    <div class="pagination-arrows">
        <span class="arrow-left"><a  href="#"></a></span>
        <span class="arrow-right"><a href="#"></a></span>

    <div class="pagination">



    </div>
    </div>
    <?php endif ?>


    <?php if (in_array($this->car['status'], array("SOLD", "SOLD_HANDED_OUT"))): ?>
        <span class="reservation">
						<?= $this->translate->_('SOLD') ?>
					</span>
    <?php elseif ($this->car['is_reserved']): ?>
        <span class="reservation">
						<?= $this->translate->_('RESERVATION') ?>
					</span>
    <?php endif ?>
    <script type="text/javascript" charset="utf-8">
        $(function(){


            $('body').append($('.swiper-overlay'));

            var mySwiper = $('.swiper-container').swiper({
                //Your options here:
                mode:'horizontal',
                loop: false,
                pagination: '.pagination',
                grabCursor: true,
                paginationClickable: true

            });
            $('.arrow-left').bind('click', function(e){
                e.preventDefault()
                mySwiper.swipePrev()
            })
            $('.arrow-right').bind('click', function(e){
                e.preventDefault()
                mySwiper.swipeNext()
            })


            if ($("#car_photo > a.photo").length > 0) {
                $("#car_photo > a.photo:gt(0)").hide();
                $("#car_photo a.video").hide();
            }
            else if ($("#car_photo > a.video").length > 0) {
                $("#car_photo > a.video:gt(0)").hide();
            }

        })
    </script>
    <?php if (count($this->car['photos']) < 1 && count($this->car['videos']) < 1): ?><div class="no_photo_icon no_photo_<?= $this->car['vc_key'] ?>"></div><?php endif ?>
</div>
<div id="car_data_right">
<table id="car_other_data" cellspacing="0" cellpadding="0">
    <tr>
        <?php if ($this->isRental): ?>

        <?php elseif (in_array($this->car['status'], array("SOLD", "SOLD_HANDED_OUT"))): ?>
            <td class="similar_cars"colspan="2">
                <div>
                    <form action="<?= $this->url(array('language' => $this->language), 'list', true) ?>" method="post">
                        <?php if(isset($this->similarMakesWithCounts['params'])) : ?>
                            <?php foreach ($this->similarMakesWithCounts['params'] as $key => $value): ?>
                                <?php if (is_array($value)): ?>
                                    <?php foreach ($value as $val): ?>
                                        <input type="hidden" name="<?= $key ?>[]" value="<?= $this->escape($val) ?>" />
                                    <?php endforeach ?>
                                <?php else: ?>
                                    <input type="hidden" name="<?= $key ?>" value="<?= $this->escape($value) ?>" />
                                <?php endif ?>
                            <?php endforeach ?>
                        <?php endif;?>
                        <input type="submit" name="submit" value="<?= $this->translate->_('SIMILAR_CARS') ?>">
                    </form>
                </div>
            </td>
        <?php else: ?>
            <td style="width: 130px;">
                <div class="price">

                    <?php if($this->siteVariant == 'autoauto.by') :?>
                        <?= $this->escape($this->carPrice($this->car, $this->language_row, 'EUR')) ?>
                    <?php else: ?>
                        <?= $this->escape($this->carPrice($this->car, $this->language_row)) ?>

                        <?php if($this->carPrice($this->car, $this->language_row, 'EUR')):?>
                            <div class="small_price">
                                <?= $this->escape($this->carPrice($this->car, $this->language_row, 'EUR')) ?>
                            </div>
                        <?php endif;?>
                    <?php endif;?>

                </div>
            <td>



                <?php if (!$this->isRental && !in_array($this->car['status'], array("SOLD", "SOLD_HANDED_OUT"))): ?>
                    <div class="arrow noprint">


                        <a id="show_reservation_form" href="#reservation_form" rel="prettyPhoto" title=""><b><?= $this->translate->_('MAKE_RESERVATION') ?></b</a>

                    </div>
                <?php endif ?>



            </td>
        <?php endif ?>
    </tr>
    <tr>
        <td style="width: 150px;" rowspan="<?php if ($this->car['consumption_city'] !== null || $this->car['consumption_motorway'] !== null): ?>4<?php else: ?>3<?php endif ?>">

            <div class="actions actions_<?= $this->car['location_group_id'] ?>">



                <a class="share" href="#" data-dropdown="#dropdown-<?= $this->car['car_id'] ?>">

                       <span>
                           <?= strtolower($this->translate->_('SEND')) ?><br />
                           <?= strtolower($this->translate->_('PRINT')) ?>
                           <div class="caret"></div>
                        </span>
                </a>

                <?php if($this->isFavourite): ?>
                    <?php if(Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == 'user') : ?>
                        <a class="favourite-del"  href="<?= $this->domain . $this->url(array('language' => $this->language), 'favouriteList') .  '?del=' . $this->car['car_id'] ?>"><span><span>-</span><br/><?= $this->translate->_('FAVOURITE_ADD_DEL') ?></span></a>
                    <?php else: ?>
                        <a class="favourite-del"  href="<?= $this->domain . $this->url(array('language' => $this->language), 'favouriteListSession') .  '?del=' . $this->car['car_id'] ?>"><span><span>-</span><br/><?= $this->translate->_('FAVOURITE_ADD_DEL') ?></span></a>
                    <?endif ?>
                <?php else: ?>
                    <a class="favourite"  href="<?= $this->domain . $this->carLink($this->car, null, $noMarkup=true) . ($this->hash ? $this->hash . "&amp;perPage=" . $this->perPage.'&amp;favourite=' . $this->car['car_id'] : "?perPage=" . $this->perPage .'&amp;favourite=' . $this->car['car_id']) ?> "><span><span>+</span><br/><?= $this->translate->_('FAVOURITE_ADD_DEL') ?></span></a>
                <?php endif?>


            </div>


        </td>
        <td><?= $this->engineData($this->car) ?></td>
    </tr>
    <tr>

        <td><?= $this->car['build_year'] . ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year'] : "") ?> / <?= $this->mileage($this->car['odometer']) ?> <?= $this->escape($this->translate->_('MILEAGE_' . $this->car['odometer_unit'])) ?></td>
    </tr>
    <tr>

        <td><?= $this->carColour($this->car) ?></td>
    </tr>
    <?php if ($this->car['consumption_city'] !== null || $this->car['consumption_motorway'] !== null): ?>
        <tr>

            <td class="fuel_consumption">
                <?= number_format($this->car['consumption_city'], 1) ?> L / 100km
                <?= number_format($this->car['consumption_motorway'], 1) ?> L / 100km
            </td>
        </tr>
    <?php endif ?>

    <?php if (!$this->isRental): ?>
        <tr>
            <td>
                <a id="leasing_credit_ask" href="<?= $this->leasingCreditInstalment['type'] == "leasing" ? "#leasingForm" : "#creditForm" ?>" rel="prettyPhoto" title=""><?= $this->translate->_('LEASING_CREDIT_ASK') ?></a>
            </td>
            <td>
                <strong><?= /* this already formatted and recalculated for current currency */ $this->leasingCreditInstalment['instalment'] . " " . $this->translate->_('brutto') ?></strong>
                <?= $this->translate->_('PER_MONTH') ?>

            </td>
        </tr>
    <?php endif ?>
    <tr id="car_utils">
        <td  colspan="2" class="location_group_<?= $this->car['location_group_id'] ?>">
            <a id="item_leasing_form" href="#leasingForm" rel="prettyPhoto" class="item" title=""><span class="for_icon">&nbsp;</span> <?= $this->translate->_('LEASING_CALCULATOR') ?></a>
            <a id="item_credit_form" href="#creditForm" rel="prettyPhoto" class="item" title=""><span class="for_icon">&nbsp;</span> <?= $this->translate->_('LOAN_CALCULATOR') ?></a>
        </td>

    </tr>
</table>

<? if ($this->car['caretaker_visible']) :?>
    <div class="float-contact">


        <div class="caretaker-photo">
            <div class="caretaker-photo-box">
                <?php if (!empty($this->car['caretaker_photo_basename'])): ?>

                    <img data="/images/employees/<?= $this->car['caretaker_photo_basename'] . '_L.' . $this->car['caretaker_photo_extension'] ?>" src="/images/employees/<?= $this->car['caretaker_photo_basename'] . '_S.' . $this->car['caretaker_photo_extension'] ?>" alt="<?= $this->escape($this->car['caretaker_first_name'] . " " . $this->car['caretaker_last_name']) ?>" />

                <?php endif ?>
            </div>

        </div>
        <div class="float-contact-container">
            <div class="info">
                <?= $this->escape($this->car['caretaker_first_name'] . " " . $this->car['caretaker_last_name']) ?> <br />
                <span class="phone"><a href="tel:<?= $this->escape($this->car['caretaker_phone']) ?>"><?= $this->escape($this->car['caretaker_phone']) ?></a></span>

            </div>
            <div class="caretaker-feedback">
                <a class="caretaker_contact" href="#contact_caretaker_form" rel="prettyPhoto">
                    <?= $this->translate->_('CONTACT_CARETAKER') ?>

                </a>

            </div>
        </div>

    </div>
<?php endif ?>
<? if ($this->financingEmployee) :?>
    <div class="float-contact financing">


        <div class="caretaker-photo">
            <div class="caretaker-photo-box">
                <?php if (!empty($this->financingEmployee['photo_basename'])): ?>

                    <img data="/images/employees/<?= $this->financingEmployee['photo_basename'] . '_L.' . $this->financingEmployee['photo_extension'] ?>" src="/images/employees/<?= $this->financingEmployee['photo_basename'] . '_S.' . $this->financingEmployee['photo_extension'] ?>" alt="<?= $this->escape($this->financingEmployee['first_name'] . " " . $this->financingEmployee['last_name']) ?>" />

                <?php endif ?>
            </div>

        </div>
        <div class="float-contact-container">
            <div class="info">
                <?= $this->escape($this->financingEmployee['first_name'] . " " . $this->financingEmployee['last_name']) ?> <br />
                <span class="phone"><a href="tel:<?= $this->escape($this->financingEmployee['phone']) ?>"><?= $this->escape($this->financingEmployee['phone']) ?></a></span>

            </div>
            <div class="caretaker-feedback">


                <a class="caretaker_contact" href="#contact_financing_form" rel="prettyPhoto">
                    Email o kredyt lub leasing

                </a>



            </div>
        </div>

    </div>
<?php endif ?>
<? if ($this->languageEmployee) :?>
    <div class="float-contact language">


        <div class="caretaker-photo">
            <div class="caretaker-photo-box">
                <?php if (!empty($this->languageEmployee['photo_basename'])): ?>

                    <img data="/images/employees/<?= $this->languageEmployee['photo_basename'] . '_L.' . $this->languageEmployee['photo_extension'] ?>" src="/images/employees/<?= $this->languageEmployee['photo_basename'] . '_S.' . $this->languageEmployee['photo_extension'] ?>" alt="<?= $this->escape($this->languageEmployee['first_name'] . " " . $this->languageEmployee['last_name']) ?>" />

                <?php endif ?>
            </div>

        </div>
        <div class="float-contact-container">
            <div class="info">
                <?= $this->escape($this->languageEmployee['first_name'] . " " . $this->languageEmployee['last_name']) ?> <br />
                <span class="phone"><a href="tel:<?= $this->escape($this->languageEmployee['phone']) ?>"><?= $this->escape($this->languageEmployee['phone']) ?></a></span>

            </div>
            <div class="caretaker-feedback">


                <a class="caretaker_contact" href="#contact_language_form" rel="prettyPhoto">
                    <?= $this->translate->_('I_SPEEK_LANG_HOW_CAN_I_HELP') ?>
                </a>



            </div>
        </div>

    </div>
<?php endif ?>
<script type="text/javascript">

    $('.caretaker-photo img').qtip({
        content: {
            text: function(api) {
                return '<img src="' + $(api.currentTarget).attr('data') + '" />';
            }
        },
        position: {
            my: 'center right',
            at: 'left center'

        },
        show: {
            event: 'click mouseenter'
        },
        style: {
            tip: true,
            classes: 'qtip-caretaker'
        }
    });


</script>

<div class="print_send" style="display: none;">
    <a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id']), 'showCar', true) ?>?print=true" class="noprint print_offer" target="_blank">
        <span class="for_icon">&nbsp;</span>
        <?= $this->translate->_('PRINT_OFFER') ?>
    </a>
    <a href="#send_offer_form" rel="prettyPhoto" title="" class="noprint send_offer">
        <span class="for_icon">&nbsp;</span>
        <?= $this->translate->_('SEND_OFFER') ?>
    </a>
</div>

</div>
</div>

<div class="clear"></div>


<div id="car_description">
    <table style="display:none;" cellspacing="0" cellpadding="0" class="noprint">
        <tr>
            <td class="location_group_<?= $this->car['location_group_id'] ?>"><a id="item_test_drive" href="#test_drive_form" rel="prettyPhoto" class="item" title=""><span class="for_icon">&nbsp;</span> <?= $this->translate->_('TEST_DRIVE') ?></a></td>

        </tr>
    </table>

    <div class="text">
        <?= $dummy=null;;
        if ($this->language == "pl"): ?>
            <?php if(!empty($this->car['description2'])) : ?>
                <div class="description-row">
                    <h4>
                        Historia auta, jego stan i właściciele
                    </h4>
                    <?= $this->carDescription($this->car['description2']) ?>
                </div>
            <?php endif; ?>
            <?php if(!empty($this->car['description'])) : ?>
                <div class="description-row">
                    <h4>
                        Wyposażenie ponadstandardowe
                    </h4>
                    <?= $this->carDescription($this->car['description']) ?>
                </div>
            <?php endif; ?>
            <?php if(!empty($this->car['description3'])) : ?>
                <div class="description-row">
                    <h4>
                        Cena, finansowanie  i kontakt do opiekuna
                    </h4>

                    <?= $this->carDescription($this->car['description3']) ?>

                </div>
            <?php endif; ?>


            <?php $dummy=null; endif; ?>
        <?php
        $separator = "\n- ";
        $checkboxes = null;
        $descriptionsCount = count($this->car['descriptions']);
        if (count($this->car['descriptions']) > 0 && $this->language != "pl") {
            $i = 0;
            foreach($this->car['descriptions'] as $d)
            {
                $i++;
                $checkboxes .=  $d['value'] . ($descriptionsCount > $i ? $separator : '');
            }
        }
        if ($checkboxes) {
            $checkboxes = $separator . $checkboxes;
        }

        $checkboxes .=  $separator . implode($separator, $this->car['features']);

        if (count($this->car['extras']) > 0) {
            $checkboxes .= $separator . implode($separator, $this->car['extras']);
        }
        if ($checkboxes) {
            if ($this->language == "pl") echo "<br /><br />";
            echo $this->carDescription($checkboxes);
        }

        ?>
    </div>

    <div class="print_send">
        <a href="#comment_form" rel="prettyPhoto" title="" class="noprint comment_car">
            <span class="for_icon">&nbsp;</span>
            <?= $this->translate->_('COMMENT_CAR_OR_SERVICE') ?>
        </a>

    </div>

</div>

<div class="clear"></div>

<div id="similar_cars">
    <div id="similar_cars_title"><?= $this->translate->_('SIMILAR_CARS') ?></div>

    <?php if ($this->similarMakesWithCounts && isset($this->similarMakesWithCounts['cars']) && is_array($this->similarMakesWithCounts['cars']) && count($this->similarMakesWithCounts['cars']) > 0): ?>
        <div id="similar_makes">
            <?php $similarMakesCount = 0; foreach ($this->similarMakesWithCounts['cars'] as $smMake): ?>
                <? $similarMakesCount += $smMake['car_count']; ?>
            <?php endforeach ?>

            <span>
						<form action="<?= $this->url(array('language' => $this->language), 'list', true) ?>" method="post">
                            <?php foreach ($this->similarMakesWithCounts['params'] as $key => $value): ?>
                                <?php if (is_array($value)): ?>
                                    <?php foreach ($value as $val): ?>
                                        <input type="hidden" name="<?= $key ?>[]" value="<?= $this->escape($val) ?>" />
                                    <?php endforeach ?>
                                <?php else: ?>
                                    <input type="hidden" name="<?= $key ?>" value="<?= $this->escape($value) ?>" />
                                <?php endif ?>
                            <?php endforeach ?>
                            <span class="for_icon">&nbsp;</span><input class="submit" type="submit" name="submit" value="<?= $this->translate->_('SIMILAR_MAKES_ALL') . ' (' . $similarMakesCount . ')' ?>">
                        </form>
					</span>

            <?php foreach ($this->similarMakesWithCounts['cars'] as $smMake): ?>
                <span>
							<form action="<?= $this->url(array('language' => $this->language), 'list', true) ?>" method="post">
                                <input type="hidden" name="makes" value="<?= $smMake['name'] ?>" />
                                <?php foreach ($this->similarMakesWithCounts['params'] as $key => $value): ?>
                                    <?php if (is_array($value)): ?>
                                        <?php foreach ($value as $val): ?>
                                            <input type="hidden" name="<?= $key ?>[]" value="<?= $this->escape($val) ?>" />
                                        <?php endforeach ?>
                                    <?php else: ?>
                                        <input type="hidden" name="<?= $key ?>" value="<?= $this->escape($value) ?>" />
                                    <?php endif ?>
                                <?php endforeach ?>
                                <span class="for_icon">&nbsp;</span><input class="submit" type="submit" name="submit" value="<?= $smMake['name'] . ' (' . $smMake['car_count'] . ')' ?>">
                            </form>
						</span>
            <?php endforeach ?>
        </div>
    <?php endif ?>


    <?php foreach($this->similarCars as $car): ?>
        <?= $this->partial('car_search_item.phtml', array('car' => $car, 'translate' => $this->translate, 'language' => $this->language, 'language_row' => $this->language_row, 'isFavourite' => in_array($car['car_id'], $this->favouritesIds) )) ?>
    <?php endforeach; ?>
</div>
</div>

</div>

<div id="dropdown-<?= $this->car['car_id'] ?>" class="dropdown">
    <ul class="dropdown-menu">
        <li><a href="sms:?&body=To jest link do dokladnego opisu auta <?= $this->car['make_name'] . ' '. $this->car['model_name'] . ' '. $this->car['build_year'] ?> o ktorym rozmawialismy: <?= $this->domain . $this->carLink($this->car, null, $noMarkup=true) ?>"> <?= $this->translate->_('SEND_OFFER_SMS') ?></a></li>
        <li><a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarSendOffer', true) ?>"> <?= $this->translate->_('SEND_OFFER') ?></a></li>
        <li><a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarContactCartaker', true) ?>"> <?= $this->translate->_('CONTACT_CARETAKER') ?></a></li>
        <li><a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id']), 'showCar', true) ?>?print=true">  <?= $this->translate->_('PRINT_OFFER') ?></a></li>
        <li><a href="<?= $this->url(array('language' => $this->language,'id' => $this->car['car_id'],'description' => $this->carPermalink($this->car)),'forceShowCarTestDrive',true) ?>"> <?= $this->translate->_('TEST_DRIVE') ?></a></li>
        <li><a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($this->domain.$this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)))) ?>" target="_blank">Facebook</a></li>
    </ul>
</div>

<div class="clear"></div>

<div id="contact_caretaker_form">
    <div class="pp_html">
        <br />
        <h2><?= $this->translate->_('CONTACT_CARETAKER') ?></h2>
        <?php

        echo $this->testDriveForm2;

        ?>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $("#contact_caretaker_form").hide();
        <?php if ($this->showContactCaretakerForm): ?>
        $("a[href='#contact_caretaker_form']").click();
        <?php endif
 ?>
    });
</script>

<div id="contact_financing_form">
    <div class="pp_html">
        <br />
        <h2>Zapytaj o kredy lub leasing</h2>
        <?php

        echo $this->testDriveForm3;

        ?>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $("#contact_financing_form").hide();
        <?php if ($this->showContactFinancingForm): ?>
        $("a[href='#contact_financing_form']").click();
        <?php endif
 ?>
    });
</script>

<div id="contact_language_form">
    <div class="pp_html">
        <br />
        <h2><?= $this->translate->_('I_SPEEK_LANG_HOW_CAN_I_HELP') ?></h2>
        <?php

        echo $this->testDriveForm4;

        ?>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $("#contact_language_form").hide();
        <?php if ($this->showContactLanguageForm): ?>
        $("a[href='#contact_language_form']").click();
        <?php endif
 ?>
    });
</script>

<div id="test_drive_form">
    <div class="pp_html">
        <br />
        <h2><?= $this->translate->_('TEST_DRIVE') ?></h2>
        <?= $this->testDriveForm ?>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $("#test_drive_form").hide();
        <?php if ($this->showTestDriveForm): ?>
        $("a[href='#test_drive_form']").click();
        <?php endif ?>
    });
</script>

<div id="comment_form">
    <div class="pp_html">
        <br />
        <h2><?= $this->translate->_('CONTACT_COMMENT_TITLE') ?></h2>
        <?= $this->commentForm ?>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $("#comment_form").hide();
        <?php if ($this->showCommentForm): ?>
        $("a[href='#comment_form']").click();
        <?php endif ?>
    });
</script>

<div id="offer_form">
    <div class="pp_html">
        <br />
        <h2><?= $this->translate->_('MAKE_OFFER_TITLE') ?></h2>
        <?= $this->offerForm ?>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $("#offer_form").hide();
        <?php if ($this->showOfferForm): ?>
        $("a[href='#offer_form']").click();
        <?php endif ?>
    });
</script>

<?php if ($this->reservationForm): ?>
    <div id="reservation_form">
        <div class="pp_html">
            <br />
            <h2><?= $this->translate->_('RESERVATION_FORM_TITLE') ?></h2>
            <?= $this->reservationForm ?>
        </div>
    </div>
    <script type="text/javascript">
        var post = "";
        $(function(){
            $("#reservation_form").hide();

            <?php if ($this->showReservationForm) : ?>
            $("#show_reservation_form").click();
            <?php endif; ?>

            $("form#reservation #preview").live('click', function(e){

                post = '&'+$(this).attr("name") + "=1";
            });
            $("form#reservation").live('submit', function(e){
                var sdata = $(this).serialize() + post;
                post = "";
                e.preventDefault();
                $.post(
                    $(this).attr('action'),
                    sdata,
                    function(data){

                        if (!data.ok) {
                            $("form#reservation").replaceWith(data.formHtml);
                        }
                        else {
                            if(data.preview)
                            {
                                var a = document.createElement('a');
                                a.setAttribute("href", data.url+'?'+sdata);
                                a.setAttribute("target", "_blank");

                                var dispatch = document.createEvent("HTMLEvents")
                                dispatch.initEvent("click", true, true);
                                console.log(a);
                                a.dispatchEvent(dispatch);
                                window.open(data.url+'?'+sdata,"_blank");
                            }
                            else
                            {
                                var inputsHtml = '';
                                for (var i in data.data) {
                                    inputsHtml += ' <input type="hidden" name="'+i+'" value="'+data.data[i].replace('"', '&quot;')+'" />';
                                }
                                $('<form method="post" action="'+data.url+'">'+inputsHtml+'</form>').appendTo('body').submit();
                            }
                        }
                    },
                    'json'
                );
                return false;

            });
        });
    </script>
<?php endif ?>

<div id="leasingForm" style="display: none;">
    <div class="pp_html">
        <?php if ($this->leasingForm): ?>
            <br />
            <h2><?= $this->translate->_('CAR_PRICE_NET') ?>: <?= $this->price($this->car['price'], $this->language_row) ?></h2>
            <?= $this->leasingForm;	?>

            <table border="0" cellspacing="0" cellpadding="0" class="leasing_calculator">
                <tr>
                    <th></th>
                    <th><?= $this->translate->_('VALUE') ?></th>
                    <th><?= $this->translate->_('NET') ?></th>
                    <th><?= $this->translate->_('VAT') ?></th>
                    <th><?= $this->translate->_('GROSS') ?></th>
                </tr>
                <tr>
                    <td><?= $this->translate->_('LEASING_PERIOD') ?></td>
                    <td id="calc_instalments_no"></td>
                    <td id="calc_price_net"><?= $this->price($this->car['price'], $this->language_row) ?></td>
                    <td id="calc_price_tax"><?= $this->price($this->priceGross - $this->car['price'], $this->language_row) ?></td>
                    <td id="calc_price_gross"><?= $this->price($this->priceGross, $this->language_row) ?></td>
                </tr>
                <tr class="even">
                    <td><?= $this->translate->_('CONTRIBUTION') ?></td>
                    <td id="calc_contribution_percent"></td>
                    <td id="calc_contribution_net"></td>
                    <td id="calc_contribution_vat"></td>
                    <td id="calc_contribution_gross"></td>
                </tr>
                <tr class="bold">
                    <td><?= $this->translate->_('LEASING_INSTALLMENT') ?></td>
                    <td id="calc_interest"></td>
                    <td id="calc_instalment_net"></td>
                    <td id="calc_instalment_vat"></td>
                    <td id="calc_instalment_gross"></td>
                </tr>
                <tr>
                    <td><?= $this->translate->_('LEASING_BUYOUT') ?></td>
                    <td id="calc_buyout_percent"></td>
                    <td id="calc_buyout_net"></td>
                    <td id="calc_buyout_vat"></td>
                    <td id="calc_buyout_gross"></td>
                </tr>
            </table>

            <div class="calculator_disclaimer">
                <span>*</span> <?= $this->translate->_('CALCULATOR_DISCLAIMER') ?>
            </div>
        <?php else: ?>
            <br />
            <h2><?= $this->translate->_('NO_LEASING_AVAILABLE') ?></h2>
            <div>
                <?= $this->translate->_('NO_LEASING_CONTACT') ?>
            </div>
        <?php endif ?>

        <?php if (count($this->financingEmployees) > 0): ?>
            <br />
            <?php if ($this->leasingForm): ?>
                <?= $this->translate->_('FINANCING_CONTACT_EMPLOYEES') ?>
            <?php endif ?>
            <br />
            <br />

            <?php foreach ($this->financingEmployees as $employee): ?>
                <div class="data">
                    <strong><?= $employee['first_name'] . ' ' . $employee['last_name'] ?></strong><br />

                    <?php if (!empty($employee['position'])): ?>
                        <strong><?= $employee['position'] ?></strong>
                        <br />
                    <?php endif ?>

                    <?php if (!empty($employee['phone'])): ?>
                        <?= $this->translate->_('PHONE_SHORT') ?>: <?= $employee['phone'] ?>
                        <br />
                    <?php endif ?>

                    <?php if (!empty($employee['skype'])): ?>
                        <?= $this->translate->_('SKYPE_SHORT') ?>: <a href="skype:<?= $this->escape($employee['skype']) ?>?call" class="sss_skype"><?= $this->escape($employee['skype']) ?></a>
                        <br />
                    <?php endif ?>

                    <?php if (!empty($employee['email'])): ?>
                        <?= $this->translate->_('EMAIL') ?>: <?= $employee['email'] ?>
                        <br />
                    <?php endif ?>
                    <br /><br />
                </div>
            <?php endforeach ?>
        <?php endif ?>
    </div>
</div>

<div id="creditForm" style="display: none">
    <div class="pp_html">
        <br />
        <h2><?= $this->translate->_('CAR_PRICE_GROSS') ?>: <?= $this->price($this->priceGross, $this->language_row) ?></h2>
        <?= $this->creditForm;	?>
        <div class="result"><?= $this->translate->_('INSTALLMENT_VALUE') ?>: <span id="credit-amount"></span></div>

        <div class="calculator_disclaimer">
            <span>*</span> <?= $this->translate->_('CALCULATOR_DISCLAIMER') ?>
        </div>

        <?php if (count($this->financingEmployees) > 0): ?>
            <br />
            <?= $this->translate->_('FINANCING_CONTACT_EMPLOYEES') ?>
            <br />
            <br />

            <?php foreach ($this->financingEmployees as $employee): ?>
                <div class="data">
                    <strong><?= $employee['first_name'] . ' ' . $employee['last_name'] ?></strong><br />

                    <?php if (!empty($employee['position'])): ?>
                        <strong><?= $employee['position'] ?></strong>
                        <br />
                    <?php endif ?>

                    <?php if (!empty($employee['phone'])): ?>
                        <?= $this->translate->_('PHONE_SHORT') ?>: <?= $employee['phone'] ?>
                        <br />
                    <?php endif ?>

                    <?php if (!empty($employee['skype'])): ?>
                        <?= $this->translate->_('SKYPE_SHORT') ?>: <a href="skype:<?= $this->escape($employee['skype']) ?>?call" class="sss_skype"><?= $this->escape($employee['skype']) ?></a>
                        <br />
                    <?php endif ?>

                    <?php if (!empty($employee['email'])): ?>
                        <?= $this->translate->_('EMAIL') ?>: <?= $employee['email'] ?>
                        <br />
                    <?php endif ?>
                    <br /><br />
                </div>
            <?php endforeach ?>
        <?php endif ?>
    </div>
</div>

<div id="send_offer_form">
    <div class="pp_html">
        <br />
        <h2><?= $this->translate->_('SEND_OFFER') ?></h2>
        <?= $this->sendOfferForm ?>
    </div>
</div>

<script type="text/javascript">
    $(function () {

        setInterval(function(){

                if("rgb(51, 51, 51)" == $("#car_other_data #caretaker_contact").css('color'))
                    $("#car_other_data #caretaker_contact").css('color', 'rgb(255,0,0)');
                else if($("#car_other_data #caretaker_contact").css('color'))
                    $("#car_other_data #caretaker_contact").css('color', 'rgb(51, 51, 51)');
            }
            ,1500);


    });
</script>
<script type="text/javascript">
    $(function(){
        $("#send_offer_form").hide();
        <?php if ($this->showSendOfferForm): ?>
        $("a[href='#send_offer_form']").click();
        <?php endif ?>
    });
</script>

<script type="text/javascript">
    function calculate(object) {
        var form = $(object);

        $.ajax({
            type: 'GET',
            url: '<?= $this->url(array("language" => $this->language), "getInstalments", true); ?>',
            data: form.serialize(),
            dataType: 'json',
            async: false,
            success: function(data) {
                form.siblings(".result").find('span').html(data.instalment_gross);
                if (form.find("[name='type']").val() == 'leasing') {
                    var fields = ['instalments_no', 'price_net', 'price_tax', 'price_gross', 'contribution_percent', 'contribution_net', 'contribution_vat', 'contribution_gross', 'interest', 'instalment_net', 'instalment_vat', 'instalment_gross', 'buyout_percent', 'buyout_net', 'buyout_gross', 'buyout_vat'];
                    for (var f in fields) {
                        $(".pp_content #calc_" + fields[f]).text(data[fields[f]]);
                    }
                }
            },
            error: function() {
            }
        });
        return false;
    }

    $(function(){
        if ($("input#price").length > 0 && $("select#contribution").length > 0) {
            $(".pp_content input#price")
                .live('keyup', function(){
                    var value = parseInt($(this).val(), 10);
                    if (isNaN(value)) return;

                    var opts = $(".pp_content select#contribution option");
                    opts.each(function(){
                        var label = $(this).text();
                        var contrib = $(this).attr('value');
                        var newVal = contrib * value;

                        label = label.replace(/=\s([0-9]+\s)?([0-9])+\s/g, "= " + number_format(newVal, 0, '.', ' ') + " ");
                        $(this).text(label).attr('label', label);
                    });
                })
                .live('change', function(){$(this).keyup();})
                .live('blur', function(){$(this).keyup();})
            ;
        }
    });
</script>


<script type="text/javascript">
    $(document).ready(function() {
        var photoIndex = 0;
        var photoCount = $("#car_photos a.photo, #car_photos a.video").length;

        $("#car_photos a.video img").click(function(e){
            e.preventDefault();
            e.stopPropagation();

            var a = $(this).closest("a");

            $("#car_photo img:visible, #car_photo iframe:visible").closest("a").hide();
            $("#car_photo iframe[src='" + a.attr('href') + "']").closest("a").show();
        });

        $("#car_photos a.photo").click(function(e){
            e.preventDefault();
            e.stopPropagation();

            var a = $(this);
            $("#car_photo img:visible, #car_photo iframe:visible").closest("a").hide();
            $("#car_photo img[src='"+a.attr('href')+"']").closest("a").show();
        });

        var aaPhotoScroll = {
            items: 4,
            height: 105,
            margin: 2,
            step: 1
        };

        if ($("#car_photos a").length > aaPhotoScroll.items) {
            var scLeft = '<a href="javascript:;" class="aa_scroll_photos_left" ><span></span></a>';
            var scRight = '<a href="javascript:;" class="aa_scroll_photos_right" ><span></span></a>';
            $("#car_photos").prepend($(scRight)).prepend($(scLeft));
            $("#car_photos").append($(scLeft)).append($(scRight));
            $("#car_photos").css({
                height: parseInt($("#car_photos").css('height').replace('px', '')) + $(".aa_scroll_photos_left").outerHeight() + $(".aa_scroll_photos_right").outerHeight()
            });
        }

        $(".aa_scroll_photos_left").click(function(){
            var slider = $("#car_photos .inner");
            var left = parseInt(slider.css('top').replace('px', ''), 10);
            if (left <= 0) {
                left = Math.min(0, left + (aaPhotoScroll.step * (aaPhotoScroll.height + aaPhotoScroll.margin)));
                slider.css('top', left+'px');
            }
            if (photoIndex > 0) {
                photoIndex--;
                $("#car_photos a.photo, #car_photos a.video").eq(photoIndex).click();
            }
        });

        $(".aa_scroll_photos_right").click(function(){
            var slider = $("#car_photos .inner");
            var left = parseInt(slider.css('top').replace('px', ''), 10);
            var imgCount = $("#car_photos .inner a").length;
            var minLeft = 0 - ((imgCount - aaPhotoScroll.items) * (aaPhotoScroll.height + aaPhotoScroll.margin));
            if (left >= minLeft) {
                left = Math.max(minLeft, left - (aaPhotoScroll.step * (aaPhotoScroll.height + aaPhotoScroll.margin)));
                slider.css('top', left+'px');
            }
            if (photoIndex < photoCount - 1) {
                photoIndex++;
                $("#car_photos a.photo, #car_photos a.video").eq(photoIndex).click();
            }
        });

        $(".pp_html .form_label.for_captcha").hide();
        $("input[name='captcha[input]']").before("<br />");
    });
</script>

		