<?php

class Form_WeFindCar extends My_Form {



    protected function array_merge_keys(){
        $args = func_get_args();
        $result = array();
        foreach($args as &$array){
            foreach($array as $key => &$value){
                $result[$key] = $value;
            }
        }
        return $result;
    }

    public function init() {

        $this->setAttrib('enctype', 'multipart/form-data');

        $cars = new Model_Cars_Cars();
        $tr = Zend_Registry::get('Zend_Translate');

        $this->addElements(array(


            new Zend_Form_Element_Textarea('comments', array(
                'label'	=>	'BUY_CAR_DESCRIPTION',
                'required' => true,
                'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('rows' => 3, 'cols' => 40)
            )),
            new Zend_Form_Element_Text('email', array(
                'label'	=>	'EMAIL',
                'required' => true,
                'validators' => array(
                    new Zend_Validate_EmailAddress(),
                    new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')),
                )
            )),

            new Zend_Form_Element_Text('first_name', array(
                'label'	=>	'FIRST_NAME',
                'required' => true,
                'filters' => array(new Zend_Filter_StripTags()),
                'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
            )),
            new Zend_Form_Element_Text('phone', array(
                'label'	=>	'PHONE',
                'filters' => array(new Zend_Filter_StripTags()),
                'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
            )),

            new Zend_Form_Element_Hidden('buy_car', array(
                'value' => 1,
            )),



            new Zend_Form_Element_Captcha('captcha', array(
                'label' => 'CAPTCHA',
                'captcha' => new My_Captcha_ReCaptcha(array(
                    'siteKey'  => '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-',
                    'secretKey' => '6Lfx7A0UAAAAAL41t8hFSPCzuh6VQGhcpXttwrJd'
                )),
            )),
            new Zend_Form_Element_Hash('csrf', array(
                'label'	=>	'',
                'salt' => 'csrf_foo_' . get_class($this)
            )),
            new Zend_Form_Element_Submit('submit', array(
                'label'	=>	'SEND'
            )),
        ));
        


        parent::init();

        


    }//init



}