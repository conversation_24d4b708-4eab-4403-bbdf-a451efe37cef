<?php

class Form_Car_Offer extends My_Form {
	
	public function init() {
		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
				'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8'))
				)
			)),
			new Zend_Form_Element_Text('first_name', array(
				'label'	=>	'FIRST_NAME',
				'required' => true,
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('last_name', array(
				'label'	=>	'LAST_NAME',
				'required' => true,
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('company_name', array(
				'label'	=>	'COMPANY_NAME',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 255, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('phone', array(
				'label'	=>	'PHONE',
				'filters' => array(new Zend_Filter_StripTags()),
                'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('price', array(
				'label'	=>	'OFFER_PRICE',
				'required' => true,
				'validators' => array(new Zend_Validate_Int())
			)),
			new Zend_Form_Element_Textarea('message', array(
				'label'	=>	'OFFER_MESSAGE',
				'filters' => array(new Zend_Filter_StripTags()),
				'attribs' => array('rows' => 3, 'cols' => 40)
			)),
			new Zend_Form_Element_Captcha('captcha', array(
				'label' => 'CAPTCHA',
				'captcha' => new My_Captcha_Math(array(
					'timeout' => '180',
				)),
			)),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label' => 'SUBMIT_MAKE_OFFER'
			)),
		));
		
		if (Zend_Auth::getInstance()->hasIdentity()) {
			$this->removeElement('captcha');
		}
		
		parent::init();
	}
	
}