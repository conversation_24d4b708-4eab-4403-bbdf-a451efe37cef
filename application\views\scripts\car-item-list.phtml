
<div class="car row pb-4<?= empty($this->car['filename_base']) ? ' no-photo' : ''?>">
    <div class="col-lg-3">
        <div class="img d-flex justify-content-center align-items-center">

            <a  class="d-flex justify-content-center align-items-center" href="<?= $this->carLink ?>">
                <?php if (!empty($this->car['filename_base'])):?>
                    <img class="img-fluid" src="<?= $this->domain ?>/images/cars/<?= $this->car['car_id'] ?>/<?= $this->escape($this->car['filename_base']) ?>_M.<?= $this->escape($this->car['filename_extension']) ?>" alt="">

                <?php else: ?>


                    <img class="img-fluid " src="/files/car.svg" alt="">

                <?php endif ?>

                <?php if($this->car['ownership_type'] == 'OWN') :?>
                    <div class="certain-car"><?= $this->translate->_('CERTIFIED_GUARANTEED')?></div>
                <?php endif ?>
            </a>

        </div>
    </div>
    <div class="col-lg-6">
        <div class="d-flex">
            <div class="location mr-1">id: <?= $this->car['sr_car_id'] ?></div>
            <div class="location col">
                <?php if ($this->car['status'] == "NOT_ON_SITE" || $this->car['status'] == 'WITHDRAWN' ): ?>
                    <strong><?= $this->translate->_('CAR_NOT_ON_SITE') ?></strong>
                <?php endif ?>
                <?= $this->escape($this->car['address'] . " - " . $this->car['name']) ?>
                <?php if(!empty($this->car['position'])) : ?>
                    <?= " | " . $this->translate->_('CAR_POSITION') . ": " . '<span class="position">'.$this->escape($this->car['position']).'</span>' ?>
                <?php endif; ?>

                <?php if($this->car['auction_price'] > 0 && isset($this->auctionSearch) && $this->auctionSearch): ?>
                    <a id="force_show_offer_form" href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarMakeOffer', true) ?>?auction=1"  title=""><b><?= $this->translate->_('MAKE_OFFER_TITLE') ?></b></a>
                <?php endif ;?>
            </div>
        </div>
        <div class="title">
            <a href="<?= $this->carLink ?>">

                <?= $this->escape($this->car['make_name']) . " " . $this->escape($this->car['model_name']) . " " . ($this->car['is_new_car'] ? date('Y') : $this->car['build_year'])  .' '. $this->translate->_('PROD.'). ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year']  .' '. $this->translate->_('REG.') : "") . ($this->language == "pl" ? " " . ( $this->car['auction_price'] > 0  && isset($this->auctionSearch) && $this->auctionSearch ? ('- cena Klienta plus '. number_format(500, 0, '', ' '). ' PLN') : $this->escape($this->car['title'])) : "")?>

            </a>
        </div>

        <div class="added">
            data dodania: <?= $this->car['added_datetime'] ?>
        </div>

        <div class="price">

            <?php if ($this->searchParameters && isset($this->searchParameters['rental']) && $this->searchParameters['rental'] == 1): ?>
                <a href="http://aa2.autoauto.pl/files/cennik_wynajmu_aut.html" target="_blank"><?= $this->translate->_('RENTAL_PRICE_LIST') ?></a>
            <?php elseif (in_array($this->car['status'], array("SOLD", "SOLD_HANDED_OUT"))): ?>
                <?= $this->translate->_('SOLD') ?>
                <?php if (in_array($this->car['status'], array("SOLD", "SOLD_HANDED_OUT"))): ?>
                <?php elseif ($this->car['is_reserved']): ?>
                    <?= $this->translate->_('RESERVATION') ?>
                <?php endif ?>
            <?php else: ?>
                <?php if($this->car['auction_price'] > 0  && isset($this->auctionSearch) && $this->auctionSearch): ?>
                    <div class="brutto"><?= $this->escape($this->carPrice($this->car, $this->language_row,false,false,'brutto',$this->car['auction_price'])) ?></div>
                <?php else: ?>

                    <div class="<?= $this->car['price_type_key']?> red-color"><?= $this->escape($this->carPrice($this->car, $this->language_row)) ?></div>
                    <?php if($this->language == 'pl') :?>
                        <?php if($this->car['price_type_key'] == 'netto'): ?>
                            <div class="brutto gray-color"><?= $this->carPrice($this->car, $this->language_row,false,false,'brutto') ?></div>
                        <?php endif; ?>
                    <?php else: ?>
                        <?php if($this->carPrice($this->car, $this->language_row, 'EUR')):?>
                            <div class="brutto gray-color"><?= $this->escape($this->carPrice($this->car, $this->language_row, 'EUR')) ?></div>
                        <?php endif;?>
                    <?php endif; ?>
                <?php endif;?>
            <?php endif ?>
        </div>

    </div>
    <div class="col-lg-3">

        <div class="actions d-flex flex-column">
            <div class="row">
                <div class="col-lg-12">
                    <a class="btn btn-action btn-action-orange d-flex justify-content-center" href="<?= $this->url(array('language' => $this->language,'id' => $this->car['car_id'],'description' => $this->carPermalink($this->car)),'forceShowCarContactCartaker',true) ?>"><span class="align-self-center"><?= $this->translate->_('CONTACT_CARETAKER') ?></span></a>
                </div>
            </div>

            <div class="row mt-auto">
                <div class="col-lg-12 col-xl-6">
                    <a class="btn btn-action" href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarSendOffer', true).$this->getString ?>"><?= $this->translate->_('SEND_OFFER') ?></a>
                    <a class="btn btn-action" href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarContactCartaker', true) ?>"><?= $this->translate->_('CONTACT_CARETAKER') ?></a>
                </div>
                <div class="col-lg-12 col-xl-6">
                    <a class="btn btn-action btn-action-gray d-flex justify-content-center" href="<?= $this->url(array('language' => $this->language,'id' => $this->car['car_id'],'description' => $this->carPermalink($this->car)),'forceShowCarTestDrive',true) ?>"><span class="align-self-center"><?= $this->translate->_('TEST_DRIVE') ?></span></a>
                </div>
            </div>

        </div>
    </div>

</div>

