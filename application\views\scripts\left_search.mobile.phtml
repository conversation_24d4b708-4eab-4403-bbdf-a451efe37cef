<div id="save-form" style="display: none">
    <?
    if ($this->saveForm) {
        $this->saveForm->setAction($this->url(array('language' => $this->language), 'list', false) . "?hash=" . $this->hash);
        echo '<div class="pp_html"><br /><h2>' . $this->translate->_('SAVE_FORM_TITLE') . '</h2>';
        echo $this->translate->_('SAVE_FORM_INSTRUCTION') . "<br /><br />";
        echo $this->saveForm;
        echo '</div>';
    }
    ?>
</div>

<h3 id="find_car"> <span class="for_icon">&nbsp;</span><?= $this->translate->_('FILTER') ?></h3>
<div id="full_form" style="display:none;">


	<h2>
		&nbsp;
		<div class="form_item form_item_submit for_submit ">
			<div class="form_input input_submit for_submit ">
				<input type="submit" helper="formSubmit" value="<?= $this->translate->_('FILTER') ?>" id="submit" name="submit">
			</div>
		</div>
	</h2>

	<div id="left_form">
		<?= $this->form; ?>
	</div>
	

</div>

<script type="text/javascript" charset="utf-8">
	$(document).ready(function(){

        $("#find_car").click(function(){
           $("#full_form").toggle();
        });

		$("#left_search h2 .form_item")
			.click(function(){
				$("#full_form form input[type='submit']").click();
			})
			.show();
		
		var autoautoLang = "<?= $this->language ?>";
		
		var confirmDeleteText = "<?= $this->escape($this->translate->_('CONFIRM_DELETE')) ?>";
		$(".confirm-delete").click(function(){
			return confirm(confirmDeleteText);
		});
	
		var multiselectOptions = {
			checkAllText: "<?= $this->escape($this->translate->_('MULTISELECT_CHECK_ALL')) ?>",
			uncheckAllText: "<?= $this->escape($this->translate->_('MULTISELECT_UNCHECK_ALL')) ?>",
			selectedText: "<?= $this->escape($this->translate->_('MULTISELECT_SELECTED_TEXT')) ?>",
			minWidth: 270
		};
		
		$("#left_search #full_form .form_label, #left_search #make_form .form_label").hide();
		
		$('#full_form #build').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('PRODUCTION_YEAR')) ?>"}));
		//$('#full_form #odometer').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('ODOMETER')) ?>"}));
		$('#full_form #categories').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('CATEGORY')) ?>"}));
		$('#full_form #important_features').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('IMPORTANT_FEATURES')) ?>"}));
		$('#full_form #engine').multiselect($.extend({}, multiselectOptions, {minWidth: 110, classes: 'engine', noneSelectedText: "<?= $this->escape($this->translate->_('ENGINE')) ?>"}));
		$('#full_form #gearboxes').multiselect($.extend({}, multiselectOptions, {minWidth: 110, noneSelectedText: "<?= $this->escape($this->translate->_('GEARBOX')) ?>"}));

        var fixedDisabledFeaturesIndexStart = 13;
        var fixedDisabledFeaturesIndexStop = 18;
		var divideModulo = [50];
		$(".for_important_features ul.ui-multiselect-checkboxes").append('<div class="clear" />');
		var lastModulo = 0;
		for (var i in divideModulo) {
			$(".for_important_features ul.ui-multiselect-checkboxes>li:lt("+divideModulo[i]+")").wrapAll('<div class="features_column" />');
		}
        $(".for_important_features ul.ui-multiselect-checkboxes li").slice(fixedDisabledFeaturesIndexStart, fixedDisabledFeaturesIndexStop).find('input').attr('checked', 'checked').attr('disabled', 'disabled').siblings('span').css('color', '#f67812');
        $(".for_important_features ul.ui-multiselect-checkboxes li").find('input[value*="category"]').siblings('span').attr('class', 'category');
        $(".for_important_features ul.ui-multiselect-checkboxes li").find('input[value*="category"]').remove();

		/*var buildFrom = $(".for_build_from").outerHtml();
		var buildTo =  $(".for_build_to").outerHtml();
		
		$(".for_build_from, .for_build_to").remove();
		$(".for_build div.ui-multiselect-menu").append(buildFrom, buildTo);
		$(".for_build_from input, .for_build_to input").css('color', '#333');
		$(".for_build_from, .for_build_to").css({'width': '115px', 'float': 'left'});
		$(".for_build_from").css({'padding-right': '27px'});
		$(".for_build_from input, .for_build_to input").css({'width': '105px'});
        
        var cubicCapacityFrom = $(".for_cubic_capacity_from").outerHtml();
        var cubicCapacityTo =  $(".for_cubic_capacity_to").outerHtml();

        $(".for_cubic_capacity_from, .for_cubic_capacity_to").remove();
        $(".for_engine div.ui-multiselect-menu").append(cubicCapacityFrom, cubicCapacityTo);
        $(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css('color', '#333');
        $(".for_cubic_capacity_from, .for_cubic_capacity_to").css({'width': '115px', 'float': 'left'});
        $(".for_cubic_capacity_from").css({'padding-right': '27px'});
        $(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css({'width': '105px'});*/
		
		$("#left_search #full_form .input_text input").compactInputs({
			getLabelFn: function(el) {
				return $(el).closest(".form_input").siblings(".form_label").find("label");
			}
		});
		
		<?php if ($this->showSaveForm): ?>
			$("#search_save").click();
		<?php endif; ?>
	
	});
</script>