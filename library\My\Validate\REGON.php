<?php

class My_Validate_REGON extends Zend_Validate_Abstract {

	const REGON_NUMBER_INVALID = 'regonNumberInvalid';
	
	protected $_messageTemplates = array(
		self::REGON_NUMBER_INVALID => "regonNumberInvalid",
	);
	
	public function isValid($value) {
		if (strlen($value) != 9 && strlen($value) != 14) {
			$this->_error(self::REGON_NUMBER_INVALID);
			return false;
		}
		
		if (strlen($value) == 9) {
			$arrSteps = array(8, 9, 2, 3, 4, 5, 6, 7);
			$intSum=0;
			for ($i = 0; $i < 8; $i++) {
				$intSum += $arrSteps[$i] * $value[$i];
			}
			$int = $intSum % 11;
			$intControlNr=($int == 10)?0:$int;
			if ($intControlNr == $value[8]) {
				return true;
			}
			
			$this->_error(self::REGON_NUMBER_INVALID);
			return false;
		}
		elseif (strlen($value) == 14) {
			//sprawdz pierwsze 9
			$arrSteps = array(8, 9, 2, 3, 4, 5, 6, 7);
			$intSum=0;
			for ($i = 0; $i < 8; $i++) {
				$intSum += $arrSteps[$i] * $value[$i];
			}
			$int = $intSum % 11;
			$intControlNr=($int == 10)?0:$int;
			if ($intControlNr != $value[8]) {
				$this->_error(self::REGON_NUMBER_INVALID);
				return false;
			}
			
			//jesli OK, sprawdz cale 14
			$arrSteps = array(2, 4, 8, 5, 0, 9, 7, 3, 6, 1, 2, 4, 8);
			$intSum=0;
			for ($i = 0; $i < 13; $i++) {
				$intSum += $arrSteps[$i] * $value[$i];
			}
			$int = $intSum % 11;
			$intControlNr=($int == 10)?0:$int;
			if ($intControlNr == $value[13]) {
				return true;
			}
			
			$this->_error(self::REGON_NUMBER_INVALID);
			return false;
		}
		else {
			$this->_error(self::REGON_NUMBER_INVALID);
			return false;
		}

	}
	
}