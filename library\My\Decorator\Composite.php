<?php

class My_Decorator_Composite extends Zend_Form_Decorator_Abstract

{
	
	protected $_noAsteriskInLabel = false;
	
	public function __construct($options=null) {
		
		if (is_array($options)) {
			if (array_key_exists('customOptions', $options)) {
				if (array_key_exists('noAsteriskInLabel', $options['customOptions'])) {
					$this->_noAsteriskInLabel = (bool)$options['customOptions']['noAsteriskInLabel'];
				}
			}
			unset($options['customOptions']);
		}
		
		parent::__construct($options);
	}

	public function buildLabel()
	{
		$element = $this->getElement();
		if ($element->getType() == "Zend_Form_Element_Submit") return "";
		
		$label = $element->getLabel();
		
		if ($translator = $element->getTranslator()) {
			$label = $translator->translate($label);
		}
		if ($element->isRequired() && !$this->_noAsteriskInLabel) {
			$label .= ' <span class="red">*</span>';
		}
		//$label .= ':';
		return '<div class="form_label for_' . $element->getName() . (($element->getAttrib('class')) ? " " . $element->getAttrib('class') : "") . '">' . $element->getView()
		->formLabel($element->getName(), $label, array('escape' => false)) . '</div>';
	}
	
	public function buildInput()
	{
		$element = $this->getElement();
		$helper = $element->helper;
		
		$value = $element->getValue();
		if ($element->getType() == "Zend_Form_Element_Submit") {
			$value = $element->getLabel();
		}
		
		return '<div class="form_input input_' . mb_strtolower(array_pop(explode("_", $element->getType())))  . " for_" . $element->getName() . " " . $element->getAttrib('class') . '">' . $element->getView()->$helper(
			$element->getName(),
			$value,
			$element->getAttribs(),
			$element->options
		) . '</div>';
	}
	
	public function buildErrors()
	{
		$element  = $this->getElement();
		$messages = $element->getMessages();
		if (empty($messages)) {
			return '';
		}
		return '<div class="form_errors">' .
		$element->getView()->formErrors($messages) . '</div>';
	}
	
	public function buildDescription()
	{
		$element = $this->getElement();
		$desc    = $element->getDescription();
		if (empty($desc)) {
			return '';
		}
		return '<div class="form_description">' . $desc . '</div>';
	}
	
	public function render($content)
	{
		
		$element = $this->getElement();
		if (!$element instanceof Zend_Form_Element) {
			return '<div class="form_item form_item_' . mb_strtolower(array_pop(explode("_", get_class($element)))) . '">' . $content . '</div>';
		}
		
		if (!empty($content)) {
			return '<div class="form_item form_item_' . mb_strtolower(array_pop(explode("_", $element->getType()))) . (($element->getAttrib('class')) ? " " . $element->getAttrib('class') : "") . ' for_' . $element->getName() . '">' . $this->buildLabel() . '<div class="form_input input_' . mb_strtolower(array_pop(explode("_", $element->getType()))) . ' for_' . $element->getName() . '">' . $content . '</div></div>' . $this->buildErrors() . (($description = $element->getDescription()) ? '<div class="form_description">' . $description . '</div>' : '');
		}
		
		if (null === $element->getView()) {
			return '<div class="form_item form_item_' . mb_strtolower(array_pop(explode("_", $element->getType()))) . (($element->getAttrib('class')) ? " " . $element->getAttrib('class') : "") . '">' . $content . '</div>';
		}
		
		$separator = $this->getSeparator();
		$placement = $this->getPlacement();
		$label     = $this->buildLabel();
		$input     = $this->buildInput();
		$errors    = $this->buildErrors();
		$desc      = $this->buildDescription();
		
		$label_input = $label . $input;
		if ($element instanceof Zend_Form_Element_Checkbox) {
			$label_input = $input . $label;
		}
		
		$output = '<div class="form_item form_item_' . mb_strtolower(array_pop(explode("_", $element->getType())))  . " for_" . $element->getName() . " " . $element->getAttrib('class') . '">'
		. $label_input
		. $errors
		. $desc
		. '</div>';
		
		switch ($placement) {
			case (self::PREPEND):
				return $output . $separator . $content;
			case (self::APPEND):
			default:
				return $content . $separator . $output;
		}
	}
}