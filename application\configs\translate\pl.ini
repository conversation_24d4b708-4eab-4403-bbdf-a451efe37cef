dateInvalid = "Ni<PERSON><PERSON><PERSON><PERSON><PERSON> wartość, powinien być napis, liczba całkowita, tablica lub Zend_Date"
dateNotYYYY-MM-DD = "'%value%' nie jest w formacie RRRR-MM-DD"
dateInvalidDate = "'%value%' nie jest prawidłową datą"
dateFalseFormat = "'%value%' nie jest datą w odpowiednim formacie"

emailAddressInvalid = "Nieprawidłowa wartość, powinien być napis"
emailAddressInvalidFormat = "Wartość nie jest w podstawowym formacie czesc-lokalna@domena"
emailAddressInvalidHostname = "'%hostname%' nie jest prawidłową domeną dla adresu '%value%'"
emailAddressLengthExceeded = "Podana wartość jest za długa"

hostnameUnknownTld = "'%value%' jest nieprawidłową domeną najwyższego poziomu"
hostnameLocalNameNotAllowed = "'%value%' wydaje się być nazwą lokalną, a nazwy lokalne są niedozwolone"

isEmpty = "Pole jest wymagane"
noRecordFound = "Brak wpisu o wartości '%value%' w bazie"
notEmptyInvalid = "Nieprawdiłowy typ wartośći, powinna być liczba zmiennoprzecinkowa, napis, tablica, zmienna boolowska lub liczba całkowita"
notFloat = "'%value%' nie jest liczbą zmiennoprzecinkową"
notGreaterOrEqual = "'%value%' nie jest większe lub równe %min%"
notGreaterThan = "'%value%' nie jest większe niż %min%"
notInt = "'%value%' nie jest liczbą całkowitą"
recordFound = "W bazie widnieje już wpis '%value%'"

stringLengthInvalid = "Nieprawidłowa wartość, powinna być typu 'napis'"
stringLengthTooShort = "'%value%' jest krótsze niż %min% znaków"
stringLengthTooLong = "'%value%' jest dłuższe niż %max% znaków"
