<?php


/*
	If data is empty(), creates empty array
	Creates paginator from data
	Checks request for 'page' parameter and if present, sets paginator's current page accordingly
	Sets given amount of items per page
	Returns paginator object
*/

class My_Controller_Action_Helper_Paginator extends Zend_Controller_Action_Helper_Abstract {
	
	public function direct($data, $itemsPerPage) {
		return $this->setupPaginator($data, $itemsPerPage);
	}
	
	public function setupPaginator(&$data, $itemsPerPage) {
		if (empty($data)) $data = array();
		$paginator = Zend_Paginator::factory($data);
		$page = (int)$this->getRequest()->getParam('page');
		if (!empty($page)) $paginator->setCurrentPageNumber($page);
    	$paginator->setItemCountPerPage($itemsPerPage);
    	return $paginator;
	}
	
}