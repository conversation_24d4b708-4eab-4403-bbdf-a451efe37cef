<html>
<head>
<title>Docs for page tcpdf.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">com-tecnick-tcpdf</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_com-tecnick-tcpdf.html" class="menu">class tree: com-tecnick-tcpdf</a> ]
		  [ <a href="../elementindex_com-tecnick-tcpdf.html" class="menu">index: com-tecnick-tcpdf</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_com-tecnick-tcpdf.html">com-tecnick-tcpdf</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../com-tecnick-tcpdf/_2dbarcodes.php.html">		2dbarcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_barcodes.php.html">		barcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_htmlcolors.php.html">		htmlcolors.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_qrcode.php.html">		qrcode.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_tcpdf.php.html">		tcpdf.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_config---tcpdf_config.php.html">		tcpdf_config.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_unicode_data.php.html">		unicode_data.php
		</a><br>
	  </div><br />
      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../com-tecnick-tcpdf/QRcode.html">QRcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF.html">TCPDF</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF2DBarcode.html">TCPDF2DBarcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDFBarcode.html">TCPDFBarcode</a><br />
	  </div>
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: tcpdf.php</h1>
Source Location: /tcpdf.php<br /><br />

<br>
<br>

<div class="contents">
<h2>Classes:</h2>
<dt><a href="../com-tecnick-tcpdf/TCPDF.html">TCPDF</a></dt>
	<dd>This is a PHP class for generating PDF documents without requiring external extensions.<br /></dd>
</div><br /><br />

<h2>Page Details:</h2>
This is a PHP class for generating PDF documents without requiring external extensions.<br /><br /><br /><p>TCPDF project (http://www.tcpdf.org) was originally derived in 2002 from the Public Domain FPDF class by Olivier Plathey (http://www.fpdf.org), but now is almost entirely rewritten.<br />  &lt;h3&gt;TCPDF main features are:&lt;/h3&gt;  <ul><li>no external libraries are required for the basic functions;</li><li>supports all ISO page formats;</li><li>supports custom page formats, margins and units of measure;</li><li>supports UTF-8 Unicode and Right-To-Left languages;</li><li>supports TrueTypeUnicode, OpenTypeUnicode, TrueType, OpenType, Type1 and CID-0 fonts;</li><li>supports document encryption;</li><li>includes methods to publish some XHTML code, including forms;</li><li>includes graphic (geometric) and transformation methods;</li><li>includes Javascript and Forms support;</li><li>includes a method to print various barcode formats: CODE 39, ANSI MH10.8M-1983, USD-3, 3 of 9, CODE 93, USS-93, Standard 2 of 5, Interleaved 2 of 5, CODE 128 A/B/C, 2 and 5 Digits UPC-Based Extention, EAN 8, EAN 13, UPC-A, UPC-E, MSI, POSTNET, PLANET, RMS4CC (Royal Mail 4-state Customer Code), CBC (Customer Bar Code), KIX (Klant index - Customer index), Intelligent Mail Barcode, Onecode, USPS-B-3200, CODABAR, CODE 11, PHARMACODE, PHARMACODE TWO-TRACKS</li><li>includes methods to set Bookmarks and print a Table of Content;</li><li>includes methods to move and delete pages;</li><li>includes methods for automatic page header and footer management;</li><li>supports automatic page break;</li><li>supports automatic page numbering and page groups;</li><li>supports automatic line break and text justification;</li><li>supports JPEG and PNG images natively, all images supported by GD (GD, GD2, GD2PART, GIF, JPEG, PNG, BMP, XBM, XPM) and all images supported via ImagMagick (http://www.imagemagick.org/www/formats.html)</li><li>supports stroke and clipping mode for text;</li><li>supports clipping masks;</li><li>supports Grayscale, RGB, CMYK, Spot Colors and Transparencies;</li><li>supports several annotations, including links, text and file attachments;</li><li>supports page compression (requires zlib extension);</li><li>supports text hyphenation.</li><li>supports transactions to UNDO commands.</li><li>supports signature certifications.</li></ul>  Tools to encode your unicode fonts are on fonts/utils directory.&lt;/p&gt;</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Nicola Asuni</td>
  </tr>
  <tr>
    <td><b>version:</b>&nbsp;&nbsp;</td><td>4.9.001</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2002-2010 Nicola Asuni - Tecnick.com S.r.l (www.tecnick.com) Via Della Pace, 11 - 09044 - Quartucciu (CA) - ITALY - www.tecnick.com - <EMAIL></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.tcpdf.org">http://www.tcpdf.org</a></td>
  </tr>
  <tr>
    <td><b>abstract:</b>&nbsp;&nbsp;</td><td>Class for generating PDF files on-the-fly without requiring external extensions.</td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a></td>
  </tr>
</table>
</div>
<br /><br />
<h4>Includes:</h4>
<div class="tags">
require_once(dirname(__FILE__).'/config/tcpdf_config.php') [line 138]<br />
main configuration file<br /><br />require_once(dirname(__FILE__).'/htmlcolors.php') [line 150]<br />
html colors table<br /><br />require_once(dirname(__FILE__).'/unicode_data.php') [line 145]<br />
unicode data<br /><br /></div>
<br /><br />
<br /><br />
  <hr />
	<a name="definePDF_PRODUCER"></a>
	<h3>PDF_PRODUCER <span class="smalllinenumber">[line 156]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_PRODUCER = 'TCPDF 4.9.001 (http://www.tcpdf.org)'</code>
    </td></tr></table>
    </td></tr></table>

    define default PDF document producer<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
<br />

        <div class="credit">
		    <hr />
		    Documentation generated on Sun, 28 Mar 2010 22:22:40 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>