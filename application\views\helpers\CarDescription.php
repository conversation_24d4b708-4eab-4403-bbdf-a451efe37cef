<?php

class Zend_View_Helper_CarDescription extends Zend_View_Helper_Abstract {

    public function carDescription($text, $justStrip=false) {
        $t = $text;

        //bullets
        $pattern = array(
            "/^[\-]\s?(.+)/um", //m - treat newlines properly
        );
        $replacement = array(
            '<div class="bullet"><span class="for_icon"></span>$1</div>',
        );
        if ($justStrip) {
            $replacement = array(
                "* $1"
            );
        }

        $t = preg_replace($pattern, $replacement, $t);

        $t = str_replace("</div>\n", "</div>", $t);

        //bold
        $pattern = "/\[b\](.*)\[\/b\]/";
        $replacement = '<strong>$1</strong>';
        if ($justStrip) {
            $replacement = "$1";
        }

        $t = preg_replace($pattern, $replacement, $t);

        //newlines -> br
        if (!$justStrip) {
            $t = nl2br($t);
        }

        return $t;
    }

}