<?php

class Model_SrAdmin extends Model_Base {
		
	public function cleanCache($data) {
		try {
			$cache = Zend_Registry::get('Cache');
			
			if (count($data['ids']) > 0) {
				foreach ($data['ids'] as $id) {
					if (!empty($id)) {
						$cache->remove($id);
					}
				}
			}
			
			$tagModes = array('MATCHING_TAG' => Zend_Cache::CLEANING_MODE_MATCHING_TAG, 'NOT_MATCHING_TAG' => Zend_Cache::CLEANING_MODE_NOT_MATCHING_TAG, 'MATCHING_ANY_TAG' => Zend_Cache::CLEANING_MODE_MATCHING_ANY_TAG);
			
			if (count($data['tags']) > 0 && array_key_exists($data['mode'], $tagModes)) {
				$mode = $tagModes[$data['mode']];
				$cache->clean($mode, $data['tags']);
			}
		}
		catch (Exception $e) {
			Zend_Debug::dump($data,'<h2>data</h2>');
			throw $e;
		}
	}
	
	public function cleanCacheAll() {
		$cache = Zend_Registry::get('Cache');
		$cache->clean();
	}
	
}