<?php

class My_ImageResize {
	
	protected function _imageResizeStrict($source, $destination, $destination_X, $destination_Y, $forceExtension=null, $overlayFile=null) {
        $allowedExtensions = array('jpg', 'jpeg', 'gif', 'png');

        $name = explode(".", $source);
        $fileExtension = strtolower(array_pop($name));
        if ($forceExtension !== null) $fileExtension = strtolower($forceExtension);

        if (in_array($fileExtension, $allowedExtensions)) {
            $size = getImageSize($source);
            $width = $size[0];
            $height = $size[1];

            if ($destination_X > $width)
                $destination_X = $width;
            if ($destination_Y > $height)
                $destination_Y = $height;
            if ($destination_X == 0 && $destination_Y != 0) {
                $proportion_Y = $height / $destination_Y;
                $destination_X = $width / $proportion_Y;
            } else if ($destination_X != 0 && $destination_Y == 0) {
                $proportion_X = $width / $destination_X;
                $destination_Y = $height / $proportion_X;
            }

            if ($width >= $destination_X and $height >= $destination_Y) {

                $proportion_X = $width / $destination_X;
                $proportion_Y = $height / $destination_Y;

                if ($proportion_X > $proportion_Y )
                    $proportion = $proportion_Y;
                else
                    $proportion = $proportion_X ;
               
                $target['width'] = $destination_X * $proportion;
                $target['height'] = $destination_Y * $proportion;

                $original['diagonal_center'] = round(sqrt(($width * $width) + ($height * $height)) / 2);
                $target['diagonal_center'] = round(sqrt(($target['width'] * $target['width']) + ($target['height'] * $target['height'])) / 2);

                $crop = round($original['diagonal_center'] - $target['diagonal_center']);

                if ($proportion_X < $proportion_Y ) {
                    $target['x'] = 0;
                    $target['y'] = round((($height / 2) * $crop) / $target['diagonal_center']);
                } else {
                    $target['x'] =  round((($width / 2) * $crop) / $target['diagonal_center']);
                    $target['y'] = 0;
                }

                if ($fileExtension == "jpg" or $fileExtension == 'jpeg')
                    $from = ImageCreateFromJpeg($source);
                elseif ($fileExtension == "gif")
                    $from = ImageCreateFromGIF($source);
                elseif ($fileExtension == 'png')
                     $from = imageCreateFromPNG($source);
            }
           
            $new = ImageCreateTrueColor ($destination_X, $destination_Y);

            imagecopyresampled ($new,  $from,  0, 0, $target['x'], $target['y'], $destination_X, $destination_Y, $target['width'], $target['height']);

			if ($overlayFile !== null) {
				$extension = strtolower(array_pop(explode('.', basename($overlayFile))));
				if ($extension !== "png") {
				        throw new Exception("Nieprawidłowe rozszerzenie znaku wodnego (musi być PNG)");
				}
				
				imagealphablending($new, true); 
				
				$overlay = imagecreatefrompng($overlayFile);
				$owidth = imagesx($overlay);
				$oheight = imagesy($overlay);
				
				/*
				//bottom-right
				$offsetX = 10;
				$offsetY = 10;
				imagecopy($new, $overlay, $destination_X - $owidth - $offsetX, $destination_Y - $oheight - $offsetY, 0, 0, $owidth, $oheight);
				*/
				
				
				//center
				$offsetX = ($destination_X - $owidth) / 2;
				$offsetY = ($destination_Y - $oheight) / 2;
				// echo "$offsetX -- $offsetY";
				imagecopy($new, $overlay, $offsetX, $offsetY, 0, 0, $owidth, $oheight);
				
				
			}//if

            if ($fileExtension == "jpg" OR $fileExtension == 'jpeg') imagejpeg($new, $destination, 75);
            elseif ($fileExtension == "gif") imagegif($new, $destination);
            elseif ($fileExtension == 'png') imagepng($new, $destination);
                
	    }//if
        else {
        	throw new Exception("Nieznane rozszerzenie pliku");
        }
    }

    protected function _imageResize($source, $destination, $destination_X, $destination_Y, $forceExtension=null, $overlayFile=null) {
        $allowedExtensions = array('jpg', 'jpeg', 'gif', 'png');

        $name = explode(".", $source);
        $fileExtension = strtolower(array_pop($name));
        if ($forceExtension !== null) $fileExtension = strtolower($forceExtension);

        if (in_array($fileExtension, $allowedExtensions)) {
        	$new = null;
        	$from = null;
            $size = getimagesize($source);
            $width = $size[0];
            $height = $size[1];
            
			if($fileExtension == "jpg" or $fileExtension == 'jpeg')
				$from = ImageCreateFromJpeg($source);
			elseif ($fileExtension == "gif")
				$from = ImageCreateFromGIF($source);
			elseif ($fileExtension == 'png')
				$from = imageCreateFromPNG($source);

            if ($destination_X > $width)
                $destination_X = $width;
            if ($destination_Y > $height)
                $destination_Y = $height;
            if ($destination_X == 0 && $destination_Y != 0) {
                $proportion_Y = $height / $destination_Y;
                $destination_X = $width / $proportion_Y;
            } else if ($destination_X != 0 && $destination_Y == 0) {
                $proportion_X = $width / $destination_X;
                $destination_Y = $height / $proportion_X;
            }
            
			if($width > $destination_X || $height > $destination_Y) {
				$proportion_X = $destination_X / $width;
				$proportion_Y = $destination_Y / $height;
				
				if($proportion_X > $proportion_Y ) {
					$proportion = $proportion_Y;
				}
				else {
					$proportion = $proportion_X;
				}
				
				$destination_X = round($width * $proportion);
				$destination_Y = round($height * $proportion);
			}
			else {
				$destination_X = $width;
				$destination_Y = $height;
			}
			
			$new = ImageCreateTrueColor ($destination_X, $destination_Y);
			imagecopyresampled($new,  $from,  0, 0, 0, 0, $destination_X, $destination_Y, $width, $height);

			if ($overlayFile !== null) {
				$extension = strtolower(array_pop(explode('.', basename($overlayFile))));
				if ($extension !== "png") {
				        throw new Exception("Nieprawidłowe rozszerzenie znaku wodnego (musi być PNG)");
				}
				
				imagealphablending($new, true); 
				
				$overlay = imagecreatefrompng($overlayFile);
				$owidth = imagesx($overlay);
				$oheight = imagesy($overlay);
				
				/*
				//bottom-right
				$offsetX = 10;
				$offsetY = 10;
				imagecopy($new, $overlay, $destination_X - $owidth - $offsetX, $destination_Y - $oheight - $offsetY, 0, 0, $owidth, $oheight);
				*/
				
				//center
				$offsetX = ($destination_X - $owidth) / 2;
				$offsetY = ($destination_Y - $oheight) / 2;
				// echo "$offsetX -- $offsetY";
				imagecopy($new, $overlay, $offsetX, $offsetY, 0, 0, $owidth, $oheight);
				
				
			}//if overlay

            if ($fileExtension == "jpg" OR $fileExtension == 'jpeg') imagejpeg($new, $destination, 75);
            elseif ($fileExtension == "gif") imagegif($new, $destination);
            elseif ($fileExtension == 'png') imagepng($new, $destination);
                
	    }//if
        else {
        	throw new Exception("Nieznane rozszerzenie pliku");
        }
    }
	
	public function process($tmpName, $uploadName, $destDir, $dstSize, $overlayFile=null, $strictSize=false) {
		//$upload == $_FILES['input_name']
		$extExplode = explode(".", $uploadName);
		if (count($extExplode) < 2) {
			throw new Exception("Could not determine file extension in " . __METHOD__ . ", line " . (__LINE__ - 1) . "<br />" . $uploadName);
		}
		$fileExt = array_pop($extExplode);
		$destFile = $destDir . DIRECTORY_SEPARATOR . $uploadName;
		
		$size = getimagesize($tmpName);
        $width = $size[0];
        $height = $size[1];
        
        if (is_array($dstSize)) {
        	$sizeX = $dstSize[0];
        	$sizeY = $dstSize[1];
        }
        else {
	        if ($width > $height) {
	        	$sizeX = $dstSize;
	        	$sizeY = $height / $width * $dstSize;
	        }
	        else {
	        	$sizeY = $dstSize;
	        	$sizeX = $width / $height * $dstSize;
	        }
        }
        if ($strictSize) {
        	$this->_imageResizeStrict($tmpName, $destFile, $sizeX, $sizeY, $fileExt, $overlayFile, $strict=true);
        }
        else {
			$this->_imageResize($tmpName, $destFile, $sizeX, $sizeY, $fileExt, $overlayFile);
		}
	}
   
}