<html>
	<table width="100%" border="1" bgcolor="#000" cellpadding="5" cellspacing="0">
		<tr align="center">
			<td><b><font color="#fff"><?= $this->translate->_('CAR') ?></font></b></td>
			<td><b><font color="#fff"><?= $this->translate->_('PRICE') ?></font></b></td>
			<td><b><font color="#fff"><?= $this->translate->_('LOCATION') ?></font></b></td>
			<td><b><font color="#fff"><?= $this->translate->_('CAR_POSITION') ?></font></b></td>
		</tr>
		<?php $colors = array(false => array("#fff", "#000"), true => array("#f3f3f3", "#000")); $odd = true; foreach ($this->cars as $car): ?>
			<tr bgcolor="<?= $colors[$odd][0] ?>" align="center">
				<td><b><?= $this->escape($car['make_name'] . " " . $car['model_name'] . " " . $car['title'] . ", " . $car['build_year']) ?></b></td>
				<td><b><?= $this->escape($this->carPrice($car, $this->language_row)) ?></b></td>
				<td><b><?= $this->escape($car['address'] . " - " . $car['name'])?></b></td>
				<td><b><font size="8">
                
                        <?php if ($car['status'] == "NOT_ON_SITE" || $car['status'] == "WITHDRAWN"): ?>
                            <strong><?= $this->translate->_('NOT_ON_SITE') ?></strong>
                        <?php else: ?>
                            <?= $this->escape($this->cat['short_name'] . " " . $car['position']) ?>
                        <?php endif ;?>
                        </font></b>
                
                
                </td>
			</tr>
		<?php $odd = !$odd; endforeach; ?>
	</table>
</html>