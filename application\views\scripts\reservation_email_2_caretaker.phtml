<html>
	<head>
		
	</head>
	<body>
		<div><b>Zmiana statusu rezerwacji samochodu <?= $this->escape($this->carText) ?></b></div>
		<div>
			<a target="_blank" href="<?= $this->wwwDomain . $this->carLink($this->carData, "", $onlyHref=true) ?>">&raquo; Strona samochodu w serwisie www (z obrazkami)</a><br />
			<a target="_blank" href="<?= $this->srDomain .  "/cars/show-car/id/" . $this->carData['sr_car_id']?>">&raquo; Zobacz samochód w systemie rozliczeniowym (sys2)</a>
		</div>
		
		<br />
		
		<div>
			Informacja od platnosci.pl - zmiana statusu płatności wadium
			<br />
			<b>Status rezerwacji</b>: <u><?= $this->translate->_($this->status) ?></u><br />
			<b>Kwota wadium</b>: <?= $this->escape($this->reservation['amount']) ?> zł<br />
			<b>Cena zakupu</b>: <?= $this->escape($this->reservation['buy_price']) ?> zł<br />
		</div>
		
		<br />
		
		<div>
			<b>Dane klienta</b><br />
			<b>Imię, nazwisko</b>: <?= $this->escape($this->user['first_name'] . ' ' . $this->user['last_name']) ?><br />
			<b>E-mail</b>: <a href="mailto:<?= $this->escape($this->user['email']) ?>?subject=Odp: Oferta na samochód <?= $this->escape($this->carText) ?>"><?= $this->escape($this->user['email']) ?></a><br />
			<?php if (!empty($this->user['company_name'])): ?>
				<b>Nazwa firmy</b>: <?= $this->escape($this->user['company_name']) ?><br />
			<?php endif ?>
			<?php if (!empty($this->user['phone'])): ?>
				<b>Telefon</b>: <?= $this->escape($this->user['phone']) ?><br />
			<?php endif ?>
		</div>
	</body>
</html>