<?php

class My_PlatnosciPl {
	
	protected $_encoding = "UTF";
	protected $_key1;
	protected $_key2;
	protected $_pos_id;
	protected $_pos_auth_key;
	
	protected $_url_platnosci_pl = "https://www.platnosci.pl/paygw";
	protected $_url_new_payment = "NewPayment";
	protected $_url_payment_get = "Payment/get";
	
	public function __construct($options) {
		if (array_key_exists("encoding", $options)) {
			$this->_encoding = $options['encoding'];
		}
		if (array_key_exists("key1", $options)) {
			$this->_key1 = $options['key1'];
		}
		if (array_key_exists("key2", $options)) {
			$this->_key2 = $options['key2'];
		}
		if (array_key_exists("pos_id", $options)) {
			$this->_pos_id = $options['pos_id'];
		}
		if (array_key_exists("pos_auth_key", $options)) {
			$this->_pos_auth_key = $options['pos_auth_key'];
		}
		
		$obligatory = array("_encoding", "_key1", "_key2", "_pos_id", "_pos_auth_key", "_url_platnosci_pl", "_url_new_payment", "_url_payment_get");
		foreach ($obligatory as $fieldName) {
			if (empty($this->{$fieldName})) {
				throw new Exception("Błąd przy inicjalizowaniu klasy Platnosci.pl - puste pole " . $fieldName . " w " . __METHOD__ . ", line " . (__LINE__ - 1));
			}
		}
	}
	
	public function checkStatus($payment) {
		$sig = md5($this->_pos_id . $payment['session_id'] . $payment['ts'] . $this->_key1);
				
		$parameters = "pos_id=" . $this->_pos_id . "&session_id=" . $payment['session_id'] . "&ts=" . $payment['ts'] . "&sig=" . $sig;
		$result = false;

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $this->getUrlPaymentGet());
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_TIMEOUT, 20);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $parameters);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		$response = curl_exec($ch);
		curl_close($ch);
		$xml = new SimpleXMLElement($response);

		
		if ($xml->trans->pos_id) {
			$posId = $xml->trans->pos_id;
			$sessionId = $xml->trans->session_id;
			$orderId = $xml->trans->order_id;
			$amount = $xml->trans->amount;
			$status = $xml->trans->status;
			$desc = $xml->trans->desc;
			$ts = $xml->trans->ts;
			$sig = $xml->trans->sig;
			
			$responseSig = md5($posId . $sessionId . $orderId . $status . $amount . $desc . $ts . $this->_key2);
			if ($responseSig == $sig) {
				if ($sessionId == $payment['session_id']) {
					$retData = array(
						'status' => $status,
						'amount' => $amount
					);
					return $retData;
				} else {
					throw new Exception("Session id mismatch in " . __METHOD__ . ", line " . (__LINE__ - 1));
				}
			}
			else {
				throw new Exception("Response signature mismatch in " . __METHOD__ . ", line " . (__LINE__ - 1));
			}
		}
		else {
			throw new Exception("Pos_id field not found in response in " . __METHOD__ . ", line " . (__LINE__ - 1) . " xml=" . $response);
		}
	}
	
	public function getEncoding() {
		return $this->_encoding;
	}
	
	public function getKey1() {
		return $this->_key1;
	}
	
	public function getKey2() {
		return $this->_key2;
	}
	
	public function getNewPaymentSig($data) {
		$str = "";
		$fields = array("pos_id", "pay_type", "session_id", "pos_auth_key", "amount", "desc", "desc2", "trsDesc", "order_id", "first_name", "last_name", "payback_login", "street", "street_hn", "street_an", "city", "post_code", "country", "email", "phone", "language", "client_ip", "ts");
		foreach ($fields as $fieldName) {
			if (array_key_exists($fieldName, $data)) {
				$str .= $data[$fieldName];
			}
		}
		$str .= $this->_key1;
		return md5($str);
	}
	
	public function getPayTypes() {
		$xml = file_get_contents($this->_url_platnosci_pl . "/" . $this->_encoding . "/xml/" . $this->_pos_id . "/" . substr($this->_key1, 0, 2) . "/paytype.xml");
		Zend_Debug::dump($xml,'<h2></h2>');
		die("Niedokonczone z braku klucza :P");
	}
	
	public function getPosId() {
		return $this->_pos_id;
	}
	
	public function getPosAuthKey() {
		return $this->_pos_auth_key;
	}
	
	public function getUrlNewPayment($qsData=null) {
		//qsData = query string data for GET requests
		$url = $this->_url_platnosci_pl . "/" . $this->_encoding . "/" . $this->_url_new_payment;
		if (is_array($qsData) && count($qsData) > 0) {
			$dataString = "?";
			foreach ($qsData as $key => $value) {
				$dataString .= urlencode($key) . "=" . urlencode($value) . "&";
			}
			$dataString = rtrim($dataString, "&");
			$url .= $dataString;
		}
		return $url;
	}
	
	public function getUrlPaymentGet() {
		$url = $this->_url_platnosci_pl . "/" . $this->_encoding . "/" . $this->_url_payment_get;
		return $url;
	}
	
	public function getUrlPlatnosciPl() {
		return $this->_url_platnosci_pl;
	}
	
}