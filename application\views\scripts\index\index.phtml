<?php if ($this->makesWithCounts && count($this->makesWithCounts) > 0): ?>
    <div id="front-makes" class="py-4">

        <?php
        $count = count($this->makesWithCounts);
        $cols = 5;
        $rows = $count / $cols;
        $mod = $count % $cols;
        if ($mod > 0) $rows++;

        $makesRows = array();
        for ($i = 0; $i < $cols; $i++) {
            $makesRows[$i] = array();
        }
        $i = 0;

        foreach ($this->makesWithCounts as $index => $item) {
            $makesRows[$i % $rows][] = array($index => $item);
            $i++;
        }

        ?>
        <div class="row">


            <?php $i = 0;
            foreach ($this->makesWithCounts as $item):

                ?>

                <div class="col-6 col-md-3 col-lg-3">

                    <?= $this->partial(
                        'make_boxes.phtml',
                        array(
                            'view' => $this,
                            'makesWithCounts' => array($item),
                            'types' => array(1),
                            'nonArrayFilters' => array()
                        )
                    )
                    ?>
                </div>
                <?php $i++; endforeach ?>


        </div>


    </div>
<?php endif ?>



<div id="cars-categories">
    <div class="row">
        <?php


        $params = array(
            0 => array('description' => 'CUSTOM_CATEGORY_DESCRIPTION_UO_TO3.5t', 'image' => '/images/category_up_to_3.5t.jpg', 'categories' => array('up_to_3.5t')),
            1 => array('description' => 'CUSTOM_CATEGORY_DESCRIPTION_SEDAN_HATCHBACK', 'image' => '/images/coupe.jpg', 'categories' => array('sedan', 'hatchback')),
            2 => array('description' => 'CUSTOM_CATEGORY_DESCRIPTION_SUV_PICKUP', 'image' => '/images/exclusive.jpg', 'categories' => array('suv', 'pickup')),
            3 => array('description' => 'CUSTOM_CATEGORY_DESCRIPTION_COMBI_VAN', 'image' => '/images/kombi.jpg', 'categories' => array('van', 'combi')),
            4 => array('description' => 'CUSTOM_CATEGORY_DESCRIPTION_COUPE_CABRIO', 'image' => '/images/pickup.jpg', 'categories' => array('coupe', 'cabrio')),
            5 => array('description' => 'CUSTOM_CATEGORY_DESCRIPTION_PREMIUM', 'image' => '/images/premium.jpg', 'premium' => '1'),
            6 => array('description' => 'CUSTOM_CATEGORY_DESCRIPTION_EXCLUSIVE', 'image' => '/images/sedan.jpg', 'exclusive' => '1'),

        );
        $i = 0;
        foreach ($params as $p):
            ?>

            <div class="col-4 col-lg-2">
                <a class="d-flex"
                   href="<?= $this->url(array('language' => $this->language), 'list', true) . '?' . http_build_query(array('categories' => $p['categories'])) ?>"
                   style="background: url('<?= $p['image'] ?>') center center">
                    <div class="cars-categories-text d-flex align-items-center justify-content-center">
                        <span class=""><?= $this->translate->_($p['description']) ?></span>
                    </div>

                </a>
            </div>

            <?php $i++; endforeach ?>
    </div>
</div>



<div id="contact" class="row py-4">
    <div class="col-lg-4">
        <h3 class="contact">Kontakt</h3>
        <p class="open-hours">7 dni w tygodniu od 9 do 18</p>
        <?php if (is_array($this->randomEmployees) && $this->randomEmployees[0]): ?>
            <p class="random-employee">
                <?= $this->escape($this->randomEmployees[0]['first_name'] . " " . $this->randomEmployees[0]['last_name']) ?><br />
                <a href="mailto:<?= $this->escape($this->randomEmployees[0]['email']) ?>"><?= $this->escape($this->randomEmployees[0]['email']) ?></a> <br />
                <?= $this->escape($this->randomEmployees[0]['phone']) ?>
                <?php if (!empty($this->randomEmployees[0]['skype'])): ?>
                    <br />
                    <a class="skype" href="skype:<?= $this->escape($this->randomEmployees[0]['skype']) ?>?call"></a>
                <?php endif ?>
            </p>
        <?php endif ?>
        <div class="new-cars mt-4" onclick="window.location.href='<?= $this->url(array('language' => $this->language), 'list', true) ?>'">
            <h3>Codziennie ponad <br />
                <strong>10 nowych ofert</strong>
            </h3>
            <p>sprawdź ></p>
        </div>


    </div>
    <div class="col-lg-8">
        <script src="https://maps.googleapis.com/maps/api/js?v=3&libraries=places"></script>
        <script>
            var geocoder;
            var googleMap;
            var marker;

            function initializeMap() {
                setTimeout(function() {
                    geocoder = new google.maps.Geocoder();
                    var myLatlng = new google.maps.LatLng(52.183400, 20.952342);
                    var pointerAlKrakowska = new google.maps.LatLng(52.183400, 20.952342);

                    var mapOptions = {
                        zoom: 11,
                        scrollwheel: false,
                        clickable: false,
                        disableDefaultUI: true,
                        center: myLatlng,
                        draggable: false,
                        styles: [
                            {
                                featureType: 'water',
                                stylers: [
                                    { color: '#b5d2ea' },
                                    { visibility: 'on' }
                                ]
                            },
                            {
                                featureType: 'landscape',
                                stylers: [ { color:'#f2f2f2' } ]
                            },
                            {
                                featureType: 'road',
                                stylers: [
                                    { saturation: -100 },
                                    { lightness: 45 }
                                ]
                            },
                            {
                                featureType: 'road.highway',
                                stylers: [ { visibility:'on' } ]
                            },
                            {
                                featureType: 'road.arterial',
                                elementType: 'labels.icon',
                                stylers:[ { visibility:'on' } ]
                            },
                            {
                                featureType: 'administrative',
                                elementType: 'labels.text.fill',
                                stylers: [ { color: '#444444' } ]
                            },
                            {
                                featureType: 'transit',
                                stylers:[ { visibility:'on' } ]
                            },{
                                featureType: 'poi',
                                stylers: [
                                    { visibility: 'on' },
                                    { color: '#dcecd6' }
                                ]
                            }
                        ]
                    };

                    googleMap = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);


                    marker = new google.maps.Marker({
                        map: googleMap,
                        draggable:false,
                        animation: google.maps.Animation.DROP,
                        position: pointerAlKrakowska,
                        icon: '/images/_new/marker-blue.png',
                        url: 'http://maps.google.com/maps?q=Al.+Krakowska+178,+Warszawa&amp;hl=<?= $this->language ?>&amp;ie=UTF8&amp;ll=52.1833,20.952344&amp;spn=0.009736,0.027466&amp;sll=52.232491,21.11392&amp;sspn=0.009725,0.027466&amp;z=16&amp;output=embed&amp;iframe=true&amp;width=900&amp;height=550'
                    });

                    google.maps.event.addListener(marker, 'click', function() {
                        window.open(marker.url);
                    });


                }, 100);

            }

            google.maps.event.addDomListener(window, 'load', initializeMap);

        </script>
        <div id="map-canvas"></div>
    </div>
</div>
<div id="info" class="row py-4">
    <div class="col-lg-4">
        <h3>Od ponad <strong>20 lat skupujemy i&nbsp;sprzedajemy auta używane</strong>. Warszawa to miasto, na terenie którego prowadzimy naszą działalność, współpracując z szerokim gronem przedsiębiorców oraz klientów indywidualnych.<h3>
    </div>
    <div class="col-lg-8 text">
        W przygotowanej przez nas ofercie sprzedażowej można znaleźć utrzymane w doskonałym stanie, luksusowe samochody używane takich marek, jak Land Rover, Jaguar czy Aston Martin. Wszystkie auta są poddane szczegółowym czynnościom kontrolnym i serwisowym- zyskujemy dzięki temu pewność, iż są one w pełni sprawne technicznie. Sprzedajemy również przeznaczone do codziennego użytku, osobowe samochody używane oraz auta terenowe. Używane pojazdy dostępne są w bardzo atrakcyjnych cenach. Na stronie zamieściliśmy wygodną w obsłudze wyszukiwarkę, pozwalającą na określenie takich danych, jak rocznik pojazdu, cena, pojemność silnika czy rodzaj nadwozia. Korzystając z tego udogodnienia, można w wygodny sposób odnaleźć odpowiednie samochody używane (Sprzedaż). Warszawa to miasto, w którym do dyspozycji naszych klientów pozostają setki pojazdów najlepszych marek. Zapewniamy kompleksową obsługę, pomagając w ubieganiu się o kredyty oraz leasingi. Współpracujemy także z firmami ubezpieczeniowymi, negocjując korzystne warunki współpracy dla osób, zakupujących nasze używane samochody. Luksusowe pojazdy, takie jak Lexus, czy Lincoln, dostępne są w niewiarygodnie korzystnych cenach. Osoby, poszukujące uczciwego, godnego zaufania sprzedawcy, często odwiedzają nasze komisy samochodowe. Warszawa to miasto, w którym działamy w trzech, doskonale zlokalizowanych punktach. Sprzedając samochody luksusowe używane udostępniamy możliwość wykonania jazdy próbnej oraz dokładnej oceny stanu pojazdu. Nasi konsultanci zapewniają profesjonalne doradztwo oraz pomagają w dokonaniu rozsądnego wyboru auta. Każdego dnia oferta naszej firmy uzupełniana jest o luksusowe auta używane oraz co najmniej dziesięć nowych samochodów osobowych. Zapewniamy także możliwość kupna samochodu za gotówkę od osób z Warszawy i okolic.
    </div>
</div>