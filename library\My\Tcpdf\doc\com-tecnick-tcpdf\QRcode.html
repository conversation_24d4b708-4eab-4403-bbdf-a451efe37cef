<html>
<head>
<title>Docs For Class QRcode</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">com-tecnick-tcpdf</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_com-tecnick-tcpdf.html" class="menu">class tree: com-tecnick-tcpdf</a> ]
		  [ <a href="../elementindex_com-tecnick-tcpdf.html" class="menu">index: com-tecnick-tcpdf</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_com-tecnick-tcpdf.html">com-tecnick-tcpdf</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../com-tecnick-tcpdf/_2dbarcodes.php.html">		2dbarcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_barcodes.php.html">		barcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_htmlcolors.php.html">		htmlcolors.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_qrcode.php.html">		qrcode.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_tcpdf.php.html">		tcpdf.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_config---tcpdf_config.php.html">		tcpdf_config.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_unicode_data.php.html">		unicode_data.php
		</a><br>
	  </div><br />
      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../com-tecnick-tcpdf/QRcode.html">QRcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF.html">TCPDF</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF2DBarcode.html">TCPDF2DBarcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDFBarcode.html">TCPDFBarcode</a><br />
	  </div>
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Class: QRcode</h1>
Source Location: /qrcode.php<br /><br />


<table width="100%" border="0">
<tr><td valign="top">

<h3><a href="#class_details">Class Overview</a></h3>
<pre></pre><br />
<div class="description">Class to create QR-code arrays for TCPDF class.</div><br /><br />
<h4>Author(s):</h4>
<ul>
          <li>Nicola Asuni</li>
                                          </ul>




        
          
                  
<h4>Version:</h4>
<ul>
  <li>1.0.000</li>
</ul>

<h4>Copyright:</h4>
<ul>
  <li>2010 Nicola Asuni - Tecnick.com S.r.l (www.tecnick.com) Via Della Pace, 11 - 09044 - Quartucciu (CA) - ITALY - www.tecnick.com - <EMAIL></li>
</ul>
        
</td>

<td valign="top">
<h3><a href="#class_vars">Variables</a></h3>
<ul>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$alignmentPattern">$alignmentPattern</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$anTable">$anTable</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$b1">$b1</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$barcode_array">$barcode_array</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$bit">$bit</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$blocks">$blocks</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$capacity">$capacity</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$casesensitive">$casesensitive</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$count">$count</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$data">$data</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$datacode">$datacode</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$dataLength">$dataLength</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$dataStr">$dataStr</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$dir">$dir</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$ecccode">$ecccode</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$eccLength">$eccLength</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$eccTable">$eccTable</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$formatInfo">$formatInfo</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$frame">$frame</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$frames">$frames</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$hint">$hint</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$items">$items</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$lengthTableBits">$lengthTableBits</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$level">$level</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$rsblocks">$rsblocks</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$rsitems">$rsitems</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$runLength">$runLength</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$structured">$structured</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$version">$version</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$versionPattern">$versionPattern</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$width">$width</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$x">$x</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#var$y">$y</a></li>
  </ul>
</td>


<td valign="top">
<h3><a href="#class_methods">Methods</a></h3>
<ul>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#method__construct">__construct</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodallocate">allocate</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodappendBitstream">appendBitstream</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodappendBytes">appendBytes</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodappendNewInputItem">appendNewInputItem</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodappendNum">appendNum</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodappendPaddingBit">appendPaddingBit</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodbinarize">binarize</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodbitstreamToByte">bitstreamToByte</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodcalcN1N3">calcN1N3</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodcalcParity">calcParity</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodcheck">check</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodcheckModeAn">checkModeAn</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodcheckModeKanji">checkModeKanji</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodcheckModeNum">checkModeNum</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodconvertData">convertData</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodcreateBitStream">createBitStream</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodcreateFrame">createFrame</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodeat8">eat8</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodeatAn">eatAn</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodeatKanji">eatKanji</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodeatNum">eatNum</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodencodeBitStream">encodeBitStream</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodencodeMask">encodeMask</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodencodeMode8">encodeMode8</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodencodeModeAn">encodeModeAn</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodencodeModeKanji">encodeModeKanji</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodencodeModeNum">encodeModeNum</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodencodeModeStructure">encodeModeStructure</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodencodeString">encodeString</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodencode_rs_char">encode_rs_char</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodestimateBitsMode8">estimateBitsMode8</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodestimateBitsModeAn">estimateBitsModeAn</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodestimateBitsModeKanji">estimateBitsModeKanji</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodestimateBitsModeNum">estimateBitsModeNum</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodestimateBitStreamSize">estimateBitStreamSize</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodestimateVersion">estimateVersion</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodevaluateSymbol">evaluateSymbol</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgenerateMaskNo">generateMaskNo</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetBarcodeArray">getBarcodeArray</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetBitStream">getBitStream</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetByteStream">getByteStream</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetCode">getCode</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetDataLength">getDataLength</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetECCLength">getECCLength</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetEccSpec">getEccSpec</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetFormatInfo">getFormatInfo</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetFrameAt">getFrameAt</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetMinimumVersion">getMinimumVersion</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetNextPosition">getNextPosition</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetRemainder">getRemainder</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetVersionPattern">getVersionPattern</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodgetWidth">getWidth</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodidentifyMode">identifyMode</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodinit">init</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodinit_rs">init_rs</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodinit_rs_char">init_rs_char</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodinsertStructuredAppendHeader">insertStructuredAppendHeader</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodisalnumat">isalnumat</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodisdigitat">isdigitat</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodlengthIndicator">lengthIndicator</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodlengthOfCode">lengthOfCode</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodlookAnTable">lookAnTable</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmakeMask">makeMask</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmakeMaskNo">makeMaskNo</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmask">mask</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmask0">mask0</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmask1">mask1</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmask2">mask2</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmask3">mask3</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmask4">mask4</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmask5">mask5</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmask6">mask6</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmask7">mask7</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmaximumWords">maximumWords</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmergeBitStream">mergeBitStream</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodmodnn">modnn</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodnewFrame">newFrame</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodnewFromBytes">newFromBytes</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodnewFromNum">newFromNum</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodnewInputItem">newInputItem</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodputAlignmentMarker">putAlignmentMarker</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodputAlignmentPattern">putAlignmentPattern</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodputFinderPattern">putFinderPattern</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodqrstrset">qrstrset</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodrsBlockNum">rsBlockNum</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodrsBlockNum1">rsBlockNum1</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodrsBlockNum2">rsBlockNum2</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodrsDataCodes1">rsDataCodes1</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodrsDataCodes2">rsDataCodes2</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodrsDataLength">rsDataLength</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodrsEccCodes1">rsEccCodes1</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodrsEccCodes2">rsEccCodes2</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodrsEccLength">rsEccLength</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodsetFrameAt">setFrameAt</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodsplitString">splitString</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodtoUpper">toUpper</a></li>
    <li><a href="../com-tecnick-tcpdf/QRcode.html#methodwriteFormatInformation">writeFormatInformation</a></li>
  </ul>
</td>

</tr></table>
<hr />

<table width="100%" border="0"><tr>






</tr></table>
<hr />

<a name="class_details"></a>
<h3>Class Details</h3>
<div class="tags">
[line 308]<br />
Class to create QR-code arrays for TCPDF class.<br /><br /><p>QR Code symbol is a 2D barcode that can be scanned by handy terminals such as a mobile phone with CCD.  The capacity of QR Code is up to 7000 digits or 4000 characters, and has high robustness.  This class supports QR Code model 2, described in JIS (Japanese Industrial Standards) X0510:2004 or ISO/IEC 18004.  Currently the following features are not supported: ECI and FNC1 mode, Micro QR Code, QR Code model 1, Structured mode.</p><p>This class is derived from &quot;PHP QR Code encoder&quot; by Dominik Dzienia (http://phpqrcode.sourceforge.net/) based on &quot;libqrencode C library 3.1.1.&quot; by Kentaro Fukuchi (http://megaui.net/fukuchi/works/qrencode/index.en.html), contains Reed-Solomon code written by Phil Karn, KA9Q. QR Code is registered trademark of DENSO WAVE INCORPORATED (http://www.denso-wave.com/qrcode/index-e.html).  Please read comments on this class source file for full copyright and license information.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Nicola Asuni</td>
  </tr>
  <tr>
    <td><b>version:</b>&nbsp;&nbsp;</td><td>1.0.000</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2010 Nicola Asuni - Tecnick.com S.r.l (www.tecnick.com) Via Della Pace, 11 - 09044 - Quartucciu (CA) - ITALY - www.tecnick.com - <EMAIL></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.tcpdf.org">http://www.tcpdf.org</a></td>
  </tr>
  <tr>
    <td><b>abstract:</b>&nbsp;&nbsp;</td><td>Class for generating QR-code array for TCPDF.</td>
  </tr>
  <tr>
    <td><b>name:</b>&nbsp;&nbsp;</td><td>QRcode</td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a></td>
  </tr>
</table>
</div>
</div><br /><br />
<div class="top">[ <a href="#top">Top</a> ]</div><br />

<hr />
<a name="class_vars"></a>
<h3>Class Variables</h3>
<div class="tags">
	<a name="var$alignmentPattern"></a>
	<p></p>
	<h4>$alignmentPattern = <span class="value">array(<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;array(&nbsp;0,&nbsp;&nbsp;0),array(0,0),array(18,0),array(22,0),array(26,0),array(30,0),//&nbsp;&nbsp;1-&nbsp;5<br>
array(34,0),array(22,38),array(24,42),array(26,46),array(28,50),//&nbsp;&nbsp;6-10<br>
array(30,54),array(32,58),array(34,62),array(26,46),array(26,48),//&nbsp;11-15<br>
array(26,50),array(30,54),array(30,56),array(30,58),array(34,62),//&nbsp;16-20<br>
array(28,50),array(26,50),array(30,54),array(28,54),array(32,58),//&nbsp;21-25<br>
array(30,58),array(34,62),array(26,50),array(30,54),array(26,52),//&nbsp;26-30<br>
array(30,56),array(34,60),array(30,58),array(34,62),array(30,54),//&nbsp;31-35<br>
array(24,50),array(28,54),array(32,58),array(26,54),array(30,58)//&nbsp;35-40<br>
)</span></h4>
	<p>[line 606]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>Positions of alignment patterns.  This array includes only the second and the third position of the alignment patterns. Rest of them can be calculated from the distance between them.  See Table 1 in Appendix E (pp.71) of JIS X0510:2004.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$anTable"></a>
	<p></p>
	<h4>$anTable = <span class="value">array(<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;36,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;37,&nbsp;38,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;39,&nbsp;40,&nbsp;-1,&nbsp;41,&nbsp;42,&nbsp;43,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0,&nbsp;&nbsp;1,&nbsp;&nbsp;2,&nbsp;&nbsp;3,&nbsp;&nbsp;4,&nbsp;&nbsp;5,&nbsp;&nbsp;6,&nbsp;&nbsp;7,&nbsp;&nbsp;8,&nbsp;&nbsp;9,&nbsp;44,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-1,&nbsp;10,&nbsp;11,&nbsp;12,&nbsp;13,&nbsp;14,&nbsp;15,&nbsp;16,&nbsp;17,&nbsp;18,&nbsp;19,&nbsp;20,&nbsp;21,&nbsp;22,&nbsp;23,&nbsp;24,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;25,&nbsp;26,&nbsp;27,&nbsp;28,&nbsp;29,&nbsp;30,&nbsp;31,&nbsp;32,&nbsp;33,&nbsp;34,&nbsp;35,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1,&nbsp;-1&nbsp;&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></h4>
	<p>[line 480]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>convesion table</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>alphabet-numeric</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$b1"></a>
	<p></p>
	<h4>$b1 = <span class="value"></span></h4>
	<p>[line 438]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>b1</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$barcode_array"></a>
	<p></p>
	<h4>$barcode_array = <span class="value">array()</span></h4>
	<p>[line 314]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>array to be returned which is readable by TCPDF</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>barcode</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$bit"></a>
	<p></p>
	<h4>$bit = <span class="value"></span></h4>
	<p>[line 388]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>bit</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>single</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$blocks"></a>
	<p></p>
	<h4>$blocks = <span class="value"></span></h4>
	<p>[line 408]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>blocks</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$capacity"></a>
	<p></p>
	<h4>$capacity = <span class="value">array(<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;array(&nbsp;&nbsp;0,&nbsp;&nbsp;&nbsp;&nbsp;0,&nbsp;0,&nbsp;array(&nbsp;&nbsp;&nbsp;0,&nbsp;&nbsp;&nbsp;&nbsp;0,&nbsp;&nbsp;&nbsp;&nbsp;0,&nbsp;&nbsp;&nbsp;&nbsp;0)),//<br>
array(21,26,0,array(7,10,13,17)),//&nbsp;&nbsp;1<br>
array(25,44,7,array(10,16,22,28)),//<br>
array(29,70,7,array(15,26,36,44)),//<br>
array(33,100,7,array(20,36,52,64)),//<br>
array(37,134,7,array(26,48,72,88)),//&nbsp;&nbsp;5<br>
array(41,172,7,array(36,64,96,112)),//<br>
array(45,196,0,array(40,72,108,130)),//<br>
array(49,242,0,array(48,88,132,156)),//<br>
array(53,292,0,array(60,110,160,192)),//<br>
array(57,346,0,array(72,130,192,224)),//&nbsp;10<br>
array(61,404,0,array(80,150,224,264)),//<br>
array(65,466,0,array(96,176,260,308)),//<br>
array(69,532,0,array(104,198,288,352)),//<br>
array(73,581,3,array(120,216,320,384)),//<br>
array(77,655,3,array(132,240,360,432)),//&nbsp;15<br>
array(81,733,3,array(144,280,408,480)),//<br>
array(85,815,3,array(168,308,448,532)),//<br>
array(89,901,3,array(180,338,504,588)),//<br>
array(93,991,3,array(196,364,546,650)),//<br>
array(97,1085,3,array(224,416,600,700)),//&nbsp;20<br>
array(101,1156,4,array(224,442,644,750)),//<br>
array(105,1258,4,array(252,476,690,816)),//<br>
array(109,1364,4,array(270,504,750,900)),//<br>
array(113,1474,4,array(300,560,810,960)),//<br>
array(117,1588,4,array(312,588,870,1050)),//&nbsp;25<br>
array(121,1706,4,array(336,644,952,1110)),//<br>
array(125,1828,4,array(360,700,1020,1200)),//<br>
array(129,1921,3,array(390,728,1050,1260)),//<br>
array(133,2051,3,array(420,784,1140,1350)),//<br>
array(137,2185,3,array(450,812,1200,1440)),//&nbsp;30<br>
array(141,2323,3,array(480,868,1290,1530)),//<br>
array(145,2465,3,array(510,924,1350,1620)),//<br>
array(149,2611,3,array(540,980,1440,1710)),//<br>
array(153,2761,3,array(570,1036,1530,1800)),//<br>
array(157,2876,0,array(570,1064,1590,1890)),//&nbsp;35<br>
array(161,3034,0,array(600,1120,1680,1980)),//<br>
array(165,3196,0,array(630,1204,1770,2100)),//<br>
array(169,3362,0,array(660,1260,1860,2220)),//<br>
array(173,3532,0,array(720,1316,1950,2310)),//<br>
array(177,3706,0,array(750,1372,2040,2430))//&nbsp;40<br>
)</span></h4>
	<p>[line 496]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>Table of the capacity of symbols  See Table 1 (pp.13) and Table 12-16 (pp.30-36), JIS X0510:2004.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$casesensitive"></a>
	<p></p>
	<h4>$casesensitive = <span class="value">&nbsp;true</span></h4>
	<p>[line 338]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>true the input string will be converted to uppercase</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>if</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$count"></a>
	<p></p>
	<h4>$count = <span class="value"></span></h4>
	<p>[line 420]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>counter</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$data"></a>
	<p></p>
	<h4>$data = <span class="value"></span></h4>
	<p>[line 350]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>data</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>mask</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$datacode"></a>
	<p></p>
	<h4>$datacode = <span class="value">array()</span></h4>
	<p>[line 396]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>code</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>data</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$dataLength"></a>
	<p></p>
	<h4>$dataLength = <span class="value"></span></h4>
	<p>[line 426]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>length</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>data</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$dataStr"></a>
	<p></p>
	<h4>$dataStr = <span class="value">&nbsp;''</span></h4>
	<p>[line 454]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>data string</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>input</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$dir"></a>
	<p></p>
	<h4>$dir = <span class="value"></span></h4>
	<p>[line 382]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>direction</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$ecccode"></a>
	<p></p>
	<h4>$ecccode = <span class="value">array()</span></h4>
	<p>[line 402]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>correction code</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>error</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$eccLength"></a>
	<p></p>
	<h4>$eccLength = <span class="value"></span></h4>
	<p>[line 432]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>correction length</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>error</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$eccTable"></a>
	<p></p>
	<h4>$eccTable = <span class="value">array(<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;array(array(&nbsp;0,&nbsp;&nbsp;0),array(0,0),array(0,0),array(0,0)),//<br>
array(array(1,0),array(1,0),array(1,0),array(1,0)),//&nbsp;&nbsp;1<br>
array(array(1,0),array(1,0),array(1,0),array(1,0)),//<br>
array(array(1,0),array(1,0),array(2,0),array(2,0)),//<br>
array(array(1,0),array(2,0),array(2,0),array(4,0)),//<br>
array(array(1,0),array(2,0),array(2,2),array(2,2)),//&nbsp;&nbsp;5<br>
array(array(2,0),array(4,0),array(4,0),array(4,0)),//<br>
array(array(2,0),array(4,0),array(2,4),array(4,1)),//<br>
array(array(2,0),array(2,2),array(4,2),array(4,2)),//<br>
array(array(2,0),array(3,2),array(4,4),array(4,4)),//<br>
array(array(2,2),array(4,1),array(6,2),array(6,2)),//&nbsp;10<br>
array(array(4,0),array(1,4),array(4,4),array(3,8)),//<br>
array(array(2,2),array(6,2),array(4,6),array(7,4)),//<br>
array(array(4,0),array(8,1),array(8,4),array(12,4)),//<br>
array(array(3,1),array(4,5),array(11,5),array(11,5)),//<br>
array(array(5,1),array(5,5),array(5,7),array(11,7)),//&nbsp;15<br>
array(array(5,1),array(7,3),array(15,2),array(3,13)),//<br>
array(array(1,5),array(10,1),array(1,15),array(2,17)),//<br>
array(array(5,1),array(9,4),array(17,1),array(2,19)),//<br>
array(array(3,4),array(3,11),array(17,4),array(9,16)),//<br>
array(array(3,5),array(3,13),array(15,5),array(15,10)),//&nbsp;20<br>
array(array(4,4),array(17,0),array(17,6),array(19,6)),//<br>
array(array(2,7),array(17,0),array(7,16),array(34,0)),//<br>
array(array(4,5),array(4,14),array(11,14),array(16,14)),//<br>
array(array(6,4),array(6,14),array(11,16),array(30,2)),//<br>
array(array(8,4),array(8,13),array(7,22),array(22,13)),//&nbsp;25<br>
array(array(10,2),array(19,4),array(28,6),array(33,4)),//<br>
array(array(8,4),array(22,3),array(8,26),array(12,28)),//<br>
array(array(3,10),array(3,23),array(4,31),array(11,31)),//<br>
array(array(7,7),array(21,7),array(1,37),array(19,26)),//<br>
array(array(5,10),array(19,10),array(15,25),array(23,25)),//&nbsp;30<br>
array(array(13,3),array(2,29),array(42,1),array(23,28)),//<br>
array(array(17,0),array(10,23),array(10,35),array(19,35)),//<br>
array(array(17,1),array(14,21),array(29,19),array(11,46)),//<br>
array(array(13,6),array(14,23),array(44,7),array(59,1)),//<br>
array(array(12,7),array(12,26),array(39,14),array(22,41)),//&nbsp;35<br>
array(array(6,14),array(6,34),array(46,10),array(2,64)),//<br>
array(array(17,4),array(29,14),array(49,10),array(24,46)),//<br>
array(array(4,18),array(13,32),array(48,14),array(42,32)),//<br>
array(array(20,4),array(40,7),array(43,22),array(10,67)),//<br>
array(array(19,6),array(18,31),array(34,34),array(20,61))//&nbsp;40<br>
)</span></h4>
	<p>[line 556]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>Table of the error correction code (Reed-Solomon block)  See Table 12-16 (pp.30-36), JIS X0510:2004.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$formatInfo"></a>
	<p></p>
	<h4>$formatInfo = <span class="value">array(<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;array(0x77c4,&nbsp;0x72f3,&nbsp;0x7daa,&nbsp;0x789d,&nbsp;0x662f,&nbsp;0x6318,&nbsp;0x6c41,&nbsp;0x6976),//<br>
array(0x5412,0x5125,0x5e7c,0x5b4b,0x45f9,0x40ce,0x4f97,0x4aa0),//<br>
array(0x355f,0x3068,0x3f31,0x3a06,0x24b4,0x2183,0x2eda,0x2bed),//<br>
array(0x1689,0x13be,0x1ce7,0x19d0,0x0762,0x0255,0x0d0c,0x083b)//<br>
)</span></h4>
	<p>[line 636]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>Format information</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$frame"></a>
	<p></p>
	<h4>$frame = <span class="value"></span></h4>
	<p>[line 364]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>frame</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$frames"></a>
	<p></p>
	<h4>$frames = <span class="value">array()</span></h4>
	<p>[line 474]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>of frames</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$hint"></a>
	<p></p>
	<h4>$hint = <span class="value">&nbsp;QR_MODE_8B</span></h4>
	<p>[line 332]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>mode</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>Encoding</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$items"></a>
	<p></p>
	<h4>$items = <span class="value"></span></h4>
	<p>[line 460]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>items</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>input</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$lengthTableBits"></a>
	<p></p>
	<h4>$lengthTableBits = <span class="value">array(<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;array(10,&nbsp;12,&nbsp;14),array(9,11,13),array(8,16,16),array(8,10,12))</span></h4>
	<p>[line 544]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>Length indicator</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$level"></a>
	<p></p>
	<h4>$level = <span class="value">&nbsp;QR_ECLEVEL_L</span></h4>
	<p>[line 326]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>of error correction. See definitions for possible values.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>Levels</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$rsblocks"></a>
	<p></p>
	<h4>$rsblocks = <span class="value">array()</span></h4>
	<p>[line 414]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>blocks</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>Reed-Solomon</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$rsitems"></a>
	<p></p>
	<h4>$rsitems = <span class="value">array()</span></h4>
	<p>[line 468]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>items</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>Reed-Solomon</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$runLength"></a>
	<p></p>
	<h4>$runLength = <span class="value">array()</span></h4>
	<p>[line 446]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>length</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>run</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$structured"></a>
	<p></p>
	<h4>$structured = <span class="value">&nbsp;0</span></h4>
	<p>[line 344]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>QR code (not supported yet)</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>structured</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$version"></a>
	<p></p>
	<h4>$version = <span class="value">&nbsp;0</span></h4>
	<p>[line 320]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>code version. Size of QRcode is defined as version. Version is from 1 to 40. Version 1 is 21*21 matrix. And 4 modules increases whenever 1 version increases. So version 40 is 177*177 matrix.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>QR</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$versionPattern"></a>
	<p></p>
	<h4>$versionPattern = <span class="value">array(<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x07c94,&nbsp;0x085bc,&nbsp;0x09a99,&nbsp;0x0a4d3,&nbsp;0x0bbf6,&nbsp;0x0c762,&nbsp;0x0d847,&nbsp;0x0e60d,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x0f928,&nbsp;0x10b78,&nbsp;0x1145d,&nbsp;0x12a17,&nbsp;0x13532,&nbsp;0x149a6,&nbsp;0x15683,&nbsp;0x168c9,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x177ec,&nbsp;0x18ec4,&nbsp;0x191e1,&nbsp;0x1afab,&nbsp;0x1b08e,&nbsp;0x1cc1a,&nbsp;0x1d33f,&nbsp;0x1ed75,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x1f250,&nbsp;0x209d5,&nbsp;0x216f0,&nbsp;0x228ba,&nbsp;0x2379f,&nbsp;0x24b0b,&nbsp;0x2542e,&nbsp;0x26a64,&nbsp;//<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x27541,&nbsp;0x28c69<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></h4>
	<p>[line 624]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>Version information pattern (BCH coded).  See Table 1 in Appendix D (pp.68) of JIS X0510:2004.  size: [QRSPEC_VERSION_MAX - 6]</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$width"></a>
	<p></p>
	<h4>$width = <span class="value"></span></h4>
	<p>[line 358]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>width</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$x"></a>
	<p></p>
	<h4>$x = <span class="value"></span></h4>
	<p>[line 370]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>position of bit</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>X</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$y"></a>
	<p></p>
	<h4>$y = <span class="value"></span></h4>
	<p>[line 376]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>position of bit</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>Y</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
</div><br />

<hr />
<a name="class_methods"></a>
<h3>Class Methods</h3>
<div class="tags">

  <hr />
	<a name="method__construct"></a>
	<h3>constructor __construct <span class="smalllinenumber">[line 656]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>QRcode __construct(
string
$code, [string
$eclevel = 'L'])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		This is the class constructor.<br /><br /><p>Creates a QRcode object</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>since:</b>&nbsp;&nbsp;</td><td>1.0.000</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent using QRcode</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$eclevel</b>&nbsp;&nbsp;</td>
        <td>error level: <ul><li>L : About 7% or less errors can be corrected.</li><li>M : About 15% or less errors can be corrected.</li><li>Q : About 25% or less errors can be corrected.</li><li>H : About 30% or less errors can be corrected.</li></ul></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodallocate"></a>
	<h3>method allocate <span class="smalllinenumber">[line 2137]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array allocate(
int
$setLength)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return an array with zeros<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$setLength</b>&nbsp;&nbsp;</td>
        <td>array size</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodappendBitstream"></a>
	<h3>method appendBitstream <span class="smalllinenumber">[line 2191]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array appendBitstream(
array
$bitstream, array
$append)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Append one bitstream to another<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>bitstream</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$bitstream</b>&nbsp;&nbsp;</td>
        <td>original bitstream</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$append</b>&nbsp;&nbsp;</td>
        <td>bitstream to append</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodappendBytes"></a>
	<h3>method appendBytes <span class="smalllinenumber">[line 2223]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array appendBytes(
array
$bitstream, int
$size, array
$data)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Append one bitstream created from bytes to another<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>bitstream</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$bitstream</b>&nbsp;&nbsp;</td>
        <td>original bitstream</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td>size</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$data</b>&nbsp;&nbsp;</td>
        <td>bytes</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodappendNewInputItem"></a>
	<h3>method appendNewInputItem <span class="smalllinenumber">[line 1709]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>items appendNewInputItem(
array
$items, int
$mode, int
$size, array
$data)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Append data to an input object.<br /><br /><p>The data is copied and appended to the input object.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td>encoding mode.</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td>size of data (byte).</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$data</b>&nbsp;&nbsp;</td>
        <td>array of input data.</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td>items input items</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodappendNum"></a>
	<h3>method appendNum <span class="smalllinenumber">[line 2208]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array appendNum(
array
$bitstream, int
$bits, int
$num)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Append one bitstream created from number to another<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>bitstream</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$bitstream</b>&nbsp;&nbsp;</td>
        <td>original bitstream</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$bits</b>&nbsp;&nbsp;</td>
        <td>number of bits</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$num</b>&nbsp;&nbsp;</td>
        <td>number</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodappendPaddingBit"></a>
	<h3>method appendPaddingBit <span class="smalllinenumber">[line 2069]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array appendPaddingBit(
array
$bstream)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Append Padding Bit to bitstream<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>bitstream</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$bstream</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbinarize"></a>
	<h3>method binarize <span class="smalllinenumber">[line 703]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array binarize(
array
$frame)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert the frame in binary form<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>frame in binary form</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$frame</b>&nbsp;&nbsp;</td>
        <td>array to binarize</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbitstreamToByte"></a>
	<h3>method bitstreamToByte <span class="smalllinenumber">[line 2236]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array bitstreamToByte(

$bstream, array
$bitstream)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert bitstream to bytes<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>of bytes</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$bitstream</b>&nbsp;&nbsp;</td>
        <td>original bitstream</td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>$bstream</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcalcN1N3"></a>
	<h3>method calcN1N3 <span class="smalllinenumber">[line 1139]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int calcN1N3(
int
$length)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		calcN1N3<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>demerit</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$length</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcalcParity"></a>
	<h3>method calcParity <span class="smalllinenumber">[line 1740]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int calcParity(
array
$items)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		calcParity<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>parity</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheck"></a>
	<h3>method check <span class="smalllinenumber">[line 1873]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean check(
int
$mode, int
$size, array
$data)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Validate the input data.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>true in case of valid data, false otherwise</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td>encoding mode.</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td>size of data (byte).</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$data</b>&nbsp;&nbsp;</td>
        <td>data data to validate</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckModeAn"></a>
	<h3>method checkModeAn <span class="smalllinenumber">[line 1806]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkModeAn(
int
$size, array
$data)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		checkModeAn<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>true or false</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$data</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckModeKanji"></a>
	<h3>method checkModeKanji <span class="smalllinenumber">[line 1853]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkModeKanji(
int
$size, array
$data)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		checkModeKanji<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>true or false</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$data</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckModeNum"></a>
	<h3>method checkModeNum <span class="smalllinenumber">[line 1758]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkModeNum(
int
$size, array
$data)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		checkModeNum<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>true or false</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$data</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodconvertData"></a>
	<h3>method convertData <span class="smalllinenumber">[line 2040]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array convertData(
array
$items)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		convertData<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>items</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcreateBitStream"></a>
	<h3>method createBitStream <span class="smalllinenumber">[line 2025]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array createBitStream(
array
$items)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		createBitStream<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>of items and total bits</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcreateFrame"></a>
	<h3>method createFrame <span class="smalllinenumber">[line 2536]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>Array createFrame(
int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return a copy of initialized frame.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>of unsigned char.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodeat8"></a>
	<h3>method eat8 <span class="smalllinenumber">[line 1408]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int eat8(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		eat8<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>run</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodeatAn"></a>
	<h3>method eatAn <span class="smalllinenumber">[line 1356]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int eatAn(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		eatAn<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>run</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodeatKanji"></a>
	<h3>method eatKanji <span class="smalllinenumber">[line 1395]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int eatKanji(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		eatKanji<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>run</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodeatNum"></a>
	<h3>method eatNum <span class="smalllinenumber">[line 1324]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int eatNum(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		eatNum<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>run</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencodeBitStream"></a>
	<h3>method encodeBitStream <span class="smalllinenumber">[line 1654]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array encodeBitStream(
array
$inputitem, int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		encodeBitStream<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>input item</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$inputitem</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencodeMask"></a>
	<h3>method encodeMask <span class="smalllinenumber">[line 733]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void encodeMask(
int
$mask)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Encode mask<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$mask</b>&nbsp;&nbsp;</td>
        <td>masking mode</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencodeMode8"></a>
	<h3>method encodeMode8 <span class="smalllinenumber">[line 1600]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array encodeMode8(
array
$inputitem, int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		encodeMode8<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>input item</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$inputitem</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencodeModeAn"></a>
	<h3>method encodeModeAn <span class="smalllinenumber">[line 1577]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array encodeModeAn(
array
$inputitem, int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		encodeModeAn<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>input item</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$inputitem</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencodeModeKanji"></a>
	<h3>method encodeModeKanji <span class="smalllinenumber">[line 1616]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array encodeModeKanji(
array
$inputitem, int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		encodeModeKanji<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>input item</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$inputitem</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencodeModeNum"></a>
	<h3>method encodeModeNum <span class="smalllinenumber">[line 1548]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array encodeModeNum(
array
$inputitem, int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		encodeModeNum<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>input item</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$inputitem</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencodeModeStructure"></a>
	<h3>method encodeModeStructure <span class="smalllinenumber">[line 1639]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array encodeModeStructure(
array
$inputitem)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		encodeModeStructure<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>input item</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$inputitem</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencodeString"></a>
	<h3>method encodeString <span class="smalllinenumber">[line 717]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void encodeString(
string
$string)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Encode the input string to QR code<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$string</b>&nbsp;&nbsp;</td>
        <td>input string to encode</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencode_rs_char"></a>
	<h3>method encode_rs_char <span class="smalllinenumber">[line 2836]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>parity encode_rs_char(
array
$rs, array
$data, array
$parity)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Encode a Reed-Solomon codec and returns the parity array<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>array</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$rs</b>&nbsp;&nbsp;</td>
        <td>RS values</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$data</b>&nbsp;&nbsp;</td>
        <td>data</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$parity</b>&nbsp;&nbsp;</td>
        <td>parity</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodestimateBitsMode8"></a>
	<h3>method estimateBitsMode8 <span class="smalllinenumber">[line 1834]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int estimateBitsMode8(
int
$size)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		estimateBitsMode8<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>number of bits</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodestimateBitsModeAn"></a>
	<h3>method estimateBitsModeAn <span class="smalllinenumber">[line 1820]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int estimateBitsModeAn(
int
$size)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		estimateBitsModeAn<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>number of bits</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodestimateBitsModeKanji"></a>
	<h3>method estimateBitsModeKanji <span class="smalllinenumber">[line 1843]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int estimateBitsModeKanji(
int
$size)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		estimateBitsModeKanji<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>number of bits</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodestimateBitsModeNum"></a>
	<h3>method estimateBitsModeNum <span class="smalllinenumber">[line 1772]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int estimateBitsModeNum(
int
$size)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		estimateBitsModeNum<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>number of bits</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodestimateBitStreamSize"></a>
	<h3>method estimateBitStreamSize <span class="smalllinenumber">[line 1906]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int estimateBitStreamSize(
array
$items, int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		estimateBitStreamSize<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>bits</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodestimateVersion"></a>
	<h3>method estimateVersion <span class="smalllinenumber">[line 1949]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int estimateVersion(
array
$items)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		estimateVersion<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>version</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodevaluateSymbol"></a>
	<h3>method evaluateSymbol <span class="smalllinenumber">[line 1170]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int evaluateSymbol(
int
$width, array
$frame)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		evaluateSymbol<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>demerit</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$width</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$frame</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgenerateMaskNo"></a>
	<h3>method generateMaskNo <span class="smalllinenumber">[line 1076]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array generateMaskNo(
int
$maskNo, int
$width, array
$frame)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return bitmask<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>bitmask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$maskNo</b>&nbsp;&nbsp;</td>
        <td>mask number</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$width</b>&nbsp;&nbsp;</td>
        <td>width</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$frame</b>&nbsp;&nbsp;</td>
        <td>frame</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetBarcodeArray"></a>
	<h3>method getBarcodeArray <span class="smalllinenumber">[line 694]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array getBarcodeArray(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Returns a barcode array which is readable by TCPDF<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode array readable by TCPDF;</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetBitStream"></a>
	<h3>method getBitStream <span class="smalllinenumber">[line 2113]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array getBitStream(
int
$items)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Returns a stream of bits.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>padded merged byte stream</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetByteStream"></a>
	<h3>method getByteStream <span class="smalllinenumber">[line 2123]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array getByteStream(
int
$items)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Pack all bit streams padding bits into a byte array.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>padded merged byte stream</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetCode"></a>
	<h3>method getCode <span class="smalllinenumber">[line 922]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array getCode(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return Reed-Solomon block code.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>rsblocks</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetDataLength"></a>
	<h3>method getDataLength <span class="smalllinenumber">[line 2289]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int getDataLength(
int
$version, int
$level)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return maximum data code length (bytes) for the version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>maximum size (bytes)</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$level</b>&nbsp;&nbsp;</td>
        <td>error correction level</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetECCLength"></a>
	<h3>method getECCLength <span class="smalllinenumber">[line 2299]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int getECCLength(
int
$version, int
$level)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return maximum error correction code length (bytes) for the version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>ECC size (bytes)</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$level</b>&nbsp;&nbsp;</td>
        <td>error correction level</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetEccSpec"></a>
	<h3>method getEccSpec <span class="smalllinenumber">[line 2389]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array getEccSpec(
int
$version, int
$level, array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return an array of ECC specification.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>spec</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$level</b>&nbsp;&nbsp;</td>
        <td>error correction level</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td>an array of ECC specification contains as following: {# of type1 blocks, # of data code, # of ecc code, # of type2 blocks, # of data code}</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetFormatInfo"></a>
	<h3>method getFormatInfo <span class="smalllinenumber">[line 2497]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>BCH getFormatInfo(
array
$mask, int
$level)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return BCH encoded format information pattern.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>encoded format information pattern</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$mask</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$level</b>&nbsp;&nbsp;</td>
        <td>error correction level</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetFrameAt"></a>
	<h3>method getFrameAt <span class="smalllinenumber">[line 807]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>value getFrameAt(
array
$at)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get frame value at specified position<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>at specified position</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$at</b>&nbsp;&nbsp;</td>
        <td>x,y position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetMinimumVersion"></a>
	<h3>method getMinimumVersion <span class="smalllinenumber">[line 2327]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int getMinimumVersion(
int
$size, int
$level)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return a version number that satisfies the input code length.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>version number</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td>input code length (byte)</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$level</b>&nbsp;&nbsp;</td>
        <td>error correction level</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetNextPosition"></a>
	<h3>method getNextPosition <span class="smalllinenumber">[line 815]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array getNextPosition(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return the next frame position<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>of x,y coordinates</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetRemainder"></a>
	<h3>method getRemainder <span class="smalllinenumber">[line 2317]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int getRemainder(
int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return the numer of remainder bits.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>number of remainder bits</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetVersionPattern"></a>
	<h3>method getVersionPattern <span class="smalllinenumber">[line 2484]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>BCH getVersionPattern(
int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return BCH encoded version information pattern that is used for the symbol of version 7 or greater. Use lower 18 bits.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>encoded version information pattern</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetWidth"></a>
	<h3>method getWidth <span class="smalllinenumber">[line 2308]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int getWidth(
int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return the width of the symbol for the version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>width</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodidentifyMode"></a>
	<h3>method identifyMode <span class="smalllinenumber">[line 1299]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int identifyMode(
int
$pos)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		identifyMode<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mode</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$pos</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodinit"></a>
	<h3>method init <span class="smalllinenumber">[line 871]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>0 init(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Initialize code.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>in case of success, -1 in case of error</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td>array of ECC specification</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodinit_rs"></a>
	<h3>method init_rs <span class="smalllinenumber">[line 2709]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array init_rs(
int
$symsize, int
$gfpoly, int
$fcr, int
$prim, int
$nroots, int
$pad)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Initialize a Reed-Solomon codec and add it to existing rsitems<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Array of RS values:<ul><li>mm = Bits per symbol;</li><li>nn = Symbols per block;</li><li>alpha_to = log lookup table array;</li><li>index_of = Antilog lookup table array;</li><li>genpoly = Generator polynomial array;</li><li>nroots = Number of generator;</li><li>roots = number of parity symbols;</li><li>fcr = First consecutive root, index form;</li><li>prim = Primitive element, index form;</li><li>iprim = prim-th root of 1, index form;</li><li>pad = Padding bytes in shortened block;</li><li>gfpoly</li></ul>.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$symsize</b>&nbsp;&nbsp;</td>
        <td>symbol size, bits</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$gfpoly</b>&nbsp;&nbsp;</td>
        <td>Field generator polynomial coefficients</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$fcr</b>&nbsp;&nbsp;</td>
        <td>first root of RS code generator polynomial, index form</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$prim</b>&nbsp;&nbsp;</td>
        <td>primitive element to generate polynomial roots</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$nroots</b>&nbsp;&nbsp;</td>
        <td>RS code generator polynomial degree (number of roots)</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$pad</b>&nbsp;&nbsp;</td>
        <td>padding bytes at front of shortened block</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodinit_rs_char"></a>
	<h3>method init_rs_char <span class="smalllinenumber">[line 2750]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array init_rs_char(
int
$symsize, int
$gfpoly, int
$fcr, int
$prim, int
$nroots, int
$pad)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Initialize a Reed-Solomon codec and returns an array of values.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Array of RS values:<ul><li>mm = Bits per symbol;</li><li>nn = Symbols per block;</li><li>alpha_to = log lookup table array;</li><li>index_of = Antilog lookup table array;</li><li>genpoly = Generator polynomial array;</li><li>nroots = Number of generator;</li><li>roots = number of parity symbols;</li><li>fcr = First consecutive root, index form;</li><li>prim = Primitive element, index form;</li><li>iprim = prim-th root of 1, index form;</li><li>pad = Padding bytes in shortened block;</li><li>gfpoly</li></ul>.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$symsize</b>&nbsp;&nbsp;</td>
        <td>symbol size, bits</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$gfpoly</b>&nbsp;&nbsp;</td>
        <td>Field generator polynomial coefficients</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$fcr</b>&nbsp;&nbsp;</td>
        <td>first root of RS code generator polynomial, index form</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$prim</b>&nbsp;&nbsp;</td>
        <td>primitive element to generate polynomial roots</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$nroots</b>&nbsp;&nbsp;</td>
        <td>RS code generator polynomial degree (number of roots)</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$pad</b>&nbsp;&nbsp;</td>
        <td>padding bytes at front of shortened block</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodinsertStructuredAppendHeader"></a>
	<h3>method insertStructuredAppendHeader <span class="smalllinenumber">[line 1722]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array insertStructuredAppendHeader(
array
$items, int
$size, int
$index, int
$parity)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		insertStructuredAppendHeader<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>items</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$index</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$parity</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodisalnumat"></a>
	<h3>method isalnumat <span class="smalllinenumber">[line 1287]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean isalnumat(
string
$str, int
$pos)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return true if the character at specified position is an alphanumeric character<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>true of false</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>string</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$pos</b>&nbsp;&nbsp;</td>
        <td>characted position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodisdigitat"></a>
	<h3>method isdigitat <span class="smalllinenumber">[line 1274]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean isdigitat(
string
$str, int
$pos)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return true if the character at specified position is a number<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>true of false</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$str</b>&nbsp;&nbsp;</td>
        <td>string</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$pos</b>&nbsp;&nbsp;</td>
        <td>characted position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodlengthIndicator"></a>
	<h3>method lengthIndicator <span class="smalllinenumber">[line 2343]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int lengthIndicator(
int
$mode, int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return the size of length indicator for the mode and version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>the size of the appropriate length indicator (bits).</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td>encoding mode</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodlengthOfCode"></a>
	<h3>method lengthOfCode <span class="smalllinenumber">[line 1970]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int lengthOfCode(
int
$mode, int
$version, int
$bits)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		lengthOfCode<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>size</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$bits</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodlookAnTable"></a>
	<h3>method lookAnTable <span class="smalllinenumber">[line 1796]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>value lookAnTable(
int
$c)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Look up the alphabet-numeric convesion table (see JIS X0510:2004, pp.19).<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$c</b>&nbsp;&nbsp;</td>
        <td>character value</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmakeMask"></a>
	<h3>method makeMask <span class="smalllinenumber">[line 1127]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array makeMask(
int
$width, array
$frame, int
$maskNo, int
$level)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		makeMask<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$width</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$frame</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$maskNo</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$level</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmakeMaskNo"></a>
	<h3>method makeMaskNo <span class="smalllinenumber">[line 1100]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int makeMaskNo(
int
$maskNo, int
$width, int
$s, 
&$d, [boolean
$maskGenOnly = false], int
$d)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		makeMaskNo<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>b</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$maskNo</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$width</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$s</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$d</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$maskGenOnly</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>&$d</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmask"></a>
	<h3>method mask <span class="smalllinenumber">[line 1232]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array mask(
int
$width, array
$frame, int
$level)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mask<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>best mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$width</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$frame</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$level</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmask0"></a>
	<h3>method mask0 <span class="smalllinenumber">[line 995]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int mask0(
int
$x, int
$y)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mask0<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Y position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmask1"></a>
	<h3>method mask1 <span class="smalllinenumber">[line 1005]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int mask1(
int
$x, int
$y)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mask1<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Y position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmask2"></a>
	<h3>method mask2 <span class="smalllinenumber">[line 1015]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int mask2(
int
$x, int
$y)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mask2<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Y position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmask3"></a>
	<h3>method mask3 <span class="smalllinenumber">[line 1025]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int mask3(
int
$x, int
$y)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mask3<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Y position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmask4"></a>
	<h3>method mask4 <span class="smalllinenumber">[line 1035]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int mask4(
int
$x, int
$y)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mask4<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Y position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmask5"></a>
	<h3>method mask5 <span class="smalllinenumber">[line 1045]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int mask5(
int
$x, int
$y)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mask5<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Y position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmask6"></a>
	<h3>method mask6 <span class="smalllinenumber">[line 1055]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int mask6(
int
$x, int
$y)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mask6<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Y position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmask7"></a>
	<h3>method mask7 <span class="smalllinenumber">[line 1065]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int mask7(
int
$x, int
$y)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mask7<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>mask</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Y position</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmaximumWords"></a>
	<h3>method maximumWords <span class="smalllinenumber">[line 2363]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int maximumWords(
int
$mode, int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return the maximum length for the mode and version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>the maximum length (bytes)</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td>encoding mode</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmergeBitStream"></a>
	<h3>method mergeBitStream <span class="smalllinenumber">[line 2099]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array mergeBitStream(

$items, array
$bstream)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		mergeBitStream<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>bitstream</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$bstream</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>$items</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmodnn"></a>
	<h3>method modnn <span class="smalllinenumber">[line 2732]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int modnn(
array
$rs, int
$x)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		modnn<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>X osition</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$rs</b>&nbsp;&nbsp;</td>
        <td>RS values</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodnewFrame"></a>
	<h3>method newFrame <span class="smalllinenumber">[line 2601]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>Array newFrame(
int
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set new frame for the specified version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>of unsigned char.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodnewFromBytes"></a>
	<h3>method newFromBytes <span class="smalllinenumber">[line 2167]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array newFromBytes(
int
$size, array
$data)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return new bitstream from bytes<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>bitstream</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td>size</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$data</b>&nbsp;&nbsp;</td>
        <td>bytes</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodnewFromNum"></a>
	<h3>method newFromNum <span class="smalllinenumber">[line 2147]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array newFromNum(
int
$bits, int
$num)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return new bitstream from number<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>bitstream</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$bits</b>&nbsp;&nbsp;</td>
        <td>number of bits</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$num</b>&nbsp;&nbsp;</td>
        <td>number</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodnewInputItem"></a>
	<h3>method newInputItem <span class="smalllinenumber">[line 1526]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array newInputItem(
int
$mode, int
$size, array
$data, [array
$bstream = null])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		newInputItem<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>input item</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$mode</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$data</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$bstream</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodputAlignmentMarker"></a>
	<h3>method putAlignmentMarker <span class="smalllinenumber">[line 2421]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array putAlignmentMarker(
array
$frame, int
$ox, int
$oy, int
$width)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Put an alignment marker.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>frame</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$frame</b>&nbsp;&nbsp;</td>
        <td>frame</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$width</b>&nbsp;&nbsp;</td>
        <td>width</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$ox</b>&nbsp;&nbsp;</td>
        <td>X center coordinate of the pattern</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$oy</b>&nbsp;&nbsp;</td>
        <td>Y center coordinate of the pattern</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodputAlignmentPattern"></a>
	<h3>method putAlignmentPattern <span class="smalllinenumber">[line 2444]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array putAlignmentPattern(
int
$version, 
$frame, int
$width, array
$fram)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Put an alignment pattern.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>frame</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>version</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$fram</b>&nbsp;&nbsp;</td>
        <td>frame</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$width</b>&nbsp;&nbsp;</td>
        <td>width</td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>$frame</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodputFinderPattern"></a>
	<h3>method putFinderPattern <span class="smalllinenumber">[line 2515]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array putFinderPattern(
array
$frame, int
$ox, int
$oy, int
$width)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Put a finder pattern.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>frame</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$frame</b>&nbsp;&nbsp;</td>
        <td>frame</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$width</b>&nbsp;&nbsp;</td>
        <td>width</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$ox</b>&nbsp;&nbsp;</td>
        <td>X center coordinate of the pattern</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$oy</b>&nbsp;&nbsp;</td>
        <td>Y center coordinate of the pattern</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodqrstrset"></a>
	<h3>method qrstrset <span class="smalllinenumber">[line 2278]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array qrstrset(
array
$srctab, int
$x, int
$y, string
$repl, [int
$replLen = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Replace a value on the array at the specified position<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>srctab</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$srctab</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$x</b>&nbsp;&nbsp;</td>
        <td>X position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$y</b>&nbsp;&nbsp;</td>
        <td>Y position</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$repl</b>&nbsp;&nbsp;</td>
        <td>value to replace</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$replLen</b>&nbsp;&nbsp;</td>
        <td>length of the repl string</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrsBlockNum"></a>
	<h3>method rsBlockNum <span class="smalllinenumber">[line 2619]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int rsBlockNum(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return block number 0<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrsBlockNum1"></a>
	<h3>method rsBlockNum1 <span class="smalllinenumber">[line 2628]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int rsBlockNum1(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return block number 1<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrsBlockNum2"></a>
	<h3>method rsBlockNum2 <span class="smalllinenumber">[line 2655]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int rsBlockNum2(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return block number 2<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrsDataCodes1"></a>
	<h3>method rsDataCodes1 <span class="smalllinenumber">[line 2637]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int rsDataCodes1(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return data codes 1<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrsDataCodes2"></a>
	<h3>method rsDataCodes2 <span class="smalllinenumber">[line 2664]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int rsDataCodes2(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return data codes 2<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrsDataLength"></a>
	<h3>method rsDataLength <span class="smalllinenumber">[line 2682]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int rsDataLength(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return data length<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrsEccCodes1"></a>
	<h3>method rsEccCodes1 <span class="smalllinenumber">[line 2646]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int rsEccCodes1(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return ecc codes 1<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrsEccCodes2"></a>
	<h3>method rsEccCodes2 <span class="smalllinenumber">[line 2673]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int rsEccCodes2(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return ecc codes 2<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodrsEccLength"></a>
	<h3>method rsEccLength <span class="smalllinenumber">[line 2691]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int rsEccLength(
array
$spec)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return ecc length<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$spec</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetFrameAt"></a>
	<h3>method setFrameAt <span class="smalllinenumber">[line 798]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setFrameAt(
array
$at, int
$val)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set frame value at specified position<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$at</b>&nbsp;&nbsp;</td>
        <td>x,y position</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$val</b>&nbsp;&nbsp;</td>
        <td>value of the character to set</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsplitString"></a>
	<h3>method splitString <span class="smalllinenumber">[line 1456]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void splitString(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		splitString<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodtoUpper"></a>
	<h3>method toUpper <span class="smalllinenumber">[line 1497]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void toUpper(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		toUpper<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodwriteFormatInformation"></a>
	<h3>method writeFormatInformation <span class="smalllinenumber">[line 953]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int writeFormatInformation(
int
$width, 
&$frame, array
$mask, int
$level, array
$frame)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Write Format Information on frame and returns the number of black bits<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>blacks</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$width</b>&nbsp;&nbsp;</td>
        <td>frame width</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$frame</b>&nbsp;&nbsp;</td>
        <td>frame</td>
      </tr>
          <tr>
        <td class="type">array&nbsp;&nbsp;</td>
        <td><b>$mask</b>&nbsp;&nbsp;</td>
        <td>masking mode</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$level</b>&nbsp;&nbsp;</td>
        <td>error correction level</td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>&$frame</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
</div><br />


        <div class="credit">
		    <hr />
		    Documentation generated on Sun, 28 Mar 2010 22:22:40 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>