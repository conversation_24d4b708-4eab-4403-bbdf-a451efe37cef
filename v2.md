# AutoAuto.pl - Specyfikacja Funkcjonalna i Rekomendacje Przepisania

## 1. ANALIZA OBECNEJ APLIKACJI

### 1.1 Architektura Techniczna
- **Framework**: Zend Framework 1.x (PHP)
- **Baza danych**: MySQL (www_auto)
- **Frontend**: jQuery, Bootstrap (częściowo), własne CSS/JS
- **Cache**: APC
- **Struktura**: MVC (Model-View-Controller)
- **Routing**: Własny system routingu z obsługą wielojęzyczności (pl/en)

### 1.2 Główne Funkcjonalności

#### A. WYSZUKIWANIE SAMOCHODÓW
**Kontroler**: `CarsController::listAction()`
**Formularz**: `Form_Search`, `Form_NewSearch`

**Filtry wyszukiwania**:
- Marka i model (dynamiczne ładowanie modeli)
- <PERSON><PERSON> (min/max)
- Rok produkcji (od/do)
- Pojemność silnika (od/do)
- <PERSON><PERSON><PERSON> paliwa (benzyna, diesel, benzyna+gaz, hybryda, elektryczny)
- Skrzynia biegów (automatyczna, manualna)
- Przebieg
- Cechy dodatkowe
- Kategorie pojazdów
- Lokalizacja
- Sortowanie (data dodania, cena, marka, rok produkcji)
- Widok (lista/kafelki)

**Parametry URL**:
```
/:language/list/:type/:make/:model?parametry_filtrów
```

#### B. PREZENTACJA WYNIKÓW
**Widoki**: 
- `cars/list.phtml` - lista główna
- `car-items.phtml` - elementy listy
- `car-item-list.phtml` - widok listy
- `car-item-block.phtml` - widok kafelków

**Funkcje**:
- Paginacja wyników
- Sortowanie (8 opcji)
- Przełączanie widoku lista/kafelki
- Dodawanie do ulubionych
- Podgląd zdjęć
- Informacje o cenie, roku, przebiegu
- Status (sprzedane, zarezerwowane)

#### C. SZCZEGÓŁY SAMOCHODU
**Kontroler**: `CarsController::showAction()`
**Widok**: `cars/show.phtml`

**Funkcjonalności**:
- Galeria zdjęć z nawigacją
- Pełne dane techniczne
- Opis samochodu
- Dane opiekuna/sprzedawcy
- Formularze kontaktowe:
  - Jazda próbna
  - Komentarz/pytanie
  - Złożenie oferty
  - Rezerwacja
- Kalkulator rat leasingu/kredytu
- Dodawanie do ulubionych
- Udostępnianie (social media)

#### D. SYSTEM ULUBIONYCH
**Kontrolery**: 
- `CarsController::favouriteListAction()` - dla zalogowanych
- `CarsController::favouriteListSessionAction()` - dla niezalogowanych

**Funkcje**:
- Dodawanie/usuwanie z ulubionych
- Lista ulubionych z paginacją
- Usuwanie wszystkich ulubionych
- Przechowywanie w sesji dla niezalogowanych

#### E. ZAPISANE WYSZUKIWANIA I NEWSLETTER
**Kontroler**: `CarsController::haveCarFoundAction()`
**Formularz**: `Form_SearchItem`

**Funkcje**:
- Zapisywanie kryteriów wyszukiwania
- Newsletter z nowymi ofertami
- Edycja zapisanych wyszukiwań
- Usuwanie wyszukiwań (przez hash lub email)
- Automatyczne powiadomienia o nowych autach

#### F. PANEL UŻYTKOWNIKA
**Kontroler**: `UserCpController`

**Funkcje**:
- Moje samochody
- Moje rezerwacje
- Ulubione samochody
- Zapisane wyszukiwania
- Edycja profilu
- Zmiana hasła

#### G. WIELOJĘZYCZNOŚĆ
- Obsługa języków: polski (pl), angielski (en)
- Routing z prefiksem językowym
- Tłumaczenia w plikach konfiguracyjnych

#### H. FORMULARZE KONTAKTOWE
- Sprzedaż samochodu
- Kontakt serwisowy
- Finansowanie (leasing/kredyt)
- Rejestracja użytkowników
- Logowanie/przypomnienie hasła

## 2. REKOMENDACJE PRZEPISANIA

### 2.1 Wybór Frameworka

**REKOMENDACJA: Next.js 14+ z TypeScript**

**Uzasadnienie**:
- Doskonałe SEO (SSR/SSG)
- Szybkość ładowania
- Nowoczesny stack technologiczny
- Łatwe wdrażanie
- Duża społeczność i wsparcie

**Alternatywy**:
- **SvelteKit** - lżejszy, szybszy, ale mniejsza społeczność
- **Nuxt.js** - jeśli zespół preferuje Vue.js

### 2.2 Stack Technologiczny

#### Frontend:
- **Framework**: Next.js 14+ (App Router)
- **Język**: TypeScript
- **Stylowanie**: Tailwind CSS + Headless UI
- **Komponenty**: Radix UI lub Shadcn/ui
- **Zarządzanie stanem**: Zustand lub Redux Toolkit
- **Formularze**: React Hook Form + Zod
- **HTTP Client**: Axios lub Fetch API
- **Animacje**: Framer Motion

#### Backend:
- **API**: Next.js API Routes lub osobny backend (Node.js/Express)
- **Baza danych**: PostgreSQL (migracja z MySQL)
- **ORM**: Prisma
- **Cache**: Redis
- **Wyszukiwanie**: Elasticsearch lub Algolia
- **Pliki**: AWS S3 lub Cloudinary

#### DevOps:
- **Hosting**: Vercel lub AWS
- **CI/CD**: GitHub Actions
- **Monitoring**: Sentry
- **Analytics**: Google Analytics 4

### 2.3 Struktura Projektu Next.js

```
src/
├── app/                    # App Router (Next.js 13+)
│   ├── [locale]/          # Wielojęzyczność
│   │   ├── cars/
│   │   │   ├── page.tsx   # Lista samochodów
│   │   │   └── [id]/
│   │   │       └── page.tsx # Szczegóły samochodu
│   │   ├── favourites/
│   │   ├── search/
│   │   └── user/
│   ├── api/               # API Routes
│   │   ├── cars/
│   │   ├── search/
│   │   └── user/
│   └── globals.css
├── components/            # Komponenty React
│   ├── ui/               # Podstawowe komponenty UI
│   ├── forms/            # Formularze
│   ├── car/              # Komponenty związane z samochodami
│   └── layout/           # Layout komponenty
├── lib/                  # Utilities i konfiguracja
├── hooks/                # Custom hooks
├── types/                # TypeScript types
└── utils/                # Helper functions

## 3. SZCZEGÓŁOWY PLAN MIGRACJI

### 3.1 Faza 1: Przygotowanie i Analiza (2-3 tygodnie)

#### Analiza danych:
- Eksport struktury bazy danych MySQL
- Analiza relacji między tabelami
- Identyfikacja kluczowych danych (samochody, użytkownicy, wyszukiwania)
- Przygotowanie skryptów migracyjnych

#### Projektowanie API:
```typescript
// Przykładowe endpointy API
GET /api/cars                    # Lista samochodów z filtrami
GET /api/cars/[id]              # Szczegóły samochodu
GET /api/cars/makes             # Lista marek
GET /api/cars/models/[makeId]   # Modele dla marki
POST /api/search/save           # Zapisz wyszukiwanie
GET /api/user/favourites        # Ulubione użytkownika
POST /api/user/favourites       # Dodaj do ulubionych
```

### 3.2 Faza 2: Podstawowa Infrastruktura (3-4 tygodnie)

#### Setup projektu:
```bash
npx create-next-app@latest autoauto-v2 --typescript --tailwind --app
cd autoauto-v2
npm install @prisma/client prisma
npm install @radix-ui/react-select @radix-ui/react-dialog
npm install react-hook-form @hookform/resolvers zod
npm install zustand axios
```

#### Konfiguracja bazy danych:
```prisma
// prisma/schema.prisma
model Car {
  id              Int      @id @default(autoincrement())
  srCarId         String   @unique
  makeId          Int
  modelId         Int
  title           String
  buildYear       Int
  firstRegYear    Int?
  price           Decimal
  priceGross      Decimal?
  mileage         Int
  engineCapacity  Int?
  fuelType        String
  gearbox         String
  bodyType        String
  status          String   @default("ACTIVE")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  make            Make     @relation(fields: [makeId], references: [id])
  model           Model    @relation(fields: [modelId], references: [id])
  photos          Photo[]
  favourites      Favourite[]

  @@map("cars")
}

model Make {
  id       Int    @id @default(autoincrement())
  name     String
  slug     String @unique
  typeId   Int
  cars     Car[]
  models   Model[]

  @@map("makes")
}

model User {
  id          Int         @id @default(autoincrement())
  email       String      @unique
  firstName   String
  lastName    String
  phone       String?
  createdAt   DateTime    @default(now())

  favourites  Favourite[]
  searches    SavedSearch[]

  @@map("users")
}
```

#### Komponenty UI:
```typescript
// components/ui/SearchFilters.tsx
interface SearchFiltersProps {
  onFiltersChange: (filters: SearchFilters) => void;
  initialFilters?: SearchFilters;
}

export function SearchFilters({ onFiltersChange, initialFilters }: SearchFiltersProps) {
  const { register, handleSubmit, watch } = useForm<SearchFilters>();

  return (
    <form className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Select name="makeId" placeholder="Wybierz markę">
          {/* Opcje marek */}
        </Select>

        <Select name="modelId" placeholder="Wybierz model">
          {/* Opcje modeli */}
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input
          type="number"
          placeholder="Cena od"
          {...register('priceMin')}
        />
        <Input
          type="number"
          placeholder="Cena do"
          {...register('priceMax')}
        />
      </div>

      {/* Więcej filtrów */}
    </form>
  );
}
```

### 3.3 Faza 3: Główne Funkcjonalności (4-5 tygodni)

#### Lista samochodów:
```typescript
// app/[locale]/cars/page.tsx
export default async function CarsPage({
  searchParams
}: {
  searchParams: SearchParams
}) {
  const cars = await getCars(searchParams);
  const makes = await getMakes();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col lg:flex-row gap-8">
        <aside className="lg:w-1/4">
          <SearchFilters
            makes={makes}
            initialFilters={searchParams}
          />
        </aside>

        <main className="lg:w-3/4">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">
              Kup <span className="text-orange-500">auto</span>
            </h1>

            <div className="flex gap-4">
              <SortSelect />
              <ViewToggle />
            </div>
          </div>

          <CarsList cars={cars} />
          <Pagination />
        </main>
      </div>
    </div>
  );
}
```

#### Szczegóły samochodu:
```typescript
// app/[locale]/cars/[id]/page.tsx
export default async function CarDetailsPage({
  params
}: {
  params: { id: string }
}) {
  const car = await getCar(params.id);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <CarImageGallery images={car.photos} />
        </div>

        <div>
          <CarHeader car={car} />
          <CarSpecs car={car} />
          <CarActions car={car} />
        </div>
      </div>

      <div className="mt-12">
        <CarDescription description={car.description} />
        <SimilarCars makeId={car.makeId} modelId={car.modelId} />
      </div>
    </div>
  );
}
```

### 3.4 Faza 4: Funkcje Zaawansowane (3-4 tygodnie)

#### System ulubionych:
```typescript
// hooks/useFavourites.ts
export function useFavourites() {
  const [favourites, setFavourites] = useState<number[]>([]);

  const addToFavourites = async (carId: number) => {
    try {
      await axios.post('/api/user/favourites', { carId });
      setFavourites(prev => [...prev, carId]);
      toast.success('Dodano do ulubionych');
    } catch (error) {
      toast.error('Błąd podczas dodawania');
    }
  };

  const removeFromFavourites = async (carId: number) => {
    try {
      await axios.delete(`/api/user/favourites/${carId}`);
      setFavourites(prev => prev.filter(id => id !== carId));
      toast.success('Usunięto z ulubionych');
    } catch (error) {
      toast.error('Błąd podczas usuwania');
    }
  };

  return {
    favourites,
    addToFavourites,
    removeFromFavourites,
    isFavourite: (carId: number) => favourites.includes(carId)
  };
}
```

#### Zapisane wyszukiwania:
```typescript
// components/SavedSearches.tsx
export function SavedSearches() {
  const { data: searches } = useSWR('/api/user/searches', fetcher);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Zapisane wyszukiwania</h3>

      {searches?.map((search: SavedSearch) => (
        <div key={search.id} className="border rounded-lg p-4">
          <div className="flex justify-between items-start">
            <div>
              <h4 className="font-medium">{search.title}</h4>
              <p className="text-sm text-gray-600">
                {search.resultsCount} wyników
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/cars?search=${search.id}`)}
              >
                Zobacz wyniki
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => editSearch(search.id)}
              >
                Edytuj
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
```

### 3.5 Faza 5: Optymalizacja i Wdrożenie (2-3 tygodnie)

#### Performance:
- Implementacja cache'owania (Redis)
- Optymalizacja obrazów (Next.js Image)
- Lazy loading komponentów
- Service Worker dla offline

#### SEO:
```typescript
// app/[locale]/cars/[id]/page.tsx
export async function generateMetadata({ params }: { params: { id: string } }) {
  const car = await getCar(params.id);

  return {
    title: `${car.make.name} ${car.model.name} ${car.buildYear} - AutoAuto.pl`,
    description: `${car.make.name} ${car.model.name} z ${car.buildYear} roku. Cena: ${car.price} PLN. Przebieg: ${car.mileage} km.`,
    openGraph: {
      title: `${car.make.name} ${car.model.name} ${car.buildYear}`,
      description: car.description,
      images: car.photos.map(photo => photo.url),
    },
  };
}
```

## 4. KLUCZOWE RÓŻNICE I ULEPSZENIA

### 4.1 Architektura
- **Stara**: Monolityczna aplikacja PHP
- **Nowa**: Modularna aplikacja React z API

### 4.2 Performance
- **Stara**: Server-side rendering w PHP
- **Nowa**: Hybrid rendering (SSR/SSG/CSR)

### 4.3 UX/UI
- **Stara**: jQuery + własny CSS
- **Nowa**: React + Tailwind CSS + nowoczesne komponenty

### 4.4 Mobilność
- **Stara**: Osobne widoki mobilne
- **Nowa**: Responsive design

### 4.5 SEO
- **Stara**: Podstawowe SEO
- **Nowa**: Zaawansowane SEO z Next.js

## 5. HARMONOGRAM I ZASOBY

### 5.1 Zespół
- **Frontend Developer** (React/Next.js) - 1 osoba
- **Backend Developer** (Node.js/API) - 1 osoba
- **UI/UX Designer** - 0.5 etatu
- **DevOps Engineer** - 0.25 etatu

### 5.2 Czas realizacji
- **Łącznie**: 14-19 tygodni (3.5-4.5 miesiąca)
- **MVP**: 10-12 tygodni
- **Pełna funkcjonalność**: 14-19 tygodni

### 5.3 Koszty szacunkowe
- **Rozwój**: 80-120k PLN
- **Infrastruktura**: 500-1000 PLN/miesiąc
- **Narzędzia**: 200-500 PLN/miesiąc

## 6. RYZYKA I MITYGACJA

### 6.1 Ryzyka techniczne
- **Migracja danych**: Dokładne testowanie skryptów
- **Performance**: Monitoring i optymalizacja
- **SEO**: Przekierowania 301, sitemap

### 6.2 Ryzyka biznesowe
- **Downtime**: Stopniowa migracja
- **Utrata funkcjonalności**: Szczegółowa analiza
- **Szkolenie użytkowników**: Dokumentacja i wsparcie

## 7. PODSUMOWANIE

Przepisanie AutoAuto.pl na Next.js przyniesie znaczące korzyści:
- **Lepsza wydajność** i szybkość ładowania
- **Nowoczesny UX** i responsywność
- **Lepsze SEO** i pozycjonowanie
- **Łatwiejsze utrzymanie** i rozwój
- **Skalowalność** na przyszłość

Rekomendowany stack (Next.js + TypeScript + Tailwind) zapewni solidne fundamenty dla długoterminowego rozwoju platformy.
```
