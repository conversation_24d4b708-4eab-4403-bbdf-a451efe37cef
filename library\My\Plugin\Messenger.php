<?php
class My_Plugin_Messenger extends Zend_Controller_Plugin_Abstract {

       public function preDispatch(Zend_Controller_Request_Abstract $request) {
               if (!$request->isXmlHttpRequest()) {
                       $messenger = Zend_Controller_Action_HelperBroker::getStat<PERSON><PERSON><PERSON><PERSON>('FlashMessenger');
                       $view = Zend_Layout::getMvcInstance()->getView();
                       $view->messenger = $messenger;
                       $view->messages = $messenger->getMessages();
               }
       }
}