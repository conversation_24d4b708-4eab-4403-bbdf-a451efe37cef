<?php

class My_Smarty extends Smarty
{
	/**
	* Obiekt widoku
	*
	* @var Zend_View_Abstract
	*/
	protected $_zendView = null;
 
	/**
	* Konstruktor
	*
	*/
	public function __construct()
	{
		parent::__construct();
 
		$this->plugin_handler = new My_Smarty_Plugin_Handler($this);
		$this->default_plugin_handler_func = 'My_Smarty_Plugin_Handler::loadZendPlugin';
	}
 
	/**
	* Ustawienie widoku
	*
	* @param Zend_View_Abstract $view obiekt widoku
	*/
	public function setZendView(Zend_View_Abstract $view)
	{
		$this->_zendView = $view;
	}
 
	/**
	* Pobranie widoku
	*
	* @param Zend_View_Abstract obiekt widoku
	*/
	public function getZendView()
	{
		return $this->_zendView;
	}
}