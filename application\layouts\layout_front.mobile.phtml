<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta http-equiv="Content-Language" content="<?= $this->language ?>" />
        <meta name="viewport" content="width=device-width, initial-scale=1">
		<title><?= $this->siteTitle ?></title>
		<?= $this->render('el/header_includes.phtml') ?>
	</head>
	
	<body>
		<div id="outer_container">
			<div id="fx_right">
				<div id="fx_right_content">flash</div>
			</div>
			
			<div id="inner_container">
				<div id="header">
					<?= $this->render('el/top_menu.mobile.phtml') ?>
                    <div class="clear"></div>
				</div>
				<div class="clear"></div>
				<?= $this->render('el/main_menu.mobile.phtml') ?>
				
				<div id="front_breadcrumb_replacement"></div>
				
				<?= $this->render('el/show_messages.phtml') ?>

                <div id="left_search" class="column_left_1">
                    <?php
                        $this->form = new Form_Search();
                    ?>
                    <?= $this->render('left_search.mobile.phtml') ?>
                </div>
				
				<div id="fx">
					<div id="fx_left">
						<?php if ($this->makesWithCounts && count($this->makesWithCounts) > 0): ?>
							<div id="front_makes">
								
								<?php
									$count = count($this->makesWithCounts)+1;  // + wszystkie
									$cols = 2;
									$rows = $count / $cols;
									$mod = $count % $cols;
									if ($mod > 0) $rows++;
									
									$makesRows = array();
                                    $makesRows[0 % $rows][] = array(0 => array("special_flag" => "","name" => "",  'type_slug' => 'osobowe', 'make_slug' => null ,"display_name" => strtoupper($this->translate->_('ALL')). ' ('.$this->allCount.')', 'display_name_style' => 'font-size: 15px; font-weight: bold; color: #000;'));
									for ($i=1; $i < $cols; $i++) {
										$makesRows[$i] = array();
									}
									$i = 1;
									foreach ($this->makesWithCounts as $index => $item) {
										$makesRows[$i % $rows][] = array($index => $item);
										$i++;
									}

								?>
								<table cellspacing="0" cellpadding="0">
									<?php $r=0; foreach ($makesRows as $row): ?>
										<tr>
											<?php $i=0; foreach ($row as $item): ?>
												<td>
                                                    
													<?= $this->partial(
															'make_boxes.phtml',
															array(
																'view' => $this,
																'makesWithCounts' => $item,
																'types' => array(1),
																'nonArrayFilters' => array()
															)
														)
													?>
												</td>
											<?php $i++; endforeach ?>
										</tr>
									<?php $r++; endforeach ?>
                                   
                                        
								</table>
								
							</div>
						<?php endif ?>
						
						<?php if ($this->exclusiveMakesWithCounts && count($this->exclusiveMakesWithCounts) > 0): ?>
							<table id="front_exclusive">
								<tr>
									<td class="left">
										<h2><a href="<?= $this->url(array('language' => $this->language), 'list', true) ?>?addName[]=types&amp;addValue_0[]=1&amp;addName[]=exclusive&amp;addValue_1=1"><?= $this->translate->_('SEARCH_EXCLUSIVE') ?> (<?= (int)$this->exclusiveMakesWithCountsTotalCount ?>)</a></h2>
									</td>
									<td class="right">
										<?= $this->partial(
											'make_boxes.phtml',
											array(
												'view' => $this,
												'makesWithCounts' => $this->exclusiveMakesWithCounts,
												'types' => array(1),
												'nonArrayFilters' => array()
											)
										) ?>
									</td>
								</tr>
							</table>
						<?php endif ?>
						
						<?php if ($this->otherCategoriesWithCounts && count($this->otherCategoriesWithCounts) > 0): ?>
                            <table id="front_other_categories">
                                <tr>

                                    <td class="">

                                        <?=

                                        $this->partial(
                                            'category_boxes.phtml',
                                            array(
                                                'view' => $this,
                                                'catsWithCounts' => $this->otherCategoriesWithCounts,
                                                'types' => array(2, 3, 4)
                                            )
                                        ) ?>
                                    </td>
                                </tr>
                            </table>
						<?php endif ?>

                
					</div>

				</div>
				


				</div>
				
				<?= $this->layout()->content ?>
				
				<?= $this->render('el/footer.mobile.phtml') ?>

			</div>
		</div>
		
        <script type="text/javascript">
        $(function () {

            setInterval(function(){

                if("rgb(255, 255, 255)" == $(".have_car_found_bar a").css('color'))
                    $(".have_car_found_bar a").css('color', 'rgb(246, 120, 18)');
                else if("rgb(246, 120, 18)" == $(".have_car_found_bar a").css('color'))
                    $(".have_car_found_bar a").css('color', 'rgb(255,255,255)');  
                }
            ,1500);


        });

        </script>
		<script type="text/javascript">
            

            var _gaq = _gaq || [];
            _gaq.push(['_setAccount', 'UA-********-1']);
            _gaq.push(['_setDomainName', 'autoauto.pl']);
            _gaq.push(['_trackPageview']);

            (function() {
                var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
                ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
                var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
            })();

        </script>

        <script type="text/javascript">
            adroll_adv_id = "G4XRYOVPF5CGTCX5SSQN67";
            adroll_pix_id = "YQP7DP2BQFHYNCLGIBQLP5";
            (function () {
                var oldonload = window.onload;
                window.onload = function(){
                    __adroll_loaded=true;
                    var scr = document.createElement("script");
                    var host = (("https:" == document.location.protocol) ? "https://s.adroll.com" : "http://a.adroll.com");
                    scr.setAttribute('async', 'true');
                    scr.type = "text/javascript";
                    scr.src = host + "/j/roundtrip.js";
                    ((document.getElementsByTagName('head') || [null])[0] ||
                        document.getElementsByTagName('script')[0].parentNode).appendChild(scr);
                    if(oldonload){oldonload()}};
            }());
        </script>

        <!-- Kod tagu remarketingowego Google -->
        <!--------------------------------------------------
        Tagi remarketingowe nie mogą być wiązane z informacjami umożliwiającymi identyfikację osób ani umieszczane na stronach o tematyce należącej do kategorii kontrowersyjnych. Więcej informacji oraz instrukcje konfiguracji tagu znajdziesz tutaj: http://google.com/ads/remarketingsetup
        --------------------------------------------------->
        <script type="text/javascript">
            /* <![CDATA[ */
            var google_conversion_id = 976363198;
            var google_custom_params = window.google_tag_params;
            var google_remarketing_only = true;
            /* ]]> */
        </script>
        <script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
        </script>
        <noscript>
            <div style="display:inline;">
                <img height="1" width="1" style="border-style:none;" alt="" src="//googleads.g.doubleclick.net/pagead/viewthroughconversion/976363198/?value=0&amp;guid=ON&amp;script=0"/>
            </div>
        </noscript>


    </body>
	
</html>