<div id="left_static" class="column_left_1">
    <?php if($this->logged): ?>
	<div class="container">
		<?= $this->render('user_cp_menu.phtml') ?>
	</div>
    <?php endif;?>
</div>

<div id="content_static" class="column_right_1">
	<h1 id="static_title"><?= $this->translate->_('EDIT_SEARCH') ?></h1>
	<div id="content_static_container" class="<?= $this->containerClass ?>">
		<?= $this->form; ?>
	</div>
	<div class="clear"></div>
</div>


<div class="clear"></div>

<script type="text/javascript">
	$(function(){
		var multiselectOptions = {
			checkAllText: "<?= $this->escape($this->translate->_('MULTISELECT_CHECK_ALL')) ?>",
			uncheckAllText: "<?= $this->escape($this->translate->_('MULTISELECT_UNCHECK_ALL')) ?>",
			selectedText: "<?= $this->escape($this->translate->_('MULTISELECT_SELECTED_TEXT')) ?>",
			minWidth: 270
		};
		
		$("#edit_form .form_label").hide();
		
		$('#makes').multiselect($.extend({}, multiselectOptions, {multiple: true, selectedList: 1, noneSelectedText: "<?= $this->escape($this->translate->_('MAKE')) ?>"}));
		$('#models').multiselect($.extend({}, multiselectOptions, {multiple: true, selectedList: 1, noneSelectedText: "<?= $this->escape($this->translate->_('MODEL')) ?>"}));
		$('#edit_form #build').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('PRODUCTION_YEAR')) ?>"}));
		$('#edit_form #odometer').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('ODOMETER')) ?>"}));
		$('#edit_form #categories').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('CATEGORY')) ?>"}));
		$('#edit_form #important_features').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('IMPORTANT_FEATURES')) ?>"}));
		$('#edit_form #engine').multiselect($.extend({}, multiselectOptions, {minWidth: 110, noneSelectedText: "<?= $this->escape($this->translate->_('ENGINE')) ?>"}));
		$('#edit_form #gearboxes').multiselect($.extend({}, multiselectOptions, {minWidth: 110, noneSelectedText: "<?= $this->escape($this->translate->_('GEARBOX')) ?>"}));
		
		$("#makes").bind("multiselectclick", function(event, ui){
			var selectedArray = new Array();
			selectedArray = [$("#makes").multiselect('getChecked').val()]; //assume only single element selected as of 19.07.2011
			
			var modelsArray;
			$.ajax({
				type: 'GET',
				url: '<?= $this->url(array(), 'getmodels', true); ?>',
				data: {makes: selectedArray},
				dataType: 'json',
				async: false,
				success: function(data) {
					modelsArray = data;
				},
				error: function() {
				}
			});
			$('#models').find('option').remove();
			var models = $('#models');
			models.append('<option value=""><?= $this->escape($this->translate->_('MODEL')); ?></option>');
			$.each(modelsArray, function(index, value) { 
				models.append('<option value="' + index + '">' + value + '</option>');
			});
			$('#models').multiselect("refresh");
		});

        var fixedDisabledFeaturesIndexStart = 13;
        var fixedDisabledFeaturesIndexStop = 18;
        var divideModulo = [18, 18, 19];
        $(".for_important_features ul.ui-multiselect-checkboxes").append('<div class="clear" />');
        var lastModulo = 0;
        for (var i in divideModulo) {
            $(".for_important_features ul.ui-multiselect-checkboxes>li:lt("+divideModulo[i]+")").wrapAll('<div class="features_column" />');
        }
        $(".for_important_features ul.ui-multiselect-checkboxes li").slice(fixedDisabledFeaturesIndexStart, fixedDisabledFeaturesIndexStop).find('input').attr('checked', 'checked').attr('disabled', 'disabled').siblings('span').css('color', '#f67812');
									
		/*var buildFrom = $(".for_build_from").outerHtml();
		var buildTo =  $(".for_build_to").outerHtml();
		
		$(".for_build_from, .for_build_to").remove();
		$(".for_build div.ui-multiselect-menu").append(buildFrom, buildTo);
		$(".for_build_from input, .for_build_to input").css('color', '#333');
		$(".for_build_from, .for_build_to").css({'width': '115px', 'float': 'left'});
		$(".for_build_from").css({'padding-right': '27px'});
		$(".for_build_from input, .for_build_to input").css({'width': '105px'});
        
        var cubicCapacityFrom = $(".for_cubic_capacity_from").outerHtml();
        var cubicCapacityTo =  $(".for_cubic_capacity_to").outerHtml();

        $(".for_cubic_capacity_from, .for_cubic_capacity_to").remove();
        $(".for_engine div.ui-multiselect-menu").append(cubicCapacityFrom, cubicCapacityTo);
        $(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css('color', '#333');
        $(".for_cubic_capacity_from, .for_cubic_capacity_to").css({'width': '115px', 'float': 'left'});
        $(".for_cubic_capacity_from").css({'padding-right': '27px'});
        $(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css({'width': '105px'});*/
		
		$("#edit_form  .input_text input").compactInputs({
			getLabelFn: function(el) {
				return $(el).closest(".form_input").siblings(".form_label").find("label");
			}
		});
		
	});
	
</script>