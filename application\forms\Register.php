<?php

class Form_Register extends My_Form {

	public function init() {
		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
				'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')),
					new Zend_Validate_Db_NoRecordExists('aa_users', 'email'),
				)
			)),
			/*new Zend_Form_Element_Password('password', array(
				'label'	=>	'PASSWORD',
				'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 6, 'max' => 64, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Password('password_repeat', array(
				'label'	=>	'PASSWORD_REPEAT',
				'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 6, 'max' => 64, 'encoding' => 'UTF-8')))
			)),*/
			new Zend_Form_Element_Captcha('captcha', array(
				'label' => 'CAPTCHA',
				'captcha' => new My_Captcha_Math(array(
					'timeout' => '180',
				)),
			)),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Hidden('action', array(
				'label'	=>	'',
				'value' => 'register'
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label' => 'SUBMIT_REGISTER'
			)),
		));
		//parent::init();
		parent::init(array('customOptions' => array('noAsteriskInLabel' => true)));
	}//init
	
	public function isValid($data) {
		$ret = parent::isValid($data);
		$isValid = true;
		
		if ($data['password'] !== $data['password_repeat']) {
			$isValid = false;
			$this->password_repeat->addError('fieldNotMatching');
		}
		
		return $ret && $isValid;
	}


}