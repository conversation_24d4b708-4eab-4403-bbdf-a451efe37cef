<?php

class Model_Employees extends Model_Base {
	
	public function addOrEdit($data, $doTransaction=true) {
		$languages = $data['languages'];
		unset($data['languages']);

        $financingLocations = $data['financing_locations'];
        unset($data['financing_locations']);
		
		$photos = $data['photos'];
		unset($data['photos']);

		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$path = $opt['employees']['photos']['path'];
		$sizes = $opt['employees']['photos']['sizes'];

		try {
			if ($doTransaction) $this->db->beginTransaction();
			
			$employee = $this->getBySrId($data['sr_id']);
			if (!empty($employee)) {
				foreach ($sizes as $sizeName => $dimensions) {
					@unlink($path . DIRECTORY_SEPARATOR . $employee['photo_basename'] . "_" . $sizeName . "." . $employee['photo_extension']);
					if (array_key_exists($sizeName, $photos)) {
						$success = file_put_contents(
							$path . DIRECTORY_SEPARATOR . $data['photo_basename'] . "_" . $sizeName . "." . $data['photo_extension'],
							$photos[$sizeName]
						);
						if ($success === false) {
							throw new Exception("file_put_contents failed in " . __METHOD__ . ", line " . (__LINE__ - 1));
						}
					}
				}
				
				$oldLangs = $this->getEmployeesLanguages($employee['id']);
				if (!is_array($oldLangs)) {
					$oldLangs = array();
				}
				else {
					$oldLangs = array_keys($oldLangs);
				}
				
				$newLangs = (is_array($languages)) ? $languages : array();
				
				$toDelete = array_diff($oldLangs, $newLangs);
				$toAdd = array_diff($newLangs, $oldLangs);
				
				foreach ($toDelete as $langId) {
					$this->db->delete(
						$this->tables['employees_languages'],
						"employee_id = " . $employee['id'] . " AND language_id = " . (int)$langId
					);
				}
				
				foreach ($toAdd as $langId) {
					$this->db->insert(
						$this->tables['employees_languages'],
						array(
							'employee_id' => $employee['id'],
							'language_id' => (int)$langId
						)
					);
				}

                $oldFinancingLocations = $this->getEmployeesFinancingLocations($employee['id']);
                if (!is_array($oldFinancingLocations)) {
                    $oldFinancingLocations = array();
                }
                else {
                    $oldFinancingLocations = array_keys($oldFinancingLocations);
                }

                $newFinancingLocations = (is_array($financingLocations)) ? $financingLocations : array();


                $toDelete = array_diff($oldFinancingLocations, $newFinancingLocations);
                $toAdd = array_diff($newFinancingLocations, $oldFinancingLocations);

                foreach ($toDelete as $locationId) {
                    $this->db->delete(
                        $this->tables['employees_financing_locations'],
                        "employee_id = " . $employee['id'] . " AND location_id = " . (int)$locationId
                    );
                }

                foreach ($toAdd as $locationId) {
                    $this->db->insert(
                        $this->tables['employees_financing_locations'],
                        array(
                            'employee_id' => $employee['id'],
                            'location_id' => (int)$locationId
                        )
                    );
                }
				
				$this->db->update(
					$this->tables['employees'],
					$data,
					"id = " . $employee['id']
				);
			}
			else {
				$this->db->insert(
					$this->tables['employees'],
					$data
				);
				$eId = $this->db->lastInsertId($this->tables['employees'], 'id');
				if (empty($eId)) {
					throw new Exception("Emlpoyee id empty after insertion in " . __METHOD__ . ", line " . (__LINE__ - 1));
				}

				foreach ($languages as $langId) {
					$this->db->insert(
						$this->tables['employees_languages'],
						array(
							'employee_id' => $eId,
							'language_id' => $langId
						)
					);
				}

                foreach ($financingLocations as $locationId) {
                    $this->db->insert(
                        $this->tables['employees_financing_locations'],
                        array(
                            'employee_id' => $eId,
                            'location_id' => $locationId
                        )
                    );
                }
				
				foreach ($sizes as $sizeName => $dimensions) {
					@unlink($path . DIRECTORY_SEPARATOR . $data['photo_basename'] . "_" . $sizeName . "." . $data['photo_extension']);
					$success = @file_put_contents(
						$path . DIRECTORY_SEPARATOR . $data['photo_basename'] . "_" . $sizeName . "." . $data['photo_extension'],
						$photos[$sizeName]
					);
					if ($success === false) {
						throw new Exception("file_put_contents failed in " . __METHOD__ . ", line " . (__LINE__ - 1));
					}
				}
			}
			
			if ($doTransaction) $this->db->commit();
			
			$cache = Zend_Registry::get('Cache');
			$cache->clean(Zend_Cache::CLEANING_MODE_MATCHING_ANY_TAG, array('location', 'employee'));
		}
		catch (Exception $e) {
			if ($doTransaction) $this->db->rollBack();
			throw $e;
		}
	}
	
	public function getAll($options=array()) {
		if (!is_array($options)) {
			throw new Exception("Options not an array in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		
		$defaults = array(
			'assoc' => true,
			'sr_assoc' => false,
			'only_visible' => true,
		);
		
		$options = array_merge($defaults, $options);
		$fetchMethod = "fetchAll";
		
		$select = $this->db->select()
			->from(array('e' => $this->tables['employees']))
			->order(array('last_name ASC', 'first_name ASC'));
		
		if ($options['assoc']) {
			$select->reset('columns');
			$select->columns(array('id', 'CONCAT_WS("",first_name, " ", last_name , " ", phone)'));
			$select->reset('order');
			$select->order(array('first_name ASC', 'last_name ASC'));
			$fetchMethod = "fetchPairs";
		}
		
		if ($options['sr_assoc']) {
			$select->reset('columns');
			$select->columns(array('sr_id', 'CONCAT_WS("",first_name, " ", last_name, " ", phone)'));
			$select->reset('order');
			$select->order(array('first_name ASC', 'last_name ASC'));
			$fetchMethod = "fetchPairs";
		}
		
		if ($options['only_visible']) {
			$select->where('visible = 1');
		}
		
		return $this->db->$fetchMethod($select);
	}
	
	public function getByLanguage($language) {
		
		$select = $this->db->select()
			->from(array('e' => $this->tables['employees']))
			->where('visible = 1')
			->joinLeft(array('esl' => $this->tables['employees_languages']),
				'esl.employee_id = e.id',
				array()
			)
			->joinLeft(array('el' => $this->tables['employee_languages']),
				'esl.language_id = el.id',
				array()
			)
			->joinLeft(array('loc' => $this->tables['locations']),
				'IFNULL(e.display_location_id, e.location_id) = loc.location_id',
				array('location_name' => 'name')
			)
			->joinLeft(array('locgr' => $this->tables['location_groups']),
				'locgr.id = loc.location_group_id',
				array('address')
			)
			->where('el.short_name = ?', $language)
			->order(array('last_name ASC', 'first_name ASC'));
		$employees = $this->db->fetchAll($select);
				
		return $employees;
	}
	
	public function getByLocation($locationId, $withLanguages=false) {
		$locationId = (int)$locationId;
		
		$select = $this->db->select()
			->from(array('e' => $this->tables['employees']))
            ->join(array("l" => $this->tables['locations']), "l.location_id = IFNULL(e.display_location_id, e.location_id) OR l.location_alias_id = IFNULL(e.display_location_id, e.location_id)",null)    
			->where('IFNULL(l.location_alias_id, l.location_id) = ' . $locationId)
			->where('e.visible = 1')
            ->group('e.id')    
			->order(array('e.last_name ASC', 'e.first_name ASC'));
        
        $employees = $this->db->fetchAll($select);
		
		if ($withLanguages) {
			foreach ($employees as $key => $emp) {
				$employees[$key]['languages'] = $this->getEmployeesLanguages($emp['id']);
			}
		}
		
      
		return $employees;
	}
	
	public function getBySrId($id) {
		$select = $this->db->select()
			->from($this->tables['employees'])
			->where('sr_id = ' . (int)$id);
		return $this->db->fetchRow($select);
	}

    public function getById($id) {
        $select = $this->db->select()
            ->from($this->tables['employees'])
            ->where('id = ' . (int)$id);
        return $this->db->fetchRow($select);
    }
	
	public function getEmployeesLanguages($eId) {
		$select = $this->db->select()
			->from($this->tables['employees_languages'], array('language_id'))
			->joinLeft(array('l' => $this->tables['employee_languages']),
				'l.id = language_id',
				array('short_name')
			)
			->where('employee_id = ' . (int)$eId);
		return $this->db->fetchPairs($select);
	}

    public function getEmployeesFinancingLocations($eId) {
        $select = $this->db->select()
            ->from($this->tables['employees_financing_locations'], array('location_id','location_id'))
            ->where('employee_id = ' . (int)$eId);
        return $this->db->fetchPairs($select);
    }
	
	public function getFinancing($locationId=null, $random=false,$exclude=array()) {
		$select = $this->db->select()
			->from(array('e' => $this->tables['employees']), array('id','first_name', 'last_name', 'email', 'phone', 'skype', 'location_id' => new Zend_Db_Expr('IFNULL(efl.location_id,IFNULL(e.display_location_id, e.location_id))'), 'photo_basename', 'photo_extension'))
			->where('visible = 1')
			->where('financing = 1')
			->where('lg.is_visible = 1')
            ->joinLeft(array('efl' => $this->tables['employees_financing_locations']),
                'efl.employee_id = e.id', null
            )
			->joinLeft(array('l' => $this->tables['locations']),
				'l.location_id = IFNULL(efl.location_id, IFNULL(e.display_location_id, e.location_id))',
				array('l_name' => 'name')
			)
			->joinLeft(array('lg' => $this->tables['location_groups']),
				'lg.id = l.location_group_id',
				array('lg_address' => 'address')
			)

			->order(array('lg.full_name ASC', 'l.name ASC'));

        if(!empty($exclude)) {
            $select->where('e.id NOT IN(?)', $exclude);
        }
		
		if ((int)$locationId > 0) {
			$select->where('IFNULL(e.display_location_id, e.location_id) = ' . (int)$locationId);
		}


		$results= $this->db->fetchAll($select);

        if($results) {
            if($random) {
                return $results[array_rand($results)];
            }

            return $results;
        }

        return false;
	}


	
	public function getRandom($withPhoto=true, $financing=null, $count=1, $ignoreLanguage=false,$exclude=array()) {
		$select = $this->db->select()
			->from(array('e' => $this->tables['employees']), array('id', 'first_name', 'last_name', 'email', 'phone', 'skype', 'location_id' => new Zend_Db_Expr('IFNULL(e.display_location_id, e.location_id)'), 'photo_basename', 'photo_extension'))
			->where('visible = 1')
			->where('(e.email IS NOT NULL AND e.email != "") OR (e.phone IS NOT NULL AND e.phone != "")')
			->joinLeft(array('l' => $this->tables['locations']),
				'l.location_id = IFNULL(e.display_location_id, e.location_id)',
				array('l_name' => 'name')
			)
			->joinLeft(array('lg' => $this->tables['location_groups']),
				'lg.id = l.location_group_id',
				array('lg_address' => 'address')
			)
		;

        if(!empty($exclude)) {
            $select->where('e.id NOT IN(?)', $exclude);
        }
        
        if(Zend_Registry::isRegistered('randomEmploye') && Zend_Registry::get('randomEmploye'))
        {
            $select->where('e.sr_id = ?', Zend_Registry::get('randomEmploye'));
            $withPhoto = false;
            //$ignoreLanguage=true;
        }
		
		if ($withPhoto) {
			$select->where('photo_basename IS NOT NULL AND photo_basename != ""');
		}
		
		if ($financing !== null) {
			if ($financing) {
				$select->where('financing = 1');
			}
			else {
				$select->where('financing = 0');
			}
		}
		
		$selectedIds = array();
		
		$selCount = clone($select);
		$selCount->reset('columns');
		$selCount->columns(new Zend_Db_Expr('COUNT(e.id)'));

		$selectLanguageRequired = clone($select);
		$language = Zend_Registry::get('translate_language');
		if ($language != "pl" && !$ignoreLanguage) {
			$selectLanguageRequired->joinLeft(array('elan' => $this->tables['employees_languages']),
				'elan.employee_id = e.id',
				array()
			)
			->joinLeft(array('lan' => $this->tables['employee_languages']),
				'elan.language_id = lan.id',
				array()
			)
			->where('lan.id IS NOT NULL')
			->where('lan.short_name = ?', $language);
			
			$selCountLang = clone($selectLanguageRequired);
			$selCountLang->reset('columns');
			$selCountLang->columns(new Zend_Db_Expr('COUNT(e.id)'));
			
			$randomCountLang = $this->db->fetchOne($selCountLang);
		}
		$randomCount = $this->db->fetchOne($selCount);
		
		$results = array();
		for ($i = 0; $i < $count; $i++) {
			$selectOne = clone($select);
			$random = mt_rand(0, $randomCount - 1);
			
			if ($i == 0 && $financing != true && isset($randomCountLang) && $randomCountLang > 0 ) {
				$selectOne = $selectLanguageRequired;

				$random = mt_rand(0, $randomCountLang - 1);
			}
			elseif (count($selectedIds) > 0) {
				$selCount = clone($select);
				$selCount->reset('columns');
				$selCount->columns(new Zend_Db_Expr('COUNT(e.id)'));
				$selCount->where('e.id NOT IN (?)', $selectedIds);
				
				$randomCount = $this->db->fetchOne($selCount);
				$random = mt_rand(0, $randomCount - 1);
				
				$selectOne->where('e.id NOT IN (?)', $selectedIds);
			}
			
			$selectOne->limit(1, $random);
			
			
			$result = $this->db->fetchRow($selectOne);
            
			$results[$i] = $result;
			$selectedIds[] = $result['id'];
		}
		
		if (is_array($results) && isset($results[1]) && !$results[0] && $results[1]) {
			$results[0] = $results[1];
		}
		elseif (!$results[0] && !$ignoreLanguage) {
			$results = $this->getRandom($withPhoto, $financing, $count=1, $ignoreLanguage=true,$exclude);
		}
		
		return $results;
	}
	
}