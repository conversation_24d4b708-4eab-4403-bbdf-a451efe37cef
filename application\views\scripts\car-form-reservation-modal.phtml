<div class="modal fade" id="<?= $this->id ?>" tabindex="-1" role="dialog" aria-labelledby="<?= $this->id ?>" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">

                <div class="row">
                    <div class="col-lg-12">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        <div class="d-flex align-content-center flex-wrap ">
                            <span class="id mr-1 d-flex justify-content-center flex-column ">id: <?= $this->car['sr_car_id'] ?></span>
                            <span class="col">
                                    <?= $this->escape($this->car['make_name'] . " " . $this->car['model_name'] . " " . $this->car['build_year'] . ' ' . $this->translate->_('PROD.')) ?>
                                </span>
                        </div>

                    </div>
                    <div class="col-lg-12">

                        <h5 class="modal-title"><?= $this->title ?></h5>
                    </div>
                </div>

            </div>
            <form action="<?php echo $this->form->getAction() ?>"
                  enctype="<?php echo $this->form->getEnctype() ?>"
                  method="<?php echo $this->form->getMethod() ?>"
                  id="<?php echo $this->form->getId() ?>"
                  class="<?php echo $this->form->getAttrib('class') ?>" >
                <div class="modal-body">
                    <?php foreach($this->form->getElements() as $element): ?>
                        <?php if($element->getType() != 'Zend_Form_Element_Submit') : ?>
                            <?php if(in_array($element->getType(), array('Zend_Form_Element_Hidden','Zend_Form_Element_Hash')) ) : ?>
                                <?= $element->renderViewHelper() ?>
                            <?php else: ?>
                                <div class="form-group<?=$element->hasErrors() ? ' has-danger' : '' ?>">

                                    <?php if($element->getType() == 'Zend_Form_Element_Captcha') : ?>
                                        <?= $element->renderCaptcha() ?>
                                    <?php else: ?>
                                        <label class="form-control-label" for="<?= $element->getId() ?>"><?= $element->getLabel() ?></label>
                                        <?= $element->renderViewHelper() ?>
                                    <?endif;?>
                                    <?php if($element->hasErrors()): ?>
                                        <div class="form-control-feedback"><?= $element->renderErrors() ?></div>
                                    <?endif ?>
                                </div>
                            <?endif;?>
                        <?endif;?>
                    <?php endforeach; ?>

                </div>
                <div class="modal-footer flex-column">
                    <?= $this->form->submit->renderViewHelper() ?>
                    <?= $this->form->preview->renderViewHelper() ?>
                </div>
            </form>
        </div>
    </div>
</div>
