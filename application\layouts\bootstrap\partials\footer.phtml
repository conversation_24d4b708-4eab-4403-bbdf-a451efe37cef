
<footer>
    <div class="row py-4">
        <div class="col-lg-3">
            <ul class="list-unstyled">
                <li class="mb-2"><a href="<?= $this->url(array('language' => $this->language), 'list', true) ?>"><?= $this->translate->_('CAR') ?></a></li>
                <li class="mb-2"><a href="<?= $this->url(array('language' => $this->language), 'financing', true) ?>"><?= $this->translate->_('FINANCING') ?></a></li>
                <li class="mb-2"><a href="<?= $this->url(array('language' => $this->language), 'aboutUs', true) ?>"><?= $this->translate->_('COMPANY') ?></a></li>
                <li class="mb-2"><a href="<?= $this->url(array('language' => $this->language), 'aboutUsWork', true) ?>"><?= $this->translate->_('ABOUT_US_WORK') ?></a></li>
                <li class="mb-2"><a href="<?= $this->url(array('language' => $this->language), 'racingTeam', true) ?>"><?= $this->translate->_('RACING_TEAM') ?></a></li>
            </ul>
        </div>

        <?php $cache = Zend_Registry::get('Cache');
        if (!($html = $cache->load('footer_locations_' . $this->language))):
        $locations = new Model_Locations();
        $data = $locations->getLocationGroupsWithLocations(array(1,3, 8));
        ob_start();
        ?>
        <?php foreach ($data as $lg): ?>
            <div class="col-lg-3 location-column location-<?= $lg['id'] ?>">
                <strong><?= $this->escape($lg['business_name']) ?></strong>
                <br />
                <?= $this->escape($lg['address']) ?>
                <br />
                <?= $this->escape($lg['zip_code'] . ' ' . $lg['city']) ?>
                <br />
                <?php foreach ($lg['locations'] as $loc): ?>
                    <?php if ($loc['phone'] || $loc['fax'] || $loc['email']): ?>
                        <br />
                        <?= $this->escape($this->translate->_('LOCATION_POINT')) ?> <strong><?= $this->escape($loc['name']) ?></strong>
                        <br />
                        <?php if ($loc['phone']): ?>
                            <?= $this->escape($this->translate->_('PHONE_SHORT') . ': ' . $loc['phone']) ?>
                            <br />
                        <?php endif ?>
                        <?php if ($loc['fax']): ?>
                            <?= $this->escape($this->translate->_('FAX_SHORT') . ': ' . $loc['fax']) ?>
                            <br />
                        <?php endif ?>
                        <?php if ($loc['email']): ?>
                            <a href="mailto:<?=$loc['email']?>"><?= $this->escape($this->translate->_('EMAIL_SHORT') . ': ' . $loc['email']) ?></a>
                            <br />
                        <?php endif ?>
                    <?php endif ?>
                <?php endforeach ?>
            </div>
        <?php endforeach ?>
        <div class="clear"></div>
    </div>
    <?php
    $html = ob_get_flush();
    $cache->save($html, 'footer_locations_' . $this->language, $tags=array('translate', 'location', $this->language));
    ?>
    <?php else: ?>
        <?= $html ?>
    <?php endif ?>


    </div>
</footer>




