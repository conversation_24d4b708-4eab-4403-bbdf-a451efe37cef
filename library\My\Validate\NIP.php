<?php

class My_Validate_NIP extends Zend_Validate_Abstract {

	const NIP_NUMBER_INVALID = 'nipNumberInvalid';
	
	protected $_messageTemplates = array(
		self::NIP_NUMBER_INVALID => "nipNumberInvalid",
	);
	
	public function isValid($value) {
		$value = preg_replace("/[^0-9]+/","",$value);
		if (strlen($value) != 10) {
			$this->_error(self::NIP_NUMBER_INVALID);
			return false;
		}
		
		$arrSteps = array(6, 5, 7, 2, 3, 4, 5, 6, 7);
		$intSum=0;
		for ($i = 0; $i < 9; $i++) {
			$intSum += $arrSteps[$i] * $value[$i];
		}
		$int = $intSum % 11;
		
		$intControlNr=($int == 10)?0:$int;
		if ($intControlNr == $value[9]) {
			return true;
		}
		
		$this->_error(self::NIP_NUMBER_INVALID);
		return false;
	}
	
}