<?php

class Form_Financing extends My_Form {
	
	protected $_type = 'leasing';
	
	public function init() {

		$cars = new Model_Cars_Cars();


		$tr = Zend_Registry::get('Zend_Translate');

		$this->addElements(array(

            new Zend_Form_Element_Text('email', array(
                'label'	=>	'EMAIL',
                'required' => true,
                'validators' => array(
                    new Zend_Validate_EmailAddress(),
                    new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8'))
                ),
                'attribs' => array('class' => 'form-control','placeholder' => $tr->_('EMAIL')),
            )),
            new Zend_Form_Element_Text('phone', array(
                'label'	=>	'PHONE',
                'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('PHONE')),
            )),

            new Zend_Form_Element_Text('first_name', array(
                'label'	=>	'FIRST_NAME',
                'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control','placeholder' => $tr->_('FIRST_NAME')),
                'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
            )),
            new Zend_Form_Element_Text('last_name', array(
                'label'	=>	'LAST_NAME',
                'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control','placeholder' => $tr->_('LAST_NAME')),
                'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
            )),

            new Zend_Form_Element_Select('make', array(
                'label'	=>	'MAKE',
                'required' => true,
                'validators' => array(),
                'multiOptions' =>  array("" => "" ) + $cars->getCarMakesWithCountsByNames(null, null, false, $forSelect=true),
                'attribs' => array('class' => 'form-control', 'data-placeholder' => $tr->_('MAKE'))
            )),
            new Zend_Form_Element_Select('model', array(
                'label'	=>	'MODEL',
                'required' => true,
                'validators' => array(),
                'registerInArrayValidator' => false,
                'attribs' => array( 'disabled' => 'disabled', 'class' => 'select2 form-control' , 'data-placeholder' => $tr->_('MODEL'))
            )),


            new Zend_Form_Element_Textarea('message', array(
                'label'	=>	'SELL_CAR_DESCRIPTION',
                'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control','placeholder' => $tr->_('SELL_CAR_DESCRIPTION'), 'rows' => 6),
            )),

            new Zend_Form_Element_Radio('type', array(

                'required' => true,
                'label'	=>	'TYPE',
                'validators' => array(),
                'multiOptions' =>  array("leasing" => $tr->_('LEASING'), "credit" => $tr->_('CREDIT'), "i_do_not_know" => $tr->_('I_DO_NOT_KNOW')  ),
                'attribs' => array('class' => 'form-check-input', 'label_class' => 'form-check-label', 'placeholder' => $tr->_('TYPE'))
            )),


			new Zend_Form_Element_Text('price', array(
				'label'	=>	'CAR_PRICE',
                'required' => true,
                'attribs' => array('class' => 'form-control','placeholder' => $tr->_('CAR_PRICE')),
			)),
            new Zend_Form_Element_Hidden('financing', array(
                'value' => 1,
            )),
			new Zend_Form_Element_Text('contribution', array(
				'label'	=>	'CONTRIBUTION',
                'required' => true,
                'attribs' => array('class' => 'form-control','placeholder' => $tr->_('CONTRIBUTION')),
			)),

            new Zend_Form_Element_Captcha('captcha', array(
                'label' => 'CAPTCHA',
                'captcha' => new My_Captcha_ReCaptcha(array(
                    'siteKey'  => '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-',
                    'secretKey' => '6Lfx7A0UAAAAAL41t8hFSPCzuh6VQGhcpXttwrJd'
                )),
            )),


			new Zend_Form_Element_Submit('submit', array(
				'label'	=>	'SEND',
                'attribs' => array('class' => ' btn btn-action btn-action-orange'),
			)),
		));

        $this->getElement('type')->setSeparator('');
		parent::init();
	}
	
	public function setType($value) {
		$this->_type = $value;
	}
	
}