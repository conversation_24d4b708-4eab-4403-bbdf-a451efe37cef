<?php

class UserCpController extends Zend_Controller_Action {
	
	public function changePasswordAction() {
		$form = new Form_UserCp_ChangePassword();
		$this->view->form = $form;
		
		if ($this->_request->isPost()) {
			if ($form->isValid($this->_request->getPost())) {
				$data = $form->getValues();
			
				$users = new Model_Users();
				$userId = Zend_Auth::getInstance()->getIdentity()->id;
				$users->changePassword($userId, $data);
				
				$this->view->messenger->addMessage($this->_helper->translate('PASSWORD_CHANGED'));
				$this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);
				$this->_helper->redirectBack('', $this->view->url(array('language' => $this->_request->getParam('language'), 'controller' => 'user-cp'), 'general', true));
			}//isValid
			else {
			}
		}//isPost
	}
	
	public function editAction() {
		$form = new Form_UserCp_Edit();
		$this->view->form = $form;
		if(!Zend_Auth::getInstance()->getIdentity()->passwordPassed)
        {
            $this->_redirect($this->view->url(array('language' => $this->language), 'login', true).'?show_password=1&set_redir_back=user_cp');
        }
            
            
		$users = new Model_Users();
		$userId = Zend_Auth::getInstance()->getIdentity()->id;
		$user = $users->getUser($userId);
		
		if ($this->_request->getParam('reason', '') == 'reservation') {
			$this->view->showReservationInstruction = true;
			$req = array('address', 'zip_code', 'city', 'country');
			foreach ($req as $name) {
				$form->{$name}->setRequired(true);
			}
		}
		
		$form->populate($user);
		
		/*some logic check; if sensitive data already supplied, disallow changing:
		if (!empty($user['company_name'])) {
			$form->removeElement('company_name');
		}
		if (!empty($user['nip_or_pesel'])) {
			$form->removeElement('nip_or_pesel');
		}
		if (!empty($user['regon'])) {
			$form->removeElement('regon');
		}
		if (empty($user['id_document'])) {
			$form->id_document->setRequired(false);
		}
		//end of logic check*/
		
		if ($this->_request->isPost()) {
			if ($form->isValid($this->_request->getPost())) {
				$data = $form->getValues();
				unset($data['csrf']);
				
				$users = new Model_Users();
				$users->edit($userId, $data);
				
				$this->view->messenger->addMessage($this->_helper->translate('CHANGES_SAVED'));
				$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => $user['email']), Zend_Log::INFO);
				$this->_helper->redirectBack('user_cp_edit', $this->view->url(array('language' => $this->_request->getParam('language'), 'controller' => 'user-cp'), 'general', true));
			}//isValid
			else {
			}
		}//isPost
	}
	
	public function indexAction() {
		$this->_redirect($this->view->url(array('language' => $this->view->language), 'favouriteList', true));
		$this->_helper->setRedirBack('user_cp_edit');
	}
	
}