<html>
	<head>
		
	</head>
	<body>
		<div>
			<b>Auto do wyceny</b>
		</div>
		
		<hr />		
		
		<div>
			<b><PERSON><PERSON>, model</b>: <?= $this->escape($this->data['make'] . ' ' . $this->data['model']) ?><br />
			<b>Rok produkcji</b>: <?= $this->escape($this->data['build_year']) ?><br />
			<b>Silnik</b>: <?= $this->escape($this->data['engine']) ?><br />
			<b><PERSON>lor</b>: <?= $this->escape($this->data['color']) ?><br />
			<b>Przebieg</b>: <?= $this->escape($this->data['odometer']) ?><br />
			<b>Pochodzenie</b>: <?= $this->escape($this->data['origin']) ?><br />
			<b>Bezwypadkowy</b>: <?= $this->data['no_accident'] == "1" ? "tak" : "NIE" ?><br />
			<b>Naprawiane elementy</b>: <?= $this->escape($this->data['repaired_elements']) ?><br />
			<?php if (array_key_exists('exchange_for', $this->data)): ?>
				<b>Zamiana na</b>: <?= $this->escape($this->data['exchange_for']) ?>
			<?php endif ?>
		</div>
		
		<hr />
		
		<div>
			<b>Dane osoby zgłaszającej</b>
			<br />
			<b>Godność</b>: <?= $this->escape($this->data['name']) ?><br />
			<b>Email</b>: <?= $this->escape($this->data['email']) ?><br />
			<b>Telefon</b>: <?= $this->escape($this->data['phone']) ?><br />
		</div>
	</body>
</html>