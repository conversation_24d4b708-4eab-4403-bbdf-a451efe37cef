<?php

class Form_UserCp_Edit extends My_Form {

	public function init() {
		$this->addElements(array(
			new Zend_Form_Element_Text('first_name', array(
				'label'	=>	'FIRST_NAME',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('last_name', array(
				'label'	=>	'LAST_NAME',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('company_name', array(
				'label'	=>	'COMPANY_NAME',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 255, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('nip_or_pesel', array(
				'label'	=>	'NIP_OR_PESEL',
				'filters' => array(new Zend_Filter_Digits()),
				'validators' => array(
					new Zend_Validate_StringLength(array('min' => 10, 'max' => 11, 'encoding' => 'UTF-8')),
					new My_Validate_NIPorPESEL()
				)
			)),
			new Zend_Form_Element_Text('regon', array(
				'label'	=>	'REGON',
				'filters' => array(new Zend_Filter_Digits()),
				'validators' => array(new My_Validate_REGON())
			)),
			new Zend_Form_Element_Text('id_document', array(
				'label'	=>	'ID_DOCUMENT',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 32, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('id_type', array(
				'label'	=>	'ID_TYPE',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('address', array(
				'label'	=>	'ADDRESS',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 255, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('zip_code', array(
				'label'	=>	'ZIP_CODE',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 32, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('city', array(
				'label'	=>	'CITY',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('country', array(
				'label'	=>	'COUNTRY',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 64, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('phone', array(
				'label'	=>	'PHONE',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label'	=>	'SAVE_CHANGES'
			)),
		));
		parent::init();
	}//init

}