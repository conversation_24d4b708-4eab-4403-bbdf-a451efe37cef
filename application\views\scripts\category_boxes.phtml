<?php $i = 0; foreach ($this->catsWithCounts as $name => $str): ?>
	<?php if ($this->linkToAddParam): ?>
		<span class="make_box">
			<span class="for_icon">&nbsp;</span><a href="<?= $this->view->url(array('language' => $this->view->language), 'list', 'true') . '?hash=' . $this->view->hash . '&amp;addName[]=categories&amp;removeOther_0[]=categories&amp;addValue_0=' . $this->escape($name) ?>"><?= str_replace(" ", "&nbsp;", $this->escape($str)) ?></a>
		</span>
	<?php else: ?>
		<span class="make_box">
			<form action="<?= $this->view->url(array('language' => $this->view->language), 'list', 'true') ?>" method="post">
				<?php foreach(explode(',',$name) as $key): ?>
					<input type="hidden" name="categories[]" value="<?= $this->escape($key) ?>">
				<?php endforeach; ?>
				<?php if ( false && isset($this->types) && is_array($this->types)): ?>
					<div class="form_item hidden">
						<select multiple="multiple" name="types[]">
							<?php foreach ($this->types as $type): ?>
								<option selected="selected" value="<?php echo $this->escape($type) ?>"></option>
							<?php endforeach ?>
						</select>
					</div>
				<?php endif ?>
				<span class="for_icon">&nbsp;</span><input class="submit" type="submit" name="submit" style="<?= $i == 0 ? 'text-transform: uppercase; font-size: 15px; ' : '' ?>font-weight: bold; color:#000;" value="<?= $this->escape($str) ?>" />
			</form>
		</span>
	<?php endif ?>
<?php $i++; endforeach ?>
