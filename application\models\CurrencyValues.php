<?php

class Model_CurrencyValues extends Model_Base {
	
	public function getCurrentCurrencyValue($currency) {
		$select = $this->db->select()
			->from($this->tables['currency_values'], array('value'))
			->where('currency = ?', $currency)
			->order('id DESC');
		return $this->db->fetchOne($select);
	}
	
	public function importCurrencyList()
	{
		$nbp = new My_NBP();
		
		$date = date('Y-m-d');
		
		foreach($nbp->getCurrencyArray() as $key => $value )
		{
			$this->db->insert($this->tables['currency_values'],
			array(
				'date' => $date,
				'currency' => $key,
				'value' => $value
				)
			);
		}
	}
	
	
}