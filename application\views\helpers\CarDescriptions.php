<?php

class Zend_View_Helper_CarDescriptions extends Zend_View_Helper_Abstract {
	
	public function carDescriptions($data) {
		if (!is_array($data) || count($data) < 1) {
			return "";
		}
		
		$ret = "";
		$i = 0;
		foreach ($data as $item) {
			$val = $item['value'];
			if ($i != 0) {
				$ret .= ", ";
			}
			else {
				$val = ucfirst($val);
			}
			$ret .= $val;
			$i = 1;
		}
		$ret .= ".";
		return $ret;
	}
	
}