<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
	"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
	<head>
		<style type="text/css">
			<?= $this->css ?>
			
			.car_search_item {width: 640px;}
		</style>
	</head>
	<body>		
		<div id="search_results">
			
			<h1 class="title">
                <?php if(isset($this->newsletterGreeting)): ?>
                    <?= nl2br($this->newsletterGreeting) ?>
                <?php else :?>
                    <?= $this->translate->_('NEWSLETTER_GREETING') ?><br />

                    <?php if($this->singleCar && $this->language == 'pl' && $this->employee && $this->employee['phone']): ?>
                        mam nastepną ofertę! Proszę o tel. na nr: <?= $this->employee['phone'] ?> może znajdziemy tym razem coś, co Państwa zainteresuje.
                    <?php else :?>
                        <?= $this->translate->_('NEWSLETTER_HEADING') ?>: <?= $this->escape($this->user['search_name']) ?>
                    <?php endif ?>
                <?php endif ?>
			</h1>
			<br/>
			<a href="https://www.facebook.com/pages/autoautopl/238941756145961?sk=wall"> <img src="<?= $this->domain ?>/images/like_it_pl.png" /></a>
			<br/>
			<br/>
			<?php $index = 1; foreach($this->user['cars'] as $car): ?>
				<?= $this->partial('car_search_item.phtml', array('car' => $car, 'translate' => $this->translate, 'language' => $this->language, 'language_row' => $this->language_row, 'page' => $this->page, 'domain' => $this->domain, 'hideCaretaker' => true, 'employeeMail' => ($this->employee ? $this->employee['email']: false), 'newsletter' => true )) ?>
			<?php $index++; endforeach; ?>
			
			<br /><br />
			<h1>
				<?php if ($this->employee): ?>
					<?= $this->translate->_('NEWSLETTER_GOODBYE_FROM_SALESMAN') ?>
					<br />
					<?= $this->escape($this->employee['first_name'] . ' ' . $this->employee['last_name']) ?><br />
					<?php if (!empty($this->employee['phone'])): ?>
						tel: <?= $this->escape($this->employee['phone']) ?><br />
					<?php endif ?>
					<?php if (!empty($this->employee['email'])): ?>
						email: <a href="mailto:<?= $this->escape($this->employee['email']) ?>"><?= $this->escape($this->employee['email']) ?></a>
					<?php endif ?>
				<?php else: ?>
					<?= $this->translate->_('NEWSLETTER_GOODBYE') ?>
					<br />
					AutoAuto.pl
				<?php endif ?>
			</h1>
		</div>
		
		<?php if ($this->user['hash']): ?>
			<br /><br /><br />
			<a href="<?= $this->domain ?><?= $this->url(array('language' => $this->language, 'hash' => $this->user['hash']), 'deleteSearchByHashInit', true) ?>"><?= $this->translate->_('CLICK_TO_DELETE_NEWSLETTER_PROFILE') ?></a>
		<?php endif ?>
		
	</body>
</html>