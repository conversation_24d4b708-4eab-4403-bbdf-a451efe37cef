<div id="left_search" class="column_left_1">
	<?= $this->render('left_search.phtml') ?>
</div>

<div id="content_static" class="column_right_1">
    <div id="favourites-header">
        <h1 id="static_title" class="favourites"><?= $this->translate->_('FAVOURITE_CARS') ?></h1>
        <div class="actions favourites">

            <?php if ($this->showActions): ?>
            <div class="item">
                <div class="link">
                    <a class="save"  href="<?= $this->favouritesSavePermanentlyForm->getAction() ?>"  title=""></a>
                </div>
                <div class="desc">
                    <?= $this->translate->_('FAVOURITES_SAVE_PERMANENTLY') ?>
                </div>
            </div>


            <div class="item">
                <div class="link">
                    <a class="fmail"  href="#nl_form" rel="prettyPhoto" title=""></a>
                </div>
                <div class="desc">
                    <?= $this->translate->_('SEND_OFFER') ?>
                </div>
            </div>
            <div class="item">
                <div class="link">
                    <a class="caretaker" href="#sm_form" rel="prettyPhoto" ></a>
                </div>
                <div class="desc">
                    <?= $this->translate->_('SEND_TO_OUR_CONSULTANT') ?>
                </div>
            </div>
            <div class="item">
                <div class="link">
                    <a class="print" href="<?= $this->url() ?>?print=true" target="_new"></a>
                </div>
                <div class="desc">
                    <?= $this->translate->_('PRINT_OFFER') ?>
                </div>
            </div>
            <div class="item">
                <div class="link">
                    <a class="favourite-del" href="<?= $this->url() ?>?del-all=1""></a>
                </div>
                <div class="desc">
                    <?= $this->translate->_('REMOVE_FROM_FAVOURITES') ?>

                </div>
            </div>
            <?php endif ?>

        </div>

        <div class="clear"></div>
    </div>
	
	<div id="content_static_container" class="<?= $this->containerClass ?>">
		<?php if ($this->paginator): ?>
			

			
			<?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''))); ?>
			
			<?php $index = 1; foreach($this->paginator as $car): ?>
				<?= $this->partial('car_search_item.phtml', array('car' => $car, 'translate' => $this->translate, 'language' => $this->language, 'language_row' => $this->language_row, 'hash' => ($this->hash ? '?hash=' . $this->hash . '&i=' . (($this->page - 1) * $this->paginator->getItemCountPerPage() + $index) : ''), 'page' => $this->page, 'isFavourite' => $car['car_id'])) ?>
			<?php $index++; endforeach; ?>
			
			<?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''))); ?>
		<?php endif; ?>
	</div>
	<div class="clear"></div>
</div>


<div class="clear"></div>

<div id="nl_form">
	<div class="pp_html">
		<br />
		<h2><?= $this->translate->_('SEND_FAVOURITES_BY_EMAIL') ?></h2>
		<?= $this->nlForm ?>
	</div>
</div>
<script type="text/javascript">
	$(function(){
		$("#nl_form").hide();
		<?php if ($this->showNlForm): ?>
			$("a[href='#nl_form']").click();
		<?php endif ?>
	});
</script>

<div id="sm_form">
    <div class="pp_html">
        <br />
        <h2><?= $this->translate->_('SEND_TO_OUR_CONSULTANT') ?></h2>
        <?= $this->smForm ?>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $("#sm_form").hide();
        <?php if ($this->showSmForm): ?>
        $("a[href='#sm_form']").click();
        <?php endif ?>
    });
</script>