<?php

class Error<PERSON>ontroller extends Zend_Controller_Action
{

    public function errorAction()
    {
    	$this->_helper->layout->setLayout('error');
    	
        $errors = $this->_getParam('error_handler');
		
        switch ($errors->type) { 
        	case Zend_Controller_Plugin_ErrorHandler::EXCEPTION_NO_ROUTE:
            case Zend_Controller_Plugin_ErrorHandler::EXCEPTION_NO_CONTROLLER:
            case Zend_Controller_Plugin_ErrorHandler::EXCEPTION_NO_ACTION:
        
                // 404 error -- controller or action not found
                $this->view->message = 'Page not found';
                return;
                break;
            default:
                break;
        }
        
        $this->view->exception = $errors->exception;
        $this->view->request   = $errors->request;
        
        if (in_array(APPLICATION_ENV, array('development'))) {
        	echo "<h2>Error:</h2>" . $errors->exception->getMessage();
        	echo "<h2>Stack trace:</h2>" . nl2br($errors->exception->getTraceAsString());
        }
        else {
            $this->getResponse()->setHttpResponseCode(500);
            $message = '<h2>Wystą<PERSON>ł błąd aplikacji</h2><br />';
            
        	if (Zend_Registry::isRegistered('errorLogger')) {
        		$userId = "";
        		try {
        			$userId = Zend_Auth::getInstance()->getIdentity()->id;
        		}
        		catch (Exception $e) {
        			$userId = "nieznany";
        		}
        		
        		ob_start();
        		$logger = Zend_Registry::get('errorLogger');
        		$logger->log(
        			PHP_EOL . $errors->exception->getMessage() . PHP_EOL . '==============================' . PHP_EOL . $errors->exception->getTraceAsString() . PHP_EOL . "Referer: " . $_SERVER['HTTP_REFERER'] . PHP_EOL . "Request URI: " . $_SERVER['REQUEST_URI'] . PHP_EOL . "Request method: " . $_SERVER['REQUEST_METHOD'] . PHP_EOL . "User agent:" . $_SERVER['HTTP_USER_AGENT'] . PHP_EOL . "isAjax:" . ((isset($_SERVER['HTTP_X_REQUESTED_WITH']) && ($_SERVER[' HTTP_X_REQUESTED_WITH'] == 'XMLHttpRequest')) ? "yes" : "no") . PHP_EOL . "UserId: " . $userId . PHP_EOL . PHP_EOL,
	        		Zend_Log::ERR
        		);
        		ob_end_clean();
        		$message .= "Wystąpił błąd aplikacji i został zapisany do dziennika błędów. Administrator został powiadomiony";
        	}
        	else {
        		$message .= "Wystąpił błąd aplikacji, dodatkowo wyłączony jest dziennik błędów. Powiadom administratora";
        	}
        	$this->view->message = $message;
        }
    }


}

