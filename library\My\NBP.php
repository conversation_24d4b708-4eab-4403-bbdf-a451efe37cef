<?php
/**
 * Pobierane kursów walut ze strony NBP
 * <AUTHOR> <czahor[at]gmail[dot]com>
 */


class My_NBP
{
	
	protected  $todayXMLFile = null;
	
	protected $currency = array();
	
	
	
	function __construct()
	{
		$this->getTodayXML();
		$this->createCurrencyArray();
	}
	

	private function createCurrencyArray()
	{
		try{
			$content = file_get_contents($this->todayXMLFile);
			$xml = new SimpleXMLElement($content);
			foreach ($xml->pozycja as $pozycja) {
				
				$this->currency[(string)$pozycja->kod_waluty] = str_replace(',', '.',(string) $pozycja->kurs_sredni);
			}
		}
		catch (Exception $e) {
					
		}
	}
	
	public function setTodayXML() {
		try{

			$content  = file_get_contents('http://nbp.pl/Kursy/KursyA.html');
			$pattern = '/xml\/[\d\w]+\.xml/';
			$result  = preg_match($pattern, $content, $match);
		
			if (!empty($match))
				$this->todayXMLFile = 'http://nbp.pl/Kursy/'.$match[0];
		}
		catch (Exception $e) {
			
		}
	
		return $this;
	}
	
	public function getTodayXML() {
			
		if($this->todayXMLFile === null)
			$this->setTodayXML();
	
		return $this->todayXMLFile;
	}
	
	public function getExchange($currency)
	{
		if(isset($this->currency[$currency]))
			return $this->currency[$currency];
		return false;
	}
	
	public function getCurrencyArray()
	{
		return $this->currency;
	}
	
	
};