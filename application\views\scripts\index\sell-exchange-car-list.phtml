

<div id="search_results" class="">




    <table id="results_top_paginator" cellspacing="0" cellpadding="0">
        <tr>


            <td class="middle">
                <?php if ($this->paginator && $this->paginator->getTotalItemCount() > $this->paginator->getItemCountPerPage()): ?>
                    <?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''), 'carListShowMax' => $this->pagingMaxResults, 'carListShowDefault' => $this->pagingDefaultResults, 'translate' => $this->translate)); ?>
                <?php else: ?>
                    <div class="paginator"><div class="paginator_inner"></div></div>
                <?php endif; ?>

            </td>


    </table>

    <?php if ($this->paginator): ?>
        <?php foreach($this->paginator as $car):


            ?>



            <div class="car_search_item car_search_item_sell_exchange">
            <div class="location">

                <?= $car['make_name'] . ' '.  $car['model_name'] . ' '.  $car['build_year']?>
	    </span>



            </div>
            <a name="car_id_<?= $this->car['car_id'] ?>"></a>
            <div class="photo">

                <div class="img">
                    <?php if (!empty($car['photo'])):?>

                        <a href="#">

                            <?php

                            $pathinfo = pathinfo($car['photo']);
                            $filename =$pathinfo['filename'];
                            $extension = $pathinfo['extension'];

                            ?>

                            <img src="<?= $this->domain ?>/images/user-sell-exchange/<?= $this->escape($filename) ?>_M.<?= $this->escape($extension) ?>" alt="">
                        </a>



                            <a href="#">

                                <div></div>
                            </a>


                            <?php endif ?>
                </div>

            </div>

            <div class="description">
                <div class="title">

                        <a href="#">



                            <?= $this->escape($car['make_name']) . " " . $this->escape($car['model_name']) . " " . ($this->car['is_new_car'] ? date('Y') : $car['build_year']) . ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year'] : "") . ($this->language == "pl" ? " " . $this->escape($this->car['title']) : "")?>

                        </a>
                </div>



                       <table>

                           <tr>
                               <td><?= $this->translate->_('MAKE') ?>, <?= $this->translate->_('MODEL') ?>:</td><td><?= $this->escape($car['make_name']) . " " . $this->escape($car['model_name']) ?></td>
                           </tr>
                           <tr>

                               <td><?= $this->translate->_('BUILD') ?>:</td><td><?= $this->escape($car['build_year']) ?></td>


                           </tr>
                           <tr>
                               <td><?= $this->translate->_('ENGINE') ?>:</td><td><?= $this->escape($car['engine']) ?></td>

                           </tr>
                           <tr>
                               <td><?= $this->translate->_('COLOR') ?>:</td><td><?= $this->escape($car['color']) ?></td>

                           </tr>
                           <tr>
                               <td><?= $this->translate->_('ODOMETER') ?>:</td><td><?= $this->escape($car['odometer']) ?></td>
                           </tr>
                           <tr>
                               <td><?= $this->translate->_('SELL_CAR_ORIGIN') ?>:</td><td><?= $this->escape($car['origin']) ?></td>
                           </tr>
                           <tr>
                               <td><?= $this->translate->_('SELL_CAR_NO_ACCIDENT') ?>:</td><td> <?= $car['no_accident'] == 'y' ? $this->translate->_('YES') : $this->translate->_('NO') ?></td>
                           </tr>


                           <?php if($car['is_for_exchange']): ?>
                               <tr>
                                   <td><?= $this->translate->_('SELL_CAR_EXCHANGE_FOR') ?></td><td><?= $this->escape($car['exchange_makes']) ?></td>
                               </tr>
                           <?php endif ?>
                           <tr>
                               <td><?= $this->translate->_('COMMENTS') ?>:</td><td><?= $this->escape($car['comments']) ?></td>
                           </tr>

                       </table>

            </div>
            <div class="description">
                <div class="title"><?= $this->translate->_('SELL_CAR_DESCRIPTION') ?></div>
                <?= $this->escape($car['comments']) ?>
            </div>
            <div class="misc">
                <div class="price" style="line-height: normal;">

                </div>

                    <div class="caretaker">
                        <div class="phone"><?= $this->escape($car['caretaker_phone']) ?></div>
                        <div class="name"><?= $this->escape(date('Y-m-d',strtotime($car['added_datetime']))) ?></div>

                        <div class="name"><?= $this->escape($car['caretaker_first_name'] . " " . $car['caretaker_last_name']) ?></div>
                    </div>



            </div>
            </div>


        <?php endforeach; ?>


        <?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''), 'carListShowMax' => $this->pagingMaxResults, 'carListShowDefault' => $this->pagingDefaultResults, 'translate' => $this->translate)); ?>
    <?php endif ?>

</div>

<div class="clear"></div>
