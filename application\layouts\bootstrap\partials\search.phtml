<div class="search-container">
    <div class="container hidden-lg-up">
        <div class="row">
            <div class="col-md-12 py-4 text-center">
                <button class="btn btn-mobile-filter" data-toggle="collapse" data-target="#search-form"><i
                        class="fa fa-filter" aria-hidden="true"></i>
                    <span><?= $this->translate->_('FILTER') ?></span></button>
            </div>

        </div>
    </div>

    <form id="search-form" class="collapse" method="post" action="<?= $this->topSearchForm->getAction() ?>">
        <div class="container">

            <div class="form-aa  pt-md-0 pt-lg-4  pb-3 row" method="post" action="<?= $this->topSearchForm->getAction() ?>">

                <div class="col-md-12 col-lg-11">

                    <div class="form-group row">
                        <div class="col-sm-12 col-md-6 col-lg-3">
                            <?php echo $this->topSearchForm->make->renderViewHelper() ?>
                        </div>
                        <div class="col-sm-12 col-md-6  col-lg-3">
                            <?php echo $this->topSearchForm->model->renderViewHelper() ?>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-3 d-flex">

                            <div class="col-6 p-0"><?php echo $this->topSearchForm->price_min->renderViewHelper() ?></div>
                            <div class="col-6 p-0"><?php echo $this->topSearchForm->price_max->renderViewHelper() ?></div>

                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-3 d-flex">
                            <div class="col-6 p-0"> <?php echo $this->topSearchForm->build_from->renderViewHelper() ?></div>
                            <div class="col-6 p-0">  <?php echo $this->topSearchForm->build_to->renderViewHelper() ?></div>

                        </div>


                        <div class="col-sm-12 col-md-6 col-lg-3">
                            <?php echo $this->topSearchForm->odometer_to->renderViewHelper() ?>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-3">
                            <?php echo $this->topSearchForm->engine->renderViewHelper() ?>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-3">
                            <?php echo $this->topSearchForm->origin_country->renderViewHelper() ?>
                        </div>
                        <div class="col-sm-12 col-md-6 col-lg-3">
                            <?php echo $this->topSearchForm->important_features->renderViewHelper() ?>
                        </div>

                        <div class="row m-0 advanced-search">
                            <div class="col-sm-12 col-md-6 col-lg-3">
                                <?php echo $this->topSearchForm->category->renderViewHelper() ?>
                            </div>
                            <div class="col-sm-12 col-md-6 col-lg-3">
                                <?php echo $this->topSearchForm->gearboxes->renderViewHelper() ?>
                            </div>
                            <div class="col-sm-12 col-md-6 col-lg-3">
                                <?php echo $this->topSearchForm->drives->renderViewHelper() ?>
                            </div>
                            <div class="col-sm-12 col-md-6 col-lg-3 d-flex">
                                <div class="col-6 p-0"> <?php echo $this->topSearchForm->cubic_capacity_from->renderViewHelper() ?></div>
                                <div class="col-6 p-0">  <?php echo $this->topSearchForm->cubic_capacity_to->renderViewHelper() ?></div>

                            </div>
                            <div class="col-sm-12 col-md-6 col-lg-3">
                                <?php echo $this->topSearchForm->financing->renderViewHelper() ?>
                            </div>
                            <div class="col-sm-12 col-md-6 col-lg-9">
                                <?php echo $this->topSearchForm->query->renderViewHelper() ?>
                            </div>
                        </div>


                    </div>


                </div>
                <div class="col-lg-1 align-self-lg-end hidden-md-down submit-container">

                    <button name="submit" class="btn btn-search" id="submit" type="submit">
                        <i class="fa fa-search" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="col-md-12 col-lg-11">

                    <div class="row">
                        <div class="col-sm-12 text-right">
                            <button type="button" class="btn btn-advanced-search">Więcej opcji <span
                                    class="orange-color">wyszukiwania <i class="fa fa-caret-up"
                                                                         aria-hidden="true"></i></span></button>
                        </div>
                    </div>

                </div>

            </div>
            <div class="search-container-button hidden-lg-up py-3">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <button name="submit" class="btn btn-search" id="submit2" type="submit">
                                <i class="fa fa-search" aria-hidden="true"></i> <span
                                ><?= $this->translate->_('FILTER') ?></span>
                            </button>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <?php echo $this->topSearchForm->order->renderViewHelper() ?>
        <?php echo $this->topSearchForm->list_view->renderViewHelper() ?>
    </form>
</div>