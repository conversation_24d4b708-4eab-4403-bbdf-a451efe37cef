<div id="contact">

    <div class="row py-4">
        <div class="col-lg-12">

            <h1 class="title"><?=  $this->translate->_('CONTACT') ?></h1>

        </div>

    </div>

    <div class="row">
        <div class="col-lg-3">
            <ul class="left-menu list-unstyled">
                <li><a href="<?= $this->url(array('language' => $this->language), 'list', true) ?>"><?= $this->translate->_('CAR') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language), 'financing', true) ?>"><?= $this->translate->_('FINANCING') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language), 'aboutUs', true) ?>"><?= $this->translate->_('COMPANY') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language), 'aboutUsWork', true) ?>"><?= $this->translate->_('ABOUT_US_WORK') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language), 'racingTeam', true) ?>"><?= $this->translate->_('RACING_TEAM') ?></a></li>
                <li><a<?= $this->leftMenu == 'contact' ? ' class="active"' :'' ?> href="<?= $this->url(array('language' => $this->language), 'locations', true) ?>"><?= $this->translate->_('CONTACT') ?></a></li>
            </ul>
        </div>
        <div class="col-lg-9">
            <script src="https://maps.googleapis.com/maps/api/js?v=3&libraries=places"></script>
            <script>
                var geocoder;
                var googleMap;
                var marker;

                function initializeMap() {
                    setTimeout(function() {
                        geocoder = new google.maps.Geocoder();
                        var myLatlng = new google.maps.LatLng(52.183400, 20.952342);
                        var pointerAlKrakowska = new google.maps.LatLng(52.183400, 20.952342);

                        var mapOptions = {
                            zoom: 11,
                            scrollwheel: false,
                            clickable: false,
                            disableDefaultUI: true,
                            center: myLatlng,
                            draggable: false,
                            styles: [
                                {
                                    featureType: 'water',
                                    stylers: [
                                        { color: '#b5d2ea' },
                                        { visibility: 'on' }
                                    ]
                                },
                                {
                                    featureType: 'landscape',
                                    stylers: [ { color:'#f2f2f2' } ]
                                },
                                {
                                    featureType: 'road',
                                    stylers: [
                                        { saturation: -100 },
                                        { lightness: 45 }
                                    ]
                                },
                                {
                                    featureType: 'road.highway',
                                    stylers: [ { visibility:'on' } ]
                                },
                                {
                                    featureType: 'road.arterial',
                                    elementType: 'labels.icon',
                                    stylers:[ { visibility:'on' } ]
                                },
                                {
                                    featureType: 'administrative',
                                    elementType: 'labels.text.fill',
                                    stylers: [ { color: '#444444' } ]
                                },
                                {
                                    featureType: 'transit',
                                    stylers:[ { visibility:'on' } ]
                                },{
                                    featureType: 'poi',
                                    stylers: [
                                        { visibility: 'on' },
                                        { color: '#dcecd6' }
                                    ]
                                }
                            ]
                        };

                        googleMap = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);


                        marker = new google.maps.Marker({
                            map: googleMap,
                            draggable:false,
                            animation: google.maps.Animation.DROP,
                            position: pointerAlKrakowska,
                            icon: '/images/_new/marker-blue.png',
                            url: 'http://maps.google.com/maps?q=Al.+Krakowska+178,+Warszawa&amp;hl=<?= $this->language ?>&amp;ie=UTF8&amp;ll=52.1833,20.952344&amp;spn=0.009736,0.027466&amp;sll=52.232491,21.11392&amp;sspn=0.009725,0.027466&amp;z=16&amp;output=embed&amp;iframe=true&amp;width=900&amp;height=550'
                        });

                        google.maps.event.addListener(marker, 'click', function() {
                            window.open(marker.url);
                        });


                    }, 100);

                }

                google.maps.event.addDomListener(window, 'load', initializeMap);

            </script>

            <div  id="map-canvas"></div>

            <div class="row pt-4">
                <div class="col-lg-5">
                    <h2><?= $this->translate->_('CONTACT') ?></h2>


                    <div>
                        <?php echo nl2br($this->escape($this->translate->_('CONTACT_OPENING_HOURS'))) ?>
                    </div>


                </div>
                <div class="col-lg-7">
                    <h2><?= $this->translate->_('LOCATIONS') ?></h2>

                    <div class="locations">


                        <?php if (false && $this->locationGroupsHtml): ?>
                            <?= $this->locationGroupsContent ?>
                        <?php else: ?>
                            <?php ob_start(); ?>
                            <?php foreach ($this->locationGroups as $groupData): ?>
                                <?php
                                foreach ($groupData['locations'] as $location):
                                    $aHref = $this->url(array('language' => $this->language, 'id' => $location['location_id'], 'description' => My_FilterPermalink::filter($groupData['address'] . ' ' . $this->translate->_('LOCATION_POINT') . ' ' . $location['name'])), 'locationDetails', true);
                                    $dataEmpty = true;

                                    ?>
                                    <div class="location location-<?= $groupData['id'] ?> d-flex">
                                        <div class="photo"><a class="d-flex justify-content-center align-items-center" href="<?= $aHref ?>">
                                                <?php if(file_exists(APPLICATION_PATH.'/../public/images/locations/'.$location['location_id'].'_S.jpg')): ?>
                                                    <img class="img-fluid " src="/images/locations/<?= $location['location_id'] ?>_S.jpg" />
                                                <?php else: ?>
                                                    <i class="fa fa-flag-o" aria-hidden="true"></i>
                                                <?php endif; ?>
                                            </a></div>
                                        <div class="location-data d-flex flex-column">
                                            <div>
                                                <p class="lower"><a href="<?= $aHref ?>"><?= $groupData['address'] . " " . $this->translate->_("LOCATION_POINT") ?> <span><?= $location['name'] ?></span></a></p>
                                                <p>  <a href="<?= $aHref ?>"><?= $groupData['zip_code'] . " " . $groupData['city'] ?></a></p>
                                            </div>
                                            <div class="details mt-auto">

                                                <?php if (!empty($location['phone'])): ?>
                                                    <? if (!$dataEmpty) ?><p><i class="fa fa-phone" aria-hidden="true"></i> <?= $location['phone'] ?></p>
                                                    <?php $dataEmpty = false; endif ?>

                                                <?php if (!empty($location['email'])): ?>
                                                    <? if (!$dataEmpty) ?><p><a href="mailto:<?= $location['email'] ?>"><i class="fa fa-envelope" aria-hidden="true"></i> <?= $location['email'] ?></a></p>
                                                    <?php $dataEmpty = false; endif ?>
                                            </div>
                                        </div>
                                        <div class="clear"></div>
                                    </div>
                                <?php endforeach ?>
                            <?php endforeach ?>
                            <?php
                            $html = ob_get_flush();
                            $this->cache->save($html, 'location_groups_with_locations_content' . $this->language, $tags=array('location', $this->language, 'translate'));
                            ?>
                        <?php endif ?>
                    </div>
                </div>
            </div>

        </div>
    </div>

</div>

