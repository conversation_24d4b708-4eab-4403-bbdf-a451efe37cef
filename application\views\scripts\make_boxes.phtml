<?php foreach ($this->makesWithCounts as $data): ?>
	<?php if ($this->linkToAddParam): ?>
		<span class="make-box add_param<?php if ($data['special_flag']): ?> <?php echo $this->escape($data['special_flag'])?><?php endif ?>">
			<i class="arrow fa fa-caret-right orange-color" aria-hidden="true"></i><a href="<?= $this->view->url(array('language' => $this->view->language, 'type' => $data['type_slug'], 'make' => $data['make_slug']), 'list', 'true').(!empty($this->queryStringArray) ? '?'.http_build_query($this->queryStringArray) : '')  ?>"><?= str_replace(" ", "&nbsp;", $this->escape($data['display_name'])) ?></a>
		</span>
	<?php else: ?>
		<span class="make-box<?php if ($data['special_flag']): ?> <?php echo $this->escape($data['special_flag'])?><?php endif ?>">

				<i class="arrow fa fa-caret-right orange-color" aria-hidden="true"></i><a  href="<?= $this->view->url(array('language' => $this->view->language, 'type' => $data['type_slug'], 'make' => $data['make_slug']), 'list', 'true').(!empty($this->queryStringArray) ? '?'.http_build_query($this->queryStringArray) : '')  ?>" style="<?= isset($data['display_name_style']) ? $data['display_name_style'] : '' ?>" class="submit"><?= $this->escape($data['display_name']) ?></a>
		</span>
	<?php endif ?>
<?php endforeach ?>
