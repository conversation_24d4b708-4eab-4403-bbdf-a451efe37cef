<html>
	<head>
	  <meta http-equiv="content-type" content="text/html; charset=utf-8;" />
	  <title><?= $this->escape($this->car['make_name'] . " " . $this->car['model_name'] . " " . $this->car['build_year'] . " " . $this->car['title']) ?></title>
	</head>
	<body style="padding: 5px; font-family: \'trebuchet MS\', Tahoma">
		<p style="text-align: left; font-size: 11px; font-family: \'trebuchet MS\', Tahoma"><?= $this->escape($this->data['message']) ?></p>
		<hr />
	  
		<table border="1" width="600" cellspacing="0" cellpadding="5">
			<tr>
				<td colspan="2" valign="top" align="center">
				<span style="color: #e43202; font-size: 16px;"><b><?= $this->escape($this->car['make_name'] . " " . $this->car['model_name'] . " " . $this->car['build_year'] . " " . ($this->car['auction_price'] > 0  && $this->data['auction'] ? ('- cena Klienta plus '. number_format(500, 0, '', ' '). ' PLN') : $this->car['title'])) ?></b> <?= $this->engineData($this->car) ?></span>
				</td>
			</tr>
			<tr>
				<td width="300" valign="middle" align="center">
					<?php foreach ($this->car['photos'] as $photo): ?>
						<a href="<?= isset($this->data['employee']) ? 'mailto:'.$this->data['employee']['email'] : $this->domain . $this->carLink($this->car, null, $noMarkup=true).($this->data['auction'] ? '?auction=1' : '')?>" target="_blank">
							<img src="<?= $this->domain ?>/images/cars/<?= $this->car['car_id'] ?>/<?= $photo['filename_base'] . "_M." . $photo['filename_extension'] ?>" alt="">
						</a>
					<?php break; endforeach ?>
				</td>
				<td width="300" valign="top">
				<?= $this->translate->_('PRICE') ?>: <b>
                        <?php if($this->car['auction_price'] > 0  && $this->data['auction']): ?>
                            <?= $this->translate->_('CURRENT_PRICE') ?> <br />
                            <?= $this->carPrice($this->car, $this->language_row,false,false,'brutto',$this->car['auction_price']) ?>
                        <?php else :?>
                            <?= $this->escape($this->carPrice($this->car, $this->language_row)) ?>

                        <?php endif ?>

                    </b><br /><br />
				<?= $this->translate->_('CAR_ENGINE') ?>: <b><?= $this->engineData($this->car) ?></b><br /><br />
				<?= $this->translate->_('ODOMETER') ?>: <b><?= $this->mileage($this->car['odometer']) ?> km</b><br /><br />
				<?= $this->translate->_('COLOR') ?>: <b><?= $this->carColour($this->car) ?></b><br /><br />
				<?= $this->translate->_('CAR_BUILD_YEAR') ?>: <b><?= $this->car['build_year'] ?></b><br /><br />
				<? if (!isset($this->data['employee']) && $this->car['caretaker_visible']) : ?>
					<?= $this->translate->_('CARETAKER') ?>: <b><?= $this->escape($this->car['caretaker_first_name'] . " " . $this->car['caretaker_last_name']) ?><br /><?= $this->escape($this->car['caretaker_phone']) ?></b>
				<? endif; ?>
				
				</td>
			</tr>
			<tr>
				<td colspan="2" valign="top" align="center">
					<?= $this->carDescription($this->car['description'], $stripNotReplace=false) ?>
				</td>
			</tr>
			<tr>
				<td colspan="2" valign="top" align="center">
					<table border="0" width="600" cellspacing="2" cellpadding="2" align="center">
						<tr>
							<?php $i=1; foreach($this->car['photos'] as $photo) : ?>
								<td>
									<a href="<?= isset($this->data['employee']) ? 'mailto:'.$this->data['employee']['email'] : $this->domain . $this->carLink($this->car, null, $noMarkup=true).($this->data['auction'] ? '?auction=1' : '')?>">
										<img src="<?= $this->domain ?>/images/cars/<?= $this->car['car_id'] ?>/<?= $photo['filename_base'] . "_S." . $photo['filename_extension'] ?>" alt="">
									</a>
								</td>
								
								<?php if ($i % 3 == 0): ?>
									</tr><tr>
								<?php endif; $i++; ?>
								
							<?php endforeach; ?>
						</tr>
					</table>
				</td>
			</tr>
		</table>
		<p>
		  <?= $this->translate->_('CLICK_TO_SEE_OFFER') ?>:<br />
		  <a href="<?= isset($this->data['employee']) ? 'mailto:'.$this->data['employee']['email'] : $this->domain . $this->carLink($this->car, null, $noMarkup=true).($this->data['auction'] ? '?auction=1' : '') ?>" title="" target="_blank"><?= $this->domain . $this->carLink($this->car, null, $noMarkup=true).($this->data['auction'] ? '?auction=1' : '') ?></a>

		  </p>
	</body>
</html>