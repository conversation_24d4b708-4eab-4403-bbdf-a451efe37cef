<?php

class Form_Car_TestDrive extends My_Form {

	public function init() {
		$tr = Zend_Registry::get('Zend_Translate');
		
		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
                'attribs' => array('class' => 'form-control'),
                'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8'))
				)
			)),
			new Zend_Form_Element_Textarea('message', array(
				'label'	=>	'OFFER_MESSAGE',
				'filters' => array(new Zend_Filter_StripTags()),
				'attribs' => array('rows' => 3, 'cols' => 40,'class' => 'form-control'),
                'required' => true,
			)),
            new Zend_Form_Element_Text('phone', array(
                'label'	=>	'PHONE',
                'filters' => array(new Zend_Filter_StripTags()),
                'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8'))),
                'attribs' => array('class' => 'form-control'),
                'required' => true,
            )),
            new Zend_Form_Element_Text('date', array(
                'label'	=>	'TEST_DRIVE_DATE',
                'validators' => array(new Zend_Validate_Date('yyyy-MM-dd')),
                'attribs' => array('class' => 'form-control'),
                'value' => date("Y-m-d", time()),
                'required' => true,
            )),
            new Zend_Form_Element_Captcha('captcha', array(
                'label' => 'CAPTCHA',
                'captcha' => new My_Captcha_ReCaptcha(array(
                    'siteKey'  => '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-',
                    'secretKey' => '6Lfx7A0UAAAAAL41t8hFSPCzuh6VQGhcpXttwrJd'
                )),
            )),
			new Zend_Form_Element_Text('name', array(
				'label'	=>	'FIRST_NAME_LAST_NAME',
				'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control'),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),

			new Zend_Form_Element_Submit('submit', array(
				'label'	=>	'TEST_DRIVE_SUBMIT',
                'attribs' => array('class' => 'btn btn-action btn-action-orange'),
			)),
		));
		$this->setAttrib("id", "form_test_drive");
		
		if (Zend_Auth::getInstance()->hasIdentity()) {
			$this->message->setDescription($this->captcha->getDescription());
			$this->removeElement('captcha');
		}
		
		parent::init();
	}//init

}