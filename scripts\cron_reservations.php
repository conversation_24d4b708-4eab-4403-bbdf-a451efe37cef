<?php

require_once 'paths.php';
//paths to library

date_default_timezone_set("Europe/Warsaw");

echo "\n\n\n"; //ZendServer's annoying timezone warning messages above

require_once 'Zend/Config.php';
require_once 'Zend/Config/Ini.php';

$config = new Zend_Config_Ini(
	'../application/configs/application.ini',
	'production',
	$options
);

$timezone = $config->general->timezone;
$wgetPath = $config->cron->wget->path;
$wgetDomain = $config->cron->wget->domain;
$wgetParams = $config->cron->wget->params;
$secretPass = $config->cron->secretPass;

date_default_timezone_set($timezone);

$now = date("Y-m-d", time());
$pass = md5($secretPass . $now);

$execCommand = $wgetPath . DIRECTORY_SEPARATOR . "wget $wgetParams $wgetDomain/cron/expire-reservations/pass/$pass";
echo "$now / $secretPass / $pass\n";
echo $execCommand;
exec($execCommand);
