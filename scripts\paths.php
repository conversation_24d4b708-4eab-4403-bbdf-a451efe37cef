<?php

$path = "C:\Program Files\Zend\ZendServer\share\ZendFramework\library";
$path2 = "C:\Program Files\Zend\Apache2\htdocs\autoauto\library";

date_default_timezone_set("Europe/Warsaw");

set_include_path(get_include_path() . PATH_SEPARATOR . $path . PATH_SEPARATOR . $path2);

// Define path to application directory
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../application'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/../library'),
    get_include_path(),
)));
?>