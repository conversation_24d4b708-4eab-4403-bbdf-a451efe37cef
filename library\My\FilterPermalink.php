<?php

class My_FilterPermalink {
	
	public static function filter($string, $separator="-", $trim=true) {
		$string = iconv("UTF-8", "ASCII//TRANSLIT", $string);
		$string = preg_replace("/[^a-zA-Z0-9,.\/\s]/", "", $string);
		$string = preg_replace("/[,.\/]/", $separator, $string);
		$string = str_replace(" ", $separator, $string);
		$string = preg_replace("/[$separator]+/", $separator, $string);
		
		
		if ($trim) {
			$string = trim($string, " " . $separator);
		}
		
		return $string;
	}
	
}