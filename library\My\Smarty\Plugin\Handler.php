<?php

class My_Smarty_Plugin_Handler extends Smarty_Internal_Plugin_Handler
{
	/**
	* Wywolanie pluginu
	*
	* @param string $name nazwa pluginu
	* @param array $args $args[0] = tablica atrybutów
	* @return bool czy udalo sie wywolac plugin
	*/
	public function __call($name, $args)
	{
		if ($this->loadSmartyPlugin($name, $args[1])) {
			$plugin = $this->smarty->registered_plugins[$name][1];
			
			$pluginClass = get_class($plugin[0]);
	    		if (strncmp($pluginClass, 'My_', 3) || strncmp($pluginClass, 'Zend_', 5)) {
	    			try {
					if (isset($args[0][0]['_method'])) {
						$plugin[1] = $args[0][0]['_method'];
						unset($args[0][0]['_method']);
					}
		    			return call_user_func_array($plugin, $args[0][0]);
		    		} catch (Exception $e) {
	    				return false;
		    		}
			}
		}
		return parent::__call($name, $args);
	}
 
	/**
	* Ladowanie helperów Zend Framework
	*
	* @param string $name nazwa helpera
	* @param array $plugin_type typ pluginu
	* @param Smarty_Internal_Plugin_Handler $smarty_plugin_handler
	* @return bool czy ladowanie przebieglo pomyslnie
	*/
	public static function loadZendPlugin($name, $plugin_type, $smarty_plugin_handler)
	{
		try {
			$smarty_plugin_handler->smarty->registered_plugins[$name] = array(
				'function',
				array(
					$smarty_plugin_handler->smarty->getZendView()->getHelper($name),
					$name
				),
				true
			);
		} catch (Zend_Loader_PluginLoader_Exception $e) {
			// Nie znaleziono helpera
			return false;
		}
		return true;
	}
}