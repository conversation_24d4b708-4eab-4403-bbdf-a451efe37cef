<?php

class Form_FavouritesNewsletter extends My_Form {

    protected $variant = "newsletter";

	public function init() {
        $empl = new Model_Employees();
        $tr = Zend_Registry::get('Zend_Translate');

        if($this->variant == 'salesman') {
            $this->addElements(array(

                new Zend_Form_Element_Select('sr_id', array(
                    'label'	=>	'FAVOURITE_SALESMAN',
                    'multiOptions' => array('' => $tr->_('FAVOURITE_SALESMAN')) + $empl->getAll(array('sr_assoc' => true)),
                    'required' => true
                ))));

        }
        else {
            $this->addElements(array(
                new Zend_Form_Element_Text('email', array(
                    'label'	=>	'EMAIL',
                    'required' => true,
                    'validators' => array(
                        new Zend_Validate_EmailAddress(),
                        new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')),
                    )
                ))));


        }
		$this->addElements(array(


            new Zend_Form_Element_Textarea('newsletter_greeting', array(
                'label'	=>	'OFFER_MESSAGE',
                'required' => true,
                'value' => $tr->_('NEWSLETTER_GREETING') . PHP_EOL . $tr->_('NEWSLETTER_HEADING')

            )),

			new Zend_Form_Element_Captcha('captcha', array(
				'label' => 'CAPTCHA',
				'captcha' => new My_Captcha_Math(array(
					'timeout' => '180',
				)),
			)),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this).$this->variant
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label'	=>	'SEND'
			)),
		));
		
		parent::init();
	}//init

    public function setVariant($value) {

        $this->variant = $value;

    }

}