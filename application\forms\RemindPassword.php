<?php

class Form_RemindPassword extends My_Form {

	public function init() {
		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
				'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8'))
				)
			)),
			new Zend_Form_Element_Captcha('captcha', array(
				'label' => 'CAPTCHA',
				'captcha' => new My_Captcha_Math(array(
					'timeout' => '180',
				)),
			)),
			new Zend_Form_Element_Hidden('action', array(
				'label'	=>	'',
				'value' => 'remind'
			)),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label' => 'RESET_PASSWORD_SUBMIT'
			)),
		));
		
		parent::init(array('customOptions' => array('noAsteriskInLabel' => true)));
	}//init

}