<? define("TEMPORARY_HOST", 'http://' . $_SERVER['HTTP_HOST']) ?>


<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/global.css?nocache=2016-12-07" type="text/css" media="screen" title="no title" charset="utf-8">


<?php if($this->isMobile): ?>
<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/mobile.css?nocache=2014-08-25" type="text/css" media="screen" title="no title" charset="utf-8">
<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/idangerous.swiper.css?nocache=2013-08-08" type="text/css" media="screen" title="no title" charset="utf-8">
<?php endif;?>
<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/jquery-ui-1.8.10.custom.css?nocache=2013-11-28" type="text/css" media="screen" title="no title" charset="utf-8">
<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/css/select2.min.css?nocache=2013-11-28" type="text/css" media="screen" title="no title" charset="utf-8">
<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/prettyphoto/prettyPhoto.css" type="text/css" media="screen" title="no title" charset="utf-8">
<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/qtip2/jquery.qtip.min.css?nocache=1" type="text/css" media="screen" title="no title" charset="utf-8">
<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/jquery.dropdown.css?nocache=2013-08-06" type="text/css" media="screen" title="no title" charset="utf-8">

<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/_new/global.css?nocache=2015-11-24" type="text/css" media="screen" charset="utf-8">
<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/_new/font-awesome.min.css?nocache=2015-10-10" type="text/css" media="screen" charset="utf-8">
<link rel="stylesheet" href="<?= TEMPORARY_HOST ?>/files/jquery.bxslider.css?nocache=2015-10-10" type="text/css" media="screen" charset="utf-8">


<?php if(isset($this->canonicalUrl) && $this->canonicalUrl) :?>
<link rel="canonical" href="<?= $this->canonicalUrl ?>" />
<?endif ?>

<script src="//ajax.googleapis.com/ajax/libs/jquery/1.7/jquery.min.js"></script>
<script src="<?= TEMPORARY_HOST ?>/files/jquery-ui-1.8.10.custom.min.js" type="text/javascript"></script>
<script src="<?= TEMPORARY_HOST ?>/files/jquery.multiselect.min.js" type="text/javascript"></script>
<script src="<?= TEMPORARY_HOST ?>/files/js/select2.full.min.js" type="text/javascript"></script>
<script src="<?= TEMPORARY_HOST ?>/files/jquery.prettyPhoto.js?ver=2013-02-20" type="text/javascript" charset="utf-8"></script>
<script src="<?= TEMPORARY_HOST ?>/files/aa.js?nocache=2015-11-23" type="text/javascript" charset="utf-8"></script>
<script src="<?= TEMPORARY_HOST ?>/files/jquery.animate-colors-min.js?nocache=1" type="text/javascript" charset="utf-8"></script>
<script src="<?= TEMPORARY_HOST ?>/files/qtip2/jquery.qtip.min.js?nocache=2013-08-06" type="text/javascript" charset="utf-8"></script>
<script src="<?= TEMPORARY_HOST ?>/files/jquery.dropdown.js?nocache=2013-08-08" type="text/javascript" charset="utf-8"></script>
<script src="<?= TEMPORARY_HOST ?>/files/jquery.number.min.js?nocache=2013-11-18" type="text/javascript" charset="utf-8"></script>
<script src="<?= TEMPORARY_HOST ?>/files/jquery.marquee.min.js?nocache=2015-09-25" type="text/javascript" charset="utf-8"></script>
<script src="<?= TEMPORARY_HOST ?>/files/js/jquery.bxslider.min.js?nocache=2015-12-18" type="text/javascript" charset="utf-8"></script>
<script src="http://www.google.com/recaptcha/api.js?onload=CaptchaCallback&render=explicit"
        async defer>
</script>

<script type="text/javascript">
    var CaptchaCallback = function() {

        $( ".g-recaptcha" ).each(function( index ) {
            grecaptcha.render(this, {'sitekey' : '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-'});
        });

    };
</script>



<script src="<?= TEMPORARY_HOST ?>/files/_new/global.js?nocache=2016-04-26" type="text/javascript" charset="utf-8"></script>

<?php if($this->isMobile): ?>
    <script src="<?= TEMPORARY_HOST ?>/files/idangerous.swiper-2.0.min.js?nocache=2013-08-08" type="text/javascript" charset="utf-8"></script>
<?php endif;?>

<script type="text/javascript">
    var language = '<?= $this->language ?>';
    var originalHost = 'http://autoauto.pl';
    var temporaryHost = '<?= "http://" . $_SERVER["HTTP_HOST"] ?>';
</script>


<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "WebSite",
  "url": "http://www.autoauto.pl",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "http://www.autoauto.pl/<?= $this->language ?>/list?query={query}",
    "query-input": "required name=query"
  }
}
</script>



<?php 
if($this->siteVariant == "autoauto.by"): ?>
<style>
    
#logo {
	background-image: url('/images/logo_autoauto_by.png?ver=2');
}
    
</style>  
<?php endif;?>


