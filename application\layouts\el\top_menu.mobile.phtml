
<div id="top_menu">

    <div id="top_menu_left">

        <div class="item adropdown">
            <a  href="#"><img height="25" src="/images/phone50.png?ver=2" /></a>

            <div id="phone_dropdown"  class="dropdown_block">
                <?php $cache = Zend_Registry::get('Cache');
                if (!($html = $cache->load('phone_dropdown_locations_' . $this->language))):
                $locations = new Model_Locations();
                $data = $locations->getLocationGroupsWithLocations(array(1,2,3,4));
                ob_start();
                ?>
                <?php foreach ($data as $lg): ?>
                    <div class="column column_<?= $lg['id'] ?>">
                        <strong><?= $this->escape($lg['business_name']) ?></strong> <br />

                        <?php foreach ($lg['locations'] as $loc): ?>
                            <?php if ($loc['phone']): ?>
                                <?= $this->escape($this->translate->_('LOCATION_POINT')) ?> <strong><?= $this->escape($loc['name']) ?></strong>


                                <?= $this->escape($this->translate->_('PHONE_SHORT') . ': ') ?> <a href="tel:<?=  $loc['phone']?>"><?=  $loc['phone']?></a>
                                <br />

                            <?php endif ?>
                        <?php endforeach ?>
                    </div>
                <?php endforeach ?>
                <div class="clear"></div>
            </div>
            <?php
            $html = ob_get_flush();
            $cache->save($html, 'phone_dropdown_locations_' . $this->language, $tags=array('translate', 'location', $this->language));
            ?>
            <?php else: ?>
                <?= $html ?>
            <?php endif ?>


        </div>



    </div>

    <div id="top_menu_center">
        <div class="item" id="top_menu_search">
            <form method="post" action="<?= $this->url(array('language' => $this->language), 'list', true); ?>">
                <input type="submit" name="" value="" id="find_car_quick_submit">
                <input type="text" name="query" value="" id="find_car_quick_value" />

            </form>
        </div>
    </div>

    <div id="top_menu_right">
        <?php $cache = Zend_Registry::get('Cache'); ?>

        <div style="text-align: center;" class="item adropdown">
            <a href="#">  <span><?= $this->translate->_('DIRECTIONS') ?></span> <img height="25" style="vertical-align: middle" src="/images/arrow50.png?ver=2" /> </a>

            <div id="directions_dropdown"  class="dropdown_block">
                <?php $cache = Zend_Registry::get('Cache');
                if (!($html = $cache->load('directions_dropdown_locations_' . $this->language))):
                $locations = new Model_Locations();
                $data = $locations->getLocationGroupsWithLocations(array(1,2,3,4));
                ob_start();
                ?>

                <?php foreach ($data as $lg): ?>
                    <div class="column column_<?= $lg['id'] ?>">
                        <a target="_new" href="http://mapy.google.pl/maps?daddr=<?= $this->escape($lg['address']) ?>,+<?= $this->escape($lg['city'])?>" title="">
                            <strong><?= $this->escape($lg['business_name']) ?></strong>
                            <br />
                            <?= $this->escape($lg['address']) ?>
                            <br />
                            <?= $this->escape($lg['zip_code'] . ' ' . $lg['city']) ?>
                        </a>


                    </div>
                <?php endforeach ?>
                <div class="clear"></div>
            </div>
            <?php
            $html = ob_get_flush();
            $cache->save($html, 'directions_dropdown_locations_' . $this->language, $tags=array('translate', 'location', $this->language));
            ?>
            <?php else: ?>
                <?= $html ?>
            <?php endif ?>


        </div>



    </div>

</div>
<script type="text/javascript">

    $(function(){
        $("#top_menu_left .adropdown, #top_menu_right .adropdown").find('a:first').click(function(e){

            e.preventDefault();

            var hidden = $(this).parent().find(".dropdown_block").is(':hidden');
            $(".dropdown_block").hide();


            if(hidden) {

                $(this).parent().find(".dropdown_block").show();

            } else {

                $(this).parent().find(".dropdown_block").hide();
            }


            });


    });

</script>
