<?php

class Zend_View_Helper_CarPermalink extends Zend_View_Helper_Abstract {
	
	const SEPARATOR = "_";
	
	protected function _convert($string) {
		return My_FilterPermalink::filter($string, self::SEPARATOR);
	}
	
	protected $_tr;
	protected $_filter;
	
	public function __construct($arg=null) {
		$this->_tr = Zend_Registry::get('Zend_Translate');
	}
	
	public function carPermalink($carData) {
		$ret = "";
		$ret .= $this->_convert($carData['make_name']);
		$ret .= self::SEPARATOR;
		$ret .= $this->_convert($carData['model_name']);
		$ret .= self::SEPARATOR;
		$ret .= (int)$carData['build_year'];
		//$ret .= self::SEPARATOR;
		//$ret .= $this->_convert($carData['colour_string']);
		//$ret .= self::SEPARATOR;
		//$ret .= (int)$carData['price'];
		//$ret .= self::SEPARATOR;
		//$ret .= "zl";
		//$ret .= self::SEPARATOR;
		//$ret .= $this->_convert($carData['price_type_key']);
        
        if($this->view->language == 'pl')
        {
            $title = $this->_convert($carData['title']);
            if (!empty($title)) {
                $ret .= self::SEPARATOR;
                $ret .= $title;
            }
        }
		
		return $ret;
	}
	
}