<?php

class Model_ImportExport_Export extends Model_Base {
	
	const CHECKSUM_SALT = 'aPO4$Mvf09uHOF)MsKL';
	const ENCRYPTION_KEY = 'a8F4%hGn*';
	
	protected function _calculateChecksum($type, $data) {
		switch ($type) {
			case 'addCarQuick':
				return sha1(self::CHECKSUM_SALT . $data['c_vin'] . $data['c_production_year'] . $data['c_company_id'] . self::CHECKSUM_SALT . $data['c_make_id'] . $data['c_model_id']);
				break;
				
			case 'carPhotos':
				return sha1(self::CHECKSUM_SALT . serialize($data) . (count($data) - 13));
				break;
				
			case 'client':
				return sha1(self::CHECKSUM_SALT . $data['id'] . $data['email'] . self::CHECKSUM_SALT . $data['address']);
				break;
			case 'clientSell':
				return sha1(self::CHECKSUM_SALT . $data['email'] . self::CHECKSUM_SALT . $data['first_name'].$data['last_name']);
				break;
            case 'clientBuy':
                return sha1(self::CHECKSUM_SALT . $data['email'] . self::CHECKSUM_SALT . $data['first_name']);
                break;
            case 'reservation':
				return sha1(self::CHECKSUM_SALT . $data['id'] . $data['sr_car_id'] . self::CHECKSUM_SALT . $data['user_id']);
				break;
				
			case 'verifySalesman':
				return sha1(self::CHECKSUM_SALT . $data['id'] . $data['hash'] . self::CHECKSUM_SALT . $data['timestamp']);
				break;
			
			default:
				throw new Exception("Unknown data type in " . __METHOD__ . ", line " . (__LINE__ - 1));
				break;
		}
	}
	
	protected function _encrypt(&$data, $iv=null) {
		//by reference!
		
		$td = mcrypt_module_open(MCRYPT_DES, '', MCRYPT_MODE_ECB, '');
		$key = substr(self::ENCRYPTION_KEY, 0, mcrypt_enc_get_key_size($td));
		if ($iv === null) {
			$iv_size = mcrypt_enc_get_iv_size($td);
			$iv = mcrypt_create_iv($iv_size, MCRYPT_RAND);
		}
		$iv = str_replace("/", "]", $iv); //some data exploded using "/" - avoid this char
		
		if (mcrypt_generic_init($td, $key, $iv) == -1) {
			throw new Exception("Inicjalizacja enkrypcji nie powiodla sie");
		}
		$data = mcrypt_generic($td, $data);
		
		return $iv;
	}
	
	protected function _decrypt($data, $iv) {
		$td = mcrypt_module_open(MCRYPT_DES, '', MCRYPT_MODE_ECB, '');
		$key = substr(self::ENCRYPTION_KEY, 0, mcrypt_enc_get_key_size($td));
		
		if (mcrypt_generic_init($td, $key, $iv) == -1) {
			throw new Exception("Inicjalizacja dekrypcji nie powiodla sie");
		}
		
		$data = mdecrypt_generic($td, $data);
		
		return $data;
	}
	
	protected function _receiveData($data, $iv) {
		$data = $this->_decrypt(My_Utils::urlSafeB64Decode($data), My_Utils::urlSafeB64Decode($iv));
		
		return unserialize($data);
	}
	
	protected function _sendData($action, $data, $checksum, $returnBool=true) {
		$data = serialize($data);
		
		$iv = $this->_encrypt($data);
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		
		$url = $opt['synchronization']['sr']['domain'];
		$url .= "/" . $action;
		
        
		$postData = array(
			'checksum' => $checksum,
			'iv' => My_Utils::urlSafeB64Encode($iv),
			'data' => My_Utils::urlSafeB64Encode($data)
		);
		
		$ch = curl_init($url);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);

        $result = curl_exec($ch); 
		curl_close($ch);
		
        
		if ($returnBool) {
			if ($result == "OK") return true;
			else return $result;
		}
		else {
			return $result;
		}
	}
	
	public function sendClient($data) {
		$template = array(
			'id' => '',
			'company_name' => '',
			'first_name' => '',
			'last_name' => '',
			'nip_or_pesel' => '',
			'regon' => '',
			'id_document' => '',
			'id_type' => '',
			'address' => '',
			'zip_code' => '',
			'city' => '',
			'country' => '',
			'phone' => '',
			'email' => '',
			'is_deleted' => '',
		);
		
		foreach ($data as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksum = $this->_calculateChecksum('client', $template);
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$action = $opt['synchronization']['sr']['export']['client'];
		
		if (empty($action)) {
			throw new Exception("Action empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
				
		$ret = $this->_sendData($action, $template, $checksum, $retBool=false);
		
		//check if returned string is composed of at least 3 substrings (delimited by "/"); OK/$iv/$data
		$xpld = explode("/", $ret);
		if (count($xpld) < 3) {
			throw new Exception("count(xpld) < 3; client_id: " . $template['id'] . "; data: " . $ret);
		}
		else {
			$ok = array_shift($xpld);
			if ($ok !== "OK") {
				throw new Exception("OK not in response from SR; client_id: " . $template['id'] . "; ret: " . $ret);
			}
			
			$iv = array_shift($xpld);
			
			//remaining data may include slashes and explode into multiple array entries; glue them back:
			$data = implode("", $xpld);
			
			$data = $this->_decrypt($data, $iv);
			ob_start();
			$dataUnserialized = unserialize($data);
			ob_end_clean();
			
			if ($dataUnserialized === false) {
				throw new Exception("Unable to unserialize SR data; client_id = " . $template['id'] . "; data: " . $data);
			}
			if ((int)$dataUnserialized > 0) {
				return true; //ok
			}
			else {
				throw new Exception("Data unserialized for client_id " . $template['id'] . " , but (int)returned_value is not > 0; original data: " . $data);
			}
		}

	}
	
	public function sendClientEdit($data) {
		$template = array(
			'id' => '',
			'company_name' => '',
			'first_name' => '',
			'last_name' => '',
			'nip_or_pesel' => '',
			'regon' => '',
			'id_document' => '',
			'id_type' => '',
			'address' => '',
			'zip_code' => '',
			'city' => '',
			'country' => '',
			'phone' => '',
			'email' => '',
			'is_deleted' => '',
            'date_added' => '',
            'added_by_sr_id' => '',
		);
		
		foreach ($data as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksum = $this->_calculateChecksum('client', $template);
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$action = $opt['synchronization']['sr']['export']['client_edit'];
		
		if (empty($action)) {
			throw new Exception("Action empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
				
		$ret = $this->_sendData($action, $template, $checksum, $retBool=false);

		if ($ret === "OK") {
			return true;
		}
		else {
			throw new Exception($ret);
		}
	}
	
	public function sendReservation($data) {
		$template = array(
			'id' => '',
			'date_added' => '',
			'valid_until' => '',
			'amount' => '',
			'buy_price' => '',
			'buy_price_type_key' => '',
			'user_id' => '',
			'sr_car_id' => '',
			'payment_id' => '',
			'status' => '',
			'status_change_datetime' => '',
            'agreed_with_sr_id' => '',
            'lang' => '',
		);
		
		foreach ($data as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksum = $this->_calculateChecksum('reservation', $template);
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$action = $opt['synchronization']['sr']['export']['reservation'];
		
		if (empty($action)) {
			throw new Exception("Action empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
				
		$ret = $this->_sendData($action, $template, $checksum, $retBool=false);
		
		if ($ret === "OK") {
			return true;
		}
		else {
			throw new Exception($ret);
		}
	}
	
	public function sendVerifySalesman($data) {
		$template = array(
			'id' => '',
			'hash' => '',
			'timestamp' => ''
		);
		
		foreach ($data as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksum = $this->_calculateChecksum('verifySalesman', $template);
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$action = $opt['synchronization']['sr']['export']['verify_salesman'];
		
		if (empty($action)) {
			throw new Exception("Action empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
				
		$ret = $this->_sendData($action, $template, $checksum, $retBool=false);
		
		$xpld = explode("/", $ret);
		if (count($xpld) < 3) {
			throw new Exception("count(xpld) < 3; client_id: " . $template['id'] . "; data: " . $ret);
		}
		else {
			$ok = trim(array_shift($xpld));
			if ($ok !== "OK") {
				throw new Exception("OK not in response from SR; login id: " . $template['id'] . "; ret: " . $ret);
			}
			
			$iv = array_shift($xpld);
			
			//remaining data may include slashes and explode into multiple array entries; glue them back:
			$data = implode("/", $xpld);
			
			$data = $this->_decrypt($data, $iv);
			
			ob_start();
			$dataUnserialized = unserialize($data);
			ob_end_clean();
			
			return $dataUnserialized;
		}
	}
	
	public function serializeAndEncrypt($data, $iv=null) {
		$data = serialize($data);
		$iv = $this->_encrypt($data, $iv); //by reference
		return $data;
	}
    
    public function sendClientSell($data) {
		$template = array(
            'id' => '',
            'user_id' => '',
			'sr_model_id' => '',
			'build_year' => '',
			'engine' => '',
			'color' => '',
			'odometer' => '',
			'origin' => '',
			'no_accident' => '',
			'repaired_elements' => '',
			'exchange_models' => '',
            'comments' => '',
            'caretaker_sr_id' => '',
            'photo' => '',
			'email' => '',
			'phone' => '',
            'first_name' => '',
            'last_name' => '',
            'is_for_exchange' => '',
            'is_visible' => '',
            'added_datetime' => '',
            'updated_datetime' => '',
		);
		
		foreach ($data as $key => $value) {
			if (array_key_exists($key, $template)) $template[$key] = $value;
		}
		
		$checksum = $this->_calculateChecksum('clientSell', $template);
		
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$action = $opt['synchronization']['sr']['export']['client_sell'];
		
		if (empty($action)) {
			throw new Exception("Action empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
				
		$ret = $this->_sendData($action, $template, $checksum, $retBool=false);
		
		if ($ret === "OK") {
			return true;
		}
		else {
			throw new Exception($ret);
		}
	}

    public function sendClientBuy($data) {
        $template = array(
            'id' => '',
            'user_id' => '',
            'comments' => '',
            'caretaker_sr_id' => '',
            'email' => '',
            'phone' => '',
            'first_name' => '',
            'added_datetime' => '',
            'updated_datetime' => '',
        );

        foreach ($data as $key => $value) {
            if (array_key_exists($key, $template)) $template[$key] = $value;
        }

        $checksum = $this->_calculateChecksum('clientBuy', $template);

        $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
        $action = $opt['synchronization']['sr']['export']['client_buy'];

        if (empty($action)) {
            throw new Exception("Action empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
        }

        $ret = $this->_sendData($action, $template, $checksum, $retBool=false);

        if ($ret === "OK") {
            return true;
        }
        else {
            throw new Exception($ret);
        }
    }
}