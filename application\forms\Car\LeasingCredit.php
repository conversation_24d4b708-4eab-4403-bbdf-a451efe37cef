<?php

class Form_Car_LeasingCredit extends My_Form {
	
	protected $_type = 'leasing';
	
	public function init() {

		$cars = new Model_Cars_Cars();
		$instalmentsNo = $cars->getInstalmentNoOptions($this->_type);

		$tr = Zend_Registry::get('Zend_Translate');

		$this->addElements(array(
			new Zend_Form_Element_Text('price', array(
				'label'	=>	'CAR_PRICE',
                'attribs' => array('class' => 'form-control'),
			)),
			new Zend_Form_Element_Text('contribution', array(
				'label'	=>	'CONTRIBUTION',
                'attribs' => array('class' => 'form-control'),
				'required' => false
			)),
			new Zend_Form_Element_Select('contribution_select', array(
				'label'	=>	'CONTRIBUTION',

				'required' => false,
				 'attribs' => array('class' => 'form-control'),
			)),
			new Zend_Form_Element_Select('instalmentsNo', array(
				'label'	=>	'INSTALLMENTSNO',
				'required' => false,
                'attribs' => array('class' => 'form-control'),
				'multiOptions' => $instalmentsNo,
				'value' => 36,


			)),
			new Zend_Form_Element_Hidden('carId', array()),
			new Zend_Form_Element_Hidden('type', array()),
			new Zend_Form_Element_Submit('submit', array(
				'label'	=>	'LEASINGCREDIT_SUBMIT',
                'attribs' => array('class' => ' btn btn-action btn-action-orange'),
			)),
		));
		parent::init();
	}
	
	public function setType($value) {
		$this->_type = $value;
	}
	
}