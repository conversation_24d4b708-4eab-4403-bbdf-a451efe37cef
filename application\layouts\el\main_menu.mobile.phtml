<div id="logo">
    <div id="top_menu_languages">
        <div class="language language_<?= $this->language ?> active">

            <a  href="<?= $this->url(array('language' => $this->language), null, false) ?><?php echo (!empty($this->changeLanguageData['queryString']) ? (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash']."&" : "?" ) . $this->changeLanguageData['queryString'] : (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash'] : "" ) ) ?>">&nbsp;</a>


        </div>

        <div id="language_dropdown" class="dropdown_block">
            <?php foreach ($this->getEnabledLanguages() as $key => $value): ?>
                <div class="language language_<?= $key ?> ">

                    <a href="<?= $this->url(array('language' => $key), null, false) ?><?php echo (!empty($this->changeLanguageData['queryString']) ? (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash']."&" : "?" ) . $this->changeLanguageData['queryString'] : (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash'] : "" ) ) ?>">&nbsp;</a>


                </div>
            <?php endforeach ?>
        </div>
    </div>
	<a  class="logo" href="<?= $this->url(array('language' => $this->language), 'home', true) ?>"></a>
    <div class="float_right">

        <div class="item favourites">
            <a href="<?= $this->url(array('language' => $this->language), 'favouriteListSession', true) ?>">
                <img src="/images/heart3.png?ver=3" />
                <span id="favourites-count"><?= $this->favouritesCount ?></span>
            </a>
        </div>


    </div>
</div>
<div class="clear"></div>

<script type="text/javascript">

    $(function(){


        $("#top_menu_languages").find('a:first').click(function(e){

            e.preventDefault();

            var hidden =  $(this).parent().parent().find(".dropdown_block").is(':hidden');
            $(".dropdown_block").hide();


            if(hidden) {

                $(this).parent().parent().find(".dropdown_block").show();

            } else {

                $(this).parent().parent().find(".dropdown_block").hide();
            }


        });


    });

</script>

