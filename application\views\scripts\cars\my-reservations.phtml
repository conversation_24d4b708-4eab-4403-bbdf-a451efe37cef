<div id="left_static" class="column_left_1">
	<div class="container">
		<?= $this->render('user_cp_menu.phtml') ?>
	</div>
</div>

<div class="column_right_1">	
	<h1 id="list_title"><?= $this->translate->_('MY_RESERVATIONS') ?></h1>
	
	<?php if ($this->paginator): ?>
		<?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''))); ?>
		
		<?php $index = 1; foreach($this->paginator as $car): ?>
			<?= $this->partial('car_search_item.phtml', array('car' => $car, 'translate' => $this->translate, 'language' => $this->language, 'language_row' => $this->language_row, 'hash' => ($this->hash ? '?hash=' . $this->hash . '&i=' . (($this->page - 1) * $this->paginator->getItemCountPerPage() + $index) : ''), 'page' => $this->page, 'showReservationData' => true, 'isFavourite' => in_array($car['car_id'], $this->favouritesIds))) ?>
		<?php $index++; endforeach; ?>
		
		<?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''))); ?>
		
	<?php endif; ?>
</div>