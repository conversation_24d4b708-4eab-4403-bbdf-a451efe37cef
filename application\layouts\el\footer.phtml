<div id="bottom_links">
	<div class="column column_0">
		<strong><a href="<?= $this->url(array('language' => $this->language), 'list', true) ?>"><?= $this->translate->_('CAR') ?></a></strong>
		<br /><br />
		<strong><a href="<?= $this->url(array('language' => $this->language), 'financing', true) ?>"><?= $this->translate->_('FINANCING') ?></a></strong>
		<br /><br />
		<strong><a href="<?= $this->url(array('language' => $this->language), 'aboutUs', true) ?>"><?= $this->translate->_('COMPANY') ?></a></strong>
		<br /><br />
		<strong><a href="<?= $this->url(array('language' => $this->language), 'aboutUsWork', true) ?>"><?= $this->translate->_('ABOUT_US_WORK') ?></a></strong>
		<br /><br />
		<strong><a href="<?= $this->url(array('language' => $this->language), 'racingTeam', true) ?>"><?= $this->translate->_('RACING_TEAM') ?></a></strong>
	</div>
	<?php $cache = Zend_Registry::get('Cache');
	if (!($html = $cache->load('footer_locations_' . $this->language))): 
	$locations = new Model_Locations();
	$data = $locations->getLocationGroupsWithLocations(array(1,2,3,4,7));
	ob_start();
	?>
		<?php foreach ($data as $lg): ?>
			<div class="column column_<?= $lg['id'] ?>">
				<strong><?= $this->escape($lg['business_name']) ?></strong>
				<br />
				<?= $this->escape($lg['address']) ?>
				<br />
				<?= $this->escape($lg['zip_code'] . ' ' . $lg['city']) ?>
				<br />
				<?php foreach ($lg['locations'] as $loc): ?>
					<?php if ($loc['phone'] || $loc['fax'] || $loc['email']): ?>
						<br />
						<?= $this->escape($this->translate->_('LOCATION_POINT')) ?> <strong><?= $this->escape($loc['name']) ?></strong>
						<br />
						<?php if ($loc['phone']): ?>
							<?= $this->escape($this->translate->_('PHONE_SHORT') . ': ' . $loc['phone']) ?>
							<br />
						<?php endif ?>
						<?php if ($loc['fax']): ?>
							<?= $this->escape($this->translate->_('FAX_SHORT') . ': ' . $loc['fax']) ?>
							<br />
						<?php endif ?>
						<?php if ($loc['email']): ?>
							<a href="mailto:<?=$loc['email']?>"><?= $this->escape($this->translate->_('EMAIL_SHORT') . ': ' . $loc['email']) ?></a>
							<br />
						<?php endif ?>
					<?php endif ?>
				<?php endforeach ?>
			</div>
		<?php endforeach ?>
		<div class="clear"></div>
	</div>
	<?php 
		$html = ob_get_flush();
		$cache->save($html, 'footer_locations_' . $this->language, $tags=array('translate', 'location', $this->language));
	?>
	<?php else: ?>
		<?= $html ?>
	<?php endif ?>
	

<div id="footer">
	<?= $this->render('el/cookies_policy.phtml') ?>

	<iframe src="http://www.facebook.com/plugins/like.php?app_id=276768325670466&amp;href=autoauto.pl&amp;send=false&amp;layout=button_count&amp;width=200&amp;show_faces=false&amp;action=like&amp;colorscheme=light&amp;font=lucida+grande&amp;height=21" scrolling="no" frameborder="0" style="border:none;overflow:hidden; width:200px; height:21px; float: right; margin-top: 2px;" allowTransparency="true"></iframe>
	
	<?= $this->translate->_('FOOTER_DISCLAIMER') ?>
	<br />
	&copy; 2015 AutoAuto Sp. z o.o. NIP: 1132548072 <?= $this->translate->_('ALL_RIGHTS_RESERVED') ?>

    <?php if($this->language == 'pl') : ?>

        <span class="links">
    <?php if($this->language == 'pl') : ?>

        <a style="float:right;" href="<?= $this->url(array('language' => $this->language), 'cookiesPolicy', 'true') ?>">Polityka cookies</a>


    <?php endif;?>
            <a style="float:right;" href="?layout=mobile"><?=$this->translate->_('MOBILE_VERSION')?></a>

		  <div class="clear"></div>
        </span>

   
    
    <?php endif;?>

	<div><a target="_blank" href="https://wizytowka.rzetelnafirma.pl/CFWU6J5U" rel="nofollow"><img title="Kliknij i sprawdź status certyfikatu" alt="" src="http://aktywnybaner.rzetelnafirma.pl/ActiveBanner/GetActiveBannerImage/3/CFWU6J5U" style="border:none;" mce_style="border:none;" mce_style="border:none;" mce_style="border:none;" mce_style="border:none;"></a>  </div>
</div>