<div id="logo">
						<a href="<?= $this->url(array('language' => $this->language), 'home', true) ?>"></a>
					</div>
<div id="main_menu">

	<?php $cache = Zend_Registry::get('Cache'); ?>
	
	<?php if ($menuHtml = $cache->load('main_menu_' . $this->language)) : ?>
		<?= $menuHtml ?>
	<?php else: ?>
		<?php ob_start(); ?>

        <span class="item expandable" id="main_menu_shortcut">
			<a href="javascript:;" title="" ><?= $this->translate->_('SHORTCUT') ?><span class="for_icon">&nbsp;</span></a>
		</span>
		<span class="item">
			<a href="<?= $this->url(array('language' => $this->language), 'haveCarFound', true) ?>" title="" ><?= $this->translate->_('BUY2') ?></a>
		</span>
        <span class="item">
			<a href="<?= $this->url(array('language' => $this->language), 'sellCar', true) ?>" title="" ><?= $this->translate->_('SELL2') ?></a>
		</span>
        <span class="item">
			<a href="<?= $this->url(array('language' => $this->language), 'exchangeCar', true) ?>" title="" ><?= $this->translate->_('EXCHANGE2') ?></a>
		</span>
    <?php if(false && $this->language == 'pl'): ?>
		<span class="item">
			<a href="<?= $this->url(array('language' => $this->language), 'newCars', true) ?>" title="" ><?= $this->translate->_('NEW_CARS') ?></a>
		</span>
    <?php endif;?>
		<span class="item">
			<a href="<?= $this->url(array('language' => $this->language), 'financing', true) ?>" title="" ><?= $this->translate->_('FINANCING') ?></a>
		</span>
		<span class="item">
			<a href="<?= $this->url(array('language' => $this->language), 'service', true) ?>" title="" ><?= $this->translate->_('SERVICE') ?></a>
		</span>

		<span class="item last">
			<a href="<?= $this->url(array('language' => $this->language), 'locations', true) ?>" title="" ><?= $this->translate->_('CONTACT') ?></a>
		</span>
		
		
		<span id="main_menu_breaker"></span>
		
		<div id="main_menu_shortcut_content">
			<table cellspacing="0" cellpadding="0">
				<tr>
					<td class="bg_1">
                         <div>
                             
                            	<a href="<?= $this->url(array('language' => $this->language), 'list', true) ?>?addName[]=rental&amp;addValue_0=1" title="" >
                                    <span class="for_icon">&nbsp;</span>
                                    <?= $this->translate->_('RENTAL') ?>
                                </a>
                        </div>
						<div>
							<form>
								<input type="hidden" name="promotions" value="1">
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_PROMOTIONS') ?>
							</a>
						</div>
                       
						<div>
							<form>
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_ALL_OFFERS') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="new" value="1" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_NEW_CARS') ?>
							</a>
						</div>
						
						<div>
							<form>
								<input type="hidden" name="leasing" value="1" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_LEASING') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="last_2_years" value="1" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_LAST_2_YEARS') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="gearboxes[]" value="automatic" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_AUTOMATIC_GEARBOX') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="engine[]" value="fuels diesel" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_DIESEL') ?>
							</a>
						</div>
					</td>
					<td class="bg_2">
							
						<div>
							<form>
								<input type="hidden" name="price_min" value="" />
								<input type="hidden" name="price_max" value="15000" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_UP_TO_15K') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="price_min" value="15000" />
								<input type="hidden" name="price_max" value="30000" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_15K_TO_30K') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="price_min" value="30000" />
								<input type="hidden" name="price_max" value="50000" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_30K_TO_50K') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="price_min" value="50000" />
								<input type="hidden" name="price_max" value="80000" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_50K_TO_80K') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="price_min" value="80000" />
								<input type="hidden" name="price_max" value="120000" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_80K_TO_120K') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="price_min" value="120000" />
								<input type="hidden" name="price_max" value="200000" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_120K_TO_200K') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="price_min" value="200000" />
								<input type="hidden" name="price_max" value="" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_OVER_200K') ?>
							</a>
						</div>
					</td>
					<td class="bg_3">
							
						<div>
							<form>
								<input type="hidden" name="categories[]" value="sedan" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_SEDAN') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="categories[]" value="combi" />
								<input type="hidden" name="categories[]" value="hatchback" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_COMBI_HATCHBACK') ?>
							</a>
						</div>
						
						<div>
							<form>
								<input type="hidden" name="categories[]" value="suv" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_OFFROAD_SUV') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="categories[]" value="van" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_VAN') ?>
							</a>
						</div>
						
						<div>
							<form>
								<input type="hidden" name="categories[]" value="cabrio" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_CABRIO') ?>
							</a>
						</div>
						
						<div>
							<form>
								<input type="hidden" name="categories[]" value="coupe" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_COUPE') ?>
							</a>
						</div>
						
						<div>
							<form>
								<input type="hidden" name="categories[]" value="pickup" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_PICKUP') ?>
							</a>
						</div>
					</td>
					<td class="bg_4">
							
						<div>
							<form>
								<input type="hidden" name="important_features[]" value="origin 24" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_IMPORT_USA') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="important_features[]" value="origin 21" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_SALON_POLSKA') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="exclusive" value="1" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_EXCLUSIVE') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="leasing_transfer" value="1" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_LEASING_TRANSFER') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="leasing" value="1" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('CARS_WITH_VAT') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="after_leasing_vindication" value="1" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_AFTER_LEASING_VINDICATION') ?>
							</a>
						</div>
							
						<div>
							<form>
								<input type="hidden" name="premium" value="1" />
							</form>
							<a href="javascript:;" class="js">
								<span class="for_icon">&nbsp;</span>
								<?= $this->translate->_('SHORTCUT_PREMIUM') ?>
							</a>
						</div>
						
					</td>
				</tr>
			</table>
		</div>
		
	
		
		<script type="text/javascript" charset="utf-8">
			$(function(){
				$("#main_menu_shortcut_content a.js").click(function(e){
					e.preventDefault();
					e.stopPropagation();
					
					var f = $(this).closest("div").find("form");
					f.attr('action', "<?= $this->url(array('language' => $this->language), 'list', true) ?>").attr('method', 'post');
					$('<input type="submit" name="submit" value="" style="display: none;" />').appendTo(f).click();
					return false;
				});
				
				var quickSearchInput = $("#find_car_quick_value");
				quickSearchInput
					.data('oldValue', ''/*quickSearchInput.val()*/)
					.focus(function(){
						var qs = $(this);
						qs.select();
					})
					.blur(function(){
						var qs = $(this);
					})
				;

                $("#main_menu_shortcut").click(function() {
                    if($(this).hasClass('hover')) {

                        $("#main_menu_shortcut").removeClass('hover');
                        $("#main_menu_shortcut_content").hide();

                    } else {
                        $(this).addClass('hover');
                        $("#main_menu_shortcut_content").show();
                    }

                });

				$("#main_menu_shortcut").hover(
					function(){
						$(this).addClass('hover');
						$("#main_menu_shortcut_content").show();
					},
					function(){
						var timeoutId = setTimeout(
							function(){
								$("#main_menu_shortcut").removeClass('hover');
								$("#main_menu_shortcut_content").hide();
							},
							50
						);
						$("#main_menu_shortcut_content").data('hide_me_timeout', timeoutId);
					}
				);
				
				$("#main_menu_shortcut_content").hover(
					function(){
						clearTimeout($(this).data('hide_me_timeout'));
					},
					function(){
						$(this).hide();
						$("#main_menu_shortcut").removeClass('hover');
					}
				);
				
				$("#main_menu_car").hover(
					function(){
						$(this).addClass('hover');
						$("#main_menu_car_content").css('left', $(this).offset().left - $(this).parent().offset().left).show();
					},
					function(){
						var timeoutId = setTimeout(
							function(){
								$("#main_menu_car").removeClass('hover');
								$("#main_menu_car_content").hide();
							},
							50
						);
						$("#main_menu_car_content").data('hide_me_timeout', timeoutId);
					}
				);
				
				$("#main_menu_car_content").hover(
					function(){
						clearTimeout($(this).data('hide_me_timeout'));
					},
					function(){
						$(this).hide();
						$("#main_menu_car").removeClass('hover');
					}
				);
				
				$("#main_menu .item").not(".expandable").hover(
					function(){
						$(this).addClass('hover');
					},
					function(){
						$(this).removeClass('hover');
					}
				);
				
				<?php if ($this->query): ?>
					quickSearchInput.focus().blur();
				<?php endif; ?>
			});
		</script>
		<?php
			$html = ob_get_flush();
			$cache->save($html, 'main_menu_' . $this->language, $tags=array('main_menu', $this->language, 'translate'));
		?>
	<?php endif ?>

</div>
<div class="clear"></div>


<script type="text/javascript">
    
var menuItem = 1;
$(function () {
      $("#main_menu .item a").eq(menuItem).css('color', 'rgb(246, 120, 18)');
      menuItem++;
      setInterval(function(){
      
           
            $("#main_menu .item a").css('color', ' #E0E0E0');  
            $("#main_menu .item a").eq(menuItem).css('color', 'rgb(246, 120, 18)');
            menuItem++;
            if(menuItem > 3)
                menuItem = 1;
        }
    ,2000);
      
      
});

</script>