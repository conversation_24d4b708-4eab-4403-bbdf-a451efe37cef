<?php

class Bootstrap extends Zend_Application_Bootstrap_Bootstrap
{
	
	protected function _initAutoload()
    {
    	Zend_Loader_Autoloader::getInstance()->registerNamespace('My_');
        Zend_Loader_Autoloader::getInstance()->registerNamespace('Mobile_');
		Zend_Loader_Autoloader::getInstance()->registerNamespace('Cocur\\');
		$moduleLoader = new Zend_Application_Module_Autoloader(array(
            'namespace' => '',
            'basePath' => APPLICATION_PATH));
        return $moduleLoader;
    }

	protected function _initLogger() {
		$this->bootstrap('db');
		$columnMapping = array(	'level' => 'priority',
								'account_id' => 'account_id',
								'controller' => 'controller',
								'action' => 'action',
								'operation' => 'operation',
								'outcome' => 'outcome',
								'time' => 'time',
								'ip' => 'ip',
								'additional' => 'additional');
		$writer = new Zend_Log_Writer_Db(Zend_Db_Table::getDefaultAdapter(), 'aa_log', $columnMapping);
		$logger = new My_Log($writer);
		Zend_Registry::set('eventLogger',$logger);
	}
    
    protected function _initZFDebug()
    {
        // Setup autoloader with namespace
        $autoloader = Zend_Loader_Autoloader::getInstance();
        $autoloader->registerNamespace('ZFDebug');

        // Ensure the front controller is initialized
        $this->bootstrap('FrontController');

        // Retrieve the front controller from the bootstrap registry
        $front = $this->getResource('FrontController');

        // Only enable zfdebug if options have been specified for it
        if ($this->hasOption('zfdebug'))
        {
            // Create ZFDebug instance
            $zfdebug = new ZFDebug_Controller_Plugin_Debug($this->getOption('zfdebug'));
            			
            // Register ZFDebug with the front controller
            //$front->registerPlugin($zfdebug); //psuje redirecty!
            
        }
        // In application.ini do the following:
        // 
        // [development : production]
        // zfdebug.plugins.Variables = null
        // zfdebug.plugins.Time = null
        // zfdebug.plugins.Memory = null
        // ...
        
        // Plugins that take objects as parameters like Database and Cache
        // need to be registered manually:
        
        // $zfdebug->registerPlugin(new ZFDebug_Controller_Plugin_Debug_Plugin_Database($db));
        
        
        // Alternative configuration without application.ini
        // $options = array(
        //     'plugins' => array('variables', 'database', 
        //                        'file' => array('basePath' => '/Library/WebServer/Documents/budget', 'myLibrary' => 'Scienta'),
        //                        'memory', 'time', 'registry', 
        //                        //'auth',
        //                        //'cache' => array('backend' => $cache->getBackend()), 
        //                        'exception')
        // );
        // $zfdebug = new ZFDebug_Controller_Plugin_Debug($options);
        // Register ZFDebug with the front controller
        // $front->registerPlugin($zfdebug);
    }
    
//    protected function _initZFDebug2() {
//		$logger = new Zend_Log();
//		$writer = new Zend_Log_Writer_Firebug();
//		$logger->addWriter($writer);
//		Zend_Registry::set('ZFDlogger',$logger);
//
//        $mail = new Zend_Mail();
//        $mail->setFrom('<EMAIL>')
//                ->addTo('<EMAIL>');
//
//        $mailWriter = new Zend_Log_Writer_Mail($mail);
//        $mailWriter->setSubjectPrependText('[Error]');
//
//		$errorLogger = new Zend_Log();
//		$errorLogger->addWriter(new Zend_Log_Writer_Stream($this->_options['general']['logfile']));
//        if (APPLICATION_ENV != 'development') {
//            $errorLogger->addWriter($mailWriter);
//        }
//
//        Zend_Registry::set('errorLogger', $errorLogger);
//
//		if (!in_array(APPLICATION_ENV, array('development'))) {
//			$writer->setEnabled(false);
//		}
//		else {
//			$profiler = new Zend_Db_Profiler_Firebug('DB Queries');
//			$profiler->setEnabled(true);
//			$this->bootstrap('db');
//			$this->getResource('db')->setProfiler($profiler);
//		}
//    }
    
    protected function _initDatabaseMisc() {
		$this->bootstrap('db');
		$this->getResource('db')->query('SET NAMES UTF8'); //tymczasowo
		
		$timeCorrect = true;
		
		$this->getResource('db')->query('SET time_zone = "' . $this->_options['general']['db_time_zone'] . '"');
		$nowDb = $this->getResource('db')->fetchOne('SELECT NOW()');
		$timeDb = strtotime($nowDb);
		$timePhp = time();
		if (abs($timeDb - $timePhp) > 60 * 5) {
			$timeCorrect = false;
			//exit("Database and PHP time difference too big! db: " . $nowDb . "; php: " . date("Y-m-d H:i:s", $timePhp) . ". See application.ini general.db_time_zone setting.");
		}//if time difference bigger than 5 minutes
		
		$datePhp = date("Y-m-d", $timePhp);
		$dateDb = date("Y-m-d", $timeDb);
		if ($datePhp != $dateDb) {
			$timeCorrect = false;
			//exit("Critical error. Database and PHP date difference! db: " . $dateDb . "; php: " . $datePhp . ". See application.ini general.db_time_zone setting.");
		}//around midnight date difference may occur even if time difference is small
		
		if (!$timeCorrect) {
			$this->getResource('db')->query('SET time_zone = "' . $this->_options['general']['db_time_zone_secondary'] . '"');
			$nowDb = $this->getResource('db')->fetchOne('SELECT NOW()');
			$timeDb = strtotime($nowDb);
			$timePhp = time();
			if (abs($timeDb - $timePhp) > 60 * 5) {
				exit("Database and PHP time difference too big! db: " . $nowDb . "; php: " . date("Y-m-d H:i:s", $timePhp) . ". See application.ini general.db_time_zone setting.");
			}//if time difference bigger than 5 minutes
			
			$datePhp = date("Y-m-d", $timePhp);
			$dateDb = date("Y-m-d", $timeDb);
			if ($datePhp != $dateDb) {
				exit("Critical error. Database and PHP date difference! db: " . $dateDb . "; php: " . $datePhp . ". See application.ini general.db_time_zone setting.");
			}//around midnight date difference may occur even if time difference is small
			$timeCorrect = true;
		}
    }

	protected function _initGeneral()
    {
    	date_default_timezone_set($this->_options['general']['timezone']);
		Zend_Registry::set('currency', $this->_options['general']['currency']);
		Zend_Registry::set('password_salt', 'qkB18$F@');

		$this->bootstrap('cachemanager');
		Zend_Registry::set('Cache', $this->getResource('cachemanager')->getCache('infinite'));

		// Tymczasowo wyczyść cache po przejściu na SSL (można usunąć po stabilizacji)
		$cache = $this->getResource('cachemanager')->getCache('infinite');
		if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
			$cache->clean(Zend_Cache::CLEANING_MODE_ALL);
		}

		// Sesja jest teraz konfigurowana w _initSession()
		// Zend_Session::start(); // przeniesione do _initSession()
    }
    
    /*
    protected function _initPdf() {
    	Zend_Registry::set('pdf_contracts_path', $this->_options['tcpdf']['contracts']['path']);
    	Zend_Registry::set('pdf_invoices_path', $this->_options['tcpdf']['invoices']['path']);
    }
    */

	protected function _initSession()
	{
		$isHttps = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on');

		// 1) Migruj z PHPSESSID -> AASPSESSID (nowa nazwa = nowe, czyste cookie)
		$old = 'PHPSESSID';
		if (isset($_COOKIE[$old])) {
			// skasuj stare cookie, aby przeglądarki nie wysyłały go dalej
			setcookie($old, '', time()-3600, '/', '.autoauto.pl', $isHttps, true);
		}
		// ustaw NOWĄ nazwę ciasteczka sesji (jednorazowa zmiana po wdrożeniu)
		session_name('AASPSESSID');

		// 2) Parametry ciasteczka (jak masz teraz)
		session_set_cookie_params(0, '/', '.autoauto.pl', $isHttps, true);

		Zend_Session::setOptions(array(
			'use_only_cookies' => true,
			'cookie_httponly'  => true,
			'cookie_domain'    => '.autoauto.pl',
			// 'cookie_secure'  => $isHttps,
		));

		if (function_exists('ini_set')) {
			@ini_set('session.auto_start', 0);
			@ini_set('zlib.output_compression', 0);
			@ini_set('output_buffering', 0);
		}

		if (!Zend_Session::isStarted()) {
			try { Zend_Session::start(); }
			catch (Exception $e) {
				// w razie uszkodzonego cookie po drodze – drugi start
				setcookie(session_name(), '', time()-3600, '/', '.autoauto.pl', $isHttps, true);
				@session_write_close();
				if (function_exists('openssl_random_pseudo_bytes')) {
					@session_id(bin2hex(openssl_random_pseudo_bytes(16)));
				}
				Zend_Session::start();
			}
		}
	}


	protected function _initPlugins()
	{
		$this->bootstrap('session');

		if (APPLICATION_ENV == 'development') {
			Zend_Controller_Front::getInstance()->registerPlugin(new My_Plugin_Debug());
		}
		Zend_Controller_Front::getInstance()->registerPlugin(new My_Plugin_Translate());

		// Tymczasowo wyłączone dla testów SSL - odkomentuj jeśli problemy nadal występują:
		Zend_Controller_Front::getInstance()->registerPlugin(new My_Plugin_Auth());
		Zend_Controller_Front::getInstance()->registerPlugin(new My_Plugin_Messenger());
	}
    
    protected function _initHelpers()
    {
        /*Zend_Controller_Action_HelperBroker::addHelper(
            new My_Controller_Action_Helper_Mobile()
        );*/
    }

	
	protected function _initRoutes()
	{
		$routes_config = new Zend_Config_Ini(APPLICATION_PATH.'/configs/routes.ini', APPLICATION_ENV);
		$router = Zend_Controller_Front::getInstance()->getRouter();
		$router->addConfig($routes_config, 'routes');

		$this->bootstrap('db');
	}

}

