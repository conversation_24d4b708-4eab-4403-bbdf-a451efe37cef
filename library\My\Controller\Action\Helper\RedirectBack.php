<?php

class My_Controller_Action_Helper_RedirectBack extends Zend_Controller_Action_Helper_Abstract {
	
	public function direct($sessionKey, $location=null) {
		$controller = $this->getActionController();
		
		$redirTo = "/";
		
		if ($location !== null && !empty($location)) {
			$redirTo = $location;
		}
		
		if (!empty($sessionKey)) {
			$to = $this->getRedirectBack($sessionKey);
			if ($to != null) {
				$redirTo = $to;
			}
		}
				
		$redirector = Zend_Controller_Action_HelperBroker::getStaticHelper('redirector');
		$redirector->gotoUrl($redirTo);
	}
	
	public function getRedirectBack($sessionKey) {
		if (!empty($sessionKey)) {
			$ns = new Zend_Session_Namespace('My_Controller_Action_Helper_RedirectBack');
			if (isset($ns->urls) && is_array($ns->urls) && array_key_exists($sessionKey, $ns->urls) && !empty($ns->urls[$sessionKey])) {
				return $ns->urls[$sessionKey];
			}
		}
		return null;
	}
	
}