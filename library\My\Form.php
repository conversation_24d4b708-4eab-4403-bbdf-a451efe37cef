<?php

class My_Form extends Zend_Form {
	

	public function render($arg=null) {
		foreach ($this->getElements() as $el) {
			if ($el instanceof Zend_Form_Element_Submit) {
				//submit button special case - value, not label, is displayed on the button
				if (($label = $el->getValue()) != '') {
					try {
						$translate = Zend_Registry::get('Zend_Translate');
						$el->setValue($translate->_($label));
					}
					catch (Exception $e) {
						//not really important enough to make a fuss about it
					}
				}
			}
		}
		
		return parent::render($arg);
	}
	
	public function renderIfExist($elements) {
		$ret = "";
		foreach ($elements as $el) {
			if ($this->getElement($el)) {
				$ret .= $this->getElement($el)->render();
			}
		}
		return $ret;
	}
}