<div class="car_search_item car_search_item_<?= $this->car['location_group_id'] ?>">
    <div class="location">

        <a  target="_new" href="http://mapy.google.pl/maps?daddr=<?=$this->escape($this->car['address'])?>,+Warszawa" class="location_address"> <?= $this->escape($this->car['address'] . " - " . $this->car['name']) ?> </a> | <?= $this->escape($this->translate->_('CAR_POSITION') . ": ") . (($this->car['status'] == "NOT_ON_SITE" || $this->car['status'] == 'WITHDRAWN') ? "<strong>" . $this->translate->_('NOT_ON_SITE') . "</strong>" : ('<span class="position">'.$this->escape($this->car['position']).'</span>')) ?>

        <span class="for_icon <? if ($this->car['leasing'] == 'y' || $this->car['credit_collateral'] == 1) {echo 'y';} elseif ($this->car['ownership_type'] == "OWN") {echo 'r';} ?>">
				&bull;<?php if ($this->car['is_reserved_hidden'] || $this->car['is_sold_hidden']): ?>&bull;<?php endif ?>
	    </span>
    </div>
	<a name="car_id_<?= $this->car['car_id'] ?>"></a>
    <div class="title">
        <?php if($this->employeeMail): ?>
        <a href="mailto:<?= $this->employeeMail?>?subject=<?= $this->escape('Jestem zainteresowany samochodem ID:'. $this->car['sr_car_id']. ' '.$this->car['make_name']) . " " . $this->escape($this->car['model_name']) . " " . $this->car['build_year'] . ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year'] : "") . ($this->language == "pl" ? " " . $this->escape($this->car['title']) : "")?>">
            <?php else: ?>
            <a href="<?= $this->domain . $this->carLink($this->car, null, $noMarkup=true) . ($this->hash ? $this->hash . "&amp;perPage=" . $this->perPage : "?perPage=" . $this->perPage) ?>">
                <?php endif ?>



                <?= $this->escape($this->car['make_name']) . " " . $this->escape($this->car['model_name']) . " " . ($this->car['is_new_car'] ? date('Y') : $this->car['build_year'])  .' '. $this->translate->_('PROD.'). ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year']  .' '. $this->translate->_('REG.') : "") . ($this->language == "pl" ? " " . $this->escape($this->car['title']) : "")?>
                <?php /*if (Zend_Auth::getInstance()->hasIdentity()): ?>
                    <?php if (!$this->favouriteCarId && !$this->favouriteCarCarId): ?>
                        <a href="<?= $this->domain . $this->url(array('page' => $this->page), 'list') . ($this->hash ? $this->hash . '&amp;favourite=' . $this->car['car_id'] : '?favourite=' . $this->car['car_id'] ); ?>" class="for_icon" title="<?= $this->translate->_('ADD_TO_FAVOURITES') ?>">&nbsp;</a>
                    <?php elseif($this->favouriteCarId): ?>
                        <a href="<?= $this->domain . $this->url(array('page' => $this->page), 'favouriteList') . '?del=' . $this->favouriteCarId; ?>" class="for_icon for_icon_delete" title="<?= $this->translate->_('REMOVE_FROM_FAVOURITES') ?>">&nbsp;</a>
                    <?php endif ?>
                <?php else: ?>
                    <?php if($this->favouriteCarCarId): ?>
                        <a href="<?= $this->domain . $this->url(array('page' => $this->page), 'favouriteListSession') . '?del=' . $this->favouriteCarCarId; ?>" class="for_icon for_icon_delete" title="<?= $this->translate->_('REMOVE_FROM_FAVOURITES') ?>">&nbsp;</a>
                    <?php else: ?>
                        <a href="<?= $this->domain . $this->url(array('page' => $this->page), 'list') . ($this->hash ? $this->hash . '&amp;favourite=' . $this->car['car_id'] : '?favourite=' . $this->car['car_id'] ); ?>" class="for_icon" title="<?= $this->translate->_('ADD_TO_FAVOURITES') ?>">&nbsp;</a>
                    <?php endif ?>
                <?php endif  */?>

                <?php if($this->car['ownership_type'] == 'OWN') :?>
                    <span class="certain-car"><?= $this->translate->_('CERTIFIED_GUARANTEED')?></span> -
                <?php endif ?>

            </a>
    </div>
	<div class="photo">
        <?php if($this->newsletter) : ?>
        <div class="position">
           
                
            <?php if($this->employeeMail): ?>
                <a href="mailto:<?= $this->employeeMail?>?subject=<?= $this->escape('Jestem zainteresowany samochodem ID:'. $this->car['sr_car_id']. ' '.$this->car['make_name']) . " " . $this->escape($this->car['model_name']) . " " . $this->car['build_year'] . ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year'] : "") . ($this->language == "pl" ? " " . $this->escape($this->car['title']) : "")?>">
            <?php else: ?>
                <a href="<?= $this->domain . $this->carLink($this->car, null, $noMarkup=true) . $this->hash ?>">
            <?php endif; ?>
            
         
            <?php if(DateTime::createFromFormat('Y-m-d H:i:s',$this->car['price_change_datetime']) > DateTime::createFromFormat('Y-m-d H:i:s',$this->car['added_datetime']) ):  ?>       
                    
                    <?= $this->translate->_('SALE') ?>!
            <?php else: ?>
                    
                   <?= $this->translate->_('NEW') ?>!
                    
            <?php endif;?>
                    
             </a>       
           
        </div>
        <?php endif ?>
		<div class="img">
			<?php if (!empty($this->car['filename_base'])):?>
                <?php if($this->employeeMail): ?>
                    <a href="mailto:<?= $this->employeeMail?>?subject=<?= $this->escape('Jestem zainteresowany samochodem ID:'. $this->car['sr_car_id']. ' '.$this->car['make_name']) . " " . $this->escape($this->car['model_name']) . " " . $this->car['build_year'] . ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year'] : "") . ($this->language == "pl" ? " " . $this->escape($this->car['title']) : "")?>">
                <?php else: ?>
                    <a href="<?= $this->domain . $this->carLink($this->car, null, $noMarkup=true) . ($this->hash ? $this->hash . "&amp;perPage=" . $this->perPage : "?perPage=" . $this->perPage) ?>">
                <?php endif; ?>
                        <img src="<?= $this->domain ?>/images/cars/<?= $this->car['car_id'] ?>/<?= $this->escape($this->car['filename_base']) ?>_M.<?= $this->escape($this->car['filename_extension']) ?>" alt="">
                    </a>
                
            <?php else: ?>
                <?php if($this->employeeMail): ?>
                    <a href="mailto:<?= $this->employeeMail?>?subject=<?= $this->escape('Jestem zainteresowany samochodem ID:'. $this->car['sr_car_id']. ' '.$this->car['make_name']) . " " . $this->escape($this->car['model_name']) . " " . $this->car['build_year'] . ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year'] : "") . ($this->language == "pl" ? " " . $this->escape($this->car['title']) : "")?>">
                <?php else: ?>
				<a href="<?= $this->domain . $this->carLink($this->car, null, $noMarkup=true) . ($this->hash ? $this->hash . "&amp;perPage=" . $this->perPage : "?perPage=" . $this->perPage) ?>" class="no_photo no_photo_<?= $this->escape(str_replace(".", "_", mb_strtolower($this->car['vc_key']))) ?>">
                <?php endif; ?>
					<div></div>
				</a>
			<?php endif ?>
			<?php if (in_array($this->car['status'], array("SOLD", "SOLD_HANDED_OUT"))): ?>
				<span class="reservation">
					<?= $this->translate->_('SOLD') ?>
				</span>
			<?php elseif ($this->car['is_reserved']): ?>
				<span class="reservation">
					<?= $this->translate->_('RESERVATION') ?>
				</span>
			<?php endif ?>
		</div>

	</div>
    <div class="misc">
        <div class="price" style="line-height: normal;">

            <?php if ($this->searchParameters && isset($this->searchParameters['rental']) && $this->searchParameters['rental'] == 1): ?>
                <a href="http://aa2.autoauto.pl/files/cennik_wynajmu_aut.html" target="_blank"><?= $this->translate->_('RENTAL_PRICE_LIST') ?></a>
            <?php elseif (in_array($this->car['status'], array("SOLD", "SOLD_HANDED_OUT"))): ?>
                <?= $this->translate->_('SOLD') ?>
            <?php else: ?>
                <?php if($this->siteVariant == 'autoauto.by') :?>
                    <?= $this->escape($this->carPrice($this->car, $this->language_row, 'EUR')) ?>
                <?php else: ?>
                    <?= $this->escape($this->carPrice($this->car, $this->language_row)) ?>
                    <?php if($this->carPrice($this->car, $this->language_row, 'EUR')):?>
                        <div>
                            <?= $this->escape($this->carPrice($this->car, $this->language_row, 'EUR')) ?>
                        </div>
                    <?php endif;?>
                <?php endif;?>
            <?php endif ?>
        </div>
        <?php if (!$this->hideCaretaker): ?>
            <div class="caretaker">
                <div class="phone"><? if ($this->car['caretaker_visible']) : ?><?= $this->escape($this->car['caretaker_phone']) ?><? endif; ?></div>
                <div class="name"><? if ($this->car['caretaker_visible']) : ?><?= $this->escape($this->car['caretaker_first_name'] . " " . $this->car['caretaker_last_name']) ?><? endif; ?></div>
            </div>
        <?php endif ?>

    </div>
    <?php if(!$this->newsletter) : ?>
        <div class="actions actions_<?= $this->car['location_group_id'] ?>">




            <a class="share" href="#" data-horizontal-offset="-80" data-dropdown="#dropdown-<?= $this->car['car_id'] ?>">

                    <span>
                        <?= strtolower($this->translate->_('SEND')) ?><br />
                        <?= strtolower($this->translate->_('PRINT')) ?>
                        <div class="caret"></div>
                    </span>

            </a>

            <?php if($this->isFavourite): ?>
                <?php if(Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == 'user') : ?>
                    <a class="favourite-del"  href="<?= $this->domain . $this->url(array('language' => $this->language), 'favouriteList') .  '?del=' . $this->car['car_id'] ?>"> <span><span>-</span><br/><?= $this->translate->_('FAVOURITE_ADD_DEL') ?></span></a>
                <?php else: ?>
                    <a class="favourite-del"  href="<?= $this->domain . $this->url(array('language' => $this->language), 'favouriteListSession') .  '?del=' . $this->car['car_id'] ?>"> <span><span>-</span><br/><?= $this->translate->_('FAVOURITE_ADD_DEL') ?></span></a>
                <?endif ?>
            <?php else: ?>
                <a class="favourite"  href="<?= $this->domain . $this->url(array('page' => $this->page), 'list') . ($this->hash ? $this->hash . '&amp;favourite=' . $this->car['car_id'] : '?favourite=' . $this->car['car_id'] ); ?>">

                    <span><span>+</span><br/><?= $this->translate->_('FAVOURITE_ADD_DEL') ?></span>
                </a>
            <?php endif?>

        </div>
        <div id="dropdown-<?= $this->car['car_id'] ?>" class="dropdown">
            <ul class="dropdown-menu">
                <li><a href="sms:?&body=To jest link do dokladnego opisu auta <?= $this->car['make_name'] . ' '. $this->car['model_name'] . ' '. $this->car['build_year'] ?> o ktorym rozmawialismy <?= $this->domain . $this->carLink($this->car, null, $noMarkup=true) ?>"> <?= $this->translate->_('SEND_OFFER_SMS') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarSendOffer', true) ?>"> <?= $this->translate->_('SEND_OFFER') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarContactCartaker', true) ?>"> <?= $this->translate->_('CONTACT_CARETAKER') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id']), 'showCar', true) ?>?print=true">  <?= $this->translate->_('PRINT_OFFER') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language,'id' => $this->car['car_id'],'description' => $this->carPermalink($this->car)),'forceShowCarTestDrive',true) ?>"> <?= $this->translate->_('TEST_DRIVE') ?></a></li>
                <li><a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($this->domain.$this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)))) ?>" target="_blank">Facebook</a></li>
            </ul>
        </div>
    <?php endif; ?>
	<div class="description clear">
        <?php if($this->employeeMail): ?>
            <a href="mailto:<?= $this->employeeMail?>?subject=<?= $this->escape('Jestem zainteresowany samochodem ID:'. $this->car['sr_car_id']. ' '.$this->car['make_name']) . " " . $this->escape($this->car['model_name']) . " " . $this->car['build_year'] . ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year'] : "") . ($this->language == "pl" ? " " . $this->escape($this->car['title']) : "")?>">
        <?php else: ?>
		<a href="<?= $this->domain . $this->carLink($this->car, null, $noMarkup=true) . ($this->hash ? $this->hash . "&amp;perPage=" . $this->perPage : "?perPage=" . $this->perPage) ?>">
		<?php endif;?>	
            <?
				if (!$this->showReservationData) :
					if ($this->language == "pl"):
						$maxLen = 280;
						$desc = $this->carDescription($this->car['description'], $stripNotReplace=true);
						if (mb_strlen($desc) > $maxLen) {
							$desc = mb_substr($desc, 0, $maxLen, "UTF-8") . "...";
						}
						echo $desc;
					else:
						echo $this->translate->_('CAR_SHOW_MORE');
					endif;
				else: ?>
					<?= $this->translate->_('RESERVATION_STATUS') ?>: <b><?= $this->translate->_($this->car['status']) ?></b><br />
					<?= $this->translate->_('RESERVATION_AMOUNT') ?>: <b><?= $this->currency($this->car['amount']) ?> zł</b><br />
					<?= $this->translate->_('RESERVATION_VALID_UNTIL') ?>: <b><? if (!empty($this->car['valid_until'])) echo date("Y-m-d", strtotime($this->car['valid_until'])) ?></b><br />
					<?= $this->translate->_('RESERVATION_PRICE') ?>: <b><?= $this->currency($this->car['buy_price']) ?> zł <?= $this->translate->_($this->car['buy_price_type_key']) ?></b>
				<? endif;
			?>
		</a>

	</div>


</div>