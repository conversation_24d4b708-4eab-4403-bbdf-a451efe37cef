<?php

class Form_Login extends My_Form {
    protected $_showPassword = false;
    
	public function init() {
		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
				'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8'))
				)
			)),
		/*	new Zend_Form_Element_Password('password', array(
				'label'	=>	'PASSWORD',
				'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 6, 'max' => 64, 'encoding' => 'UTF-8')))
			)),
                 * 
                 */
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Hidden('action', array(
				'label'	=>	'',
				'value' => 'login'
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label' => 'LOGIN'
			)),
		));
        
        if($this->_showPassword)
        {
            $this->removeElement('submit');
            
            $this->addElements(array(
                new Zend_Form_Element_Password('password', array(
                    'label'	=>	'PASSWORD',
                    'required' => true,
                    'validators' => array(new Zend_Validate_StringLength(array('min' => 6, 'max' => 64, 'encoding' => 'UTF-8')))
                )),
                new Zend_Form_Element_Submit('submit', array(
				'label' => 'LOGIN'
                )),
       
            ));
        }
            
		$view = Zend_Layout::getMvcInstance()->getView();
		$this->setAction($view->url(array('language' => $view->language), 'login', $reset=true));
		
		parent::init(array('customOptions' => array('noAsteriskInLabel' => true)));
	}//init

    public function setShowPassword($value)
    {
        $this->_showPassword = $value;
    }
}