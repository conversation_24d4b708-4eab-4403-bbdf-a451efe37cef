<?php

class Form_SellCar extends My_Form {

    protected function array_merge_keys(){
        $args = func_get_args();
        $result = array();
        foreach($args as &$array){
            foreach($array as $key => &$value){
                $result[$key] = $value;
            }
        }
        return $result;
    }

	public function init() {

        $this->setAttrib('enctype', 'multipart/form-data');

        $cars = new Model_Cars_Cars();
        $tr = Zend_Registry::get('Zend_Translate');

		$this->addElements(array(
            new Zend_Form_Element_Select('make', array(
                'label'	=>	'MAKE',
                'validators' => array(),
                'required' => true,
                'multiOptions' => array("" => $tr->_('MAKE')) + $cars->getCarMakes(array(1),true,true) ,
                'attribs' => array('class' => 'form-control', 'data-placeholder' => $tr->_('MAKE'), 'data-ids' => '1')
            )),
            new Zend_Form_Element_Select('model', array(
                'label'	=>	'MODEL',
                'validators' => array(),
                'required' => true,
                'registerInArrayValidator' => false,
                'attribs' => array( 'disabled' => 'disabled', 'class' => 'select2 form-control' , 'data-placeholder' => $tr->_('MODEL'))
            )),
			new Zend_Form_Element_Text('build_year', array(
				'label'	=>	'BUILD',
				'required' => true,
				'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('BUILD'))
			)),
			new Zend_Form_Element_Text('engine', array(
				'label'	=>	'ENGINE',
				'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('ENGINE'))
			)),
			new Zend_Form_Element_Text('color', array(
				'label'	=>	'COLOR',
				'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('COLOR'))
			)),
			new Zend_Form_Element_Text('odometer', array(
				'label'	=>	'ODOMETER',
				'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('ODOMETER'))

			)),
			new Zend_Form_Element_Text('origin', array(
				'label'	=>	'SELL_CAR_ORIGIN',
				'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('SELL_CAR_ORIGIN'))
			)),
			new Zend_Form_Element_Checkbox('no_accident', array(
				'label'	=>	'SELL_CAR_NO_ACCIDENT',
				'filters' => array(new Zend_Filter_StripTags()),
			)),
            new Zend_Form_Element_Checkbox('exchange', array(
                'label'	=>	'EXCHANGE_CAR',
                'filters' => array(new Zend_Filter_StripTags())
            )),
			new Zend_Form_Element_Hidden('repaired_elements', array(
				'label'	=>	'SELL_CAR_REPAIRED_ELEMENTS',
				'filters' => array(new Zend_Filter_StripTags()),
				'attribs' => array('rows' => 3, 'cols' => 40)
			)),
            new Zend_Form_Element_Textarea('comments', array(
                'label'	=>	'SELL_CAR_DESCRIPTION',
                'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('rows' => 3, 'cols' => 40, 'class' => 'form-control', 'placeholder' => $tr->_('SELL_CAR_DESCRIPTION'))

            )),
            new Zend_Form_Element_Textarea('exchange_comments', array(
                'label'	=>	'SELL_CAR_DESCRIPTION',
                'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('rows' => 3, 'cols' => 40, 'class' => 'form-control', 'placeholder' => $tr->_('SELL_CAR_DESCRIPTION'))

            )),
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
				'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')),
				),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('EMAIL')),
			)),
			new Zend_Form_Element_Text('phone', array(
				'label'	=>	'PHONE',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8'))),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('PHONE')),
			)),
			new Zend_Form_Element_Text('first_name', array(
				'label'	=>	'FIRST_NAME',
				'required' => true,
				'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('FIRST_NAME')),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
            new Zend_Form_Element_Text('last_name', array(
                'label'	=>	'LAST_NAME',
                'required' => true,
                'filters' => array(new Zend_Filter_StripTags()),
                'attribs' => array('class' => 'form-control', 'placeholder' => $tr->_('LAST_NAME')),
                'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
            )),

            new Zend_Form_Element_Multiselect('exchange_makes', array(
                'label'	=>	'MAKE',
                'validators' => array(),
                'required' => false,
                'attribs' => array( 'disabled' => 'disabled', 'class' => 'select2 form-control' , 'data-placeholder' => $tr->_('MAKE'), 'data-ids' => '1'),
                'multiOptions' =>  $cars->getCarMakes(array(1),true,true),
            )),
            new Zend_Form_Element_Multiselect('exchange_models', array(
                'label'	=>	'MODEL',
                'validators' => array(),
                'required' => false,
                'attribs' => array( 'disabled' => 'disabled', 'class' => 'select2 form-control' , 'data-placeholder' => $tr->_('MODEL')),
                'registerInArrayValidator' => false,
            )),


            new Zend_Form_Element_File('photo', array(
                'label'	=>	'ADD_PHOTO',
                'validators' => array(),
            )),

            new Zend_Form_Element_Captcha('captcha', array(
                'label' => 'CAPTCHA',
                'captcha' => new My_Captcha_ReCaptcha(array(
                    'siteKey'  => '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-',
                    'secretKey' => '6Lfx7A0UAAAAAL41t8hFSPCzuh6VQGhcpXttwrJd'
                )),
            )),

			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label'	=>	'SEND',
                'attribs' => array('class' => ' btn btn-action btn-action-orange'),
			)),
		));

		parent::init();

        $photo = $this->getElement('photo');
        $photo->setDecorators(array('File', array('ViewScript', array('viewScript' => '_file.phtml', 'placement' => false))));



        $photo->setDestination(APPLICATION_PATH.'/../tmp/');
        $photo->setMaxFileSize(2097152);
        $photo->addValidator('Count', false, 1);
        $photo->addValidator('Size', false, 2097152);
        $photo->addValidator('Extension', false, 'jpg,jpeg,png,gif');

        $this->addElementPrefixPath('My_Decorator', 'My/Decorator/', 'decorator');

        $checkboxDecorator = array(
            'ViewHelper',
            'MyLabel', // Your new class
            'Errors',
            array('HtmlTag', array('tag' => 'div', 'class' => 'controls')),
            array(
                'decorator' => array('Holder' => 'HtmlTag'),
                'options'   => array(
                    'tag'   => 'div',
                    'class' => 'control-group'
                )
            )
        );
        $this->getElement('no_accident')->setDecorators($checkboxDecorator);
        $this->getElement('exchange')->setDecorators($checkboxDecorator);

	}//init

    public function isValid($data) {

        $tr = Zend_Registry::get('Zend_Translate');

        if ($this->model && isset($data['make'])) {

            $this->model->setAttrib('disabled',null);

            $modelsArr = array();
            $cars = new Model_Cars_Cars();

            $models = $cars->getCarModels($data['make'],true);
            $modelsArr = $this->array_merge_keys($modelsArr, $models);

            $this->model->setMultiOptions($modelsArr);

        }


        if(isset($data['exchange']) &&  $data['exchange'] == 1) {

            $this->exchange_makes->setAttrib('disabled',null);

        }

        if (isset($data['exchange_makes']) && is_array($data['exchange_makes'])) {

            $this->exchange_models->setAttrib('disabled',null);

            $modelsArr = array();
            $cars = new Model_Cars_Cars();
            foreach($data['exchange_makes'] as $make) {
                $models = $cars->getCarModels($make,true);
                $modelsArr = $this->array_merge_keys($modelsArr, $models);
            }
            $this->exchange_models->setMultiOptions($modelsArr);

        }
        return parent::isValid($data);
    }

}