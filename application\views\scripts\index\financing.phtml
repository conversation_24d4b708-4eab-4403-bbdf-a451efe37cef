</div>

<div id="financing">
    <div class="container">
        <div class="row py-4">
            <div class="col-lg-12">

                <div class="slogan">Wszystkich formalności dokonają Państwo kompleksowo u nas w komisie.</div>

            </div>
        </div>
    </div>
    <div class="container gray-background py-4">
        <div class="row ">
            <div class="col-lg-12">

                <h1 class="title">
                    <?php
                    $text = explode(" ", $this->translate->_('FINANCE_CAR'));
                    echo $text[0] . ' <span class="orange-color">' . $text[1] . '</span>';
                    ?>
                </h1>

            </div>

            <div class="col-lg-12">
                Wypełni formularz abyśmy mogili dopasować i wyliczyć najlepszą formę finansowania dla Ciebie.
            </div>
        </div>
        <div class="row pt-4">
            <div class="col-lg-6">


                <form action="<?php echo $this->financingForm->getAction() ?>"
                      enctype="<?php echo $this->financingForm->getEnctype() ?>"
                      method="<?php echo $this->financingForm->getMethod() ?>"
                      class="financing-form form-style-aa" >

                    <div class="row form-group">
                        <div class="col-lg-6<?= $this->financingForm->phone->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->phone->renderViewHelper() ?>
                            <?php if($this->financingForm->phone->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->phone->renderErrors() ?></div>
                            <?endif ?>
                        </div>
                        <div class="col-lg-6<?=$this->financingForm->email->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->email->renderViewHelper() ?>
                            <?php if($this->financingForm->email->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->email->renderErrors() ?></div>
                            <?endif ?>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-lg-6<?= $this->financingForm->first_name->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->first_name->renderViewHelper() ?>
                            <?php if($this->financingForm->first_name->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->first_name->renderErrors() ?></div>
                            <?endif ?>
                        </div>
                        <div class="col-lg-6<?=$this->financingForm->last_name->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->last_name->renderViewHelper() ?>
                            <?php if($this->financingForm->last_name->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->last_name->renderErrors() ?></div>
                            <?endif ?>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-lg-6<?= $this->financingForm->make->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->make->renderViewHelper() ?>
                            <?php if($this->financingForm->make->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->make->renderErrors() ?></div>
                            <?endif ?>
                        </div>
                        <div class="col-lg-6<?=$this->financingForm->model->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->model->renderViewHelper() ?>
                            <?php if($this->financingForm->model->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->model->renderErrors() ?></div>
                            <?endif ?>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-lg-12<?= $this->financingForm->message->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->message->renderViewHelper() ?>
                            <?php if($this->financingForm->message->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->message->renderErrors() ?></div>
                            <?endif ?>
                        </div>

                    </div>

                    <div class="row form-group">
                         <div class="col-lg-12<?= $this->financingForm->type->hasErrors() ? ' has-danger' : '' ?>">
                            <div class="form-check form-check-inline">

                                <?= $this->financingForm->type->renderViewHelper() ?>
                                <?php if($this->financingForm->type->hasErrors()): ?>
                                    <div class="form-control-feedback"><?= $this->financingForm->type->renderErrors() ?></div>
                                <?endif ?>
                            </div>

                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-lg-6<?= $this->financingForm->price->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->price->renderViewHelper() ?>
                            <?php if($this->financingForm->price->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->price->renderErrors() ?></div>
                            <?endif ?>
                        </div>
                        <div class="col-lg-6<?=$this->financingForm->contribution->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->contribution->renderViewHelper() ?>
                            <?php if($this->financingForm->contribution->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->contribution->renderErrors() ?></div>
                            <?endif ?>
                        </div>
                    </div>


                    <div class="row form-group">
                        <div class="col-lg-12 <?= $this->financingForm->captcha->hasErrors() ? ' has-danger' : '' ?>">
                            <?= $this->financingForm->captcha->renderCaptcha() ?>
                            <?php if($this->financingForm->captcha->hasErrors()): ?>
                                <div class="form-control-feedback"><?= $this->financingForm->captcha->renderErrors() ?></div>
                            <?endif ?>
                        </div>
                    </div>

                    <?= $this->financingForm->financing->renderViewHelper() ?>
                    <div class="row form-group">
                        <div class="col-lg-6">
                            <?= $this->financingForm->submit->renderViewHelper() ?>
                        </div>
                    </div>



                </form>
            </div>
            <div class="col-lg-6">
                <div class="calculator-background">

                    <div class="calculator-title d-flex align-items-center">
                        <div><i class="fa fa-calculator" aria-hidden="true"></i></div>
                        <div>
                            <?= $this->translate->_('WHAT_CAN_I_AFFORD') ?>
                        </div>
                    </div>
                    <form action="<?php echo $this->form->getAction() ?>"
                          enctype="<?php echo $this->form->getEnctype() ?>"
                          method="<?php echo $this->form->getMethod() ?>"
                          id="<?php echo $this->form->getId() ?>"
                          class="form-style-aa pt-4">
                            <?php foreach($this->form->getElements() as $element): ?>
                                <?php if($element->getType() != 'Zend_Form_Element_Submit') : ?>
                                    <?php if(in_array($element->getType(), array('Zend_Form_Element_Hidden','Zend_Form_Element_Hash')) ) : ?>
                                        <?= $element->renderViewHelper() ?>
                                    <?php else: ?>
                                        <div class="form-group<?=$element->hasErrors() ? ' has-danger' : '' ?>">

                                            <?php if($element->getType() == 'Zend_Form_Element_Captcha') : ?>
                                                <?= $element->renderCaptcha() ?>
                                            <?php else: ?>
                                                <label class="form-control-label" for="<?= $element->getId() ?>"><?= $element->getLabel() ?></label>
                                                <?= $element->renderViewHelper() ?>
                                            <?endif;?>
                                            <?php if($element->hasErrors()): ?>
                                                <div class="form-control-feedback"><?= $element->renderErrors() ?></div>
                                            <?endif ?>
                                        </div>
                                    <?endif;?>
                                <?endif;?>
                            <?php endforeach; ?>

                            <?= $this->form->submit->renderViewHelper() ?>
                    </form>
                        <?php if ($this->calc): ?>
                            <div class="calc-result pt-3">
                                <?php if ($this->calcType == 'leasing'): ?>
                                    <div class="calc-title"><?= $this->translate->_('CALCULATION_MAX_NET') ?>:</div>
                                    <div class="calc-price mt-3"><?= $this->price($this->calc['net'], $this->language_row) ?><sup>*</sup></div>
                                <?php else: ?>
                                    <div class="calc-title"><?= $this->translate->_('CALCULATION_MAX_GROSS') ?>:</div>
                                    <div class="calc-price mt-3"><?= $this->price($this->calc['gross'], $this->language_row) ?><sup>*</sup></div>
                                <?php endif ?>

                            </div>

                            <?php if ($this->searchForm): ?>

                                <form action="<?php echo $this->searchForm->getAction() ?>"
                                      enctype="<?php echo $this->searchForm->getEnctype() ?>"
                                      method="<?php echo $this->searchForm->getMethod() ?>"
                                        class="pt-4">
                                     <?= $this->searchForm->price_min->renderViewHelper()?>
                                     <?= $this->searchForm->price_max->renderViewHelper()?>
                                     <?= $this->searchForm->search_by_price_gross->renderViewHelper()?>
                                     <?= $this->searchForm->submit->renderViewHelper()?>
                                </form>

                            <?php endif ?>

                            <div class="calc-disclaimer pt-3">
                                <span>*</span> <?= $this->translate->_('CALCULATOR_DISCLAIMER') ?>
                            </div>
                        <?php endif; ?>


                </div>


            </div>
        </div>
        <?php if($this->translate->_('FINANCING_INFO') != 'FINANCING_INFO'): ?>
        <div class="row pt-4">
            <div class="col-lg-12">
               
                <?= $this->translate->_('FINANCING_INFO') ?>
              
            </div>
        </div>
        <?php endif;?>
        <?php if($this->translate->_('LOAN_1') != 'LOAN_1' || $this->translate->_('LOAN_2') != 'LOAN_2' ||
            $this->translate->_('LEASING_1') != 'LEASING_1' || $this->translate->_('LEASING_2') != 'LEASING_2'): ?>
            <div class="row pt-4">
                <?php if($this->translate->_('LOAN_1') != 'LOAN_1' || $this->translate->_('LOAN_2') != 'LOAN_2'): ?>
                    <div class="col-lg-6">
                        <?php if($this->translate->_('LOAN_1') != 'LOAN_1'): ?>
                            <div> <?= $this->translate->_('LOAN_1') ?> </div>
                        <?php endif;?>

                        <?php if($this->translate->_('LOAN_2') != 'LOAN_2'): ?>
                            <div class="pt-4"> <?= $this->translate->_('LOAN_2') ?> </div>
                        <?php endif;?>
                    </div>
                <?php endif;?>
                <?php if($this->translate->_('LEASING_1') != 'LEASING_1' || $this->translate->_('LEASING_2') != 'LEASING_2'): ?>
                    <div class="col-lg-6">
                        <?php if($this->translate->_('LEASING_1') != 'LEASING_1'): ?>
                            <div> <?= $this->translate->_('LEASING_1') ?> </div>
                        <?php endif;?>

                        <?php if($this->translate->_('LEASING_2') != 'LEASING_2'): ?>
                            <div class="pt-4"> <?= $this->translate->_('LEASING_2') ?> </div>
                        <?php endif;?>

                    </div>
                <?php endif;?>
            </div>
        <?php endif;?>
    </div>

</div>

<script type="text/javascript" charset="utf-8">
	$(function(){
		$("#content_financing [name='type']").live('change', function(){
			$.getJSON(
				"<?= $this->url(array('language' => $this->language, 'controller' => 'index', 'action' => 'financing-get-reverse-form'), 'general', true) ?>?" + $(this).closest('form').serialize(),
				null,
				function(data) {
					if (data.html) {
						$("#reverse_form").replaceWith('<div id="reverse_form">' + data.html + '</div>');
					}
				}
			);
		});
	});
</script>