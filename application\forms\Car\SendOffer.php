<?php

class Form_Car_SendOffer extends My_Form {

    protected $_isSalesman = false;

	public function init() {
		$tr = Zend_Registry::get('Zend_Translate');
		
		$this->addElements(array(
			new Zend_Form_Element_Text('sender_email', array(
				'label'	=>	'YOUR_EMAIL',
                'attribs' => array('class' => 'form-control'),
				'required' => true,
				'validators' => array(new Zend_Validate_EmailAddress())
			)),
			new Zend_Form_Element_Text('recipient_email', array(
				'label'	=>	'RECIPIENT_EMAIL',
                'attribs' => array('class' => 'form-control'),
				'required' => true,
				'validators' => array(new Zend_Validate_EmailAddress())
			)),
			new Zend_Form_Element_Textarea('message', array(
				'label'	=>	'SEND_OFFER_MESSAGE',
                'attribs' => array('rows' => 5, 'cols' => 40,'class' => 'form-control'),
				'placeholder' => $tr->_('SEND_OFFER_DEFAULT_MESSAGE')
			)),
            new Zend_Form_Element_Captcha('captcha', array(
                'label' => 'CAPTCHA',
                'captcha' => new My_Captcha_ReCaptcha(array(
                    'siteKey'  => '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-',
                    'secretKey' => '6Lfx7A0UAAAAAL41t8hFSPCzuh6VQGhcpXttwrJd'
                )),
            )),
            new Zend_Form_Element_Textarea('comment', array(
                'label'	=>	'SEARCH_NAME',
                'attribs' => array('class' => 'form-control'),
                'value' => '',
                'required' => true,
            )),

			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label' => 'SEND_OFFER_SUBMIT',
                'attribs' => array('class' => 'btn btn-action btn-action-orange'),
			)),
		));
		
		if (Zend_Auth::getInstance()->hasIdentity()) {
			$this->removeElement('captcha');
		}

        if(!$this->_isSalesman) {
            $this->removeElement('comment');
        }
		
		parent::init();
	}//init

    public function setIsSalesman($value) {
        $this->_isSalesman = $value;
    }

}