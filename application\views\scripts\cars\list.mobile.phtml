

<div id="search_results" class="column_right_1">
	
	<?php if ($this->suggestedModels): ?>
		<div id="suggested_models">
			<div class="title">
				<?= $this->translate->_('MODELS') ?>
			</div>


			<?php foreach ($this->suggestedModels as $key => $value): ?>
				<a class="suggested_model" href="<?= $this->url(array('model' => $key)) . (!empty($this->queryStringArray) ? '?'.http_build_query($this->queryStringArray) : '') ?>"><span class="for_icon">&nbsp;</span><?= $value ?></a>
			<?php endforeach ?>
			
			<div class="clear"></div>
		</div>
	<?php endif ?>


	
	
	<script type="text/javascript">
		function switchLocation(id) {
			$("#full_form [name='location']").val(id).closest('form').find("[type='submit']").click();
		}
		
		function switchOrder(id) {
			var queryString = <?php echo Zend_json::encode((isset($this->query) && $this->query ? $this->query : null)) ?>;
			if (queryString) {
				$("#full_form [name='query']").val(queryString);
			}
			$("#full_form [name='order']").val(id).closest('form').find("[type='submit']").click();
		}
		
		$(function(){
			$("#sort_filter").show();
		});
	</script>

	<div id="sort_filter" style="display: none;">
		<div class="">


		<script type="text/javascript" charset="utf-8">
			function autoauto_switch_order(el, nosubmit) {
				var id = el.data('order_id')
				$("#order_select .item").hide().removeClass("active").next('br').removeClass("active").hide();
				el.show();
				el.addClass("active");
				if (typeof nosubmit == "undefined" || !nosubmit) {
					switchOrder(id);
				}
			}
			
			function autoauto_switch_order_toggle(show) {
				if (show) {
					var t = $("#order_select");
					t.css('height', 'auto');
					t.find(".item").show().next('br').show();
					t.css('background', '#f6f6f6');
					t.data('is_open', true);
				}
				else {
					var t = $("#order_select");
					t.css('background', 'transparent');
					t.data('is_open', false);
					$("#order_select .item").not(".active").hide().next('br').hide();
					$("#order_select").data('is_open', false);
				}
			}
			
			$(function(){
				$("body").click(function(e){
					if (($(e.target).closest("#order_select").length == 0)) {
						autoauto_switch_order_toggle(false);
					}
				});
				
				$("#order_select .item").each(function(){
					var t = $(this);
					var id = t.attr('id');
					var locId = id.split('_');
					locId = locId[2];
					t.data('order_id', locId);
				});
				
				
				$("#order_select .item")
					.click(function(){
						var t = $(this);
						if (t.closest("#order_select").data('is_open')) {
							autoauto_switch_order(t);
							autoauto_switch_order_toggle(false);
						}
						else {
							t.siblings().show();
							autoauto_switch_order_toggle(true);
						}
					})
				;
				
				$("#order_select > .for_icon").click(function(){
					$("#order_select .item:first").click();
				});
				
				autoauto_switch_order($("#order_switch_0"), true);
				autoauto_switch_order_toggle(false);
				
				<? if ($this->searchParameters && array_key_exists('order', $this->searchParameters) && $this->searchParameters['order']) : ?>
					autoauto_switch_order($("#order_switch_<?= $this->searchParameters['order'] ?>"), true);
				<? endif; ?>
			});
		</script>
		</div>
        <div id="location-select">
            <strong><?= $this->translate->_('LOCATION') ?>:</strong>
            <a href="# " onclick="$('#location-list').toggle(); return false;"  class="location location_<?= $this->locationGroup['id'] ?>"><?= $this->locationGroup['name'] ?> </a><span class="for_icon">&nbsp;</span>
            <ul id="location-list">
                <li><a href="<?= $this->url() . '?hash=' . $this->hash . '&amp;removeName=location&amp;' ?>" class="location location_0"><?= $this->translate->_('LOCATION_ANY') ?></a></li>
            <?php foreach ($this->locationGroups as $id => $name): ?>
                <li><a href="<?= $this->url() . '?hash=' . $this->hash . '&amp;addName[]=location&amp;addValue_0=' . $id ?>" class="location location_<?= $id ?>"><?= $this->escape($name) ?></a></li>
            <?php endforeach ?>
            </ul>
        </div>

	</div>
	
	<table id="results_top_paginator" cellspacing="0" cellpadding="0">
		<tr>

			<td class="middle">
				<?php if ($this->paginator && $this->paginator->getTotalItemCount() > $this->paginator->getItemCountPerPage()): ?>
					<?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.mobile.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''), 'carListShowMax' => $this->pagingMaxResults, 'carListShowDefault' => $this->pagingDefaultResults, 'translate' => $this->translate,'queryStringArray' => $this->queryStringArray)); ?>
				<?php else: ?>
					<div class="paginator"><div class="paginator_inner"></div></div>
				<?php endif; ?>
			</td>
            <td class="right">

                <div id="order_select">
                    <span class="for_icon">&nbsp;</span>

                    <div style="display:none;" class="item" id="order_switch_0"><?= $this->translate->_('ORDER_BY') ?></div>

                    <div style="display:none;" class="item" id="order_switch_1"><?= $this->translate->_('DATE_ADDED') ?><span class="for_icon desc">&nbsp;</span></div>
                    <div style="display:none;" class="item" id="order_switch_2"><?= $this->translate->_('DATE_ADDED') ?><span class="for_icon asc">&nbsp;</span></div>
                    <div style="display:none;" class="item" id="order_switch_3"><?= $this->translate->_('PRICE_SHORT') ?><span class="for_icon asc">&nbsp;</span></div>
                    <div style="display:none;" class="item" id="order_switch_4"><?= $this->translate->_('PRICE_SHORT') ?><span class="for_icon desc">&nbsp;</span></div>
                    <div style="display:none;" class="item" id="order_switch_5"><?= $this->translate->_('MAKE') ?><span class="for_icon asc">&nbsp;</span></div>
                    <div style="display:none;" class="item" id="order_switch_6"><?= $this->translate->_('MAKE') ?><span class="for_icon desc">&nbsp;</span></div>
                    <div style="display:none;" class="item" id="order_switch_7"><?= $this->translate->_('PRODUCTION_YEAR') ?><span class="for_icon asc">&nbsp;</span></div>
                    <div style="display:none;" class="item" id="order_switch_8"><?= $this->translate->_('PRODUCTION_YEAR') ?><span class="for_icon desc">&nbsp;</span></div>

                </div>
                <?php if (Zend_Auth::getInstance()->hasIdentity() && !$this->searchStored): ?>
                    <?php if (in_array(Zend_Auth::getInstance()->getIdentity()->role, array("user", "salesman")) == "user"): ?>
                        <a id="add_to_newsletter" href="#save-form" rel="prettyPhoto" title=""><span class="for_icon">&nbsp;</span></a>
                    <?php endif ?>
                <?php else: ?>
                    <a id="add_to_newsletter" href="#save-form" rel="prettyPhoto" title=""><span class="for_icon">&nbsp;</span></a>
                <?php endif ?>

            </td>
			
			</tr>
	</table>
	
	<?php if ($this->paginator): ?>
		<?php $index = 1; foreach($this->paginator as $car): ?>
			<?= $this->partial('car_search_item.mobile.phtml', array('car' => $car, 'translate' => $this->translate, 'language' => $this->language, 'language_row' => $this->language_row, 'hash' => ($this->hash ? '?hash=' . $this->hash . '&i=' . (($this->page - 1) * $this->paginator->getItemCountPerPage() + $index) : ''), 'page' => $this->page, 'perPage' => $this->perPage, 'domain' => $this->domain, 'searchParameters' => $this->searchParameters, 'siteVariant' => $this->siteVariant)) ?>
			<?php if ($index % 5 == 0 || $index == $this->paginator->getCurrentItemCount()): ?>
        <div class="car_search_item car_search_item_newsletter">
            <table class="container">
                <tr>
                    <td class="heading">
                        <span class="for_icon">&nbsp;</span>
                        <?= $this->translate->_('CAR_SEARCH_NEWSLETTER_HEADING') ?>
                    </td>
                    <td class="text">
                        <div class="text_1">
                            <?= $this->translate->_('CAR_SEARCH_NEWSLETTER_TEXT_1') ?>
                        </div>

                        <div class="text_2">
                            <?= $this->translate->_('CAR_SEARCH_NEWSLETTER_TEXT_2') ?>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
			<?php endif ?>
		<?php $index++; endforeach; ?>
		<script type="text/javascript">
			$(function(){
				$(".car_search_item_newsletter").css('cursor', 'pointer').click(function(){
					$("#add_to_newsletter").click();
				});
		
				$(".pp_html .form_label.for_captcha").hide();
				$("input[name='captcha[input]']").before("<br />");
			});
		</script>
		
		<?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator.mobile.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''), 'carListShowMax' => $this->pagingMaxResults, 'carListShowDefault' => $this->pagingDefaultResults, 'translate' => $this->translate,'queryStringArray' => $this->queryStringArray)); ?>
	<?php endif ?>
	
</div>

<div class="clear"></div>

<script type="text/javascript">
$(function () {
      
      setInterval(function(){
      
        if("rgb(246, 120, 18)" == $(".car_search_item_newsletter .heading").css('color'))
            $(".car_search_item_newsletter .heading").css('color', 'rgb(255,0,0)');
        else if("rgb(255, 0, 0)" == $(".car_search_item_newsletter .heading").css('color'))
            $(".car_search_item_newsletter .heading").css('color', 'rgb(246, 120, 18)');  
        }
    ,1500);
      
      
});

</script>