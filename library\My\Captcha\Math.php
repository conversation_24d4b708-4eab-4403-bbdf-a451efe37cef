<?php

require_once 'Zend/Captcha/Base.php';

class My_Captcha_Math extends Zend_Captcha_Base
{

    protected $_id;

    /**
     * Generated operation and result
     *
     * @var string
     */
    protected $_operation;
    protected $_result;

    /**
     * Session
     *
     * @var Zend_Session_Namespace
     */
    protected $_session;

    /**
     * Class name for sessions
     *
     * @var string
     */
    protected $_sessionClass = 'Zend_Session_Namespace';

    
    protected $_timeout = 300;

    protected $_placeholder = false;

    /**
     * Should generate() keep session or create a new one?
     *
     * @var boolean
     */
    protected $_keepSession = false;

    /**#@+
     * Error codes
     */
    const MISSING_VALUE = 'missingValue';
    const MISSING_ID    = 'missingID';
    const BAD_CAPTCHA   = 'badCaptcha';
    /**#@-*/

    /**
     * Error messages
     * @var array
     */
    protected $_messageTemplates = array(
        self::MISSING_VALUE => 'Empty captcha value',
        self::MISSING_ID    => 'Captcha ID field is missing',
        self::BAD_CAPTCHA   => 'Captcha value is wrong',
    );

    /**
     * Retrieve session class to utilize
     *
     * @return string
     */
    public function getSessionClass()
    {
        return $this->_sessionClass;
    }

    /**
     * Set session class for persistence
     *
     * @param  string $_sessionClass
     * @return Zend_Captcha_Word
     */
    public function setSessionClass($_sessionClass)
    {
        $this->_sessionClass = $_sessionClass;
        return $this;
    }

    /**
     * Retrieve captcha ID
     *
     * @return string
     */
    public function getId ()
    {
        if (null === $this->_id) {
            $this->_setId($this->_generateRandomId());
        }
        return $this->_id;
    }

    /**
     * Set captcha identifier
     *
     * @param string $id
     * return Zend_Captcha_Word
     */
    protected function _setId ($id)
    {
        $this->_id = $id;
        return $this;
    }

    /**
     * Set timeout for session token
     *
     * @param  int $ttl
     * @return Zend_Captcha_Word
     */
    public function setTimeout($ttl)
    {
        $this->_timeout = (int) $ttl;
        return $this;
    }

    /**
     * Get session token timeout
     *
     * @return int
     */
    public function getTimeout()
    {
        return $this->_timeout;
    }

    /**
     * @return boolean
     */
    public function isPlaceholder()
    {
        return $this->_placeholder;
    }

    /**
     * @param boolean $placeholder
     */
    public function setPlaceholder($placeholder)
    {
        $this->_placeholder = $placeholder;
        return $this;
    }


	/**
	 * Sets if session should be preserved on generate()
	 *
	 * @param $keepSession Should session be kept on generate()?
	 * @return Zend_Captcha_Word
	 */
	public function setKeepSession($keepSession)
	{
		$this->_keepSession = $keepSession;
		return $this;
	}
    
    /**
     * Get session object
     *
     * @return Zend_Session_Namespace
     */
    public function getSession()
    {
        if (!isset($this->_session) || (null === $this->_session)) {
            $id = $this->getId();
            if (!class_exists($this->_sessionClass)) {
                require_once 'Zend/Loader.php';
                Zend_Loader::loadClass($this->_sessionClass);
            }
            $this->_session = new $this->_sessionClass('Zend_Form_Captcha_' . $id);
            $this->_session->setExpirationHops(1, null, true);
            $this->_session->setExpirationSeconds($this->getTimeout());
        }
        return $this->_session;
    }

    /**
     * Set session namespace object
     *
     * @param  Zend_Session_Namespace $session
     * @return Zend_Captcha_Word
     */
    public function setSession(Zend_Session_Namespace $session)
    {
        $this->_session = $session;
        if($session) {
            $this->_keepSession = true;
        }
        return $this;
    }

    /**
     * Get captcha operation
     *
     * @return string
     */
    public function getOperation()
    {
        if (empty($this->_operation)) {
            $session     = $this->getSession();
            $this->_operation = $session->operation;
        }
        return $this->_operation;
    }

    /**
     * Set captcha operation
     *
     * @param  string $operation
     * @return Zend_Captcha_Word
     */
    protected function _setOperation($operation)
    {
        $session       = $this->getSession();
        $session->operation = $operation;
        $this->_operation   = $operation;
        return $this;
    }
    
    public function getResult()
    {
    	if (empty($this->_result)) {
    		$session = $this->getSession();
    		$this->_result = $session->result;
    	}
    	return $this->_result;
    }
    
    protected function _setResult($result)
    {
        $session       = $this->getSession();
        $session->result = (int)$result;
        $this->_result   = $result;
        return $this;
    }

    /**
     * Generate new random operation
     *
     * @return string
     */
    protected function _generateOperation()
    {
        $operators = array('+', '-');
        $operator = $operators[mt_rand(0,1)];
        
        $number1 = mt_rand(4, 6);
        $number2 = mt_rand(1, $number1 - 1);

		$operation = $number1 . ' ' . $operator . ' ' . $number2;

        return $operation;
    }

    /**
     * Generate new session ID and new operation
     *
     * @return string session ID
     */
    public function generate()
    {
        if(!$this->_keepSession) {
            $this->_session = null;
        }
        $id = $this->_generateRandomId();
        $this->_setId($id);
        $operation = $this->_generateOperation();
        $this->_setOperation($operation);
        eval('$result = ' . $operation . ';'); //eval is evil! remove this line ASAP!
        $this->_setResult($result);
        return $id;
    }

    protected function _generateRandomId()
    {
        return md5(mt_rand(0, 1000) . microtime(true));
    }

    /**
     * Validate the operation
     *
     * @see    Zend_Validate_Interface::isValid()
     * @param  mixed $value
     * @return boolean
     */
    public function isValid($value, $context = null)
    {
        if (!is_array($value) && !is_array($context)) {
            $this->_error(self::MISSING_VALUE);
            return false;
        }
        if (!is_array($value) && is_array($context)) {
            $value = $context;
        }

        $name = $this->getName();

        if (isset($value[$name])) {
            $value = $value[$name];
        }

        if (!isset($value['input'])) {
            $this->_error(self::MISSING_VALUE);
            return false;
        }
        $input = strtolower($value['input']);
        $this->_setValue($input);

        if (!isset($value['id'])) {
            $this->_error(self::MISSING_ID);
            return false;
        }

        $this->_id = $value['id'];
        if ((int)$input !== (int)$this->getResult()) {
            $this->_error(self::BAD_CAPTCHA);
            return false;
        }

        return true;
    }

    /**
     * Get captcha decorator
     *
     * @return string
     */
    public function getDecorator()
    {
        return "Captcha_Word";
    }
    
    public function render(Zend_View_Interface $view = null, $element = null)
    {
    	$ret = $this->getOperation();
    	if ($element) {
            if($this->isPlaceholder()) {
                $element->setAttribs(array('placeholder' => $element->getLabel() . ' ' . $ret));
                return;
            } else {
                $ret = '<span class="captcha_text">' . $element->getLabel() . ' ' . $ret . '</span>';
            }


    	}
        return $ret;
    }
}
