%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(TM) 3.2
%%AI8_CreatorVersion: 12.0.0
%%For: (fluxus) (x)
%%Title: (bug.eps)
%%CreationDate: 4/15/2006 11:11 PM
%%BoundingBox: -2 747 53 843
% -2 747 53 843
%%DocumentProcessColors: <PERSON>an <PERSON> Black
%%DocumentSuppliedResources: procset Adobe_packedarray 2.0 0
%%+ procset Adobe_cmykcolor 1.1 0
%%+ procset Adobe_cshow 1.1 0
%%+ procset Adobe_customcolor 1.0 0
%%+ procset Adobe_pattern_AI3 1.0 0
%%+ procset Adobe_Illustrator_AI3 1.0 1
%AI3_ColorUsage: Color
%AI3_IncludePlacedImages
%%CMYKCustomColor: 1 1 1 1 ([Registration])
%AI3_TemplateBox: 298.5 420.3896 298.5 420.3896
%AI3_TileBox: 0.157715 0.044861 595.1177 841.9648
%AI3_DocumentPreview: Header
%%PageOrigin:-32 11.8896
%AI7_GridSettings: 72 8 72 8 1 0 0.8 0.8 0.8 0.9 0.9 0.9
%AI9_Flatten: 1
%AI12_CMSettings: 00.MS
%%EndComments
%%BeginProlog
%%BeginResource: procset Adobe_packedarray 2.0 0
%%Title: (Packed Array Operators)
%%Version: 2.0 0
%%CreationDate: (8/2/90) ()
%%Copyright: ((C) 1987-1996 Adobe Systems Incorporated All Rights Reserved)
userdict /Adobe_packedarray 5 dict dup begin put
/initialize
{
/packedarray where
	{
	pop
	}
	{
	Adobe_packedarray begin
	Adobe_packedarray
		{
		dup xcheck
			{
			bind
			} if
		userdict 3 1 roll put
		} forall
 end
	} ifelse
} def
/terminate
{
} def
/packedarray
{
array astore readonly
} def
/setpacking
{
pop
} def
/currentpacking
{
false
} def
currentdict readonly pop end
%%EndResource
Adobe_packedarray /initialize get exec
%%BeginResource: procset Adobe_cmykcolor 1.1 0
%%Title: (CMYK Color Operators)
%%Version: 1.1 0
%%CreationDate: (1/23/89) ()
%%Copyright: ((C) 1987-1996 Adobe Systems Incorporated All Rights Reserved)
currentpacking true setpacking
userdict /Adobe_cmykcolor 4 dict dup begin put
/initialize
{
/setcmykcolor where
	{
	pop
	}
	{
	userdict /Adobe_cmykcolor_vars 2 dict dup begin put
	/_setrgbcolor
		/setrgbcolor load def
	/_currentrgbcolor
		/currentrgbcolor load def
	Adobe_cmykcolor begin
	Adobe_cmykcolor
		{
		dup xcheck
			{
			bind
			} if
		pop pop
		} forall
 end
 end
	Adobe_cmykcolor begin
	} ifelse
} def
/terminate
{
currentdict Adobe_cmykcolor eq
	{
 end
	} if
} def
/setcmykcolor
{
1 sub 4 1 roll
3
	{
	3 index add neg dup 0 lt
		{
		pop 0
		} if
	3 1 roll
	} repeat
Adobe_cmykcolor_vars /_setrgbcolor get exec
pop
} def 
/currentcmykcolor
{
Adobe_cmykcolor_vars /_currentrgbcolor get exec
3
	{
	1 sub neg 3 1 roll
	} repeat
0
} def
currentdict readonly pop end
setpacking
%%EndResource
%%BeginResource: procset Adobe_cshow 1.1 0
%%Title: (cshow Operator)
%%Version: 1.1 0
%%CreationDate: (1/23/89) ()
%%Copyright: ((C) 1987-1996 Adobe Systems Incorporated All Rights Reserved)
currentpacking true setpacking
userdict /Adobe_cshow 3 dict dup begin put
/initialize
{
/cshow where
	{
	pop
	}
	{
	userdict /Adobe_cshow_vars 1 dict dup begin put
	/_cshow
		{} def
	Adobe_cshow begin
	Adobe_cshow
		{
		dup xcheck
			{
			bind
			} if
		userdict 3 1 roll put
		} forall
 end
 end
	} ifelse
} def
/terminate
{
} def
/cshow
{
exch
Adobe_cshow_vars
	exch /_cshow
	exch put
	{
	0 0 Adobe_cshow_vars /_cshow get exec
	} forall
} def
currentdict readonly pop end
setpacking
%%EndResource
%%BeginResource: procset Adobe_customcolor 1.0 0
%%Title: (Custom Color Operators)
%%Version: 1.0 0
%%CreationDate: (5/9/88) ()
%%Copyright: ((C) 1987-1996 Adobe Systems Incorporated All Rights Reserved)
currentpacking true setpacking
userdict /Adobe_customcolor 5 dict dup begin put
/initialize
{
/setcustomcolor where
	{
	pop
	}
	{
	Adobe_customcolor begin
	Adobe_customcolor
		{
		dup xcheck
			{
			bind
			} if
		pop pop
		} forall
 end
	Adobe_customcolor begin
	} ifelse
} def
/terminate
{
currentdict Adobe_customcolor eq
	{
 end
	} if
} def
/findcmykcustomcolor
{
5 packedarray
}  def
/setcustomcolor
{
exch
aload pop pop
4
	{
	4 index mul 4 1 roll
	} repeat
5 -1 roll pop
setcmykcolor
} def
/setoverprint
{
pop
} def
currentdict readonly pop end
setpacking
%%EndResource
%%BeginResource: procset Adobe_pattern_AI3 1.1 0
%%Title: (Adobe Illustrator (R) Version 3.0 Pattern Operators)
%%Version: 1.1 0
%%CreationDate: (7/21/89) ()
%%Copyright: ((C) 1987-1996 Adobe Systems Incorporated All Rights Reserved)
currentpacking true setpacking
userdict /Adobe_pattern_AI3 16 dict dup begin put
/initialize
{
/definepattern where
	{
	pop
	}
	{
	Adobe_pattern_AI3 begin
	Adobe_pattern_AI3
		{
		dup xcheck
			{
			bind
			} if
		pop pop
		} forall
	mark
	cachestatus 7 1 roll pop pop pop pop exch pop exch
		{
		{
		10000 add
		dup 2 index gt
			{
			exit
			} if
		dup setcachelimit
		} loop
		} stopped
	cleartomark
	} ifelse
} def
/terminate
{
currentdict Adobe_pattern_AI3 eq
	{
 end
	} if
} def
errordict
/nocurrentpoint
{
pop
stop
} put
errordict
/invalidaccess
{
pop
stop
} put
/patternencoding
256 array def
0 1 255
{
patternencoding exch ( ) 2 copy exch 0 exch put cvn put
} for
/definepattern
{
17 dict begin
/uniform exch def
/cache exch def
/key exch def
/procarray exch def
/mtx exch matrix invertmatrix def
/height exch def
/width exch def
/ctm matrix currentmatrix def
/ptm matrix def
/str 32 string def
/slice 9 dict def
slice /s 1 put
slice /q 256 procarray length div sqrt floor cvi put
slice /b 0 put
/FontBBox [0 0 0 0] def
/FontMatrix mtx matrix copy def
/Encoding patternencoding def
/FontType 3 def
/BuildChar
	{
	exch
 begin
	/setstrokeadjust where {pop true setstrokeadjust} if
	slice begin
	dup q dup mul mod s idiv /i exch def
	dup q dup mul mod s mod /j exch def
	q dup mul idiv procarray exch get
	/xl j width s div mul def
	/xg j 1 add width s div mul def
	/yl i height s div mul def
	/yg i 1 add height s div mul def
	uniform
		{
		1 1
		}
		{
		width 0 dtransform
		dup mul exch dup mul add sqrt dup 1 add exch div
		0 height dtransform
		dup mul exch dup mul add sqrt dup 1 add exch div
		} ifelse
	width 0 cache
		{
		xl 4 index mul yl 4 index mul xg 6 index mul yg 6 index mul
		setcachedevice
		}
		{
		setcharwidth
		} ifelse
	gsave
	scale
	newpath
	xl yl moveto
	xg yl lineto
	xg yg lineto
	xl yg lineto
	closepath
	clip
	newpath
 end
 end
	exec
	grestore
	} def
key currentdict definefont
end
} def
/patterncachesize
{
gsave
newpath
0 0 moveto
width 0 lineto
width height lineto
0 height lineto
closepath
patternmatrix setmatrix
pathbbox
exch ceiling 4 -1 roll floor sub 3 1 roll
ceiling exch floor sub
mul 1 add
grestore
} def
/patterncachelimit
{
cachestatus 7 1 roll 6 npop 8 mul
} def
/patternpath
{
exch dup begin setfont
ctm setmatrix
concat
slice exch /b exch slice /q get dup mul mul put
FontMatrix concat
uniform
	{
	width 0 dtransform round width div exch round width div exch
	0 height dtransform round height div exch height div exch
	0 0 transform round exch round exch
	ptm astore setmatrix
	}
	{
	ptm currentmatrix pop
	} ifelse
{currentpoint} stopped not
	{
	2 npop
	pathbbox
	true
	4 index 3 index eq
	4 index 3 index eq
	and
		{
		pop false
			{
			{2 npop}
			{3 npop true}
			{7 npop true}
			{pop true}
			pathforall
			} stopped
			{
			5 npop true
			} if
		} if
		{
		height div ceiling height mul 4 1 roll
		width div ceiling width mul 4 1 roll
		height div floor height mul 4 1 roll
		width div floor width mul 4 1 roll
		2 index sub height div ceiling cvi exch
		3 index sub width div ceiling cvi exch
		4 2 roll moveto
		FontMatrix mtx invertmatrix
		dup dup 4 get exch 5 get rmoveto
		ptm ptm concatmatrix pop
		slice /s
		patterncachesize patterncachelimit div ceiling sqrt ceiling cvi
		dup slice /q get gt
			{
			pop slice /q get
			} if
		put
		0 1 slice /s get dup mul 1 sub
			{
			slice /b get add
			gsave
			0 1 str length 1 sub
				{
				str exch 2 index put
				} for
			pop
			dup
				{
				gsave
				ptm setmatrix
				1 index str length idiv {str show} repeat
				1 index str length mod str exch 0 exch getinterval show
				grestore
				0 height rmoveto
				} repeat
			grestore
			} for
		2 npop
		}
		{
		4 npop
		} ifelse
	} if
end
} def
/patternclip
{
clip
} def
/patternstrokepath
{
strokepath
} def
/patternmatrix
matrix def
/patternfill
{
dup type /dicttype eq
	{
	Adobe_pattern_AI3 /patternmatrix get
	} if
gsave
patternclip
Adobe_pattern_AI3 /patternpath get exec
grestore
newpath
} def
/patternstroke
{
dup type /dicttype eq
	{
	Adobe_pattern_AI3 /patternmatrix get
	} if
gsave
patternstrokepath
true
	{
		{
			{
			newpath 
			moveto
			}
			{
			lineto
			}
			{
			curveto
			}
			{
			closepath
			3 copy
			Adobe_pattern_AI3 /patternfill get exec
			} pathforall
		3 npop
		} stopped
			{
			5 npop
			patternclip
			Adobe_pattern_AI3 /patternfill get exec
			} if
	}
	{
	patternclip
	Adobe_pattern_AI3 /patternfill get exec
	} ifelse
grestore
newpath
} def
/patternashow
{
3 index type /dicttype eq
	{
	Adobe_pattern_AI3 /patternmatrix get 4 1 roll
	} if
	{
	2 npop (0) exch
	2 copy 0 exch put pop
	gsave
	false charpath currentpoint
	6 index 6 index 6 index
	Adobe_pattern_AI3 /patternfill get exec
	grestore
	newpath moveto
	2 copy rmoveto
	} exch cshow
5 npop
} def
/patternawidthshow
{
6 index type /dicttype eq
	{
	Adobe_pattern_AI3 /patternmatrix get 7 1 roll
	} if
	{
	2 npop (0) exch
	2 copy 0 exch put 
	gsave
	_sp eq {5 index 5 index rmoveto} if
	false charpath currentpoint
	9 index 9 index 9 index
	Adobe_pattern_AI3 /patternfill get exec
	grestore
	newpath moveto
	2 copy rmoveto
	} exch cshow
8 npop
} def
/patternashowstroke
{
4 index type /dicttype eq
	{
	patternmatrix /patternmatrix get 5 1 roll
	} if
4 1 roll
	{
	2 npop (0) exch
	2 copy 0 exch put pop
	gsave
	false charpath
	currentpoint
	4 index setmatrix
	7 index 7 index 7 index
	Adobe_pattern_AI3 /patternstroke get exec
	grestore
	newpath moveto
	2 copy rmoveto
	} exch cshow
6 npop
} def
/patternawidthshowstroke
{
7 index type /dicttype eq
	{
	patternmatrix /patternmatrix get 8 1 roll
	} if
7 1 roll
	{
	2 npop (0) exch
	2 copy 0 exch put
	gsave
	_sp eq {5 index 5 index rmoveto} if
	false charpath currentpoint
	7 index setmatrix
	10 index 10 index 10 index
	Adobe_pattern_AI3 /patternstroke get exec
	grestore
	newpath moveto
	2 copy rmoveto
	} exch cshow
9 npop
} def
currentdict readonly pop end
setpacking
%%EndResource
%%BeginResource: procset Adobe_Illustrator_AI3 1.1 0
%%Title: (Adobe Illustrator (R) Version 3.0 Full Prolog)
%%Version: 1.1 0
%%CreationDate: (3/7/1994) ()
%%Copyright: ((C) 1987-1996 Adobe Systems Incorporated All Rights Reserved)
currentpacking true setpacking
userdict /Adobe_Illustrator_AI3 71 dict dup begin put
/initialize
{
userdict /Adobe_Illustrator_AI3_vars 67 dict dup begin put
/_lp /none def
/_pf {} def
/_ps {} def
/_psf {} def
/_pss {} def
/_pjsf {} def
/_pjss {} def
/_pola 0 def
/_doClip 0 def
/cf	currentflat def
/_tm matrix def
/_renderStart [/e0 /r0 /a0 /o0 /e1 /r1 /a1 /i0] def 
/_renderEnd [null null null null /i1 /i1 /i1 /i1] def
/_render -1 def
/_rise 0 def
/_ax 0 def
/_ay 0 def
/_cx 0 def
/_cy 0 def
/_leading [0 0] def
/_ctm matrix def
/_mtx matrix def
/_sp 16#020 def
/_hyphen (-) def
/_fScl 0 def
/_cnt 0 def
/_hs 1 def
/_nativeEncoding 0 def
/_useNativeEncoding 0 def
/_tempEncode 0 def
/_pntr 0 def
/_tDict 2 dict def
/_wv 0 def
/Tx {} def
/Tj {} def
/CRender {} def
/_AI3_savepage {} def
/_gf null def
/_cf 4 array def
/_if null def
/_of false def
/_fc {} def
/_gs null def
/_cs 4 array def
/_is null def
/_os false def
/_sc {} def
/_pd 1 dict def
/_ed 15 dict def
/_pm matrix def
/_fm null def
/_fd null def
/_fdd null def
/_sm null def
/_sd null def
/_sdd null def
/_i null def
Adobe_Illustrator_AI3 begin
Adobe_Illustrator_AI3 dup /nc get begin
	{
	dup xcheck
		{
		bind
		} if
	pop pop
	} forall
end
end
end
Adobe_Illustrator_AI3 begin
Adobe_Illustrator_AI3_vars begin
newpath
} def
/terminate
{
end
end
} def
/_
null def
/ddef
{
Adobe_Illustrator_AI3_vars 3 1 roll put
} def
/xput
{
dup load dup length exch maxlength eq
	{
	dup dup load dup
	length 2 mul dict copy def
	} if
load begin def end
} def
/npop
{
	{
	pop
	} repeat
} def
/sw
{
dup length exch stringwidth
exch 5 -1 roll 3 index mul add
4 1 roll 3 1 roll mul add
} def
/swj
{
dup 4 1 roll
dup length exch stringwidth 
exch 5 -1 roll 3 index mul add
4 1 roll 3 1 roll mul add 
6 2 roll /_cnt 0 ddef
{1 index eq {/_cnt _cnt 1 add ddef} if} forall pop
exch _cnt mul exch _cnt mul 2 index add 4 1 roll 2 index add 4 1 roll pop pop
} def
/ss
{
4 1 roll
	{
	2 npop 
	(0) exch 2 copy 0 exch put pop
	gsave
	false charpath currentpoint
	4 index setmatrix
	stroke
	grestore
	moveto
	2 copy rmoveto
	} exch cshow
3 npop
} def
/jss
{
4 1 roll
	{
	2 npop 
	(0) exch 2 copy 0 exch put 
	gsave
	_sp eq 
		{
		exch 6 index 6 index 6 index 5 -1 roll widthshow  
		currentpoint
		}
		{
		false charpath currentpoint
		4 index setmatrix stroke
		}ifelse
	grestore
	moveto
	2 copy rmoveto
	} exch cshow
6 npop
} def
/sp
{
	{
	2 npop (0) exch
	2 copy 0 exch put pop
	false charpath
	2 copy rmoveto
	} exch cshow
2 npop
} def
/jsp
{
	{
	2 npop 
	(0) exch 2 copy 0 exch put 
	_sp eq 
		{
		exch 5 index 5 index 5 index 5 -1 roll widthshow  
		}
		{
		false charpath
		}ifelse
	2 copy rmoveto
	} exch cshow
5 npop
} def
/pl
{
transform
0.25 sub round 0.25 add exch
0.25 sub round 0.25 add exch
itransform
} def
/setstrokeadjust where
	{
	pop true setstrokeadjust
	/c
	{
	curveto
	} def
	/C
	/c load def
	/v
	{
	currentpoint 6 2 roll curveto
	} def
	/V
	/v load def
	/y
	{
	2 copy curveto
	} def
	/Y
	/y load def
	/l
	{
	lineto
	} def
	/L
	/l load def
	/m
	{
	moveto
	} def
	}
	{
	/c
	{
	pl curveto
	} def
	/C
	/c load def
	/v
	{
	currentpoint 6 2 roll pl curveto
	} def
	/V
	/v load def
	/y
	{
	pl 2 copy curveto
	} def
	/Y
	/y load def
	/l
	{
	pl lineto
	} def
	/L
	/l load def
	/m
	{
	pl moveto
	} def
	} ifelse
/d
{
setdash
} def
/cf	{} def
/i
{
dup 0 eq
	{
	pop cf
	} if
setflat
} def
/j
{
setlinejoin
} def
/J
{
setlinecap
} def
/M
{
setmiterlimit
} def
/w
{
setlinewidth
} def
/H
{} def
/h
{
closepath
} def
/N
{
_pola 0 eq 
	{
	_doClip 1 eq {clip /_doClip 0 ddef} if 
	newpath
	} 
	{
	/CRender {N} ddef
	}ifelse
} def
/n
{N} def
/F
{
_pola 0 eq 
	{
	_doClip 1 eq 
		{
		gsave _pf grestore clip newpath /_lp /none ddef _fc 
		/_doClip 0 ddef
		}
		{
		_pf
		}ifelse
	} 
	{
	/CRender {F} ddef
	}ifelse
} def
/f
{
closepath
F
} def
/S
{
_pola 0 eq 
	{
	_doClip 1 eq 
		{
		gsave _ps grestore clip newpath /_lp /none ddef _sc 
		/_doClip 0 ddef
		}
		{
		_ps
		}ifelse
	} 
	{
	/CRender {S} ddef
	}ifelse
} def
/s
{
closepath
S
} def
/B
{
_pola 0 eq 
	{
	_doClip 1 eq
	gsave F grestore 
		{
		gsave S grestore clip newpath /_lp /none ddef _sc
		/_doClip 0 ddef
		} 
		{
		S
		}ifelse
	}
	{
	/CRender {B} ddef
	}ifelse
} def
/b
{
closepath
B
} def
/W
{
/_doClip 1 ddef
} def
/*
{
count 0 ne 
	{
	dup type (stringtype) eq {pop} if
	} if 
_pola 0 eq {newpath} if
} def
/u
{} def
/U
{} def
/q
{
_pola 0 eq {gsave} if
} def
/Q
{
_pola 0 eq {grestore} if
} def
/*u
{
_pola 1 add /_pola exch ddef
} def
/*U
{
_pola 1 sub /_pola exch ddef 
_pola 0 eq {CRender} if
} def
/D
{pop} def
/*w
{} def
/*W
{} def
/`
{
/_i save ddef
6 1 roll 4 npop
concat pop
userdict begin
/showpage {} def
0 setgray
0 setlinecap
1 setlinewidth
0 setlinejoin
10 setmiterlimit
[] 0 setdash
/setstrokeadjust where {pop false setstrokeadjust} if
newpath
0 setgray
false setoverprint
} def
/~
{
end
_i restore
} def
/@
{} def
/&
{} def
/O
{
0 ne
/_of exch ddef
/_lp /none ddef
} def
/R
{
0 ne
/_os exch ddef
/_lp /none ddef
} def
/g
{
/_gf exch ddef
/_fc
{
_lp /fill ne
	{
	_of setoverprint
	_gf setgray
	/_lp /fill ddef
	} if
} ddef
/_pf
{
_fc
fill
} ddef
/_psf
{
_fc
ashow
} ddef
/_pjsf
{
_fc
awidthshow
} ddef
/_lp /none ddef
} def
/G
{
/_gs exch ddef
/_sc
{
_lp /stroke ne
	{
	_os setoverprint
	_gs setgray
	/_lp /stroke ddef
	} if
} ddef
/_ps
{
_sc
stroke
} ddef
/_pss
{
_sc
ss
} ddef
/_pjss
{
_sc
jss
} ddef
/_lp /none ddef
} def
/k
{
_cf astore pop
/_fc
{
_lp /fill ne
	{
	_of setoverprint
	_cf aload pop setcmykcolor
	/_lp /fill ddef
	} if
} ddef
/_pf
{
_fc
fill
} ddef
/_psf
{
_fc
ashow
} ddef
/_pjsf
{
_fc
awidthshow
} ddef
/_lp /none ddef
} def
/K
{
_cs astore pop
/_sc
{
_lp /stroke ne
	{
	_os setoverprint
	_cs aload pop setcmykcolor
	/_lp /stroke ddef
	} if
} ddef
/_ps
{
_sc
stroke
} ddef
/_pss
{
_sc
ss
} ddef
/_pjss
{
_sc
jss
} ddef
/_lp /none ddef
} def
/x
{
/_gf exch ddef
findcmykcustomcolor
/_if exch ddef
/_fc
{
_lp /fill ne
	{
	_of setoverprint
	_if _gf 1 exch sub setcustomcolor
	/_lp /fill ddef
	} if
} ddef
/_pf
{
_fc
fill
} ddef
/_psf
{
_fc
ashow
} ddef
/_pjsf
{
_fc
awidthshow
} ddef
/_lp /none ddef
} def
/X
{
/_gs exch ddef
findcmykcustomcolor
/_is exch ddef
/_sc
{
_lp /stroke ne
	{
	_os setoverprint
	_is _gs 1 exch sub setcustomcolor
	/_lp /stroke ddef
	} if
} ddef
/_ps
{
_sc
stroke
} ddef
/_pss
{
_sc
ss
} ddef
/_pjss
{
_sc
jss
} ddef
/_lp /none ddef
} def
/dp
{
dup null eq
{
pop
_dp 0 ne
	{
	0 1 _dp 1 sub _dl mod
		{
		_da exch get 3 get
		} for
	_dp 1 sub _dl mod 1 add packedarray
	_da 0 get aload pop 8 -1 roll 5 -1 roll pop 4 1 roll
	definepattern pop
	} if
}
{
_dp 0 ne _dp _dl mod 0 eq and
	{
	null dp
	} if
7 packedarray _da exch _dp _dl mod exch put
_dp _dl mod _da 0 get 4 get 2 packedarray
/_dp _dp 1 add def
} ifelse
} def
/E
{
_ed begin
dup 0 get type /arraytype ne
	{
	0
		{
		dup 1 add index type /arraytype eq
			{
			1 add
			}
			{
			exit
			} ifelse
		} loop
	array astore
	} if
/_dd exch def
/_ury exch def
/_urx exch def
/_lly exch def
/_llx exch def
/_n exch def
/_y 0 def
/_dl 4 def
/_dp 0 def
/_da _dl array def
0 1 _dd length 1 sub
	{
	/_d exch _dd exch get def
	0 2 _d length 2 sub
		{
		/_x exch def
		/_c _d _x get _ ne def
		/_r _d _x 1 add get cvlit def
		_r _ ne
			{
			_urx _llx sub _ury _lly sub [1 0 0 1 0 0] 
				[
				/save cvx
				_llx neg _lly neg /translate cvx
				_c
					{
					nc /begin cvx
					} if
				_r dup type /stringtype eq
					{
					cvx
					}
					{
					{exec} /forall cvx
					} ifelse
				_c
					{
					/end cvx
					} if
				/restore cvx
				] cvx
			/_fn 12 _n length add string def
			_y _fn cvs pop
			/_y _y 1 add def
			_fn 12 _n putinterval
			_fn _c false dp
			_d exch _x 1 add exch put
			} if
		} for
	} for
null dp
_n _dd /_pd
end xput
} def
/fc
{
_fm dup concatmatrix pop
} def
/p
{
/_fm exch ddef
9 -2 roll _pm translate fc
7 -2 roll _pm scale fc
5 -1 roll _pm rotate fc
4 -2 roll exch 0 ne
	{
	dup _pm rotate fc
	1 -1 _pm scale fc
	neg _pm rotate fc
	}
	{
	pop
	} ifelse
dup _pm rotate fc
exch dup sin exch cos div 1 0 0 1 0 6 2 roll
_pm astore fc
neg _pm rotate fc
_pd exch get /_fdd exch ddef
/_pf
{
save
/_doClip 0 ddef
0 1 _fdd length 1 sub
	{
	/_fd exch _fdd exch get ddef
	_fd
	0 2 _fd length 2 sub
		{
		gsave
		2 copy get dup _ ne
			{
			cvx exec _fc
			}
			{
			pop
			} ifelse
		2 copy 1 add get dup _ ne
			{
			aload pop findfont _fm
			patternfill
			}
			{
			pop
			fill
			} ifelse
		grestore
		pop
		} for
	pop
	} for
restore
newpath
} ddef
/_psf
{
save
/_doClip 0 ddef
0 1 _fdd length 1 sub
	{
	/_fd exch _fdd exch get ddef
	_fd
	0 2 _fd length 2 sub
		{
		gsave
		2 copy get dup _ ne
			{
			cvx exec _fc
			}
			{
			pop
			} ifelse
		2 copy 1 add get dup _ ne
			{
			aload pop findfont _fm
			9 copy 6 npop patternashow
			}
			{
			pop
			6 copy 3 npop ashow
			} ifelse
		grestore
		pop
		} for
	pop
	} for
restore
%3 npop newpath
sw rmoveto
} ddef
/_pjsf
{
save
/_doClip 0 ddef
0 1 _fdd length 1 sub
	{
	/_fd exch _fdd exch get ddef
	_fd
	0 2 _fd length 2 sub
		{
		gsave
		2 copy get dup _ ne
			{
			cvx exec _fc
			}
			{
			pop
			} ifelse
		2 copy 1 add get dup _ ne
			{
			aload pop findfont _fm
			12 copy 6 npop patternawidthshow
			}
			{
			pop 9 copy 3 npop awidthshow
			} ifelse
		grestore
		pop
		} for
	pop
	} for
restore
swj rmoveto
} ddef
/_lp /none ddef
} def
/sc
{
_sm dup concatmatrix pop
} def
/P
{
/_sm exch ddef
9 -2 roll _pm translate sc
7 -2 roll _pm scale sc
5 -1 roll _pm rotate sc
4 -2 roll exch 0 ne
	{
	dup _pm rotate sc
	1 -1 _pm scale sc
	neg _pm rotate sc
	}
	{
	pop
	} ifelse
dup _pm rotate sc
exch dup sin exch cos div 1 0 0 1 0 6 2 roll
_pm astore sc
neg _pm rotate sc
_pd exch get /_sdd exch ddef
/_ps
{
save
/_doClip 0 ddef
0 1 _sdd length 1 sub
	{
	/_sd exch _sdd exch get ddef
	_sd
	0 2 _sd length 2 sub
		{
		gsave
		2 copy get dup _ ne
			{
			cvx exec _sc
			}
			{
			pop
			} ifelse
		2 copy 1 add get dup _ ne
			{
			aload pop findfont _sm
			patternstroke
			}
			{
			pop stroke
			} ifelse
		grestore
		pop
		} for
	pop
	} for
restore
newpath
} ddef
/_pss
{
save
/_doClip 0 ddef
0 1 _sdd length 1 sub
	{
	/_sd exch _sdd exch get ddef
	_sd
	0 2 _sd length 2 sub
		{
		gsave
		2 copy get dup _ ne
			{
			cvx exec _sc
			}
			{
			pop
			} ifelse
		2 copy 1 add get dup _ ne
			{
			aload pop findfont _sm
			10 copy 6 npop patternashowstroke
			}
			{
			pop 7 copy 3 npop ss
			} ifelse
		grestore
		pop
		} for
	pop
	} for
restore
pop sw rmoveto
} ddef
/_pjss
{
save
/_doClip 0 ddef
0 1 _sdd length 1 sub
	{
	/_sd exch _sdd exch get ddef
	_sd
	0 2 _sd length 2 sub
		{
		gsave
		2 copy get dup _ ne
			{
			cvx exec _sc
			}
			{
			pop
			} ifelse
		2 copy 1 add get dup _ ne
			{
			aload pop findfont _sm
			13 copy 6 npop patternawidthshowstroke
			}
			{
			pop 10 copy 3 npop jss
			} ifelse
		grestore
		pop
		} for
	pop
	} for
restore
pop swj rmoveto
} ddef
/_lp /none ddef
} def
/A
{
pop
} def
/nc 3 dict def
nc begin
/setgray
{
pop
} bind def
/setcmykcolor
{
4 npop
} bind def
/setcustomcolor
{
2 npop
} bind def
currentdict readonly pop end
currentdict readonly pop end
setpacking
/annotatepage
{
} def
%%EndResource
%%EndProlog
%%BeginSetup
Adobe_cmykcolor /initialize get exec
Adobe_cshow /initialize get exec
Adobe_customcolor /initialize get exec
Adobe_pattern_AI3 /initialize get exec
Adobe_Illustrator_AI3 /initialize get exec
%%EndSetup
0 A
0 O
0.25 1 1 0.25 k
0 R
0 0 0 1 K
0 J 0 j 0.2 w 4 M []0 d
9.19971 841.8735 m
10.3311 842.1226 8.4126 839.4165 8 839.2729 c
9.57471 839.1421 9.3999 839.0728 v
9 838.9233 8.8623 838.561 8.6001 838.2729 c
7.39355 836.9985 6.49365 836.7915 7.3999 834.8735 C
8.03125 834.9233 8.1626 835.1919 8.6001 834.8735 c
9.32471 834.3423 7.78125 832.979 10 832.0728 c
11.356 831.5229 14.1997 832.1792 15.7998 832.2729 C
15.1997 831.8481 12.5186 830.4546 12.3999 829.6733 c
12.1812 828.2612 12.9434 828.686 13.5996 828.4731 C
13.2998 827.2544 13.106 827.5854 14.1997 827.0737 C
13.9058 825.7485 14.3687 824.8296 15.3999 824.0737 C
15.1309 824.0229 15.0996 823.2983 15.1997 823.4731 C
16.7246 820.23 19.1123 818.5229 21.9995 816.6733 C
21.6309 816.3979 20.6558 815.2925 20.5996 815.2739 C
21.5811 815.061 22.6807 814.1675 23.1997 814.0737 c
25.1621 813.7358 24.1309 815.5171 25.7998 814.2739 C
26.6689 815.0239 26.7061 815.8735 25.9995 816.2729 C
28.0439 816.4048 32.4873 820.0112 33.7998 821.6733 c
34.6494 822.7612 35.0557 823.9985 34.999 825.4731 C
36.499 826.3667 36.6807 826.5669 35.999 828.0737 C
38.5498 828.7544 34.6494 830.9604 33.5996 831.6733 C
35.2559 831.6235 37.4619 830.6421 39.3994 831.0728 c
40.8428 831.4048 40.2432 832.3169 40.5996 833.2729 c
41.0186 834.4165 42.5557 833.5415 42.999 834.4731 c
43.1182 834.7417 42.999 836.0728 Y
42.3311 836.8979 41.6055 837.5981 40.7998 838.2729 c
40.0811 838.8794 42.4736 838.6919 41.999 838.8735 c
41.293 839.1479 40.9424 840.7671 40.5996 841.4731 C
42.5557 840.1978 45.3623 837.6665 44.5986 833.8735 c
44.124 831.5298 41.2871 829.8423 40.1992 827.8735 c
37.6934 823.3608 36.5117 817.7612 33.3994 813.8735 c
33.2178 813.6548 33.1182 813.3608 32.7998 813.2739 c
32.3496 813.1606 33.4121 812.8677 33.5996 812.8735 c
38.4121 813.0864 39.874 811.2544 40.7998 816.0737 C
40.8428 815.8169 40.4053 818.0737 42.1992 818.6733 C
42.5557 815.4302 40.749 813.4673 39.3994 811.2739 C
37.5996 811.7231 36.3115 812.2739 33.7998 812.2739 C
33.7998 810.0112 33.793 805.686 33.1992 804.4731 c
33.168 804.4233 32.6182 803.5981 32.7998 803.4731 c
33.9248 802.7231 34.6367 801.4048 34.1992 800.0737 c
33.9365 799.3052 32.999 797.7427 33.7998 797.2739 c
35.624 796.2114 37.168 797.3052 38.7998 796.8735 C
40.0742 800.6548 40.874 803.8608 42.1992 807.8735 C
42.624 807.6987 42.6924 807.5425 42.999 807.2739 C
44.7861 810.73 46.999 812.7485 47.999 817.0737 C
48.3994 815.8735 L
49.7373 815.98 49.999 816.0737 V
48.4678 814.7856 47.0674 812.6235 46.999 812.4731 c
44.9561 809.3237 44.4111 808.8481 43.999 807.2739 C
44.9932 807.2427 44.6494 807.5552 45.5986 806.6733 C
44.6436 805.8989 45.2686 805.9048 44.7998 805.0737 c
44.7617 805.0171 44.0742 805.0737 43.999 805.0737 C
43.624 803.2427 42.4307 802.436 41.999 801.0737 c
41.3994 799.2046 41.5557 798.2983 40.3994 796.4741 c
38.4561 793.4302 38.249 794.9546 34.5996 793.8745 c
33.7686 792.7495 33.5996 792.6733 v
32.6748 792.2983 31.1309 792.1987 30.1992 792.0737 C
30.9619 790.1802 31.7998 790.4741 34.3994 790.4741 C
35.0371 788.0308 35.1934 789.2681 37.1992 788.2739 c
38.0742 787.8433 38.793 786.0991 39.3994 785.2739 c
39.999 784.4614 41.499 783.8433 41.999 783.0737 c
42.1807 782.7866 41.668 782.5308 41.5996 782.4741 C
44.5049 779.7056 47.499 773.8804 51.1992 772.0737 c
51.3242 772.0181 51.7803 771.8862 51.7998 771.8745 c
52.1436 771.6929 52.2178 771.3745 52.3994 771.2749 C
50.8682 771.6245 51.3057 771.1558 50.999 771.2749 C
51.2178 769.8745 50.5986 769.8745 V
49.4561 774.1743 45.3369 775.1304 43.5986 778.8745 C
43.3555 778.4624 42.8369 778.3179 42.7998 778.2749 C
40.999 780.8931 38.7305 783.2056 37.3994 786.0737 C
36.4248 785.9429 35.9678 785.9429 34.999 786.0737 C
35.1553 784.6245 35.5244 783.0181 35.3994 781.2749 c
35.2432 779.2612 34.8623 776.6558 34.5996 774.6743 C
35.3623 774.3237 36.1309 773.9556 36.7998 773.4741 c
37.2559 773.1499 38.1992 772.437 38.3994 771.8745 c
39.1621 769.6929 38.1992 765.2183 38.5996 762.0747 c
38.6748 761.437 39.2871 760.2368 39.1992 759.4741 c
39.1494 759.0991 38.4619 758.9116 38.5996 758.0747 c
38.7686 757.0122 39.7549 755.5435 40.1992 754.6753 c
41.0811 752.9253 41.6924 751.1128 41.999 748.8745 C
40.3496 749.8628 41.7373 748.5815 40.1992 748.0747 c
39.7061 747.9185 40.1992 748.8745 Y
40.3115 751.7866 39.9424 753.1685 38.999 755.2749 c
38.9492 755.3804 38.0498 756.2866 37.999 756.4741 c
37.2178 759.269 36.6367 763.4556 36.999 767.4741 c
37.1992 769.7495 37.5303 770.1245 35.999 770.8745 c
35.3311 771.2056 34.8994 771.2437 33.999 771.4741 C
33.3057 768.7056 32.5557 766.3491 30.999 764.4741 c
27.2686 759.9995 20.481 761.3491 17.9995 766.0737 c
17.1621 767.6675 16.3745 769.3179 15.7998 771.2749 C
14.7061 770.8745 14.106 770.73 13 770.2749 C
13.5308 766.6675 12.6812 763.0366 12.7998 760.0747 C
12.5308 760.4312 12.0562 760.731 11.7998 761.0747 C
10.4248 756.4253 6.0376 755.6304 5 750.2749 C
4.7998 752.7437 4 751.8745 Y
3.20605 751.9312 3.625 752.4683 3.6001 752.4741 C
5.65625 753.9116 9.43115 757.1304 10.3999 759.4741 c
11.6875 762.606 10.7437 769.3433 12 771.8745 c
12.4497 772.7993 14.3062 773.5366 15.1997 774.0737 C
14.7124 777.6558 14.1436 781.7925 14.5996 785.8745 C
13.8184 785.8745 12.9746 785.8745 12.1997 785.8745 C
11.6997 783.9868 10.1812 779.5679 9.19971 778.0737 c
9.125 777.9683 8.5376 778.2368 8.3999 778.2749 C
8.10596 777.1675 8.2876 776.6616 7.7998 775.6743 c
7.79346 775.6675 7.2251 775.6743 7.19971 775.6743 C
6.08105 773.3062 2.625 770.9116 4.19971 767.8745 C
3.88721 768.0425 3.49365 768.4312 3.3999 768.4741 C
3.33105 768.4741 3.26855 768.4741 3.19971 768.4741 C
3 768.6675 3.08105 769.1304 3 769.4741 C
0.90625 769.6245 1.65625 769.6187 2.6001 770.4741 c
3.94971 771.7124 5.19385 773.7368 6.19971 775.2749 c
4.34375 772.1187 8.3125 778.4487 7 778.4741 C
7.51855 779.5181 8.44336 780.4995 9 781.4741 C
8.78125 781.5181 9.19971 782.2749 Y
8.9751 782.2241 8.8999 782.1812 8.6001 782.0737 C
10.2813 783.4116 10.3062 786.6304 12 787.8745 c
14.1748 789.48 14.7935 787.48 15.5996 790.6733 C
18.1558 790.6733 19.1245 790.0806 19.7998 792.0737 C
19.8496 792.0864 18.7998 792.0737 Y
18.0435 792.2983 16.062 792.2495 15.1997 792.6733 c
14.9434 792.8052 14.6685 793.7808 14.3999 793.8745 c
10.856 795.1489 10.5747 792.9116 8.6001 796.6733 c
6.70605 800.2739 6.4126 803.3481 4.19971 806.0737 C
4.76855 806.9175 4.86865 806.7544 5.3999 807.0737 C
3.6626 809.73 2.69385 813.2114 0 814.2739 C
0 814.3481 0.037598 815.0552 0 815.0737 C
1.2251 815.4106 1.44385 815.311 2.3999 815.8735 C
1.44385 813.9487 5.23096 809.1489 6.3999 806.6733 C
7.1748 807.186 7.13721 807.6177 7.7998 806.2739 C
7.70605 806.2173 7.5874 805.2612 7.6001 805.0737 c
7.75 802.3608 9.5625 799.7358 10.1997 796.6733 C
12.1748 797.1802 13.3999 796.6304 15.5996 797.2739 C
15.2061 799.3921 14.6309 800.1548 14.1997 802.2739 C
15.4873 802.5171 15.231 802.6548 16.1997 803.4731 c
16.437 803.6802 15.8184 804.23 15.7998 804.2739 c
15.2749 805.3735 15.1875 810.2046 15.3999 812.0737 C
13.7124 811.73 12.3311 810.6606 11.1997 810.4731 c
10.7749 810.4106 10.4248 810.4731 10 810.4731 C
9.2749 812.2671 6.80615 814.355 6.6001 815.4731 c
6.4375 816.3481 6.875 816.7856 7 817.2729 C
7.1499 817.1733 7.94336 817.0854 8 817.0737 C
8.44336 814.0796 9.2373 813.8921 10.5996 811.4731 C
12.6558 811.8794 14.0186 812.7231 16.3999 813.0737 C
13.4497 817.5854 12.0747 823.5542 9.3999 828.4731 c
8.1875 830.6978 5.5249 832.2856 5.19971 835.2729 c
5.16846 835.5103 5.38086 836.1919 5.3999 836.0728 C
5.79346 838.0298 6.21875 838.8296 7.19971 840.0728 c
7.50586 840.4731 7.81836 840.9673 8.19971 841.2729 c
8.3999 841.4419 8.9624 841.8228 9.19971 841.8735 c
b
%%PageTrailer
gsave annotatepage grestore showpage
%%Trailer
Adobe_Illustrator_AI3 /terminate get exec
Adobe_pattern_AI3 /terminate get exec
Adobe_customcolor /terminate get exec
Adobe_cshow /terminate get exec
Adobe_cmykcolor /terminate get exec
Adobe_packedarray /terminate get exec
%%EOF
