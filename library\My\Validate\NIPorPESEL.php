<?php

class My_Validate_NIPorPESEL extends Zend_Validate_Abstract {
	
	const NIP_OR_PESEL_INVALID = 'nipOrPeselInvalid';
	
	protected $_messageTemplates = array(
		self::NIP_OR_PESEL_INVALID => 'nipOrPeselInvalid',
	);
	
	public function isValid($value) {
		$nip = new My_Validate_NIP();
		$pesel = new My_Validate_PESEL();
		
		if (!$nip->isValid($value)) {
			if (!$pesel->isValid($value)) {
				$this->_error(self::NIP_OR_PESEL_INVALID);
				return false;
			}
		}
		
		return true;
	}
	
}