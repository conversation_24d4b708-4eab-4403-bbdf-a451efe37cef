<?php

class ImportController extends Zend_Controller_Action {

    public function init() {
        $this->_helper->layout->setLayout('jqrequest');
        if (false && APPLICATION_ENV != "development" && !$this->_helper->isSsl()) {
            echo "SSL only";
            exit;
        }
    }

    public function addCarFullAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');
            $import = new Model_ImportExport_Import();
            $car = $import->receiveCarFull($data, $checksum, $iv);
            $processed = $import->processCarFull($car);

            $cars = new Model_Cars_Cars();
            $carId = $cars->addCarFull($processed);

            $users = new Model_Users();
            $searches = $users->getSearchesWithCleanup(null, null, $carId);

            $mailingCount = 0;
            foreach ($searches as $searchUser) {
                if (count($searchUser['cars'])) {
                    $mailingCount++;
                }
            }

            echo "OK/" . $mailingCount;
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            print_r($e->getMessage());
            echo $e->getMessage();
        }
    }

    public function addCarQuickAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $car = $import->receiveCarQuick($data, $checksum, $iv);
            $processed = $import->processCarQuick($car);

            $cars = new Model_Cars_Cars();
            $cars->addCarQuick($processed);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function carPhotosAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $photoData = $import->receiveCarPhotos($data, $checksum, $iv);
            $import->processCarPhotos($photoData);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function carVipautoPhotosAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $photoData = $import->receiveCarVipautoPhotos($data, $checksum, $iv);
            $import->processCarVipautoPhotos($photoData);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function changeCarPatronBulkAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $data = $import->receiveChangeCarPatronBulk($data, $checksum, $iv);

            $cars = new Model_Cars_Cars();
            $cars->changeCarPatronBulk($data);
            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($data, true)), Zend_Log::INFO);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function cleanCacheAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $cacheData = $import->receiveCleanCache($data, $checksum, $iv);

            $srAdmin = new Model_SrAdmin();
            $srAdmin->cleanCache($cacheData);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function clientAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $client = $import->receiveClient($data, $checksum, $iv);
            $processed = $import->processClient($client);

            $users = new Model_Users();
            $users->edit($processed['id'], $processed, $doExport = false);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function deleteCarAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $car = $import->receiveCarDelete($data, $checksum, $iv);

            $cars = new Model_Cars_Cars();
            $cars->deleteCar($car['sr_car_id']);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function editCarAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $car = $import->receiveCarEdit($data, $checksum, $iv);
            $processed = $import->processCarEdit($car);

            $cars = new Model_Cars_Cars();
            $cars->editCar($processed['sr_car_id'], $processed);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($processed, true)), Zend_Log::INFO);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function editMakeAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $car = $import->receiveMakeEdit($data, $checksum, $iv);
            $processed = $import->processMakeEdit($car);

            $cars = new Model_Cars_Cars();
            $cars->editMake($processed['id'], $processed);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($processed, true)), Zend_Log::INFO);

            $adm = new Model_SrAdmin();
            $adm->cleanCacheAll();

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function addMakeAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $car = $import->receiveMakeAdd($data, $checksum, $iv);

            $cars = new Model_Cars_Cars();
            $cars->addCarMake($car['cm_type_id'], $car['cm_om_id'], $car['cm_make_name'], $car['cm_make_id'], $car['cm_special_flag']);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($car, true)), Zend_Log::INFO);

            $adm = new Model_SrAdmin();
            $adm->cleanCacheAll();

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function deleteMakeAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $car = $import->receiveMakeDelete($data, $checksum, $iv);
            $processed = $import->processMakeDelete($car);

            $cars = new Model_Cars_Cars();
            $cars->deleteCarMake($processed['id']);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function editModelAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $car = $import->receiveModelEdit($data, $checksum, $iv);
            $processed = $import->processModelEdit($car);

            $cars = new Model_Cars_Cars();
            $cars->editModel($processed['id'], $processed);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($processed, true)), Zend_Log::INFO);

            //$adm = new Model_SrAdmin();
            //$adm->cleanCacheAll();

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function addModelAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $car = $import->receiveModelAdd($data, $checksum, $iv);
            $processed = $import->processModelAdd($car);

            $cars = new Model_Cars_Cars();
            $cars->addCarModel($processed['make_id'], 0, $processed['name'], $processed['sr_model_id']);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($car, true)), Zend_Log::INFO);

            //$adm = new Model_SrAdmin();
            //$adm->cleanCacheAll();

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function deleteModelAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $car = $import->receiveModelDelete($data, $checksum, $iv);
            $processed = $import->processModelDelete($car);

            $cars = new Model_Cars_Cars();
            $cars->deleteCarModel($processed['id']);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function employeeAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $employee = $import->receiveEmployee($data, $checksum, $iv);
            $processed = $import->processEmployee($employee);

            $employees = new Model_Employees();
            $employees->addOrEdit($processed);

            $adm = new Model_SrAdmin();
            $adm->cleanCacheAll();

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function fuelConsumptionAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $fcData = $import->receiveFuelConsumption($data, $checksum, $iv);

            $cars = new Model_Cars_Cars();
            $cars->updateFuelConsumption($fcData);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function reservationDeletedAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $resDel = $import->receiveReservationDeleted($data, $checksum, $iv);
            $processed = $import->processReservationDeleted($resDel);

            $resId = $processed['id'];
            unset($processed['id']);

            if (empty($resId)) {
                echo "Empty reservation id after processing; raw data: ";
                Zend_Debug::dump($resDel, '<h2></h2>');
                echo "Processed data: ";
                Zend_Debug::dump($processed, '<h2></h2>');
            }

            $ct = new Model_Cars_Transfers();
            $ct->deleteReservationFromSr($resId, $processed);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function reservationExtendedAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $resExt = $import->receiveReservationExtended($data, $checksum, $iv);
            $processed = $import->processReservationExtended($resExt);

            $resId = $processed['id'];
            unset($processed['id']);

            if (empty($resId)) {
                echo "Empty reservation id after processing; raw data: ";
                Zend_Debug::dump($resDel, '<h2></h2>');
                echo "Processed data: ";
                Zend_Debug::dump($processed, '<h2></h2>');
            }

            $ct = new Model_Cars_Transfers();
            $ct->extendReservationFromSr($resId, $processed);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function returnCarViewsAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $data = $import->receiveCarViewsRequest($data, $checksum, $iv);
            if (is_array($data)) {
                $cars = new Model_Cars_Cars();

                echo "OK/slash/" . $iv . "/slash/";
                echo My_Utils::urlSafeB64Encode($import->serializeAndEncrypt($cars->getCarViewsBySrId((int) $data['c_id']), My_Utils::urlSafeB64Decode($iv)));
            }
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function returnClientsAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $data = $import->receiveClientsRequest($data, $checksum, $iv);
            if (is_array($data)) {
                $users = new Model_Users();
                echo "OK/slash/" . $iv . "/slash/";
                echo My_Utils::urlSafeB64Encode($import->serializeAndEncrypt($users->getClientsForSys2($data), My_Utils::urlSafeB64Decode($iv)));
            } else {
                echo "Not OK in " . __METHOD__ . ", line " . (__LINE__ - 1);
            }
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function returnTranslationsAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $ok = $import->receiveTranslationRequest($data, $checksum, $iv);
            if ($ok === true) {
                $translations = new Model_Translations();
                echo "OK/slash/" . $iv . "/slash/";
                echo My_Utils::urlSafeB64Encode($import->serializeAndEncrypt($translations->getAllTranslations(), My_Utils::urlSafeB64Decode($iv)));
            }
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function salesmanLoginAction() {
        $this->_helper->viewRenderer->setNoRender(true);

        $params = $this->_request->getParams();
        $time = time();
        $timeoutLimitPast = 15 * 60; //15 minutes
        $timeoutLimitFuture = 5 * 60; //5 minutes - just in case times differ across servers

        if (array_key_exists('id', $params) && array_key_exists('hash', $params) && array_key_exists('timestamp', $params)) {
            if (($time - (int) $params['timestamp'] > $timeoutLimitPast) || ($time - (int) $params['timestamp'] < 0 - $timeoutLimitFuture)) {
                $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => 'timestamp too old or in future; ' . print_r($params, true) . '; ' . $time), Zend_Log::WARN);
                $this->view->messenger->addMessage('Niepoprawne dane logowania. Odśwież stronę w systemie rozliczeniowym i spróbuj ponownie');
                $this->_redirect('');
            } else {
                $export = new Model_ImportExport_Export();
                $resArray = $export->sendVerifySalesman($params);

                if ((bool) $resArray == false || !is_array($resArray) || !array_key_exists('u_user_id', $resArray) || empty($resArray['u_user_id'])) {
                    $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => 'credentials incorrect; ' . print_r($params, true)), Zend_Log::WARN);
                    $this->_redirect('');
                } else {
                    $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => 'credentials: ' . print_r($params, true)));

                    $auth = Zend_Auth::getInstance();
                    $auth->setStorage(new Zend_Auth_Storage_Session($namespace = "Zend_Auth_WWWAuto"));
                    $auth->clearIdentity();
                    $storage = $auth->getStorage();

                    $identityArr = array(
                        'id' => 0,
                        'email' => '',
                        'first_name' => $resArray['u_first_name'],
                        'last_name' => $resArray['u_last_name'],
                        'nip_or_pesel' => '',
                        'regon' => '',
                        'id_document' => '',
                        'id_type' => '',
                        'address' => '',
                        'zip_code' => '',
                        'city' => '',
                        'country' => '',
                        'phone' => '',
                        'is_deleted' => 0,
                        'activation_hash' => '',
                        'role' => 'salesman',
                        'sr_id' => $resArray['u_user_id']
                    );

                    $identity = new stdClass();
                    foreach ($identityArr as $key => $value) {
                        $identity->{$key} = $value;
                    }

                    $storage->write($identity);
                    $this->view->messenger->addMessage('Zalogowany jako handlowiec: ' . $identity->first_name . ' ' . $identity->last_name);
         
                    $this->_redirect($this->_getParam('redirect', ''));
                }
            }
        } else {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => 'required params not present'), Zend_Log::WARN);
            $this->_redirect('');
        }
    }

    public function stubWwwClientAction() {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        echo "OK";
        exit;
    }

    public function stubWwwClientEditAction() {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        echo "OK";
        exit;
    }

    public function stubWwwReservationAction() {
        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);
        echo "OK";
        exit;
    }

    public function updateTranslationsAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $data = $import->receiveTranslations($data, $checksum, $iv);

            $translations = new Model_Translations();
            $translations->updateFromSr($data['data']);

            $this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);

            echo "OK";
            exit;
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function editAdvertisingBarAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');




            $import = new Model_ImportExport_Import();
            $advertisingBarData = $import->receiveAdvertisingBarEdit($data, $checksum, $iv);

            $advertisingBarModel = new Model_AdvertisingBar();
            $advertisingBarModel->editAdvertisingText($advertisingBarData);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($advertisingBarData, true)), Zend_Log::INFO);

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function syncMakeAction() {

        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);


        if (!$this->_request->isPost()) {
            echo "Tylko POST";
            exit;
        }




        $checksum = $this->_request->getPost('checksum');
        $data = $this->_request->getPost('data');
        $iv = $this->_request->getPost('iv');

        $import = new Model_ImportExport_Import();
        $car = $import->receiveMakeSync($data, $checksum, $iv);

        $processed = $import->processMakeSync($car);

        if ($processed['error'] != "")
            echo '<span style="color: #ff0000;">' . $processed['error'] . '</span>';
        else
            echo $processed['name'] . ' ' . $processed['type_id'] . ' ' . $processed['special_flag'] . ' ' . $car['cm_make_id'] . ' ' . $car['cm_special_flag'];

        if ($processed['error'] == "") {
            $ret = array(
                'sr_make_id' => $car['cm_make_id'],
            );

            $cars = new Model_Cars_Cars();
            $cars->editMake($processed['id'], $ret);
        }
    }

    public function addCarSearchAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $search = $import->receiveCarSearch($data, $checksum, $iv);
            $data = $import->processCarSearch($search);

            $users = new Model_Users();
            $users->saveSearch($data['saveData'], $data['searchData'],  null);

            if(!empty($data['sr_car_id'])) {


                $cars = new Model_Cars_Cars();
                $car = $cars->getCarBySrCarId($data['sr_car_id']);
                $car = $cars->getCarFull($car['car_id']);

                $employees = new Model_Employees();
                $employee = $employees->getBySrId($data['saveData']['added_by_sr_id']);

                $offerData = array();
                $offerData['employee'] = $employee;
                $offerData['sender_email'] = $employee['email'];
                $offerData['recipient_email'] = $data['saveData']['email'];
                $offerData['message'] = '';

                $carsTr = new Model_Cars_Transfers();
                $carsTr->contactSendOffer($car, $offerData);

            }




            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function userSellExchangeAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $data = $import->receiveUserSellExchange($data, $checksum, $iv);
            $processed = $import->processUserSellExchange($data);

            $useModel = new Model_UsersSellExchange();
            $useModel->update($processed['id'], $processed);


            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function syncModelAction() {

        $this->_helper->layout->disableLayout();
        $this->_helper->viewRenderer->setNoRender(true);

        if (!$this->_request->isPost()) {
            echo "Tylko POST";
            exit;
        }

        $checksum = $this->_request->getPost('checksum');
        $data = $this->_request->getPost('data');
        $iv = $this->_request->getPost('iv');

        $import = new Model_ImportExport_Import();
        $car = $import->receiveModelSync($data, $checksum, $iv);
        $processed = $import->processModelSync($car);
        
        if ($processed['error'] != "")
            echo '<span style="color: #ff0000;">' . $processed['error'] . '</span>';
        else
            echo $processed['name'] . ' ' . $car['cm_make_id'];
        
        if ($processed['error'] == "") {
            $ret = array(
                'sr_model_id' => $car['cm_model_id'],
            );

            $cars = new Model_Cars_Cars();
            $cars->editModel($processed['id'], $ret);
        }

        //$cars = new Model_Cars_Cars();
        //$cars->editModel($processed['id'], $processed);

        //$this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($processed, true)), Zend_Log::INFO);

        //$adm = new Model_SrAdmin();
        //$adm->cleanCacheAll();
    }


    public function editAccountAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $account = $import->receiveAccountEdit($data, $checksum, $iv);
            $processed = $import->processAccountEdit($account);

            $companiesModel = new Model_Companies();
            $companiesModel->editCompany($processed['id'], $processed);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($processed, true)), Zend_Log::INFO);

            $adm = new Model_SrAdmin();
            $adm->cleanCacheAll();

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function addAccountAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $account = $import->receiveAccountAdd($data, $checksum, $iv);
            $processed = $import->processAccountAdd($account);

            $companiesModel = new Model_Companies();
            $companiesModel->addCompany($processed);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($account, true)), Zend_Log::INFO);

            $adm = new Model_SrAdmin();
            $adm->cleanCacheAll();

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function editLocationAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $account = $import->receiveLocationEdit($data, $checksum, $iv);
            $processed = $import->processLocationEdit($account);

            $locationsModel = new Model_Locations();
            $locationsModel->editLocation($processed['location_id'], $processed);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($processed, true)), Zend_Log::INFO);

            $adm = new Model_SrAdmin();
            $adm->cleanCacheAll();

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

    public function addLocationAction() {
        try {
            $this->_helper->layout->disableLayout();
            $this->_helper->viewRenderer->setNoRender(true);

            if (!$this->_request->isPost()) {
                echo "Tylko POST";
                exit;
            }

            $checksum = $this->_request->getPost('checksum');
            $data = $this->_request->getPost('data');
            $iv = $this->_request->getPost('iv');

            $import = new Model_ImportExport_Import();
            $account = $import->receiveLocationAdd($data, $checksum, $iv);
            $processed = $import->processLocationAdd($account);

            $locationsModel = new Model_Locations();
            $locationsModel->addLocation($processed);

            $this->_helper->eventLog->log(array('outcome' => 'ok', 'additional' => print_r($account, true)), Zend_Log::INFO);

            $adm = new Model_SrAdmin();
            $adm->cleanCacheAll();

            echo "OK";
        } catch (Exception $e) {
            $this->_helper->eventLog->log(array('outcome' => 'fail', 'additional' => $e->getMessage()), Zend_Log::INFO);
            echo $e->getMessage();
        }
    }

}