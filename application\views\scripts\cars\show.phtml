</div>

<div id="car">
    <div class="container">
        <div class="row pt-4">
            <div class="col-lg-12">
                <div class="d-flex align-content-center flex-wrap ">
                    <span class="id mr-1 d-flex justify-content-center flex-column ">id: <?= $this->car['sr_car_id'] ?></span>
                    <h1 class="col">
                        <?= $this->escape($this->car['make_name'] . " " . $this->car['model_name'] . " " . $this->car['build_year'] . ' ' . $this->translate->_('PROD.') . ($this->car['first_registration_year'] ? " / " . $this->car['first_registration_year'] . ' ' . $this->translate->_('REG.') : "") . ($this->language == "pl" ? " " . ($this->car['auction_price'] > 0 && $this->auction ? ('- cena <PERSON> plus ' . number_format(500, 0, '', ' ') . ' PLN') : $this->car['title']) : "")) ?>
                    </h1>
                </div>
            </div>
        </div>

        <div class="row py-4 align-items-center">
            <div class="col-lg-4">
                <div class="position py-3">
                    <?= $this->escape($this->car['address'] . " - " . $this->car['name'] . " | " . $this->translate->_('CAR_POSITION') . ": ") . (($this->car['status'] == "NOT_ON_SITE" || $this->car['status'] == 'WITHDRAWN') ? "<strong>" . $this->translate->_('NOT_ON_SITE') . "</strong>" : ('<span>' . $this->escape($this->car['position']) . '</span>')) ?><?= ($this->car['status'] == "NOT_ON_SITE" && $this->car['offer_id']) ? ' | ' . $this->translate->_('OFFER_NUMBER') . ': ' . sprintf($this->car['offer_id']) : '' ?>

                </div>

            </div>
            <div class="col-lg-5">
                <div class="d-flex align-items-center">
                    <a href="tel:<?= $this->escape($this->car['caretaker_phone']) ?>" class="phone mr-1 pr-3">
                        <?= $this->escape($this->car['caretaker_first_name']) ?>  <?= $this->escape($this->car['caretaker_last_name']) ?>
                        <br />
                        <i
                                class="fa fa-phone"
                                aria-hidden="true"></i><?= $this->escape($this->car['caretaker_phone']) ?>


                       </a>
                    <a class="btn btn-action btn-action-orange col" data-toggle="modal" data-target="#contactCartaker"
                       href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarContactCartaker', true) ?>"> <?= $this->translate->_('CONTACT_CARETAKER') ?></a>
                </div>
            </div>
            <div class="col-lg-3">


                <?php if ($this->isFavourite): ?>
                    <?php if (Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == 'user') : ?>
                        <a class="favourite favourite-del"
                           href="<?= $this->domain . $this->url(array('language' => $this->language), 'favouriteList') . '?del=' . $this->car['car_id'] ?>"><span> <i
                                        class="fa fa-heart-o pr-4"
                                        aria-hidden="true"></i>- <?= $this->translate->_('FAVOURITE_ADD_DEL') ?></span></a>
                    <?php else: ?>
                        <a class="favourite favourite-del"
                           href="<?= $this->domain . $this->url(array('language' => $this->language), 'favouriteListSession') . '?del=' . $this->car['car_id'] ?>"><span><i
                                        class="fa fa-heart-o pr-4"
                                        aria-hidden="true"></i>- <?= $this->translate->_('FAVOURITE_ADD_DEL') ?></span></a>
                    <? endif ?>
                <?php else: ?>
                    <a class="favourite"
                       href="<?= $this->domain . $this->carLink($this->car, null, $noMarkup = true) . ($this->hash ? $this->hash . "&amp;perPage=" . $this->perPage . '&amp;favourite=' . $this->car['car_id'] : "?perPage=" . $this->perPage . '&amp;favourite=' . $this->car['car_id']) ?> "><span><i
                                    class="fa fa-heart-o pr-4"
                                    aria-hidden="true"></i><?= $this->translate->_('FAVOURITE_ADD_DEL') ?></span></a>
                <?php endif ?>
            </div>
        </div>
    </div>

    <div class="gallery-container">

        <div class="container py-4">
            <div class="d-flex pt-2 pb-4 flex-column flex-sm-column flex-md-column flex-lg-row">

                <div class="car-photo <?php if (count($this->car['photos']) < 1): ?> no-photo<?php endif ?>">

                    <?php if (count($this->car['photos']) > 0) :

                        ?>
                        <ul class="bxslider">
                            <?php foreach ($this->car['photos'] as $photo):

                                ?>
                                <li>
                                    <a class="main-photo" data-lightbox="roadtrip"
                                       data-lightbox="image-<?= $photo['id'] ?>"
                                       href="http://www.autoauto.pl/images/cars/<?= $this->car['car_id'] ?>/<?= $photo['filename_base'] . "_L." . $photo['filename_extension'] ?>"
                                       title="<?= $photo['description'] ?>" rel="prettyPhoto[gallery]">
                                        <img class="img-fluid"
                                             src="http://www.autoauto.pl/images/cars/<?= $this->car['car_id'] ?>/<?= $photo['filename_base'] . "_L." . $photo['filename_extension'] ?>"
                                             alt="<?= $photo['description'] ?>">

                                    </a>
                                </li>

                            <?php endforeach ?>
                        </ul>
                    <?php endif ?>

                </div>


                <div class="car-photos<?= count($this->car['photos']) < 1 ? " empty" : "" ?> hidden-md-down">
                    <ul id="gallery-thumbs">

                        <?php $first = true; ?>

                        <?php foreach ($this->car['photos'] as $photo): ?>
                            <li class="thumb-item">
                                <a class="<?= $first ? "photo first" : "photo" ?> thumb"
                                   href="http://www.autoauto.pl/images/cars/<?= $this->car['car_id'] ?>/<?= $photo['filename_base'] . "_L." . $photo['filename_extension'] ?>">
                                    <img class="img-fluid"
                                         src="http://www.autoauto.pl/images/cars/<?= $this->car['car_id'] ?>/<?= $photo['filename_base'] . "_SM." . $photo['filename_extension'] ?>"
                                         alt="">
                                </a>
                            </li>
                            <?php $first = false; endforeach ?>
                    </ul>
                </div>


                <div class="utils">
                    <div class="price">


                        <?php if ($this->car['auction_price'] > 0 && $this->auction): ?>
                            <?= $this->translate->_('AUCTION') ?> <br/>
                            <?= $this->translate->_('CURRENT_PRICE') ?> <br/>
                            <?= $this->carPrice($this->car, $this->language_row, false, false, 'brutto', $this->car['auction_price']) ?>


                        <?php else: ?>
                        <div class="<?= $this->car['price_type_key'] ?> red-color"><?= $this->escape($this->carPrice($this->car, $this->language_row)) ?></div>


                    <?php if ($this->language == 'pl') : ?>
                        <?php if ($this->car['price_type_key'] == 'netto'): ?>
                            <div class="brutto gray-color"><?= $this->carPrice($this->car, $this->language_row, false, false, 'brutto') ?></div>
                        <?php endif; ?>

                    <?php else: ?>
                    <?php if ($this->carPrice($this->car, $this->language_row, 'EUR')): ?>
                        <div class="<?= $this->car['price_type_key'] ?> red-color">
                            <?= $this->escape($this->carPrice($this->car, $this->language_row, 'EUR')) ?>

                            <?php endif; ?>
                            <?php endif; ?>
                            <?php endif; ?>

                        </div>

                        <?php if (!$this->isRental): ?>
                            <div class="leasing-credit">
                                <span> lub <strong><?= $this->leasingCreditInstalment['instalment'] ?></strong> <?= $this->translate->_('PER_MONTH') ?></span>
                                <a class="btn btn-action btn-leasing-credit" data-toggle="modal" data-target="#contactFinancing"
                                   href="<?= $this->leasingCreditInstalment['type'] == "leasing" ? "#leasingForm" : "#creditForm" ?>"
                                   rel="prettyPhoto" title=""><?= $this->translate->_('LEASING_CREDIT_ASK') ?></a>

                            </div>
                        <?php endif ?>

                        <div class="leasing-credit-utils pt-4">
                            <?php if($this->leasingForm): ?>
                            <div class="">
                                <a id="item_leasing_form" href="#" data-toggle="modal" data-target="#contactLeasing"
                                   class="btn btn-action btn-leasing-credit-util" title=""> <i class="fa fa-calculator" aria-hidden="true"></i><?= $this->translate->_('LEASING_CALCULATOR') ?>
                                </a>
                            </div>
                            <? endif ?>
                            <div class="">
                                <a id="item_credit_form" href="#" data-toggle="modal" data-target="#contactCredit"
                                   class="btn btn-action btn-leasing-credit-util" title=""><i class="fa fa-calculator"
                                                                                              aria-hidden="true"></i><?= $this->translate->_('LOAN_CALCULATOR') ?>
                                </a>
                            </div>
                        </div>

                        <div class="actions pt-4">
                            <ul>

                                <?php if (!$this->isRental && !in_array($this->car['status'], array("SOLD", "SOLD_HANDED_OUT"))): ?>


                                    <?php if ($this->car['auction_price'] > 0 && $this->auction): ?>
                                        <li><a id="show_offer_form" class="big-link" href="#offer_form"
                                               rel="prettyPhoto"
                                               title=""><?= $this->translate->_('MAKE_OFFER_TITLE') ?></a></li>
                                    <?php else : ?>

                                        <?php if(false): ?>
                                            <li><a class="big-link" href="#" title="" data-toggle="modal" data-target="#contactReservation"><?= $this->translate->_('MAKE_RESERVATION') ?></a></li>
                                        <?php endif; ?>

                                        <li><a class="big-link" href="#" title="" data-toggle="modal" data-target="#contactTestDrive"><?= $this->translate->_('TEST_DRIVE') ?></a>
                                        </li>

                                    <?php endif; ?>


                                <?php endif ?>

                                <li class="pt-4">
                                    <a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarSendOffer', true) . $this->getString ?>" data-toggle="modal" data-target="#contactSendOffer"> <?= $this->translate->_('SEND_OFFER') ?></a>
                                </li>
                                <li>
                                    <a class="hidden-md-up" href="sms:?&body=To jest link do dokladnego opisu auta <?= $this->car['make_name'] . ' '. $this->car['model_name'] . ' '. $this->car['build_year'] ?> o ktorym rozmawialismy: <?= $this->domain . $this->carLink($this->car, null, $noMarkup=true) ?>"> <?= $this->translate->_('SEND_OFFER_SMS') ?></a></li>
                                <li>
                                    <a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarContactCartaker', true) ?>" data-toggle="modal" data-target="#contactCartaker"> <?= $this->translate->_('CONTACT_CARETAKER') ?></a>
                                </li>
                                <li>
                                    <a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id']), 'showCar', true) ?>?print=true">  <?= $this->translate->_('PRINT_OFFER') ?></a>
                                </li>
                                <li>
                                    <a href="<?= $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)), 'forceShowCarTestDrive', true) ?>" data-toggle="modal" data-target="#contactTestDrive"> <?= $this->translate->_('TEST_DRIVE') ?></a>
                                </li>
                                <li>
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($this->domain . $this->url(array('language' => $this->language, 'id' => $this->car['car_id'], 'description' => $this->carPermalink($this->car)))) ?>"
                                       target="_blank">Facebook</a></li>
                            </ul>

                        </div>


                    </div>
                </div>
            </div>
        </div>


    <div class="container">


        <div class="row py-4 details">


            <?php


            $cars = new Model_Cars_Cars();
            $gearboxTypes = $cars->getGearboxTypesByName();
            $countries = $cars->getCountriesByName();
            $categories = $cars->getCarCategoriesByName(true,false,'id');
            $types = $cars->getCarTypes('id')


            ?>
            <ul class="col-lg-4">
                <li><strong><?= $this->translate->_('CAR_TYPE') ?></strong> <?=  $this->translate->_('type_'.$types[$this->car['car_type_id']]) ?></li>
                <li><strong><?= $this->translate->_('MAKE') ?></strong> <?= $this->car['make_name'] ?></li>
                <li><strong><?= $this->translate->_('MODEL') ?></strong> <?= $this->car['model_name'] ?></li>
                <li><strong><?= $this->translate->_('PRODUCTION_YEAR') ?></strong> <?= $this->car['build_year']  ?></li>
                <li><strong><?= $this->translate->_('ODOMETER') ?></strong> <?= $this->mileage($this->car['odometer']) ?> <?= $this->escape($this->translate->_('MILEAGE_' . $this->car['odometer_unit'])) ?></li>
                <li><strong><?= $this->translate->_('CATEGORY') ?></strong> <?=  $categories[$this->car['vehicle_category_id']]  ?></li>

            </ul>
            <ul class="col-lg-4">
                <li><strong><?= $this->translate->_('FUEL') ?></strong> <?= $this->translate->_($this->car['fuel_key']);  ?></li>
                <li><strong><?= $this->translate->_('POWER') ?></strong> <?= $this->car['power'] . ' ' . $this->translate->_($this->car['power_unit']);  ?></li>
                <li><strong><?= $this->translate->_('ENGINE_CAPACITY') ?></strong> <?= $this->car['cubic_capacity']   ?></li>
                <li><strong><?= $this->translate->_('GEARBOX') ?></strong> <?=  $gearboxTypes[$this->car['gearbox_type_id']]  ?></li>
                <li><strong><?= $this->translate->_('COLOR') ?></strong> <?= $this->translate->_('COLOR_' . $this->car['colour_key'])  ?></li>
                <li><strong><?= $this->translate->_('VD_METALLIC') ?></strong> <?= $this->car['colour_metallic'] == 'y' ?  $this->translate->_('YES') :  $this->translate->_('NO') ?></li>



            </ul>
            <ul class="col-lg-4">

                <li><strong><?= $this->translate->_('FINANCING') ?></strong> <?= $this->translate->_('YES')   ?></li>
                <li><strong><?= $this->translate->_('vat_invoice') ?></strong> <?= $this->car['price_type_key'] == 'netto'  ?$this->translate->_('YES') :  $this->translate->_('NO')  ?></li>
                <li><strong><?= $this->translate->_('SEAT_COUNT') ?></strong> <?=  $this->car['seat_count'] ?></li>
                <li><strong><?= $this->translate->_('DOOR_COUNT') ?></strong> <?=  $this->car['door_count']  ?></li>
                <li><strong><?= $this->translate->_('ORIGIN_COUNTRY') ?></strong> <?=  $countries[$this->car['origin_country']]  ?></li>
                <li><strong><?= $this->translate->_('PRODUCTION_COUNTRY') ?></strong> <?=  $countries[$this->car['production_country_id']]  ?></li>
            </ul>

        </div>


    </div>
        <?php if ($this->language == "pl"): ?>
            <?php if (!empty($this->car['description'])) : ?>
                <div class="equipment">
                    <div class="container py-4">

                        <h4 class="pb-3">Wyposażenie <span class="orange-color">ponadstandardowe</span></h4>

                        <div class="row">
                            <div class="col-md-12">
                                <?= $this->carDescription($this->car['description'],false) ?>
                            </div>

                        </div>

                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($this->car['description2']) || !empty($this->car['description3'])) : ?>
            <div class="container py-4">
                <div class="row">
                    <?php if (!empty($this->car['description2'])) : ?>

                        <div class="col-md-6">
                            <h4 class="pb-3">Historia <span class="orange-color">auto</span></h4>
                        </div>

                    <?php endif; ?>

                    <?php if (!empty($this->car['description3'])) : ?>

                        <div class="col-md-6">
                            <h4 class="pb-3">Finansowanie <span class="orange-color">auto</span></h4>
                        </div>

                    <?php endif; ?>
                </div>
                <div class="row">
                    <?php if (!empty($this->car['description2'])) : ?>

                    <div class="col-md-6">
                        <?= $this->carDescription($this->car['description2'],false,'col-md-6') ?>
                    </div>
                    <?php endif; ?>
                    <?php if (!empty($this->car['description3'])) : ?>
                        <div class="col-md-6">
                            <?= $this->carDescription($this->car['description3'],false,'col-md-6') ?>
                        </div>
                    <?php endif; ?>

                </div>


            </div>
            <?php endif; ?>
        <?php else: ?>

            <div class="equipment">
                <div class="container py-4">

                    <h4 class="pb-3">Wyposażenie <span class="orange-color">ponadstandardowe</span></h4>

                    <div class="row">

                        <?php
                        $separator = "\n- ";
                        $checkboxes = null;
                        $descriptionsCount = count($this->car['descriptions']);
                        if (count($this->car['descriptions']) > 0 && $this->language != "pl") {
                            $i = 0;
                            foreach ($this->car['descriptions'] as $d) {
                                $i++;
                                $checkboxes .= $d['value'] . ($descriptionsCount > $i ? $separator : '');
                            }
                        }
                        if ($checkboxes) {
                            $checkboxes = $separator . $checkboxes;
                        }

                        $checkboxes .=  implode($separator, $this->car['features']);

                        if (count($this->car['extras']) > 0) {
                            $checkboxes .= $separator . implode($separator, $this->car['extras']);
                        }
                        if ($checkboxes) {

                            echo $this->carDescription($checkboxes);
                        }
                        ?>

                    </div>

                </div>
            </div>

        <?php endif; ?>
        <?php if ($this->similarMakesWithCounts && isset($this->similarMakesWithCounts['cars']) && is_array($this->similarMakesWithCounts['cars']) && count($this->similarMakesWithCounts['cars']) > 0): ?>
        <div class="container py-4">

            <div class="similar-cars car-list">

                <div class="row">
                    <div class="col-md-12">
                        <h4><?= $this->translate->_('SIMILAR_CARS')?></h4>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="pull-right">

						<form action="<?= $this->url(array('language' => $this->language), 'list', true) ?>"
                              method="post">
							<?php foreach ($this->similarMakesWithCounts['params'] as $key => $value): ?>
                                <?php if (is_array($value)): ?>
                                    <?php foreach ($value as $val): ?>
                                        <input type="hidden" name="<?= $key ?>[]" value="<?= $this->escape($val) ?>"/>
                                    <?php endforeach ?>
                                <?php else: ?>
                                    <input type="hidden" name="<?= $key ?>" value="<?= $this->escape($value) ?>"/>
                                <?php endif ?>
                            <?php endforeach ?>
                            <button class="btn btn-action btn-show-all" type="submit"><?= $this->translate->_('SIMILAR_MAKES_ALL') ?></button>
						</form>

                        </div>
                    </div>
                </div>




                <div class="cars row block pt-3">
                <?php foreach ($this->similarCars as $car): ?>
                    <?= $this->partial('car-item-block.phtml', array('car' => $car, 'translate' => $this->translate, 'language' => $this->language, 'language_row' => $this->language_row, 'isFavourite' => in_array($car['car_id'], $this->favouritesIds))) ?>
                <?php endforeach; ?>
                </div>
            </div>

        </div>

    <?php endif ?>






</div>


    <?= $this->partial('car-form-modal.phtml',
        array(
            'car' => $this->car,
            'title' => $this->translate->_('TEST_DRIVE'),
            'form' => $this->testDriveForm,
            'id' => 'contactTestDrive',
            'translate' => $this->translate,
        ))
    ?>

    <?= $this->partial('car-form-modal.phtml',
        array(
            'car' => $this->car,
            'title' => $this->translate->_('CONTACT_CARETAKER'),
            'form' => $this->testDriveForm2,
            'id' => 'contactCartaker',
            'translate' => $this->translate,
        ))
        ?>
    <?= $this->partial('car-form-modal.phtml',
        array(
            'car' => $this->car,
            'title' => 'Zapytaj o kredyt lub leasing',
            'form' => $this->testDriveForm3,
            'id' => 'contactFinancing',
            'translate' => $this->translate,
        ))
    ?>


    <?= $this->partial('car-form-modal.phtml',
        array(
            'car' => $this->car,
            'title' => $this->translate->_('I_SPEEK_LANG_HOW_CAN_I_HELP'),
            'form' => $this->testDriveForm4,
            'id' => 'contactLanguage',
            'translate' => $this->translate,
        ))
    ?>

    <?= $this->partial('car-form-modal.phtml',
        array(
            'car' => $this->car,
            'title' => $this->translate->_('SEND_OFFER'),
            'form' => $this->sendOfferForm,
            'id' => 'contactSendOffer',
            'translate' => $this->translate,
        ))
    ?>

    <?= $this->partial('car-form-modal.phtml',
        array(
            'car' => $this->car,
            'title' => $this->translate->_('CONTACT_COMMENT_TITLE'),
            'form' => $this->commentForm ,
            'id' => 'contactComment',
            'translate' => $this->translate,
        ))
    ?>
    <?= $this->partial('car-form-modal.phtml',
        array(
            'car' => $this->car,
            'title' => $this->translate->_('MAKE_OFFER_TITLE'),
            'form' => $this->commentForm ,
            'id' => 'contactOffer',
            'translate' => $this->translate,
        ))
    ?>
    <?php if (false && $this->reservationForm): ?>
        <?= $this->partial('car-form-reservation-modal.phtml',
            array(
                'car' => $this->car,
                'title' => $this->translate->_('RESERVATION_FORM_TITLE'),
                'form' => $this->reservationForm ,
                'id' => 'contactReservation',
                'translate' => $this->translate,
                'language_row' => $this->language_row,
            ))
        ?>
    <?php endif; ?>

    <?php if ($this->leasingForm): ?>
        <?= $this->partial('car-form-leasing-modal.phtml',
            array(
                'car' => $this->car,
                'form' => $this->leasingForm,
                'id' => 'contactLeasing',
                'translate' => $this->translate,
            ))
        ?>
    <?php endif; ?>

    <?php if ($this->creditForm): ?>
        <?= $this->partial('car-form-credit-modal.phtml',
            array(
                'car' => $this->car,
                'form' => $this->creditForm ,
                'priceGross' => $this->priceGross,
                'id' => 'contactCredit',
                'translate' => $this->translate,
            ))
        ?>
    <?php endif; ?>





    <script type="text/javascript">
    <?php $this->inlineScript()->captureStart() ?>

    $(function () {
        <?php if ($this->showContactCaretakerForm): ?>
            $('#contactCartaker').modal('show')
        <?php endif?>
        <?php if ($this->showSendOfferForm): ?>
            $('#contactSendOffer').modal('show')
        <?php endif?>
        <?php if ($this->showContactFinancingForm): ?>
            $('#contactFinancing').modal('show')
        <?php endif ?>
        <?php if ($this->showContactLanguageForm): ?>
            $('#contactLanguage').modal('show')
        <?php endif?>
        <?php if ($this->showTestDriveForm): ?>
            $('#contactTestDrive').modal('show')
        <?php endif?>
        <?php if ($this->showCommentForm): ?>
            $('#contactComment').modal('show')
        <?php endif ?>
        <?php if ($this->showOfferForm): ?>
            $('#contactOffer').modal('show')
        <?php endif ?>

        <?php if (false && $this->showReservationForm) : ?>
            $('#contactReservation').modal('show')
        <?php endif; ?>
    });


    <?php if (false && $this->reservationForm): ?>

        var post = "";


        $('body').on('click', "#contactReservation form #preview", function (e) {

            post = '&' + $(this).attr("name") + "=1";
        });
        $('body').on('submit', '#contactReservation form', function (e) {
            var sdata = $(this).serialize() + post;
            post = "";
            e.preventDefault();
            $.post(
                $(this).attr('action'),
                sdata,
                function (data) {

                    if (!data.ok) {

                        $("#contactReservation").replaceWith(data.formHtml);
                        $('.modal-backdrop').remove();
                        $('#contactReservation').modal('show');

                        $( "#contactReservation .g-recaptcha" ).each(function( index ) {
                            grecaptcha.render(this, {'sitekey' : '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-'});
                        });


                    }
                    else {
                        if (data.preview) {
                            var a = document.createElement('a');
                            a.setAttribute("href", data.url + '?' + sdata);
                            a.setAttribute("target", "_blank");

                            var dispatch = document.createEvent("HTMLEvents");
                            dispatch.initEvent("click", true, true);
                            console.log(a);
                            a.dispatchEvent(dispatch);
                            window.open(data.url + '?' + sdata, "_blank");
                        }
                        else {
                            var inputsHtml = '';
                            for (var i in data.data) {
                                inputsHtml += ' <input type="hidden" name="' + i + '" value="' + data.data[i].replace('"', '&quot;') + '" />';
                            }
                            $('<form method="post" action="' + data.url + '">' + inputsHtml + '</form>').appendTo('body').submit();
                        }
                    }
                },
                'json'
            );
            return false;

        });
    <?php endif ?>

    function calculate(object) {
        var form = $(object);

        $.ajax({
            type: 'GET',
            url: '<?= $this->url(array("language" => $this->language), "getInstalments", true); ?>',
            data: form.serialize(),
            dataType: 'json',
            async: false,
            success: function (data) {
                form.find(".result").find('span').html(data.instalment_gross);
                if (form.find("[name='type']").val() == 'leasing') {
                    var fields = ['instalments_no', 'price_net', 'price_tax', 'price_gross', 'contribution_percent', 'contribution_net', 'contribution_vat', 'contribution_gross', 'interest', 'instalment_net', 'instalment_vat', 'instalment_gross', 'buyout_percent', 'buyout_net', 'buyout_gross', 'buyout_vat'];
                    for (var f in fields) {
                        $("#contactLeasing #calc_" + fields[f]).text(data[fields[f]]);
                    }
                }
            },
            error: function () {
            }
        });
        return false;
    }

    $('body').on('submit', '#contactLeasing form', function (e) {
        return calculate(this);
    });

    $('body').on('submit', '#contactCredit form', function (e) {
        return calculate(this);
    });


    if ($("#contactLeasing input#price").length > 0 && $("#contactLeasing select#contribution").length > 0) {


        $('body').on('keyup', "#contactLeasing input#price", function (e) {
                var value = parseInt($(this).val(), 10);
                if (isNaN(value)) return;

                var opts = $("#contactLeasing select#contribution option");
                opts.each(function () {
                    var label = $(this).text();
                    var contrib = $(this).attr('value');
                    var newVal = contrib * value;

                    label = label.replace(/=\s([0-9]+\s)?([0-9])+\s/g, "= " + number_format(newVal, 0, '.', ' ') + " ");
                    $(this).text(label).attr('label', label);
                });
            });
        $('body').on('change', "#contactLeasing input#price", function (e) {
                $(this).keyup();
            });
        $('body').on('blur', "#contactLeasing input#price", function (e) {
                $(this).keyup();
            });
        ;
    }


    if ($('.bxslider img').length > 0) {

        // Cache the thumb selector for speed
        var thumb = $('#gallery-thumbs').find('.thumb');

        // How many thumbs do you want to show & scroll by
        var visibleThumbs = 4;

        // Put slider into variable to use public functions
        var gallerySlider = $('.bxslider').bxSlider({
            controls: true,
            pager: false,
            easing: 'easeInOutQuint',
            infiniteLoop: false,
            speed: 500,
            nextText: '&gt;',
            prevText: '&lt;',

            onSlideAfter: function ($slideElement, oldIndex, newIndex) {
                thumb.removeClass('pager-active');
                thumb.eq(newIndex).addClass('pager-active');
                // Action next carousel slide on one before the end, so newIndex + 1
                slideThumbs(newIndex + 1, visibleThumbs);
            }
        });

        // When clicking a thumb
        thumb.click(function (e) {

            // -6 as BX slider clones a bunch of elements
            gallerySlider.goToSlide($(this).closest('.thumb-item').index());

            // Prevent default click behaviour
            e.preventDefault();
        });

        // Function to calculate which slide to move the thumbs to
        function slideThumbs(currentSlideNumber, visibleThumbs) {

            // Calculate the first number and ignore the remainder
            var m = Math.floor(currentSlideNumber / visibleThumbs);
            // Multiply by the number of visible slides to calculate the exact slide we need to move to
            var slideTo = m;

            // Tell the slider to move
            thumbsSlider.goToSlide(slideTo);
        }

        // When you click on a thumb
        $('#gallery-thumbs').find('.thumb').click(function () {

            // Remove the active class from all thumbs
            $('#gallery-thumbs').find('.thumb').removeClass('pager-active');

            // Add the active class to the clicked thumb
            $(this).addClass('pager-active');

        });

        // Thumbnail slider
        var thumbsSlider = $('#gallery-thumbs').bxSlider({
            mode: 'vertical',
            controls: false,
            pager: false,
            easing: 'easeInOutQuint',
            //displaySlideQty: visibleThumbs,
            //moveSlideQty: visibleThumbs,
            infiniteLoop: false,
            slideWidth: 141,
            minSlides: visibleThumbs,
            maxSlides: visibleThumbs,
            slideMargin: 8
        });

    }
    <?php $this->inlineScript()->captureEnd() ?>
</script>
