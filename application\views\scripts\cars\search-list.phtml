<div id="left_static" class="column_left_1">
    <?php if($this->logged): ?>
	<div class="container">
		<?= $this->render('user_cp_menu.phtml') ?>
	</div>
    <?php endif;?>
</div>

<div id="content_static" class="column_right_1">
	<h1 id="static_title"><?= $this->translate->_('SEARCH_LIST') ?></h1>
	<div id="content_static_container" class="<?= $this->containerClass ?>">
		<?php foreach($this->storedSearches as $search): ?>
			<div class="search_item">
				<h2><?= $search['title'] ?></h2>
				<div>
					<a href="<?= $this->url(array('hash' => $search['hash']), 'editSearchList'); ?>">
						<span class="for_icon"></span>
						<?= $this->translate->_('EDIT') ?>
					</a>
					<a href="<?= $this->url(); ?>?del=<?= $search['hash']; ?>">
						<span class="for_icon"></span>
						<?= $this->translate->_('DELETE') ?>
					</a>
					<a href="<?= $this->url(); ?>?set=<?= $search['hash']; ?>">
						<span class="for_icon"></span>
						<?= $this->translate->_('SHOW_RESULTS') ?>
					</a>
				</div>
			</div>
		<?php endforeach; ?>
	</div>
	<div class="clear"></div>
</div>


<div class="clear"></div>

