<?php

/*

UPDATE `aa_cars` SET price_gross = price * 1.23 WHERE price_type_key = "netto";
UPDATE `aa_cars` SET price_gross = price WHERE price_type_key = "brutto";
UPDATE `aa_cars` SET promotion_price_gross = promotion_price * 1.23 WHERE price_type_key = "netto" AND promotion_price IS NOT NULL;
UPDATE `aa_cars` SET promotion_price_gross = promotion_price WHERE price_type_key = "brutto" AND promotion_price IS NOT NULL;

*/

class Model_Cars_Cars extends Model_Base {
		
	const MILEAGE_KM = 'KM';
	const MILEAGE_MIL = 'MIL';
	
	const PRICE_NET = 'netto';
	const PRICE_GROSS = 'brutto';
    
    const NEW_CAR_LOCATION = 16;
	
	protected $_cache;

	public function __construct($options=null) {
		parent::__construct($options);
		
		$this->_cache = Zend_Registry::get('Cache');
	}
	
	public static function getCarMileageUnits() {
		return array(
			self::MILEAGE_KM => 'km',
			self::MILEAGE_MIL => 'mil'
		);
	}
	
	public function deleteCarMake($id) {
		$id = (int)$id;
		
		try {
			$this->db->beginTransaction();
			$this->db->delete(
				$this->tables['car_makes'],
				'id = ' .$id 
			);
			
			$this->db->commit();
						
			$this->_cache->clean(Zend_Cache::CLEANING_MODE_MATCHING_TAG, array('cars'));
		}
		catch (Exception $e) {
			$this->db->rollBack();
			throw $e;
		}
	}
	
	public function deleteCarModel($id) {
		$id = (int)$id;
		
		try {
			$this->db->beginTransaction();
			$this->db->delete(
				$this->tables['car_models'],
				'id = ' .$id 
			);
			
			$this->db->commit();
						
			$this->_cache->clean(Zend_Cache::CLEANING_MODE_MATCHING_TAG, array('cars'));
		}
		catch (Exception $e) {
			$this->db->rollBack();
			throw $e;
		}
	}
	
	public function addCarMakesFromOtoMoto() {
		$types = $this->getCarTypes();
		
		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);
		
		foreach ($types as $type_id => $type_name) {
			$makes = $om->getMakesAssoc($type=$type_name);
			foreach ($makes as $om_make_id => $make_name) {
				try {
					$this->addCarMake($type_id, $om_make_id, $make_name);
				}
				catch (Exception $e) {
					$errors[] = $e->getMessage();
				}
			}
		}
		
		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia marek pojazdow:<br />".implode("<br />", $errors));
		}
	}
	
	public function getCarTypes($assoc=true) {
		$select = $this->db->select()
			->from($this->tables['car_types'], array('id', 'type'));
		return $this->db->fetchPairs($select);
	}

	public function addCarMake($type_id, $om_id, $make_name, $srMakeId = null, $specialFlag = null) {
		$this->db->insert(
			$this->tables['car_makes'],
			array(
				'om_id'		=>	((int)$om_id > 0) ? (int)$om_id : new Zend_Db_Expr('NULL'),
				'type_id'	=>	(int)$type_id,
				'name'		=>	$make_name,
                                'sr_make_id'    =>      $srMakeId,
                                'special_flag' =>       $specialFlag
			)
		);
		return $this->db->lastInsertId($this->tables['car_makes'], 'id');
	}
	
	public function addCarModelsFromOtoMoto() {
		$types = $this->getCarTypes();

		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);

		foreach ($types as $type_id => $type_name) {
			if ($type_id == 2) continue; //trucks don't have models as of 26.10.2010
			//$makes = $om->getMakesAssoc($type=$type_name);
			$makes = $this->getCarMakes($type_id);
			foreach ($makes as $make) {
				try {
					$models = $om->getModelsAssoc($type=$type_name, $make['om_id']);
				}
				catch (Exception $e) {
					$errors[] = "make_id: " . $make['id'] . " " . $e->getMessage();
					continue;
				}
				foreach ($models as $om_model_id => $model_name) {
					try {
						$this->addCarModel($make['id'], $om_model_id, $model_name);
					}
					catch (Exception $e) {
						$errors[] = $e->getMessage();
					}
				}

			}
		}

		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia modeli pojazdow:<br />".implode("<br />", $errors));
		}
	}
	
	public function getCarMakes($type_id=false, $assoc=false, $assocId=false) {

        $cols = $assocId ? array('id', 'display_name' => 'name') :  array('name', 'display_name' => 'name');

		if ($assoc) {

		    if($type_id)
            {
                $select = $this->db->select()
				->from($this->tables['car_makes'],$cols)
				->order('name ASC');

                is_array($type_id) ? $select->where("type_id IN (?)",$type_id) : $select->where("type_id = ?",$type_id);
            }
		    else
			$select = $this->db->select()
				->from($this->tables['car_makes'], $cols)
				->order('name ASC');
			return $this->db->fetchPairs($select);
		}
		else {
			$select = $this->db->select()
				->from($this->tables['car_makes'])
				->where('type_id = ' . (int)$type_id)
				->order('name ASC');
			return $this->db->fetchAll($select);
		}
	}
	
	public function addCarModel($make_id, $om_id, $model_name,$srModelId=null) {
		$this->db->insert(
			$this->tables['car_models'],
			array(
				'om_id'		=>	((int)$om_id > 0) ? (int)$om_id : new Zend_Db_Expr('NULL'),
				'make_id'	=>	(int)$make_id,
				'name'		=>	$model_name,
                                'sr_model_id'   =>      $srModelId,
			)
		);
		return $this->db->lastInsertId($this->tables['car_models'], 'id');
	}
	
	public function addCarModelVersionsFromOtoMoto() {
		$types = $this->getCarTypes();

		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);

		foreach ($types as $type_id => $type_name) {
			if ($type_id == 2) continue; //TRUCKs don't have models as of 26.10.2010
			$makes = $this->getCarMakes($type_id);
			foreach ($makes as $make) {
				try {
					$models = $this->getCarModels($make['id']);
				}
				catch (Exception $e) {
					$errors[] = "model_id: " . $model['id'] . " " . $e->getMessage();
					continue;
				}
				foreach ($models as $model) {
					try {
						$versions = $om->getVersionsAssoc($type_name, $make['om_id'], $model['om_id']);
						foreach ($versions as $om_id => $name) {
							$this->addCarModelVersion($om_id, $model['id'], $name);
						}
					}
					catch (Exception $e) {
						$errors[] = "type_id: " . $type_id ." make_id: " . $make['id'] . " model_id: " . $model['id'] . " " . $e->getMessage();
						continue;
					}
				}

			}
		}

		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia wersji modeli pojazdow:<br />".implode("<br />", $errors));
		}
	}
	
	public function getCarModels($make_id, $assoc=false) {
		if ($assoc) {
			$select = $this->db->select()
				->from($this->tables['car_models'], array('id', 'name'))
				->where('make_id = ' . (int)$make_id)
				->order('name ASC');
			return $this->db->fetchPairs($select);
		}
		else {
			$select = $this->db->select()
				->from($this->tables['car_models'])
				->where('make_id = ' . (int)$make_id)
				->order('name ASC');
			return $this->db->fetchAll($select);
		}
	}
	
	public function addCarModelVersion($om_id, $model_id, $version_name) {
		$this->db->insert(
			$this->tables['car_model_versions'],
			array(
				'om_id'		=>	(int)$om_id,
				'model_id'	=>	(int)$model_id,
				'name'		=>	$version_name
			)
		);
		return $this->db->lastInsertId($this->tables['car_model_versions'], 'id');
	}
	
	public function addCarFull($data, $doTransaction=true) {
		try {
			if ($doTransaction) $this->db->beginTransaction();
			$carsTable = $this->tables['cars'];
			$extrasTable = $this->tables['cars_extras'];
			$featuresTable = $this->tables['cars_features'];

			if ($data['is_archived'] == 1) {
				$carsTable = $this->tables['archive_cars'];
				$extrasTable = $this->tables['archive_cars_extras'];
				$featuresTable = $this->tables['archive_cars_features'];
			}
			unset($data['is_archived']);

			$extras = $data['extras'];
			unset($data['extras']);

			$features = $data['features'];
			unset($data['features']);

			$videos = $data['videos'];
			unset($data['videos']);

			$descriptions = $data['descriptions'];
			unset($data['descriptions']);

            $exchangeModels = $data['exchange_models'];
            unset($data['exchange_models']);

			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$vat = $opt['instalments']['VAT'];
			$data['price_gross'] = ($data['price_type_key'] == "netto" ? $data['price'] * (1 + $vat) : $data['price']);
			if (!empty($data['promotion_price']) && !($data['promotion_price'] instanceof Zend_Db_Expr)) {
				$data['promotion_price_gross'] = ($data['price_type_key'] == "netto"  ? $data['promotion_price'] * (1 + $vat) : $data['promotion_price']);
			}

			$this->db->insert(
				$carsTable,
				$data + array(
					'added_datetime' => new Zend_Db_Expr('NOW()')
				)
			);

			$carId = $this->db->lastInsertId($carsTable, 'id');
			if (empty($carId)) {
				throw new Exception("carId empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
			}

			foreach ($extras as $item) {
				$this->db->insert(
					$extrasTable,
					array(
						'car_id' => $carId,
						'extra_id' => $item
					)
				);
			}

			foreach ($features as $item) {
				$this->db->insert(
					$featuresTable,
					array(
						'car_id' => $carId,
						'feature_id' => $item
					)
				);
			}

            foreach ($exchangeModels as $item) {
                $this->db->insert(
                    $this->tables['cars_exchange_models'],
                    array(
                        'car_id' => $carId,
                        'model_id' => $item
                    )
                );
            }

			foreach ($videos as $item) {
				$this->db->insert(
					$this->tables['cars_videos'],
					array(
						'car_id' => $carId,
						'ord' => (int)$item['cv_ord'],
						'provider_id' => (int)$item['cv_ord'],
						'video_id' => $item['cv_video_id']
					)
				);
			}

			if (is_array($descriptions)) {
				$toAdd = array();
				foreach ($descriptions as $categoryId => $descData) {
					foreach ($descData['checkboxes'] as $descId => $value) {
						if ($value == "1") {
							$toAdd[$categoryId][] = $descId;
						}
					}
				}

				foreach ($toAdd as $categoryId => $descIds) {
					foreach ($descIds as $descriptionId) {
						$this->db->insert(
							$this->tables['cars_descriptions'],
							array(
								'car_id' => $carId,
								'description_id' => $descriptionId
							)
						);
						$cdId = $this->db->lastInsertId($this->tables['cars_descriptions'], 'id');

						if (isset($descriptions[$categoryId]['text'][$descriptionId]) && is_array($descriptions[$categoryId]['text'][$descriptionId]) && count($descriptions[$categoryId]['text'][$descriptionId]) > 0) {
							foreach ($descriptions[$categoryId]['text'][$descriptionId] as $key => $value) {
								$this->db->insert(
									$this->tables['cars_descriptions_values'],
									array(
										'car_description_id' => $cdId,
										'key' => $key,
										'value' => $value
									)
								);
							}
						}
					}
				}
			}

			if ($doTransaction) $this->db->commit();

			$this->_cache->clean(Zend_Cache::CLEANING_MODE_MATCHING_TAG, array('cars'));

			return $carId;
		}
		catch (Exception $e) {
			if ($doTransaction) $this->db->rollBack();
			throw $e;
		}
	}
	
	public function addCarQuick($data) {
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$vat = $opt['instalments']['VAT'];
		$data['price_gross'] = ($data['price_type_key'] == "netto" ? $data['price'] * (1 + $vat) : $data['price']);

		$this->db->insert(
			$this->tables['cars'],
			$data + array(
				'added_datetime' => new Zend_Db_Expr('NOW()')
			)
		);

		$makeName = $this->db->fetchOne($this->db->select()->from(array($this->tables['car_makes']), array('name'))->where('id = ' . (int)$data['make_id']));
		$this->_cache->remove('car_makes_with_counts_by_name');
		$this->_cache->remove('car_models_with_counts_make_name_' . $this->cacheNameEncode($makeName));

		return $this->db->lastInsertId($this->tables['cars'], 'id');
	}
	
	public function cacheNameEncode($string) {
		return My_FilterPermalink::filter($string, $separator="_", $trim=true);
	}
	
	public function addColoursFromOtoMoto() {
		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);

		$colours = $om->getColoursAssoc();
		foreach ($colours as $colour_id => $colour_name) {
			try {
				$this->addColour($colour_id, $colour_name);
			}
			catch (Exception $e) {
				$errors[] = $e->getMessage();
			}
		}

		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia kolorow:<br />".implode("<br />", $errors));
		}
	}
	
	public function addColour($id, $name) {
		$this->db->insert(
			$this->tables['colours'],
			array(
				'id'	=>	(int)$id,
				'name'	=>	$name,
				'key'	=>	''
			)
		);
	}
	
	public function addCountriesFromOtoMoto() {
		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);

		$countries = $om->getCountriesAssoc();
		foreach ($countries as $om_id => $name) {
			try {
				$this->addCountry($om_id, $name);
			}
			catch (Exception $e) {
				$errors[] = $e->getMessage();
			}
		}

		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia krajow:<br />".implode("<br />", $errors));
		}
	}
	
	public function addCountry($om_id, $name) {
		$this->db->insert(
			$this->tables['countries'],
			array(
				'om_id'	=>	$om_id,
				'name'	=>	$name
			)
		);
		return $this->db->lastInsertId($this->tables['countries'], 'id');
	}
	
	public function addFuelTypesFromOtoMoto() {
		$types = $this->getCarTypes();

		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);

		foreach ($types as $type_id => $type_name) {
			$fuel_types = $om->getFuelTypesAssoc($type=$type_name);
			foreach ($fuel_types as $key => $name) {
				try {
					$this->addFuelType($type_id, $key, $name);
				}
				catch (Exception $e) {
					$errors[] = $e->getMessage();
				}
			}
		}

		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia rodzajow paliwa:<br />".implode("<br />", $errors));
		}
	}
	
	public function addFuelType($type_id, $key, $name) {
		$this->db->insert(
			$this->tables['fuel_types'],
			array(
				'type_id'	=>	(int)$type_id,
				'key'		=>	$key,
				'name'		=>	$name
			)
		);
		return $this->db->lastInsertId($this->tables['fuel_types'], 'id');
	}
	
	public function addGearboxType($key, $name) {
		$this->db->insert(
			$this->tables['gearbox_types'],
			array(
				'key'		=>	$key,
				'name'		=>	$name
			)
		);
		return $this->db->lastInsertId($this->tables['gearbox_types'], 'id');
	}
	
	public function addGearboxTypesFromOtoMoto() {
		die("Do przegladniecia po usunieciu type_id w gearbox_types");
		$types = $this->getCarTypes();

		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);

		foreach ($types as $type_id => $type_name) {
			$gearbox_types = $om->getGearboxTypesAssoc($type=$type_name);
			foreach ($gearbox_types as $key => $name) {
				try {
					$this->addGearboxType($type_id, $key, $name);
				}
				catch (Exception $e) {
					$errors[] = $e->getMessage();
				}
			}
		}

		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia rodzajow skrzyni biegow:<br />".implode("<br />", $errors));
		}
	}
	
	public function addVehicleCategoriesFromOtoMoto() {
		$types = $this->getCarTypes();

		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);

		foreach ($types as $type_id => $type_name) {
			$categories = $om->getVehicleCategoriesAssoc($type=$type_name);
			foreach ($categories as $key => $name) {
				try {
					$this->addVehicleCategory($type_id, $key, $name);
				}
				catch (Exception $e) {
					$errors[] = $e->getMessage();
				}
			}
		}

		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia rodzajow nadwozia:<br />".implode("<br />", $errors));
		}
	}
	
	public function addVehicleCategory($type_id, $key, $name) {
		$this->db->insert(
			$this->tables['vehicle_categories'],
			array(
				'type_id'	=>	(int)$type_id,
				'key'		=>	$key,
				'name'		=>	$name
			)
		);
		return $this->db->lastInsertId($this->tables['vehicle_categories'], 'id');
	}
	
	public function addVehicleExtrasFromOtoMoto() {
		$types = $this->getCarTypes();

		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);

		foreach ($types as $type_id => $type_name) {
			$extras = $om->getVehicleExtrasAssoc($type=$type_name);
			foreach ($extras as $om_key => $name) {
				try {
					$this->addVehicleExtra($type_id, $key=$om_key, $om_key, $name);
				}
				catch (Exception $e) {
					$errors[] = $e->getMessage();
				}
			}
		}

		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia rodzajow dodatkow:<br />".implode("<br />", $errors));
		}
	}
	
	public function addVehicleExtra($type_id, $key, $om_key, $name) {
		$this->db->insert(
			$this->tables['vehicle_extras'],
			array(
				'type_id'	=>	(int)$type_id,
				'key'		=>	$key,
				'om_key'	=>	empty($om_key) ? new Zend_Db_Expr('NULL') : $om_key,
				'name'		=>	$name
			)
		);
		return $this->db->lastInsertId($this->tables['vehicle_extras'], 'id');
	}
	
	public function addVehicleFeaturesFromOtoMoto() {
		$types = $this->getCarTypes();

		$errors = array();
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);

		foreach ($types as $type_id => $type_name) {
			$features = $om->getVehicleFeaturesAssoc($type=$type_name);
			foreach ($features as $om_key => $name) {
				try {
					$this->addVehicleFeature($type_id, $key=$om_key, $om_key, $name);
				}
				catch (Exception $e) {
					$errors[] = $e->getMessage();
				}
			}
		}

		if (count($errors) > 0) {
			throw new Exception("Wystapily nastepujace bledy przy probie uzupelnienia rodzajow dodatkow:<br />".implode("<br />", $errors));
		}
	}
	
	public function addVehicleFeature($type_id, $key, $om_key, $name) {
		$this->db->insert(
			$this->tables['vehicle_features'],
			array(
				'type_id'	=>	(int)$type_id,
				'key'		=>	$key,
				'om_key'	=>	empty($om_key) ? new Zend_Db_Expr('NULL') : $om_key,
				'name'		=>	$name
			)
		);
		return $this->db->lastInsertId($this->tables['vehicle_features'], 'id');
	}
    
	public function changeCarPatronBulk($data) {
		$this->db->update(
			$this->tables['cars'],
			array(
				'caretaker_sr_id' => (int)$data['to']
			),
			"caretaker_sr_id = " . (int)$data['from']
		);


        // zmieniamy takze mailingi
        $this->db->update(
            $this->tables['user_searches'],
            array(
                'added_by_sr_id' => (int)$data['to']
            ),
            "added_by_sr_id = " . (int)$data['from']
        );


	}
    
	public function deleteCar($srId) {
		$srId = (int)$srId;

		try {
			$this->db->beginTransaction();

			$car = $this->getCarBySrCarId($srId);

			if (empty($car)) {
				throw new Exception("Car not found in " . __METHOD__ . ", line " . (__LINE__ - 1) . "<br />sr_id: " . $srId);
			}

			$select = $this->db->select()
				->from($this->tables['cars_photos'], array('id'))
				->where('car_id = ' . $car['car_id']);
			$photos = $this->db->fetchCol($select);

			foreach ($photos as $photoId) {
				$this->deletePhoto($photoId);
				//not only remove from db, also unlink files on disk
			}


            $select = $this->db->select()
				->from($this->tables['cars_vipauto_photos'], array('id'))
				->where('car_id = ' . $car['car_id']);
			$photos = $this->db->fetchCol($select);

			foreach ($photos as $photoId) {
				$this->deleteVipautoPhoto($photoId);
				//not only remove from db, also unlink files on disk
			}

			$this->db->delete(
				$this->tables['cars_extras'],
				'car_id = ' . $car['car_id']
			);

			$this->db->delete(
				$this->tables['cars_features'],
				'car_id = ' . $car['car_id']
			);

			$this->db->delete(
				$this->tables['cars_videos'],
				'car_id = ' . $car['car_id']
			);

			$this->db->delete(
				$this->tables['cars'],
				'car_id = ' . $car['car_id']
			);

			$this->db->delete(
				$this->tables['cars_favourites'],
				'car_id = ' . $car['car_id']
			);

			$this->db->delete(
				$this->tables['cars_reservations'],
				'sr_car_id = ' . $car['sr_car_id']
			);

			$this->db->commit();

			$this->_cache->clean(Zend_Cache::CLEANING_MODE_MATCHING_TAG, array('cars'));
		}
		catch (Exception $e) {
			$this->db->rollBack();
			throw $e;
		}
	}
	
	public function getCarBySrCarId($id) {
		$select = $this->db->select()
			->from($this->tables['cars'])
			->where('sr_car_id = ' . (int)$id);
		return $this->db->fetchRow($select);
	}
	
	public function deletePhoto($id) {
		$id = (int)$id;
		$select = $this->db->select()
			->from($this->tables['cars_photos'])
			->where('id = ' . $id);
		$photo = $this->db->fetchRow($select);
		if (empty($photo)) {
			throw new Exception("Photo not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}

		$this->db->delete(
			$this->tables['cars_photos'],
			"id = " . $id
		);

		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$path = $opt['photos']['path'];
		$prefix = $path . DIRECTORY_SEPARATOR . $photo['car_id'] . DIRECTORY_SEPARATOR . $photo['filename_base'];
		$suffix = "." . $photo['filename_extension'];
		$sizes = $opt['photos']['sizes'];

		foreach ($sizes as $sizeName => $data) {
			@unlink($prefix . "_" . $sizeName . $suffix);
		}
		@unlink($prefix . $suffix);

	}

    public function deleteVipautoPhoto($id) {
		$id = (int)$id;
		$select = $this->db->select()
			->from($this->tables['cars_vipauto_photos'])
			->where('id = ' . $id);
		$photo = $this->db->fetchRow($select);
		if (empty($photo)) {
			throw new Exception("Photo not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}

		$this->db->delete(
			$this->tables['cars_vipauto_photos'],
			"id = " . $id
		);

		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$path = $opt['photos']['vipauto_path'];
		$prefix = $path . DIRECTORY_SEPARATOR . $photo['car_id'] . DIRECTORY_SEPARATOR . $photo['filename_base'];
		$suffix = "." . $photo['filename_extension'];
		$sizes = $opt['photos']['sizes'];

		foreach ($sizes as $sizeName => $data) {
			@unlink($prefix . "_" . $sizeName . $suffix);
		}
		@unlink($prefix . $suffix);

	}

	public function editCar($srId, $data, $doTransaction=true) {
		$srId = (int)$srId;
		try {
			if ($doTransaction) $this->db->beginTransaction();

			$oldPrice = $this->db->fetchOne($this->db->select()->from($this->tables['cars'], array('price'))->where('sr_car_id =' . (int)$srId));
			$oldPromotionPrice = $this->db->fetchOne($this->db->select()->from($this->tables['cars'], array('promotion_price'))->where('sr_car_id =' . (int)$srId));
			if ($oldPromotionPrice == null) {
				$oldPromotionPrice = new Zend_Db_Expr('NULL');
			}

			$car = $this->getCarBySrCarId($srId);
			$isCurrentlyArchived = 0;
			if (empty($car)) {
				$car = $this->getArchiveCarBySrCarId($srId);
				if (empty($car)) {
					throw new Exception("Nie znaleziono samochodu po srCarId: " . $srId);
				}
				$isCurrentlyArchived = 1;
			}

			if ($isCurrentlyArchived != $data['is_archived']) {
				if ($data['is_archived'] == 1) {
					$this->moveCarToArchive($car['car_id'], $doDdbTr=!$doTransaction);
				}
				else {
					$this->restoreCarFromArchive($car['car_id'], $doDbTr=!$doTransaction);
				}
			}

			//update in proper table (archive_cars etc. or cars etc.)
			$carsTable = $this->tables['cars'];
			$extrasTable = $this->tables['cars_extras'];
			$featuresTable = $this->tables['cars_features'];
			$videosTable = $this->tables['cars_videos'];
			$descriptionsTable = $this->tables['cars_descriptions'];
			$descriptionsValuesTable = $this->tables['cars_descriptions_values'];

			if ($data['is_archived'] == 1) {
				$carsTable = $this->tables['archive_cars'];
				$extrasTable = $this->tables['archive_cars_extras'];
				$featuresTable = $this->tables['archive_cars_features'];
				$videosTable = $this->tables['archive_cars_videos'];
				$descriptionsTable = $this->tables['archive_cars_descriptions'];
				$descriptionsValuesTable = $this->tables['archive_cars_descriptions_values'];
			}
			$isArchived = $data['is_archived'];
			unset($data['is_archived']);

			$extras = $data['extras'];
			unset($data['extras']);

			$features = $data['features'];
			unset($data['features']);

			$videos = $data['videos'];
			unset($data['videos']);

			$descriptions = $data['descriptions'];
			unset($data['descriptions']);

            $exchangeModels = $data['exchange_models'];
            unset($data['exchange_models']);

			$id = (int)$car['car_id'];

			if (empty($id)) {
				throw new Exception("carId empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
			}

			//don't allow changes to some columns:
			$protectedColumns = array('car_id', 'added_datetime', 'sr_car_id');
			foreach ($protectedColumns as $key) {
				if (array_key_exists($key, $data)) {
					unset($data[$key]);
				}
			}

			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$vat = $opt['instalments']['VAT'];
			$data['price_gross'] = ($data['price_type_key'] == "netto" ? $data['price'] * (1 + $vat) : $data['price']);
			if (!empty($data['promotion_price']) && !($data['promotion_price'] instanceof Zend_Db_Expr)) {
				$data['promotion_price_gross'] = ($data['price_type_key'] == "netto"  ? $data['promotion_price'] * (1 + $vat) : $data['promotion_price']);
			}
			else {
				$data['promotion_price_gross'] = new Zend_Db_Expr('NULL');
			}

			$data += array(
				'updated_datetime' => new Zend_Db_Expr('NOW()')
			);

			if ($data['price'] != $oldPrice) {
				$data['price_change_datetime'] = new Zend_Db_Expr('NOW()');
                $data['price_last'] = $oldPrice;}

            if ($data['promotion_price'] != $oldPromotionPrice) {
                $data['price_change_datetime'] = new Zend_Db_Expr('NOW()');
                $data['promotion_price_last'] =  $oldPromotionPrice;
            }


            if(!$car['is_visible_datetime'] && $data['is_visible'] == 1)
                $data['is_visible_datetime'] = new Zend_Db_Expr('NOW()');

			//cars table
			$this->db->update(
				$carsTable,
				$data,
				"car_id = " . $id
			);


			//extras table
			$select = $this->db->select()
				->from($extrasTable, array('extra_id'))
				->where('car_id = ' . $id);
			$extrasIds = $this->db->fetchCol($select);

			$toDelete = array_diff($extrasIds, $extras);
			$toAdd = array_diff($extras, $extrasIds);

			if (count($toDelete) > 0) {
				$this->db->delete(
					$extrasTable,
					"car_id = " . $id . " AND extra_id IN(" . implode(",", $toDelete) . ")"
				);
			}
			foreach ($toAdd as $extraId) {
				$this->db->insert(
					$extrasTable,
					array(
						'car_id' => $id,
						'extra_id' => $extraId
					)
				);
			}

			//features table
			$select = $this->db->select()
				->from($featuresTable, array('feature_id'))
				->where('car_id = ' . $id);
			$featuresIds = $this->db->fetchCol($select);

			$toDelete = array_diff($featuresIds, $features);
			$toAdd = array_diff($features, $featuresIds);

			if (count($toDelete) > 0) {
				$this->db->delete(
					$featuresTable,
					"car_id = " . $id . " AND feature_id IN(" . implode(",", $toDelete) . ")"
				);
			}
			foreach ($toAdd as $featureId) {
				$this->db->insert(
					$featuresTable,
					array(
						'car_id' => $id,
						'feature_id' => $featureId
					)
				);
			}

            //exchangeModels table
            $select = $this->db->select()
                ->from($this->tables['cars_exchange_models'], array('model_id'))
                ->where('car_id = ' . $id);
            $exchangeModelsIds = $this->db->fetchCol($select);

            $toDelete = array_diff($exchangeModelsIds, $exchangeModels);
            $toAdd = array_diff($exchangeModels, $exchangeModelsIds);

            if (count($toDelete) > 0) {
                $this->db->delete(
                    $this->tables['cars_exchange_models'],
                    "car_id = " . $id . " AND model_id IN(" . implode(",", $toDelete) . ")"
                );
            }
            foreach ($toAdd as $modelId) {
                $this->db->insert(
                    $this->tables['cars_exchange_models'],
                    array(
                        'car_id' => $id,
                        'model_id' => $modelId
                    )
                );
            }

			//videos table
			$videosHashed = array();
			foreach ($videos as $key => $value) {
				$videosHashed[] = $value['cv_provider_id'] . "--=--" . $value['cv_video_id'];
			}

			$select = $this->db->select()
				->from($videosTable, array(new Zend_Db_Expr('CONCAT(provider_id, "--=--", video_id)')))
				->where('car_id = ' . $id);
			$videosIds = $this->db->fetchCol($select);

			$toDelete = array_diff($videosIds, $videosHashed);
			$toAdd = array_diff($videosHashed, $videosIds);

			if (count($toDelete) > 0) {
				foreach ($toDelete as $hashedIds) {
					$xpld = explode("--=--", $hashedIds);
					$select = $this->db->select()
						->from($videosTable, array('id'))
						->where('car_id = ' . $id)
						->where('provider_id = ' . (int)$xpld[0])
						->where('video_id = ?', $xpld[1]);
					$vidId = $this->db->fetchOne($select);

					$this->db->delete(
						$videosTable,
						"id = " . (int)$vidId
					);
				}
			}

			foreach ($toAdd as $hashedIds) {
				$found = false;
				$xpld = explode("--=--", $hashedIds);
				foreach ($videos as $item) {
					if ($item['cv_provider_id'] == $xpld[0] && $item['cv_video_id'] == $xpld[1]) {
						$found = $item;
						break;
					}
				}
				if (!$found) continue;

				$this->db->insert(
					$videosTable,
					array(
						'car_id' => $id,
						'ord' => (int)$found['cv_ord'],
						'provider_id' => (int)$found['cv_provider_id'],
						'video_id' => $found['cv_video_id']
					)
				);
			}

			//descriptions
			$existing = $this->getDescriptionsWithValues($this->getDescriptions($id, "pl", $isArchived), $isArchived);
			$descriptionsIds = array();
			if (is_array($existing)) {
				foreach ($existing as $index => $descData) {
					$descriptionsIds[$index] = $descData['description_id'];
				}
			}

			$incomingIds = array();
			$toAdd = array();
			foreach ($descriptions as $categoryId => $descData) {
				foreach ($descData['checkboxes'] as $descId => $value) {
					if ($value == "1") {
						if(!in_array($descId, $descriptionsIds)) {
							if (!isset($toAdd[$categoryId]) || !is_array($toAdd[$categoryId])) {
								$toAdd[$categoryId] = array();
							}
							$toAdd[$categoryId][] = $descId;
						}
						$incomingIds[] = $descId;
					}
				}
			}

			$toDelete = array_diff($descriptionsIds, $incomingIds);

			if (count($toDelete) > 0) {
				$select = $this->db->select()
					->from(array('cd' => $descriptionsTable), array('id'))
					->where('car_id = '. $id . ' AND description_id IN(?)', $toDelete);
				$toDeleteCDIds = $this->db->fetchCol($select);

				if (count($toDeleteCDIds) > 0) {
					$this->db->delete(
						$descriptionsValuesTable,
						$this->db->quoteInto("car_description_id IN(?)", $toDeleteCDIds)
					);
				}

				$this->db->delete(
					$descriptionsTable,
					"car_id = " . $id . " AND description_id IN(" . implode(",", $toDelete) . ")"
				);
			}

			foreach ($toAdd as $categoryId => $descIds) {
				foreach ($descIds as $descriptionId) {
					$this->db->insert(
						$descriptionsTable,
						array(
							'car_id' => $id,
							'description_id' => $descriptionId
						)
					);
					$cdId = $this->db->lastInsertId($descriptionsTable, 'id');

					if (isset($descriptions[$categoryId]['text'][$descriptionId]) && is_array($descriptions[$categoryId]['text'][$descriptionId]) && count($descriptions[$categoryId]['text'][$descriptionId]) > 0) {
						foreach ($descriptions[$categoryId]['text'][$descriptionId] as $key => $value) {
							$this->db->insert(
								$descriptionsValuesTable,
								array(
									'car_description_id' => $cdId,
									'key' => $key,
									'value' => $value
								)
							);
						}
					}
				}
			}

			//description may remain assigned, but text values may have changed:
			foreach ($descriptions as $catId => $descData) {
				if (isset($descData['text'])) {
					foreach ($descData['text'] as $descId => $values) {
						if (($index = array_search($descId, $descriptionsIds)) !== false) {
							foreach ($values as $key => $value) {
								if (isset($existing[$index]['values'][$key])) {
									if ($existing[$index]['values'][$key] == $value) {
										//same; do nothing
										continue;
									}//if; same value - continue
									else {
										$this->db->update(
											$descriptionsValuesTable,
											array(
												'value' => $value
											),
											$this->db->quoteInto("car_description_id = {$index} AND `key` = ?", $key)
										);
									}//else; different value - update
								}
								else {
									$this->db->insert(
										$descriptionsValuesTable,
										array(
											'car_description_id' => $index,
											'key' => $key,
											'value' => $value
										)
									);
								}//else; value does not exist - insert
							}//foreach; values : key => value
						}//if id found
					}//foreach; text: descId => values
				}//if
			}//foreach; descriptions: carId => data

			if ($doTransaction) $this->db->commit();

			$this->_cache->clean(Zend_Cache::CLEANING_MODE_MATCHING_TAG, array('cars'));

			return $id;
		}
		catch (Exception $e) {
			if ($doTransaction) $this->db->rollBack();
            if (Zend_Registry::isRegistered('errorLogger'))
            {
                $logger = Zend_Registry::get('errorLogger');
                $logger->log($e,Zend_Log::ERR);

            }
			throw $e;
		}
	}
	
	public function getArchiveCarBySrCarId($id) {
		$select = $this->db->select()
			->from($this->tables['archive_cars'])
			->where('sr_car_id = ' . (int)$id);
		return $this->db->fetchRow($select);
	}
	
	public function moveCarToArchive($id, $doTransaction=true, $restore=false) {
		try {
			if ($doTransaction) $this->db->beginTransaction();

			$id = (int)$id;

			$carsTable = $this->tables['cars'];
			$carsArchiveTable = $this->tables['archive_cars'];

			$extrasTable = $this->tables['cars_extras'];
			$extrasArchiveTable = $this->tables['archive_cars_extras'];

			$featuresTable = $this->tables['cars_features'];
			$featuresArchiveTable = $this->tables['archive_cars_features'];

			$photosTable = $this->tables['cars_photos'];
			$photosArchiveTable = $this->tables['archive_cars_photos'];

            $vipautoPhotosTable = $this->tables['cars_vipauto_photos'];
			$vipautoPhotosArchiveTable = $this->tables['archive_cars_vipauto_photos'];

			$videosTable = $this->tables['cars_videos'];
			$videosArchiveTable = $this->tables['archive_cars_videos'];

			$favouritesTable = $this->tables['cars_favourites'];
			$favouritesArchiveTable = $this->tables['archive_cars_favourites'];

			$reservationsTable = $this->tables['cars_reservations'];
			$reservationsArchiveTable = $this->tables['archive_cars_reservations'];

			if ($restore) {
				$carsTable = $carsArchiveTable;
				$carsArchiveTable = $this->tables['cars'];

				$extrasTable = $extrasArchiveTable;
				$extrasArchiveTable = $this->tables['cars_extras'];

				$featuresTable = $featuresArchiveTable;
				$featuresArchiveTable = $this->tables['cars_features'];

				$photosTable = $photosArchiveTable;
				$photosArchiveTable = $this->tables['cars_photos'];

                $vipautoPhotosTable = $vipautoPhotosArchiveTable;
				$vipautoPhotosArchiveTable = $this->tables['cars_vipauto_photos'];

				$videosTable = $videosArchiveTable;
				$videosArchiveTable = $this->tables['cars_videos'];

				$favouritesTable = $favouritesArchiveTable;
				$favouritesArchiveTable = $this->tables['cars_favourites'];

				$reservationsTable = $reservationsArchiveTable;
				$reservationsArchiveTable = $this->tables['cars_reservations'];
			}

			//cars / archive_cars tables
			$select = $this->db->select()
				->from($carsTable)
				->where('car_id = ' . $id);
			$car = $this->db->fetchRow($select);
			if (empty($car)) {
				throw new Exception("Car not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
			}

            $this->db->delete(
				$carsArchiveTable,
				"car_id = " . $id . " or sr_car_id = " . $car['sr_car_id']
			);

			$this->db->insert(
				$carsArchiveTable,
				$car
			);

			$this->db->delete(
				$carsTable,
				"car_id = " . $id
			);

            $this->db->delete(
				$extrasArchiveTable,
				"car_id = " . $id
			);

			//cars_extras / archive_cars_extras tables
			$select = $this->db->select()
				->from($extrasTable)
				->where('car_id = ' . $id);
			$extras = $this->db->fetchAll($select);

			foreach ($extras as $item) {
                unset($item['id']);
				$this->db->insert(
					$extrasArchiveTable,
					$item
				);
			}

			$this->db->delete(
				$extrasTable,
				"car_id = " . $id
			);

            $this->db->delete(
				$featuresArchiveTable,
				"car_id = " . $id
			);

			//cars_features / archive_cars_features tables
			$select = $this->db->select()
				->from($featuresTable)
				->where('car_id = ' . $id);
			$features = $this->db->fetchAll($select);

			foreach ($features as $item) {
                unset($item['id']);
				$this->db->insert(
					$featuresArchiveTable,
					$item
				);
			}

			$this->db->delete(
				$featuresTable,
				"car_id = " . $id
			);

            $this->db->delete(
				$photosArchiveTable,
				"car_id = " . $id
			);

			//cars_photos / archive_cars_photos tables
			$select = $this->db->select()
				->from($photosTable)
				->where('car_id = ' . $id);
			$photos = $this->db->fetchAll($select);

			foreach ($photos as $item) {
                unset($item['id']);
				$this->db->insert(
					$photosArchiveTable,
					$item
				);
			}

			$this->db->delete(
				$photosTable,
				"car_id = " . $id
			);

            $this->db->delete(
				$vipautoPhotosArchiveTable,
				"car_id = " . $id
			);

            //vipauto cars_photos / archive_cars_photos tables
			$select = $this->db->select()
				->from($vipautoPhotosTable)
				->where('car_id = ' . $id);
			$photos = $this->db->fetchAll($select);

			foreach ($photos as $item) {
                unset($item['id']);
				$this->db->insert(
					$vipautoPhotosArchiveTable,
					$item
				);
			}

			$this->db->delete(
				$vipautoPhotosTable,
				"car_id = " . $id
			);

            $this->db->delete(
				$videosArchiveTable,
				"car_id = " . $id
			);

			//cars_videos / archive_cars_videos tables
			$select = $this->db->select()
				->from($videosTable)
				->where('car_id = ' . $id);
			$videos = $this->db->fetchAll($select);

			foreach ($videos as $item) {
                unset($item['id']);
				$this->db->insert(
					$videosArchiveTable,
					$item
				);
			}

			$this->db->delete(
				$videosTable,
				"car_id = " . $id
			);

            $this->db->delete(
				$favouritesArchiveTable,
				"car_id = " . $id
			);

			//cars_favourites / archive_cars_favourites tables
			$select = $this->db->select()
				->from($favouritesTable)
				->where('car_id = ' . $id);
			$favourites = $this->db->fetchAll($select);

			foreach ($favourites as $item) {
                unset($item['id']);
				$this->db->insert(
					$favouritesArchiveTable,
					$item
				);
			}

			$this->db->delete(
				$favouritesTable,
				"car_id = " . $id
			);

            $this->db->delete(
				$reservationsArchiveTable,
				"sr_car_id = " . $car['sr_car_id']
			);

			//cars_reservations / archive_cars_reservations tables
			$select = $this->db->select()
				->from($reservationsTable)
				->where('sr_car_id = ' . $car['sr_car_id']);
			$reservations = $this->db->fetchAll($select);

			foreach ($reservations as $item) {
                unset($item['id']);
				$this->db->insert(
					$reservationsArchiveTable,
					$item
				);
			}

			$this->db->delete(
				$reservationsTable,
				"sr_car_id = " . $car['sr_car_id']
			);


			if ($doTransaction) $this->db->commit();

			$makeName = $this->db->fetchOne($this->db->select()->from(array($this->tables['car_makes']), array('name'))->where('id = ' . (int)$car['make_id']));
			$this->_cache->remove('car_makes_with_counts_by_name');
			$this->_cache->remove('car_models_with_counts_make_name_' . $this->cacheNameEncode($makeName));
		}
		catch (Exception $e) {
			if ($doTransaction) $this->db->rollBack();
			throw $e;
		}
	}

	public function restoreCarFromArchive($id, $doTransaction=true) {
		return $this->moveCarToArchive($id, $doTransaction, $restore=true);
	}

	public function getDescriptionsWithValues($descriptions, $isArchived=0) {
		if (!is_array($descriptions) || count($descriptions) == 0) return null;

		$cdvTable = $this->tables['cars_descriptions_values'];
		if ($isArchived) {
			$cdvTable = $this->tables['archive_cars_descriptions_values'];
		}

		$cdIds = array_keys($descriptions);

		$select = $this->db->select()
			->from(array('cdv' => $cdvTable))
			->where('car_description_id IN (?)', $cdIds);
		$cdv = $this->db->fetchAll($select);

		foreach ($cdv as $dv) {
			$descriptions[$dv['car_description_id']]['value'] = My_Utils::replacePseudoVars(
				$descriptions[$dv['car_description_id']]['value'],
				array($dv['key'] => $dv['value'])
			);
			if (!isset($descriptions[$dv['car_description_id']]['values']) || !is_array($descriptions[$dv['car_description_id']]['values'])) {
				$descriptions[$dv['car_description_id']]['values'] = array();
			}
			$descriptions[$dv['car_description_id']]['values'][$dv['key']] = $dv['value'];
		}

		return $descriptions;
	}

	public function getDescriptions($carId, $language, $isArchived=0) {
		$cdTable = $this->tables['cars_descriptions'];
		if ($isArchived) {
			$cdTable = $this->tables['archive_cars_descriptions'];
		}

		$select = $this->db->select()
			->from(array('cd' => $cdTable), array('id', '*'))
			->join(array('vd' => $this->tables['vehicle_descriptions']),
				'vd.id = cd.description_id',
				array('key', 'category_id')
			)
			->join(array('vdc' => $this->tables['vehicle_description_categories']),
				'vdc.id = vd.category_id',
				array('name')
			)
			->join(array('t' => $this->tables['translations']),
				$this->db->quoteInto('t.key = vd.key AND t.language = ?', $language),
				array('value')
			)
            ->joinLeft(array('t2' => $this->tables['translations']),
                $this->db->quoteInto('t2.key = vdc.key AND t.language = ?', $language),
                array('vdc_name' =>'value')
            )
			->where('car_id = ' . (int)$carId)
			->order(array('vd.category_id ASC', 'vd.ord ASC'));

		return $this->db->fetchAssoc($select);
	}

	public function editMake($id, $data) {
		$this->db->update(
			$this->tables['car_makes'],
			$data,
			"id = " . (int)$id
		);
	}

	public function editModel($id, $data) {

        if(isset($data['merge']) && isset($data['merge_model_id'])) {
            $this->db->update(
                $this->tables['cars'],
                array(
                    'model_id' 	=> $data['merge_model_id'],
                ),
                'model_id = ' . (int)$id
            );

            $this->db->update(
                $this->tables['archive_cars'],
                array(
                    'model_id' 	=> $data['merge_model_id'],
                ),
                'model_id = ' . (int)$id
            );

            $this->db->update(
                $this->tables['users_cars_models_for_exchange'],
                array(
                    'model_id' 	=> $data['merge_model_id'],
                ),
                'model_id = ' . (int)$id
            );

            $this->db->update(
                $this->tables['users_cars_sell_exchange'],
                array(
                    'model_id' 	=> $data['merge_model_id'],
                ),
                'model_id = ' . (int)$id
            );

            $this->db->update(
                $this->tables['cars_exchange_models'],
                array(
                    'model_id' 	=> $data['merge_model_id'],
                ),
                'model_id = ' . (int)$id
            );

            $this->db->delete(
                $this->tables['car_models'],
                'id = ' . (int)$id
            );

        } else {
            $this->db->update(
                $this->tables['car_models'],
                $data,
                "id = " . (int)$id
            );
        }
	}
	
	public function getAffordableCarPrice($data) {
		if ($data['type'] != 'leasing' && $data['type'] != 'credit') {
			$data['type'] = 'credit';
		}

		$config = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$instalmentsOptions = $config['instalments'];
		$vat = $instalmentsOptions['VAT'];

		if ($data['instalmentsNo'] < 1) {
			$data['instalmentsNo'] = 36; //just in case
		}

		if ($data['type'] == 'credit') {
			$interest = 0.1349;
			foreach($instalmentsOptions[$data['type']]['interestRates'] as $interestRange) {
				if ($data['instalmentsNo'] >= $interestRange['instalmentsNoMin'] && $data['instalmentsNo'] <= $interestRange['instalmentsNoMax']) {
					$interest = $interestRange['interest'];
					break;
				}
			}
			$tmp = 1 + $interest / 12;

			//$instalment = $amount * pow($tmp, $instalmentsNo) * ($tmp - 1) / (pow($tmp, $instalmentsNo) - 1) + ($amount * 0.07) / $instalmentsNo;
			$amount = $data['instalment'] / ((pow($tmp, $data['instalmentsNo']) * ($tmp - 1) / (pow($tmp, $data['instalmentsNo']) - 1)) + (0.07 / $data['instalmentsNo']));

			$carPriceGross = $amount + $data['contribution'];
			$carPriceNet = $carPriceGross / (1 + $instalmentsOptions['VAT']);

			return array(
					'gross' => round($carPriceGross / 100) * 100,
					'net' => round($carPriceNet / 100) * 100
			);
		}
		else {
			$failArray = array(
				'gross' => 0,
				'net' => 0
			);

			$instalmentsOptions = $instalmentsOptions['leasing'];

			$contribPercent = (int)($data['contribution'] * 100);
			if (!array_key_exists($contribPercent, $instalmentsOptions['interestRates'])) {
				return $failArray;
			}

			if (!array_key_exists($data['instalmentsNo'], $instalmentsOptions['interestRates'][$contribPercent])) {
				return $failArray;
			}
			//calculate leasingRate based on number of instalments and contribution in percent (LUT):
			$rate = $instalmentsOptions['interestRates'][$contribPercent][$data['instalmentsNo']]['rate'];

			//derive max possible car price from leasing rate (trivial):
			$maxPriceGross = $data['instalment'] / ($rate / 100);
			$maxPriceNet = $maxPriceGross / (1 + $vat);

			return array(
				'gross' => round($maxPriceGross / 100) * 100,
				'net' => round($maxPriceNet / 100) * 100
			);
		}
	}

    public function getCarByOfferCarId($id) {
        $select = $this->db->select()
            ->from($this->tables['cars'])
            ->where('offer_id = ' . (int)$id);
        return $this->db->fetchRow($select);
    }

    public function getCarCategoryByKey($key) {

        $select = $this->db->select()
            ->from(array('vc' => $this->tables['vehicle_categories']))
            ->where('vc.key = ?', $key  );


        return $this->db->fetchRow($select);
    }

	public function getCarCategoriesByName($all=false, $onlySelectable=false, $key = 'key') {
		$select = $this->db->select()
			->from($this->tables['cars'], array(new Zend_Db_Expr('DISTINCT vehicle_category_id')));
		$withCars = $this->db->fetchCol($select);

		$select = $this->db->select()
			->from(array('vc' => $this->tables['vehicle_categories']), array($key))
			->joinLeft(array('t' => $this->tables['translations']),
					't.key = vc.key',
					array('value')
			)
			->where('language=?', Zend_Registry::get('translate_language'))
			->order(array('vc.ord ASC', 't.value ASC'));
		if (!$all && count($withCars) > 0) {
			$select->where('vc.id IN(?)', $withCars);
		}
		if ($onlySelectable) {
			$select->where('vc.selectable = 1');
		}
		return $this->db->fetchPairs($select);
	}

	public function getCarTypesWithCounts() {
		$lang = Zend_Registry::get('translate_language');

		if (!(($types = $this->_cache->load('types_with_counts_' . $lang)))) {

			$select = $this->db->select()
				->from(array('c' => $this->tables['cars']), array('display_name' => new Zend_Db_Expr('CONCAT(t.value, " (", COUNT(DISTINCT c.car_id), ")")')))
				->join(array('mk' => $this->tables['car_makes']),
					'make_id = mk.id',
					array()
				)
				->join(array('ct' => $this->tables['car_types']),
					'mk.type_id = ct.id',
					array('type_slug' => 'ct.slug')
				)
				->joinLeft(array('t' => $this->tables['translations']),
					't.key =  CONCAT("type_", ct.front_group) AND t.language="'.Zend_Registry::get('translate_language').'"',
					array()
				)
				->group('ct.front_group')
				->where('c.is_visible = 1')

				->order(array('ct.id ASC'));



			$types = $this->db->fetchAll($select);

			$this->_cache->save($types, 'types_with_counts_' . $lang, array('cars', 'translate', 'types_with_counts'));
		}

		return $types;

	}

	public function getCarCategoriesWithCounts($types=null, $getTotalCount=false, $plural = false) {
		$lang = Zend_Registry::get('translate_language');
		$typesForCacheId = "";

		$typesFiltered = array();
		if (is_array($types)) {
			foreach ($types as $type) {
				$typesFiltered[] = (int)$type;
			}
			$types = $typesFiltered;
			$typesForCacheId = implode("_t_", $types);
		}
        $suffix = '';
        if($plural)
            $suffix = '_plural';

		if (!(($cats = $this->_cache->load('other_categories_with_counts_' . $lang . $typesForCacheId)) && ($count = $this->_cache->load('other_categories_with_counts_total_count' . $lang . $typesForCacheId)))) {

            if($plural) {
                $select = $this->db->select()
                    ->from(array('c' => $this->tables['cars']), array('GROUP_CONCAT(DISTINCT vc.key)', new Zend_Db_Expr('CASE WHEN t2.value IS NOT NULL THEN CONCAT(t2.value, " (", COUNT(DISTINCT c.car_id), ")")  ELSE CONCAT(t.value, " (", COUNT(DISTINCT c.car_id), ")") END')))
                    ->joinLeft(array('vc' => $this->tables['vehicle_categories']),
                        'c.vehicle_category_id = vc.id',
                        array()
                    )
                    ->joinLeft(array('t' => $this->tables['translations']),
                        't.key = vc.key AND t.language="'.Zend_Registry::get('translate_language').'"',
                        array()
                    )
                    ->joinLeft(array('t2' => $this->tables['translations']),
                        't2.key =  CONCAT("type_", vc.type_group) AND t2.language="'.Zend_Registry::get('translate_language').'"',
                        array()
                    )
                    ->group('vc.type_group')
                    ->where('c.is_visible = 1')
                    //->where('vc.key != ?', 'other')

                    ->order(array('vc.ord ASC', 't.value ASC'));
            } else {
                $select = $this->db->select()
                    ->from(array('c' => $this->tables['cars']), array('vc.key', new Zend_Db_Expr('CONCAT(t.value, " (", COUNT(DISTINCT c.car_id), ")")')))
                    ->joinLeft(array('vc' => $this->tables['vehicle_categories']),
                        'c.vehicle_category_id = vc.id',
                        array()
                    )
                    ->joinLeft(array('t' => $this->tables['translations']),
                        't.key = vc.key AND t.language="'.Zend_Registry::get('translate_language').'"',
                        array()
                    )
                    ->group('vc.id')
                    ->where('c.is_visible = 1')
                    ->where('vc.key != ?', 'other')

                    ->order(array('vc.ord ASC', 't.value ASC'));
            }

			$selectTotalCount = $this->db->select()
				->from(array('c' => $this->tables['cars']), array(new Zend_Db_Expr('COUNT(DISTINCT c.car_id)')))
				->joinLeft(array('vc' => $this->tables['vehicle_categories']),
					'c.vehicle_category_id = vc.id',
					array()
				)
				->joinLeft(array('t' => $this->tables['translations']),
					't.key = vc.key',
					array()
				)

				->where('c.is_visible = 1')
				->where('t.language=?', Zend_Registry::get('translate_language'))
				->order(array('vc.ord ASC', 't.value ASC'));

			if (is_array($types)) {
				$select->where('vc.type_id IN (?)', $types);
				$selectTotalCount->where('vc.type_id IN (?)', $types);
			}

			$cats = $this->db->fetchPairs($select);
			$count = $this->db->fetchOne($selectTotalCount);

			$this->_cache->save($cats, 'other_categories_with_counts_' . $lang . $typesForCacheId, array('cars', 'translate', 'other_categories_with_counts'));
			$this->_cache->save($count, 'other_categories_with_counts_total_count' . $lang . $typesForCacheId, array('cars', 'translate', 'other_categories_with_counts'));
		}

		if ($getTotalCount) {
			return $count;
		}
		else {
			return $cats;
		}
	}

	public function getCarCategoriesWithCountsWithFilters($select, $types) {
		if (!($select instanceof Zend_Db_Select)) {
			throw new Exception("Invalid parameter type in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		else {
			$select = clone($select);
			$select->reset('columns');
			$select->columns(array('vc.key', new Zend_Db_Expr('CONCAT(t.value, " (", COUNT(*), ")")')));
			$select->joinLeft(array('vc' => $this->tables['vehicle_categories']),
					'vc.id = c.vehicle_category_id',
					array()
				)
				->joinLeft(array('t' => $this->tables['translations']),
					't.key = vc.key',
					array()
				)
			;
			$select->group('vc.key');

			$wheres = $select->getPart('where');
			$newWheres = array();
			foreach ($wheres as $where) {
				//check if clause starts with AND and strip it if so
				if (mb_strtoupper(mb_substr($where, 0, 5, "UTF-8"), "UTF-8") == "AND (") {
					$where = mb_substr($where, 4 - mb_strlen($where, "UTF-8"), 999999, "UTF-8");
				}
				//dont copy over the model= WHERE part
				if (strpos($where, "cat.key") === false) {
					$newWheres[] = $where;
				}
			}

			$select->reset('where');
			foreach ($newWheres as $where) {
				$select->where($where);
			}

			if (is_array($types)) {
				$select->where('vc.type_id IN (?)', $types);
			}

			$select->reset('order');
			$select->order('vc.key ASC');

			return $this->db->fetchPairs($select);
		}
	}

	public function getCarExtrasByName($all=false) {
		$select = $this->db->select()
			->from($this->tables['cars_extras'], array(new Zend_Db_Expr('DISTINCT extra_id')));
		$withCars = $this->db->fetchCol($select);

		$select = $this->db->select()
			->from(array('ve' => $this->tables['vehicle_extras']),
				array('key')
			)
			->joinLeft(array('t' => $this->tables['translations']),
					't.key = ve.key',
					array('value')
			)
			->where('language=?', Zend_Registry::get('translate_language'))
			->order('t.value ASC');

		if (!$all && count($withCars) > 0) {
			$select->where('ve.id IN (?)', $withCars);
		}

		return $this->db->fetchPairs($select);
	}
	
	public function getCarFeaturesByName($all=false) {
		$select = $this->db->select()
			->from($this->tables['cars_features'], array(new Zend_Db_Expr('DISTINCT feature_id')));
		$withCars = $this->db->fetchCol($select);

		$select = $this->db->select()
			->from(array('vf' => $this->tables['vehicle_features']),
				array('key')
			)
			->joinLeft(array('t' => $this->tables['translations']),
					't.key = vf.key',
					array('value')
			)
			->where('language=?', Zend_Registry::get('translate_language'))
			->order('t.value ASC');

		if (!$all && count($withCars) > 0) {
			$select->where('vf.id IN (?)', $withCars);
		}

		return $this->db->fetchPairs($select);
	}
	
	public function getCarFull($id, $onlyVisible=true) {
		$id = (int)$id;
		$select = $this->db->select()
			->from(array('c' => $this->tables['cars']), array('*', 'price' => 'IFNULL(LEAST(price, promotion_price), price)', 'regular_price' => 'price'))
			->joinLeft(array('mk' => $this->tables['car_makes']),
				'mk.id = make_id',
				array('make_name' => 'name')
			)
			->joinLeft(array('md' => $this->tables['car_models']),
				'md.id = model_id',
				array('model_name' => 'name')
			)
			->joinLeft(array('l' => $this->tables['locations']),
				'l.location_id = c.location_id',
				array('short_name', 'location_group_id', 'name')
			)
			->joinLeft(array('locgr' => $this->tables['location_groups']),
				'locgr.id = l.location_group_id',
				array('address')
			)
			->joinLeft(array('ft' => $this->tables['fuel_types']),
				'ft.id = c.fuel_type_id',
				array('fuel_key' => 'key')
			)

			->joinLeft(array('vc' => $this->tables['vehicle_categories']),
				'vc.id = c.vehicle_category_id',
				array('vc_key' => 'key')
			)
			->where('car_id = ' . $id);

        if(Zend_Registry::isRegistered('carTaker') && Zend_Registry::get('carTaker'))
        {
            $select->joinLeft(array('em' => $this->tables['employees']),
				'em.sr_id = '.Zend_Registry::get('carTaker'),
				array('caretaker_first_name' => 'first_name', 'caretaker_last_name' => 'last_name', 'caretaker_email' => 'email', 'caretaker_phone' => 'phone', 'caretaker_visible' => 'visible', 'caretaker_photo_basename' => 'photo_basename', 'caretaker_photo_extension' => 'photo_extension', 'caretaker_id' => 'id')
			);
        }
        else
        {
            $select->joinLeft(array('em' => $this->tables['employees']),
				'em.sr_id = c.caretaker_sr_id',
				array('caretaker_first_name' => 'first_name', 'caretaker_last_name' => 'last_name', 'caretaker_email' => 'email', 'caretaker_phone' => 'phone', 'caretaker_visible' => 'visible', 'caretaker_photo_basename' => 'photo_basename', 'caretaker_photo_extension' => 'photo_extension', 'caretaker_id' => 'id')
			);
        }

		if ($onlyVisible) {
			$select->where('c.is_visible = 1');
		}
		$car = $this->db->fetchRow($select);

		if (empty($car)) {
			return null;
		}

		$tr = Zend_Registry::get('Zend_Translate');
		$features = $this->getCarFeatures($id);
		foreach ($features as $key => $value) {
            if($value == "max_2_elements_painted")
            {
                unset($features[$key]);
                continue;
            }
			$features[$key] = $tr->_($value);
		}
		$car['features'] = $features;

		$extras = $this->getCarExtras($id);
		foreach ($extras as $key => $value) {
			$extras[$key] = $tr->_($value);
		}
		$car['extras'] = $extras;

		$car['videos'] = $this->getCarVideos($id);

        $car['exchange_models'] = $this->getCarExchangeModels($id);

		$language = Zend_Registry::get('translate_language');
		$car['descriptions'] = $this->getDescriptionsWithValues($this->getDescriptions($id, $language));

		$car['photos'] = $this->getCarPhotos($id);
		$car['colour_string'] = $this->getCarColourString($car, $tr);

		$employees = new Model_Employees();
		$car['caretaker'] = ( (Zend_Registry::isRegistered('carTaker') && Zend_Registry::get('carTaker')) ? Zend_Registry::get('carTaker') :  $employees->getBySrId($car['caretaker_sr_id']) );

		return $car;
	}
	
	public function getCarFeatures($id) {
		$select = $this->db->select()
			->from($this->tables['cars_features'], array('feature_id'))
			->joinLeft(array('vf' => $this->tables['vehicle_features']),
				'vf.id = feature_id',
				array('key')
			)
			->where('car_id = ' . (int)$id);
		return $this->db->fetchPairs($select);
	}

	public function getCarExtras($id) {
		$select = $this->db->select()
			->from($this->tables['cars_extras'], array('extra_id'))
			->joinLeft(array('ve' => $this->tables['vehicle_extras']),
				've.id = extra_id',
				array('key')
			)
			->where('car_id = ' . (int)$id);
		return $this->db->fetchPairs($select);
	}

	public function getCarVideos($carId) {
		$select = $this->db->select()
			->from($this->tables['cars_videos'])
			->joinLeft(array('cvp' => $this->tables['cars_video_providers']),
				'cvp.id = provider_id',
				array('provider_name' => 'name')
			)
			->where('car_id = ' . (int)$carId)
			->order(array('ord ASC', 'id ASC'));
		return $this->db->fetchAll($select);
	}
	
    public function getCarExchangeModels($id) {
        $select = $this->db->select()
            ->from($this->tables['cars_exchange_models'], array('model_id', new Zend_Db_Expr('CONCAT(makes.name, " ", models.name)')))
            ->joinLeft(array('models' => $this->tables['car_models']),
                'models.id = model_id',
               null
            )
            ->joinLeft(array('makes' => $this->tables['car_makes']),
                'makes.id = models.make_id',
                null
            )
            ->where('car_id = ' . (int)$id);
        return $this->db->fetchPairs($select);
    }

	public function getCarPhotos($carId, $onlyReady=true) {
		$select = $this->db->select()
			->from($this->tables['cars_photos'])
			->where('car_id = ' . (int)$carId)
			->order('ord ASC');
		if ($onlyReady) {
			$select->where('status = ?', "READY");
		}
		return $this->db->fetchAll($select);
	}

	public function getCarColourString($car, $translate) {
		if (!empty($car['colour'])) {
			return $car['colour'];
		}
		else {
			return $translate->_('colour_' . $car['colour_key']);
		}
	}
	
	public function getCarMakeByOmId($makeOmId) {
		$select = $this->db->select()
			->from($this->tables['car_makes'])
			->where('om_id = ' . (int)$makeOmId);
		return $this->db->fetchRow($select);
	}

	public function getCarMakeByString($value, $type) {
		$select = $this->db->select()
			->from($this->tables['car_makes'])
			->where('name = ?', $value)
			->where('type_id = ' . (int)$type);
		return $this->db->fetchRow($select);
	}
	
	public function getCarMakesWithCounts($type_id) {
		die("Metoda porzucona " . __METHOD__ . ", line " . (__LINE__ - 1));
		if (!($makes = $this->_cache->load('car_makes_with_counts'))) {
			$select = $this->db->select()
				->from($this->tables['cars'], array('make_id', new Zend_Db_Expr('CONCAT(mk.name, " (", COUNT(*), ")")')))
				->joinLeft(array('mk' => $this->tables['car_makes']),
					'make_id = mk.id',
					array('name')
				)
				->where('mk.type_id = ' . (int)$type_id)
				->where('is_visible = 1')
				->group('make_id')
				->order('mk.name');
			$makes = $this->db->fetchPairs($select);
			$this->_cache->save($makes, 'car_makes_with_counts', array('cars'));
			return $makes;
		}
		else {
			return $makes;
		}
	}

	public function getCarMakesWithCountsByNames($types=null, $specialFlags=null, $getTotalCount=false, $forSelect=false, $ids=false) {
		if ($types !== null && !is_array($types)) {
			throw new Exception("Types must be null or array in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		$typeForCacheId = "";

		$typesChecked = array();
		if (is_array($types)) {
			foreach ($types as $key => $value) {
				if ((int)$value === $value) {
					$typesChecked[] = $value;
				}
			}
			$types = $typesChecked;
			$typeForCacheId = implode("_t_", $types);
		}


		if ($specialFlags !== null && !is_array($specialFlags)) {
			throw new Exception("Flag must be null or array in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		$flagForCacheId = "";

		if (is_array($specialFlags)) {
			$flagForCacheId = mb_strtolower(implode("_", $specialFlags), "UTF-8");
		}

		$cacheIdSuffix = "";
		if ($forSelect) {
			$cacheIdSuffix = "for_select_";
		}

		if (!(($makes = $this->_cache->load('car_makes_with_counts_by_name_' . $cacheIdSuffix . $typeForCacheId . $flagForCacheId.$ids)) && $count = $this->_cache->load('car_makes_with_counts_by_name_total_count_' . $typeForCacheId . $flagForCacheId))) {
			$select = $this->db->select()
				->from($this->tables['cars'], array('mk.name', 'mk.special_flag', 'display_name' => new Zend_Db_Expr('CONCAT(mk.name, " (", COUNT(*), ")")')))
				->join(array('mk' => $this->tables['car_makes']),
					'make_id = mk.id',
					array('make_slug' => 'mk.slug')
				)
				->join(array('ct' => $this->tables['car_types']),
					'mk.type_id = ct.id',
					array('type_slug' => 'ct.slug')
				)
				->where('is_visible = 1')
				->group('mk.name')
				->order('mk.name');

			if ($forSelect) {
				$select->reset('columns');

                if($ids) {

                    $select->columns(
                        array(
                            'mk.id',
                            'display_name' => new Zend_Db_Expr('CONCAT(mk.name, " (", COUNT(*), ")")'))
                    );

                } else {

                    $select->columns(
                        array(
                            'mk.name',
                            'display_name' => new Zend_Db_Expr('CONCAT(mk.name, " (", COUNT(*), ")")'))
                    );

                }

			}

			$selectTotalCount = $this->db->select()
				->from($this->tables['cars'], array('COUNT(*)'))
				->joinLeft(array('mk' => $this->tables['car_makes']),
					'make_id = mk.id',
					array()
				)
				->where('is_visible = 1')
				->order('mk.name');

			if (is_array($specialFlags)) {
				$valuesArray = array();
				$where = "";
				foreach ($specialFlags as $key => $value) {
					if ($value === "NULL") {
						$where = "mk.special_flag IS NULL";
					}
					else {
						$valuesArray[] = $value;
					}
				}

				if (!empty($where) && count($valuesArray) > 0) {
					$where .= " OR ";
				}

				if (count($valuesArray) > 0) {
					$where .= $this->db->quoteInto("mk.special_flag IN(?)", $valuesArray);
				}

				if (!empty($where)) {
					$select->where($where);
					$selectTotalCount->where($where);
				}
			}

			if (is_array($types)) {
				$select->where('mk.type_id IN (?)', $types);
				$selectTotalCount->where('mk.type_id IN (?)', $types);
			}

			if ($forSelect) {
				$makes = $this->db->fetchPairs($select);
			}
			else {
				$makes = $this->db->fetchAll($select);
			}
			$count = $this->db->fetchOne($selectTotalCount);

			$this->_cache->save($makes, 'car_makes_with_counts_by_name_' . $cacheIdSuffix . $typeForCacheId . $flagForCacheId.$ids, array('cars', 'car_makes_with_counts_by_name'));
			$this->_cache->save($count, 'car_makes_with_counts_by_name_total_count_' . $typeForCacheId . $flagForCacheId, array('cars', 'car_makes_with_counts_by_name'));
		}

		if ($getTotalCount) {
			return $count;
		}
		else {
			return $makes;
		}
	}
	
	public function getCarMakesWithCountsByNamesWithFilters($select, $types=null, $specialFlags=null) {
		if (!($select instanceof Zend_Db_Select)) {
			throw new Exception("Invalid parameter type in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		else {
			$select = clone($select);
			$select->reset('columns');
			$select->columns(array('mk.name', 'mk.special_flag', 'make_slug' => 'mk.slug', 'type_slug' => 'ct.slug'));



			//$select->group('mk.name');

			$wheres = $select->getPart('where');
			$newWheres = array();
			foreach ($wheres as $where) {
				//check if clause starts with AND and strip it if so
				if (mb_strtoupper(mb_substr($where, 0, 5, "UTF-8"), "UTF-8") == "AND (") {
					$where = mb_substr($where, 4 - mb_strlen($where, "UTF-8"), 999999, "UTF-8");
				}
				//dont copy over the make= and model= WHERE parts
				if (strpos($where, "mk.name=") === false && strpos($where, "md.id=") === false) {
					$newWheres[] = $where;
				}
			}

			$select->reset('where');
			foreach ($newWheres as $where) {
				$select->where($where);
			}

			if (is_array($specialFlags)) {
				$valuesArray = array();
				$where = "";
				foreach ($specialFlags as $key => $value) {
					if ($value === "NULL") {
						$where = "mk.special_flag IS NULL";
					}
					else {
						$valuesArray[] = $value;
					}
				}

				if (!empty($where) && count($valuesArray) > 0) {
					$where .= " OR ";
				}

				if (count($valuesArray) > 0) {
					$where .= $this->db->quoteInto("mk.special_flag IN(?)", $valuesArray);
				}

				if (!empty($where)) {
					$select->where($where);
				}
			}

			if (is_array($types)) {
				$select->where("mk.type_id IN (?)", $types);
			}

			$select->reset('order');
			$select->order('mk.name ASC');

            $selectCount = $this->db->select();

            $selectCount
            ->from(array('a' => $select), array('*', 'display_name' => new Zend_Db_Expr('CONCAT(name, " (", COUNT(*), ")")')))
            ->group('name');
			return $this->db->fetchAll($selectCount);
		}
	}
	
	public function getCarModelsWithCounts($make_id) {
		die("Metoda porzucona " . __METHOD__ . ", line " . (__LINE__ - 1));
		if (!($models = $this->_cache->load('car_models_with_counts_make_id_' . (int)$make_id))) {
			$select = $this->db->select()
				->from($this->tables['cars'], array('model_id', new Zend_Db_Expr('CONCAT(md.name, " (", COUNT(*), ")")')))
				->joinLeft(array('md' => $this->tables['car_models']),
					'model_id = md.id',
					array('name')
				)
				->where('md.make_id = ' . (int)$make_id)
				->where('is_visible = 1')
				->group('model_id')
				->order('md.name');
			$models = $this->db->fetchPairs($select);
			$this->_cache->save($models, 'car_models_with_counts_make_id_' . (int)$make_id, array('cars'));
			return $models;
		}
		else {
			return $models;
		}
	}
        
	public function getCarModelsWithCountsFromName($make_name) {
		$encodedName = $this->cacheNameEncode($make_name);
		if (!($models = $this->_cache->load('car_models_with_counts_make_name_' . $encodedName))) {
			$select = $this->db->select()
				->from($this->tables['cars'], array('model_id', new Zend_Db_Expr('CONCAT(md.name, " (", COUNT(*), ")")')))
				->joinLeft(array('md' => $this->tables['car_models']),
					'model_id = md.id',
					array('name')
				)
				->join(array('mk' => $this->tables['car_makes']),
					'md.make_id = mk.id',
					array()
				)
				->where('mk.name = ?', $make_name)
				->where('is_visible = 1')
				->group('model_id')
				->order('md.name');

			$models = $this->db->fetchPairs($select);
			$this->_cache->save($models, 'car_models_with_counts_make_name_' . $encodedName, array('cars'));
			return $models;
		}
		else {
			return $models;
		}
	}
	
	public function getCarModelsWithCountsWithFilters($select) {
		if (!($select instanceof Zend_Db_Select)) {
			throw new Exception("Invalid parameter type in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		else {
			$select = clone($select);
			$select->reset('columns');
			$select->columns(array('md.slug', new Zend_Db_Expr('CONCAT(md.name, " (", COUNT(*), ")")')));
			$select->group('md.name');

			$wheres = $select->getPart('where');
			$newWheres = array();
			foreach ($wheres as $where) {
				//check if clause starts with AND and strip it if so
				if (mb_strtoupper(mb_substr($where, 0, 5, "UTF-8"), "UTF-8") == "AND (") {
					$where = mb_substr($where, 4 - mb_strlen($where, "UTF-8"), 999999, "UTF-8");
				}
				//dont copy over the model= WHERE part
				if (strpos($where, "md.slug=") === false) {
					$newWheres[] = $where;
				}
			}

			$select->reset('where');
			foreach ($newWheres as $where) {
				$select->where($where);
			}

			$select->reset('order');
			$select->order('md.name ASC');

			return $this->db->fetchPairs($select);
		}
	}
	
	public function getCarMinimumInstalment($car) {
		$ret = array(
			'type' => 'credit'
		);

		if ($car['price_type_key'] == 'netto') {
			$ret['type'] = 'leasing';

			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$instNo = $opt['instalments']['leasing']['defaultInstsallmentsNo'];
			$leaseOpt = $opt['instalments']['leasing']['interestRates'];
			$contribs = array_keys($leaseOpt);
			sort($contribs);
			$largestContrib = array_pop($contribs);
			$periods = array_keys($leaseOpt[$largestContrib]);
			sort($periods);
			$longestPeriod = array_pop($periods);

			$instalment = $this->getInstalment($carId=$car['car_id'], $type='leasing', $contribution=$largestContrib/100, $instalmentsNo=$longestPeriod, $priceOverride=$car['price']);

			$ret['instalment'] = $instalment['instalment_gross'];
			$ret['form_params'] = array(
				'price' => $car['price'],
				'contribution_select' => $largestContrib/100,
				'instalmentsNo' => $instalmentsNo
			);
		}
		else {
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$largestContrib = $opt['instalments']['credit']['contributionMaxMultiplier'];
			$creditOpt = $opt['instalments']['credit']['interestRates'];
			$keys = array_keys($creditOpt);
			$lastOption = $creditOpt[array_pop($keys)];
			$longestPeriod = $lastOption['instalmentsNoMax'];

			$instalment = $this->getInstalment($carId=$car['car_id'], $type='credit', $contribution=$largestContrib * $car['price'], $instalmentsNo=$longestPeriod, $priceOverride=$car['price']);

			$ret['instalment'] = $instalment['instalment_gross'];
			$ret['form_params'] = array(
				'price' => $car['price'],
				'contribution' => $largestContrib * $car['price'],
				'instalmentsNo' => $instalmentsNo
			);
		}

		return $ret;
	}
	
	public function getInstalment($carId = null, $type = 'leasing', $contribution = null, $instalmentsNo = 36, $customPrice=null) {
		$select = $this->db->select()
			->from(array('c' => $this->tables['cars']),
				array('car_id', 'price' => 'IFNULL(LEAST(price, promotion_price), price)', 'price_currency', 'price_type_key', 'build_year'))
			->where('car_id=?', $carId);

		$car = $this->db->fetchRow($select);
		if (!$car || !is_array($car)) {
			return false;
		}

		if ($customPrice !== null) {
			$car['price'] = (double)$customPrice;
		}
		if ($car['price'] == 0) {
			$car['price'] = 1;//oh well, beats dividing by zero
		}

		if ($type != 'leasing' && $type != 'credit') {
			$type = 'credit';
		}

		$config = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$vat = $config['instalments']['VAT'];
		$instalmentsOptions = $config['instalments'][$type];

		if ($car['price_type_key'] == 'netto' && $type == 'credit') {
			$car['price'] = $car['price'] * (1 + $vat);
		}

		$instalmentsNo = ($instalmentsNo ? $instalmentsNo : $instalmentsOptions['defaultInstalmentsNo']);

		//recalculate contribution from possible other currency to default zl;
		$view = Zend_Layout::getMvcInstance()->getView();
		$lr = $view->language_row;
		$multiplier = $lr['multiplier'];

		if ($type == 'credit') {
			$car['contribution'] = ($contribution !== null ? $contribution : $car['price'] * $instalmentsOptions['contributionDefaultMultiplier']);
			$car['contribution'] /= $multiplier;

			$amount = $car['price'] - $car['contribution'];
			$interest = 0.1349;
			foreach($instalmentsOptions['interestRates'] as $interestRange) {
				if ($instalmentsNo >= $interestRange['instalmentsNoMin'] and $instalmentsNo <= $interestRange['instalmentsNoMax']) {
					$interest = $interestRange['interest'];
					break;
				}
			}
			$tmp = 1 + $interest / 12;

			$instalment = $amount * pow($tmp, $instalmentsNo) * ($tmp - 1) / (pow($tmp, $instalmentsNo) - 1) + ($amount * 0.07) / $instalmentsNo;

			if ($contribution > $car['price']) {
				$instalment = 0;
			}

			$contribNet = $car['contribution'];
			$contribGross = $car['contribution'] * (1 + $vat);
			$contribVat = $contribGross - $contribNet;
			$instGross = $instalment;
			$instNet = $instGross / (1 + $vat);
			$instVat = $instGross - $instNet;

			return array(
				'contribution_net' => $view->price($contribNet, $lr),
				'contribution_gross' => $view->price($contribGross, $lr),
				'contribution_vat' => $view->price($contribVat, $lr),
				'contribution_percent' => round($car['contribution'] * 100 / $car['price']) . ' %',
				'instalment_gross' => $view->price($instGross, $lr),
				'instalment_net' => $view->price($instNet, $lr),
				'instalment_vat' => $view->price($instVat, $lr),
				'interest' => round($instNet / $car['price'] * 100 * 100) / 100 . '%',
				'instalments_no' => $instalmentsNo
			);
		}
		else {
			//leasing
			$failArray = array(
				'price_net' => 0,
				'price_tax' => 0,
				'price_gross' => 0,
				'contribution_net' => 0,
				'contribution_gross' => 0,
				'contribution_vat' => 0,
				'contribution_percent' => 0,
				'instalment_gross' => 0,
				'instalment_net' => 0,
				'instalment_vat' => 0,
				'interest' => 0,
				'instalments_no' => 0,
				'buyout_percent' => 0,
				'buyout_gross' => 0,
				'buyout_net' => 0,
				'buyout_vat' => 0
			);

			$car['contribution'] = ($contribution !== null ? $contribution : $instalmentsOptions['contributionDefaultMultiplier']);
			$contribPercent = (int)($car['contribution'] * 100);
			if (!array_key_exists($contribPercent, $instalmentsOptions['interestRates'])) {
				return $failArray;
			}
			if (!array_key_exists($instalmentsNo, $instalmentsOptions['interestRates'][$contribPercent])) {
				return $failArray;
			}

			$leasingRate = $instalmentsOptions['interestRates'][$contribPercent][$instalmentsNo]['rate'] / 100;
			$buyoutRate = $instalmentsOptions['interestRates'][$contribPercent][$instalmentsNo]['buyout'] / 100;

			$priceNet = $car['price'];
			$priceGross = $car['price'] * (1 + $vat);
			$priceTax = $priceGross - $priceNet;
			$contribNet = $car['contribution'] * $car['price'];
			$contribGross = $contribNet * (1 + $vat);
			$contribVat = $contribGross - $contribNet;
			$instNet = $leasingRate * $car['price'];
			$instGross = $instNet * (1 + $vat);
			$instVat = $instGross - $instNet;
			$buyoutNet = $buyoutRate * $car['price'];
			$buyoutGross = $buyoutNet * (1 + $vat);
			$buyoutVat = $buyoutGross - $buyoutNet;

			return array(
				'price_net' => $view->price($priceNet, $lr),
				'price_tax' => $view->price($priceTax, $lr),
				'price_gross' => $view->price($priceGross, $lr),
				'contribution_net' => $view->price($contribNet, $lr),
				'contribution_gross' => $view->price($contribGross, $lr),
				'contribution_vat' => $view->price($contribVat, $lr),
				'contribution_percent' => $contribPercent . ' %',
				'instalment_gross' => $view->price($instGross, $lr),
				'instalment_net' => $view->price($instNet, $lr),
				'instalment_vat' => $view->price($instVat, $lr),
				'interest' => round($instNet / $car['price'] * 100 * 100) / 100 . '%',
				'instalments_no' => $instalmentsNo,
				'buyout_percent' => $buyoutRate * 100 . ' %',
				'buyout_gross' => $view->price($buyoutGross, $lr),
				'buyout_net' => $view->price($buyoutNet, $lr),
				'buyout_vat' => $view->price($buyoutVat, $lr)
			);
		}
	}
	
	public function getCarModelBySrModelId($srModelId) {
		$select = $this->db->select()
			->from($this->tables['car_models'])
			->where('sr_model_id = ' . (int)$srModelId);
		return $this->db->fetchRow($select);
	}
	
	public function getCarModelByOmId($modelOmId) {
		$select = $this->db->select()
			->from($this->tables['car_models'])
			->where('om_id = ' . (int)$modelOmId);
		return $this->db->fetchRow($select);
	}
	
	public function getCarModelByString($value, $makeId=null) {
		$select = $this->db->select()
			->from($this->tables['car_models'])
			->where('LOWER(name) = ?', mb_strtolower($value, "UTF-8"));

		if ((int)$makeId > 0) {
			$select->where('make_id = ' . (int)$makeId);
		}
		$model = $this->db->fetchRow($select);


		return $model;
	}
	
	public function getCarModelsByName($make_name) {

	    $select = $this->db->select()
		    ->from(array("md" => $this->tables['car_models']), array('id', 'name'))
		    ->join(array('mk' => $this->tables['car_makes']),
					'md.make_id = mk.id',
					array()
				)
		    ->where('mk.name = ?', $make_name)
		    ->order('name ASC');
	    return $this->db->fetchPairs($select);

	}

	public function getCarPriceTypes() {
		return array(
			self::PRICE_NET => 'netto',
			self::PRICE_GROSS => 'brutto'
		);
	}
	
	public function getCarViewsBySrId($id) {
		$select = $this->db->select()
			->from($this->tables['cars'], array('views'))
			->where('sr_car_id = ' . (int)$id);
		return $this->db->fetchOne($select);
	}
	
	public function getColourId($strValue) {
		if (empty($strValue)) {
			return null;
		}

		$select = $this->db->select()
			->from($this->tables['colours'], array('id'))
			->where('`name` = ?', $strValue);
		$id = $this->db->fetchOne($select);

		if (!empty($id)) return $id;

		if (mb_strlen($strValue, "UTF-8") >= 3) {
			$select = $this->db->select()
				->from($this->tables['colours'], array('id'))
				->where('SUBSTRING(name, 1, 3) = ?', mb_substr($strValue, 0, 3, "UTF-8"));
			$id = $this->db->fetchOne($select);
		}

		if (!empty($id)) return $id;

		return null;
	}
	
	public function getFuelTypesByName() {
		$select = $this->db->select()
			->from($this->tables['cars'], array(new Zend_Db_Expr('DISTINCT fuel_type_id')));
		$withCars = $this->db->fetchCol($select);

		$select = $this->db->select()
			->from(array('vf' => $this->tables['fuel_types']),
				array('key')
			)
			->joinLeft(array('t' => $this->tables['translations']),
					't.key = vf.key',
					array('value')
			)
			->where('language=?', Zend_Registry::get('translate_language'))
			->order('t.value ASC');
		if (count($withCars) > 0) {
			$select->where('vf.id IN(?)', $withCars);
		}
		return $this->db->fetchPairs($select);
	}

	public function getGearboxTypesByName() {
		$select = $this->db->select()
			->from($this->tables['cars'], array(new Zend_Db_Expr('DISTINCT gearbox_type_id')));
		$withCars = $this->db->fetchCol($select);

		$select = $this->db->select()
			->from(array('gt' => $this->tables['gearbox_types']),
				array('id')
			)
			->joinLeft(array('t' => $this->tables['translations']),
					't.key = gt.key',
					array('value')
			)
			->where('language=?', Zend_Registry::get('translate_language'))
			->order('t.value ASC');

		if (count($withCars) > 0) {
			$select->where('gt.id IN (?)', $withCars);
		}

		return $this->db->fetchPairs($select);
	}

    public function getCountriesByName() {


        $select = $this->db->select()
            ->from(array('gt' => $this->tables['countries']),
                array('id', 'name')
            );



        return $this->db->fetchPairs($select);
    }

	public function getInstalmentContributions($type='leasing') {
		if ($type == 'leasing') {
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$optInst = $opt['instalments']['leasing']['interestRates'];
			$keys = array_keys($optInst);
			foreach ($keys as $key => $value) {
				$keys[$key] = $value / 100;
			}
			return $keys;
		}
		else {
			return array();
		}
	}

	public function getInstalmentNoOptions($type='leasing') {
		$instalmentsNo = array();
		$tr = Zend_Registry::get('Zend_Translate');

		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$optInst = $opt['instalments'];
		if ($type == 'credit') {
			$step = $optInst['credit']['instalmentNoStep'];
			$optInst = $optInst['credit']['interestRates'];
			$keys = array_keys($optInst);
			$min = $optInst[$keys[0]]['instalmentsNoMin'];
			$max = $optInst[$keys[count($keys) - 1]]['instalmentsNoMax'];

			for ($i = $min; $i <= $max; $i += $step) {
				$instalmentsNo[$i] = $i . ' ' . $tr->_('INSTALLMENTS');
			}
		}
		else {
			$optInst = $optInst['leasing']['interestRates'];
			$keys = array_keys($optInst);
			$last = $optInst[$keys[count($keys) - 1]];
			$periods = array_keys($last);
			foreach ($periods as $p) {
				$instalmentsNo[$p] = $p . ' ' . $tr->_('INSTALLMENTS');
			}
		}
		return $instalmentsNo;
	}
	
	public function getLatestCarsWithPhotos() {
		$results = array();
		$foundCarIds = array();

		$select = $this->db->select()
			->from(array('c' => $this->tables['cars']), array('car_id', 'price' => 'IFNULL(LEAST(price, promotion_price), price)', 'price_currency', 'price_type_key', 'build_year', 'first_registration_year', 'odometer', 'power', 'power_unit', 'cubic_capacity', 'power_unit', 'position', 'title', 'description', 'auction_price'))
			->joinLeft(array('models' => $this->tables['car_models']),
				'c.model_id = models.id',
				array('model_name' => 'name')
			)
			->joinLeft(array('makes' => $this->tables['car_makes']),
				'models.make_id = makes.id',
				array('make_name' => 'name')
			)
            ->joinLeft(array('p' => $this->tables['cars_photos']),
				'p.car_id = c.car_id AND p.status = "READY" AND p.ord=1',
				array('filename_base', 'filename_extension')
			)
			->joinLeft(array('vc' => $this->tables['vehicle_categories'], array('key')),
				'c.vehicle_category_id = vc.id',
				array('vc.key')
			)
			->order(array(new Zend_Db_Expr('(c.ownership_type = "OWN" AND c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC'), new Zend_Db_Expr('c.ownership_type = "OWN" DESC'), new Zend_Db_Expr('(c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC'), 'c.added_datetime DESC', 'c.car_id DESC'))
			->where('filename_base IS NOT NULL')
			->where('c.is_visible = 1')
		;

		$results = array(
			1 => null,
			2 => null,
			3 => null,
			4 => null,
			5 => null,
			6 => null,
		);
		$resultDescriptions = array(
			1 => "",
			2 => "",
			3 => "",
			4 => "",
			5 => "",
			6 => ""
		);

		for ($i = 1; $i <= 6; $i++) {
			$selectByCategory = clone($select);

			if (count($foundCarIds) > 0) {
				$selectByCategory->where('c.car_id NOT IN (?)', $foundCarIds);
			}

			// 6 slots with specific types of cars to fill with
			switch ($i) {
				case 1:
					$selectByCategory->where('vc.key IN (?)', array('sedan', 'hatchback'))
									->limit(1, 0);
					$resultDescriptions[$i] = "SEDAN_HATCHBACK";
					break;
				case 2:
					$selectByCategory->where('vc.key IN (?)', array('suv', 'pickup'))
									->limit(1, 0);
					$resultDescriptions[$i] = "SUV_PICKUP";
					break;
				case 3:
					$selectByCategory->where('vc.key IN (?)', array('combi', 'van'))
									->limit(1, 0);
					$resultDescriptions[$i] = "COMBI_VAN";
					break;
				case 4:
					$selectByCategory->where('vc.key IN (?)', array('coupe', 'cabrio'))
									->limit(1, 0);
					$resultDescriptions[$i] = "COUPE_CABRIO";
					break;
                case 5:
                    $selectByCategory->where('makes.special_flag = ?', "PREMIUM")
                        ->where('c.price > 100000')
                        ->limit(1, 0);
                    $resultDescriptions[$i] = "PREMIUM";
                    break;
				case 6:

                   $selectByCategory->where('makes.special_flag = ?', "EXCLUSIVE")
					   ->where('c.price > 100000')
					   ->limit(1, 0);
                    $resultDescriptions[$i] = "EXCLUSIVE";
                    break;

				default:

					break;
			}

			$result = $this->db->fetchRow($selectByCategory);
			if ($result) {
				$results[$i] = $result;
				$foundCarIds[] = $result['car_id'];
			}

		}

		$emptySlots = array();
		foreach ($results as $key => $value) {
			if ($value == null) {
				$emptySlots[] = $key;
			}
		}

		if (count($emptySlots) > 0) {
			$select->where('c.car_id NOT IN(?)', $foundCarIds);

			$selCount = clone($select);
			$selCount->reset('columns');
			$selCount->columns(new Zend_Db_Expr('COUNT(c.car_id)'));

			$randomCount = $this->db->fetchOne($selCount);
			$random = mt_rand(0, $randomCount - 1 - (count($emptySlots) - 1));

			if ($random >= 0) {
				$select->limit(count($emptySlots), $random);
				$missingResults = $this->db->fetchAll($select);

				$i = 0;
				foreach ($emptySlots as $value) {
					if (array_key_exists($i, $missingResults)) {
						$results[$value] = $missingResults[$i];
						$i++;
					}
				}
			}
		}

		foreach ($results as $i => $value) {
			$results[$i]['custom_category_description'] = $resultDescriptions[$i];
		}
		return $results;
	}
	
	public function getSimilarCarMakesWithCounts($carFull) {
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$opt = $opt['similarMakes'];
		$priceMin = max(0, $carFull['price'] - (int)$opt['spreadBottom']);
		$priceMax = $carFull['price'] + (int)$opt['spreadTop'];

		$select = $this->db->select()
			->from(array('c' => $this->tables['cars']), array('car_count' => 'COUNT(DISTINCT c.car_id)', 'mk.name'))
			->joinLeft(array('mk' => $this->tables['car_makes']),
				'mk.id = c.make_id',
				array('name')
			)
			->where('c.is_visible = 1')
			->where('c.vehicle_category_id = ?', $carFull['vehicle_category_id'])
			->where('IFNULL(LEAST(c.price, c.promotion_price), c.price) >= ?', $priceMin)
			->where('IFNULL(LEAST(c.price, c.promotion_price), c.price) <= ?', $priceMax)
			->where('c.car_id != ?', $carFull['car_id'])
			->group('mk.name')
			->order('mk.name ASC');

			$ret = array(
				'cars' => $this->db->fetchAll($select),
				'params' => array(
					'categories' => array($carFull['vc_key']),
					'price_min' => $priceMin,
					'price_max' => $priceMax,
				)
			);

			return $ret;
	}
	
	public function getSimilarCars($car, $options=array()) {
		if (!is_array($options))
			$options = array();

		$select = $this->db->select()
			->from(array('c' => $this->tables['cars']),
                    array('car_id',
                        'price' => 'IFNULL(LEAST(price, promotion_price), price)',
                        'price_currency',
                        'price_type_key',
                        'build_year',
                        'first_registration_year',
                        'odometer', 'power',
                        'power_unit',
                        'cubic_capacity',
                        'power_unit',
                        'position',
                        'title',
                        'description',
                        'ownership_type',
                        'leasing',
                        'credit_collateral',
                        'is_reserved',
                        'is_reserved_hidden',
                        'is_sold_hidden',
                        'status',
                        'sr_car_id',
                        'auction_price',
                        'is_new_car' => new Zend_Db_Expr('CASE c.location_id WHEN '.self::NEW_CAR_LOCATION.' THEN 1 ELSE 0 END')))

			->joinLeft(array('mk' => $this->tables['car_makes']),
				'mk.id = c.make_id',
				array('make_name' => 'name')
			)
			->joinLeft(array('md' => $this->tables['car_models']),
				'md.id = c.model_id',
				array('model_name' => 'name')
			)
			->joinLeft(array('vc' => $this->tables['vehicle_categories']),
				'vc.id = c.vehicle_category_id',
				array('vc_key' => 'key')
			)
			->joinLeft(array('ph' => $this->tables['cars_photos']),
				'ph.car_id=c.car_id AND ph.ord=1 AND ph.status="READY"',
				array('filename_base', 'filename_extension'))
			->joinLeft(array('loc' => $this->tables['locations']),
				'c.location_id = loc.location_id',
				array('short_name', 'location_group_id', 'name')
			)
			->joinLeft(array('locgr' => $this->tables['location_groups']),
				'loc.location_group_id = locgr.id',
				array('address')
			);

            if(Zend_Registry::isRegistered('carTaker') && Zend_Registry::get('carTaker'))
            {
                $select->joinLeft(array('em' => $this->tables['employees']),
                    'em.sr_id = '.Zend_Registry::get('carTaker'),
                    array('caretaker_first_name' => 'first_name', 'caretaker_last_name' => 'last_name', 'caretaker_email' => 'email', 'caretaker_phone' => 'phone', 'caretaker_visible' => 'visible')
                );
            }
            else
            {
                $select->joinLeft(array('em' => $this->tables['employees']),
                    'em.sr_id = c.caretaker_sr_id',
                    array('caretaker_first_name' => 'first_name', 'caretaker_last_name' => 'last_name', 'caretaker_email' => 'email', 'caretaker_phone' => 'phone', 'caretaker_visible' => 'visible')
                );
            }

			$select ->where('c.is_visible = 1')
					->where('c.car_id != ' . (int)$car['car_id'])
					->where('vehicle_category_id=?', $car['vehicle_category_id']);

			if (!array_key_exists('car_price_spread', $options))
				$options['car_price_spread'] = 0.25;
			$select ->where('IFNULL(LEAST(price, promotion_price), price) >= ?', $car['price'] * (1 - $options['car_price_spread']))
					->where('IFNULL(LEAST(price, promotion_price), price) <= ?', $car['price'] * (1 + $options['car_price_spread']));

			if (!array_key_exists('build_year_spread', $options))
				$options['build_year_spread'] = 3;
			$select ->where('build_year >= ?', $car['build_year'] - $options['build_year_spread'])
					->where('build_year <= ?', $car['build_year'] + $options['build_year_spread']);

			if (!array_key_exists('count', $options))
				$options['count'] = 5;
			$select ->limit($options['count']);
			if (!array_key_exists('sort', $options))
				$options['sort'] = "price ASC";

			$select->order(array(new Zend_Db_Expr('(c.ownership_type = "OWN" AND c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC'), new Zend_Db_Expr('c.ownership_type = "OWN" DESC'), new Zend_Db_Expr('(c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC'), 'c.added_datetime DESC'));


			return $this->db->fetchAll($select);
	}
	
	/*
	//totally random variant
	public function getRandomCarsWithPhotos($count) {
		$results = array();
		$foundCarIds = array(0);
	
		$select = $this->db->select()
			->from(array('c' => $this->tables['cars']), array('car_id', 'price' => 'IFNULL(LEAST(price, promotion_price), price)', 'price_currency', 'price_type_key', 'build_year', 'first_registration_year', 'odometer', 'power', 'power_unit', 'cubic_capacity', 'power_unit', 'position', 'title', 'description'))
			->joinLeft(array('models' => $this->tables['car_models']),
				'c.model_id = models.id',
				array('model_name' => 'name')
			)
			->joinLeft(array('makes' => $this->tables['car_makes']),
				'models.make_id = makes.id',
				array('make_name' => 'name')
			)
			->joinLeft(array('p' => $this->tables['cars_photos']),
				'p.car_id = c.car_id AND p.status = "READY" AND p.ord=1',
				array('filename_base', 'filename_extension')
			)
			->joinLeft(array('vc' => $this->tables['vehicle_categories'], array('key')),
				'c.vehicle_category_id = vc.id',
				array('vc.key')
			)
			->where('filename_base IS NOT NULL')
			->where('is_visible = 1')
		;
		
		for ($i = 1; $i <= 8; $i++) {
			$selectByCategory = clone($select);
			
			if (count($foundCarIds) > 0) {
				$selectByCategory->where('c.car_id NOT IN (?)', $foundCarIds);
			}
			
			// 8 slots with specific types of cars to fill with
			switch ($i) {
				case 1:
					$selectByCategory->where('vc.key IN (?)', array('sedan', 'hatchback', 'combi'));					
					break;
				case 2:
					$selectByCategory->where('c.premium = 1')
									->where('c.price > 100000');
					break;
				case 3:
					$selectByCategory->where('vc.key IN (?)', array('cabrio'));
					break;
				case 4:
					$selectByCategory->where('vc.key IN (?)', array('coupe'));
					break;
				case 5:
					$selectByCategory->where('vc.key IN (?)', array('suv'));
					break;
				case 6:
					$selectByCategory->where('vc.key IN (?)', array('pickup'));
					break;
				case 7:
					$selectByCategory->where('vc.key IN (?)', array('van'));
					break;
				case 8:
					$selectByCategory->where('rental = 1');
					break;
				default:
					
					break;
			}
			
			$selCount = clone($selectByCategory);
			$selCount->reset('columns');
			$selCount->columns(new Zend_Db_Expr('COUNT(c.car_id)'));
			$randomCount = $this->db->fetchOne($selCount);
			
			if ($randomCount == 0) {
				$results[$i] = null;
				continue;
			}

			$random = mt_rand(0, $randomCount - 1);
			
			$selectByCategory->limit(1, $random);
			$result = $this->db->fetchRow($selectByCategory);
			$results[$i] = $result;
			$foundCarIds[] = $result['car_id'];
		}
		
		$emptySlots = array();
		foreach ($results as $key => $value) {
			if ($value == null) {
				$emptySlots[] = $key;
			}	
		}
		
		if (count($emptySlots) > 0) {
			$select->where('c.car_id NOT IN(?)', $foundCarIds);
			
			$selCount = clone($select);
			$selCount->reset('columns');
			$selCount->columns(new Zend_Db_Expr('COUNT(c.car_id)'));
			
			$randomCount = $this->db->fetchOne($select);
			$random = mt_rand(0, $randomCount - 1 - (count($emptySlots) - 1));
			
			$select->limit(count($emptySlots), $random);
			$missingResults = $this->db->fetchAll($select);
			
			$i = 0;
			foreach ($emptySlots as $value) {
				$results[$value] = $missingResults[$i];
				$i++;
			}
		}
		return $results;
	}
	*/
	
	public function incrementViews($id) {
		$this->db->update(
			$this->tables['cars'],
			array(
				'views' => new Zend_Db_Expr('views + 1')
			),
			"car_id = " . (int)$id
		);
	}

	public function prepareFilterArray($searchParameters, $form=null) {
		$filterArray = array();
		$tr = Zend_Registry::get('Zend_Translate');
		$view = Zend_Layout::getMvcInstance()->getView();
		$cars = new Model_Cars_Cars();
		$carTypes = $cars->getCarTypes($assoc=true);
		$carTypesSlug = $cars->getCarTypesSlug(true);

		foreach($searchParameters as $key => $value) {
			if (($value && $value != '') || $value === "0") {
				switch($key) {
					case 'query' :
						$filterArray[] = array('title' => 'QUERY', 'value' => '"' . $view->escape($value) . '"', 'appendName' => 'query', 'appendValue' => '');
						break;
					case 'types' :
						foreach($value as $selectValueId) {
							if (!array_key_exists((int)$selectValueId, $carTypes)) continue;
							$filterArray[] = array('title' => 'CAR_TYPE', 'value' => $tr->_('type_' . $carTypes[(int)$selectValueId]), 'appendName' => $key, 'appendValue' => (int)$selectValueId);
						}
						break;
					case 'type' :

						$filterArray[] = array('title' => 'CAR_TYPE', 'value' => $tr->_('type_' . $carTypesSlug[$value]), 'appendName' => $key);

						break;
					case 'build_from' :
						$filterArray[] = array('title' => 'BUILD_FROM', 'value' => $value, 'appendName' => $key, 'appendValue' => $value);
						break;
					case 'build_to' :
						$filterArray[] = array('title' => 'BUILD_TO', 'value' => $value, 'appendName' => $key, 'appendValue' => $value);
						break;
					case 'build' :
						foreach($value as $selectValueId) {
							$filterArray[] = array('title' => 'BUILD', 'value' => $tr->_(mb_strtoupper($selectValueId, "UTF-8")), 'appendName' => $key, 'appendValue' => $selectValueId);
						}
						break;
                    case 'cubic_capacity_from' :
						$filterArray[] = array('title' => 'ENGINE_CAPACITY_FROM', 'value' => $value, 'appendName' => $key, 'appendValue' => $value);
						break;
					case 'cubic_capacity_to' :
						$filterArray[] = array('title' => 'ENGINE_CAPACITY_TO', 'value' => $value, 'appendName' => $key, 'appendValue' => $value);
						break;
                    case 'odometer_from' :
                        $filterArray[] = array('title' => 'ODOMETER_FROM', 'value' => $value, 'appendName' => $key, 'appendValue' => $value);
                        break;
                    case 'odometer_to' :
                        $filterArray[] = array('title' => 'ODOMETER_TO', 'value' => $value, 'appendName' => $key, 'appendValue' => $value);
                        break;
					case 'odometer' :
						foreach($value as $selectValueId) {
							$filterArray[] = array('title' => 'ODOMETER', 'value' => $tr->_($selectValueId), 'appendName' => $key, 'appendValue' => $selectValueId);
						}
						break;
					case 'power' :
						foreach($value as $selectValueId) {
							$filterArray[] = array('title' => 'POWER', 'value' => $tr->_($selectValueId), 'appendName' => $key, 'appendValue' => $selectValueId);
						}
						break;
					case 'gearboxes' :
						foreach($value as $selectValueId) {
							$filterArray[] = array('title' => 'GEARBOX', 'value' => $tr->_($selectValueId), 'appendName' => $key, 'appendValue' => $selectValueId);
						}
						break;
					case 'fuels' :
						foreach($value as $selectValueId) {
							$filterArray[] = array('title' => 'FUEL', 'value' => $tr->_($selectValueId), 'appendName' => $key, 'appendValue' => $selectValueId);
						}
						break;
					case 'features' :
						foreach($value as $selectValueId) {
							$filterArray[] = array('title' => 'FEATURE', 'value' => $tr->_($selectValueId), 'appendName' => $key, 'appendValue' => $selectValueId);
						}
						break;
					case 'extras' :
						foreach($value as $selectValueId) {
							$filterArray[] = array('title' => 'EXTRA', 'value' => $tr->_($selectValueId), 'appendName' => $key, 'appendValue' => $selectValueId);
						}
						break;
					case 'categories' :
						foreach($value as $selectValueId) {
							$filterArray[] = array('title' => 'CATEGORY', 'value' => $tr->_($selectValueId), 'appendName' => $key, 'appendValue' => $selectValueId);
						}
						break;
					case 'makes' :
						if (is_array($value)) {
							foreach ($value as $val) {
								$filterArray[] = array('title' => 'MAKE', 'value' => $val, 'appendName' => 'makes', 'appendValue' => $val);
							}
						}
						else {
							$filterArray[] = array('title' => 'MAKE', 'value' => $value, 'appendName' => 'makes', 'appendValue' => $value);
						}
						break;

					case 'make' :
						$make = $cars->getCarMakeBySLug($value);
						$filterArray[] = array('title' => 'MAKE', 'value' => $make['name'], 'appendName' => 'makes', 'appendValue' => $value);

						break;
					case 'models' :
						if (is_array($value)) {
							foreach ($value as $val) {
								$model = $cars->getCarModel((int)$val);
								$modelName = $value;
								if ($model) {
									$modelName = $model['name'];
								}
								$filterArray[] = array('title' => 'MODEL', 'value' => $modelName, 'appendName' => 'models', 'appendValue' => $val);
							}
						}
						else {
							$model = $cars->getCarModel((int)$value);
							$modelName = $value;
							if ($model) {
								$modelName = $model['name'];
							}
							$filterArray[] = array('title' => 'MODEL', 'value' => $modelName, 'appendName' => 'models', 'appendValue' => $value);
						}
						break;
					case 'model' :

						$model = $cars->getCarModelBySlug($value);
						$modelName = $value;
						if ($model) {
							$modelName = $model['name'];
						}
						$filterArray[] = array('title' => 'MODEL', 'value' => $modelName, 'appendName' => 'models', 'appendValue' => $value);

						break;
					case 'production' :
						foreach ($value as $id) {
							$title = "PRODUCTION_".$id;
							$filterArray[] = array('title' => $title, 'value' => '', 'appendName' => 'production', 'appendValue' => $id);
						}
						break;
					case 'production_not' :
						foreach ($value as $id) {
							$title = "PRODUCTION_NOT_".$id;
							$filterArray[] = array('title' => $title, 'value' => '', 'appendName' => 'production_not', 'appendValue' => $id);
						}
						break;
					case 'colour_key' :
						foreach ($value as $id) {
							$title = "COLOR_".$id;
							$filterArray[] = array('title' => $title, 'value' => '', 'appendName' => 'colour_key', 'appendValue' => $id);
						}
						break;
					case 'colour_key_not' :
						foreach ($value as $id) {
							$title = "NOT_COLOR_".$id;
							$filterArray[] = array('title' => $title, 'value' => '', 'appendName' => 'colour_key_not', 'appendValue' => $id);
						}
						break;
					case 'door_count' :
						foreach ($value as $v) {
							$val = (is_array($v) ? implode("_", $v) : $v);
							$title = "doors_".$val;
							$filterArray[] = array('title' => $title, 'value' => '', 'appendName' => 'door_count', 'appendValue' => $val);
						}
						break;
					case 'origin' :
						foreach ($value as $id) {
							$title = "ORIGIN_".$id;
							$filterArray[] = array('title' => $title, 'value' => '', 'appendName' => 'origin', 'appendValue' => $id);
						}
						break;
					case 'origin_not' :
						foreach ($value as $id) {
							$title = "ORIGIN_NOT_".$id;
							$filterArray[] = array('title' => $title, 'value' => '', 'appendName' => 'origin_not', 'appendValue' => $id);
						}
						break;
					case 'on_site' :
						$filterArray[] = array('title' => 'CAR_ON_SITE', 'value' => '', 'appendName' => 'on_site');
						break;
					case 'seat_count_min' :
						$filterArray[] = array('title' => $value . ' ' . $tr->_('SEAT_COUNT_MIN'), 'value' => '', 'appendName' => 'seat_count_min', 'appendValue' => $value);
						break;
					case 'seat_count_max' :
						$filterArray[] = array('title' => 'SEAT_COUNT_MIN' . ' ' . $value, 'value' => '', 'appendName' => 'seat_count_min', 'appendValue' => $value);
						break;
					case 'seat_count' :
						$values = explode("_", $value);
						$values = implode(",", $values);
						$filterArray[] = array('title' => 'SEAT_COUNT', 'value' => $values, 'appendName' => 'seat_count', 'appendValue' => $value);
						break;
					case 'consumption_less_than' :
						$filterArray[] = array('title' => 'CONSUMPTION_LESS_THAN', 'value' => $value . ' l.', 'appendName' => 'consumption_less_than', 'appendValue' => $value);
						break;
					case 'displacement_max' :
						$filterArray[] = array('title' => 'DISPLACEMENT_MAX', 'value' => $view->engineDisplacement($value), 'appendName' => 'displacement_max');
						break;
					case 'price_min' :
						$view = Zend_Layout::getMvcInstance()->getView();
						$filterArray[] = array('title' => 'MINPRICE', 'value' => round($view->language_row['multiplier'] * $value), 'appendName' => 'price_min');
						break;
					case 'price_max' :
						$view = Zend_Layout::getMvcInstance()->getView();
						$filterArray[] = array('title' => 'MAXPRICE', 'value' => round($view->language_row['multiplier'] * $value), 'appendName' => 'price_max');
						break;
					case 'promotions' :
						$filterArray[] = array('title' => 'SHORTCUT_PROMOTIONS', 'value' => "", 'appendName' => 'promotions');
						break;
					case 'leasing' :
						$filterArray[] = array('title' => 'SHORTCUT_LEASING', 'value' => "", 'appendName' => 'leasing');
						break;
					case 'leasing_transfer' :
						$filterArray[] = array('title' => 'SHORTCUT_LEASING_TRANSFER', 'value' => '', 'appendName' => 'leasing_transfer');
						break;
					case 'new' :
						$filterArray[] = array('title' => 'SHORTCUT_NEW_CARS', 'value' => "", 'appendName' => 'new');
						break;
					case 'last_2_years' :
						$filterArray[] = array('title' => 'SHORTCUT_LAST_2_YEARS', 'value' => "", 'appendName' => 'last_2_years');
						break;
					case 'premium' :
						if ((int)$value == 1) {
							$filterArray[] = array('title' => 'SHORTCUT_PREMIUM', 'value' => "", 'appendName' => 'premium');
						}
						elseif ($value === "0") {
							$filterArray[] = array('title' => 'NOT_PREMIUM', 'value' => "", 'appendName' => 'premium');
						}
						break;
					case 'exclusive' :
						if ((int)$value == 1) {
							$filterArray[] = array('title' => 'SHORTCUT_EXCLUSIVE', 'value' => "", 'appendName' => 'exclusive');
						}
						elseif ($value === "0") {
							$filterArray[] = array('title' => 'NOT_EXCLUSIVE', 'value' => "", 'appendName' => 'exclusive');
						}
						break;
					case '4x4_pickup' :
						$filterArray[] = array('title' => 'SHORTCUT_4x4_PICKUP', 'value' => "", 'appendName' => '4x4_pickup');
						break;
					case 'after_leasing_vindication' :
						$filterArray[] = array('title' => 'SHORTCUT_AFTER_LEASING_VINDICATION', 'value' => "", 'appendName' => 'after_leasing_vindication');
						break;
					case 'location' :
						$filterArray[] = array('title' => 'LOCATION', 'value' => "", 'appendName' => 'location');
						break;
					case 'credit_collateral' :
						$filterArray[] = array('title' => 'SHORTCUT_CREDIT_COLLATERAL', 'value' => '', 'appendName' => 'credit_collateral');
						break;
					case 'rental' :
						$filterArray[] = array('title' => 'RENTAL', 'value' => '', 'appendName' => 'rental');
						break;
					case 'for_exchange' :
						$filterArray[] = array('title' => 'FOR_EXCHANGE', 'value' => '', 'appendName' => 'for_exchange');
						break;
					case 'price_type_key' :
						$filterArray[] = array('title' => 'NO_VAT', 'value' => '', 'appendName' => 'price_type_key');
						break;
                    case 'auction' :
                        $filterArray[] = array('title' => 'AUCTION', 'value' => '', 'appendName' => 'auction');
                        break;
				}
			}
		}
		return $filterArray;
	}
	
	public function getCarTypesSlug($assoc=true) {
		$select = $this->db->select()
			->from($this->tables['car_types'], array('slug', 'type'));
		return $this->db->fetchPairs($select);
	}

	public function getCarMakeBySlug($slug) {
		$select = $this->db->select()
			->from($this->tables['car_makes'])
			->where('slug = ?', $slug);
		return $this->db->fetchRow($select);
	}
    public function getCarMake($id) {
        $select = $this->db->select()
            ->from($this->tables['car_makes'])
            ->where('id = ' . (int)$id);
        return $this->db->fetchRow($select);
    }

	public function getCarModel($id) {
		$select = $this->db->select()
			->from($this->tables['car_models'])
			->where('id = ' . (int)$id);
		return $this->db->fetchRow($select);
	}

	public function getCarModelBySlug($slug) {
		$select = $this->db->select()
			->from($this->tables['car_models'])
			->where('slug = ? ' , $slug);
		return $this->db->fetchRow($select);
	}
	
	public function prepareFormDataFromParams($parameters) {
		$final = array();
		
		//pre-process parameters to group from mix & chaos to actual types ;)
		$distribution = array(
			"important_features" => array(
				"leasing" => true,
				"features" => array("no_accident", "small_car", "color_black", "color_white", "light_interior", "dark_interior"),
				"last_2_years" => true,
				"extras" => true,
				"origin" => true,
				"production" => true,
				"origin_not" => true,
				"production_not" => true,
				"seat_count_min" => true,
				"seat_count_max" => true,
				"seat_count" => true,
				"door_count" => true,
				"colour_key" => true,
				"colour_key_not" => true,
				"consumption_less_than" => true,
				"on_site" => true,
				"for_exchange" => true
			),
			"engine" => array(
				"fuels" => true,
				"displacement_max" => true,
				"displacement_min" => true,
				"features" => array("v6", "v8"),
				"power" => true
			)
		);
		
		$allKeys = array_keys(array_merge($distribution['important_features'], $distribution['engine']));
		
		$pre = array(
			'important_features' => array(),
			'engine' => array()
		);
		
		foreach ($parameters as $key => $value) {
			//i.e. features => array(...)
			//or   leasing => 1
			if (!in_array($key, $allKeys)) {
				//copy over as is
				$final[$key] = $value;
				continue;
			}
			else {
				foreach ($distribution as $multiselectName => $containedKeys) {
					//i.e. important_features => features
					if (array_key_exists($key, $containedKeys)) {
						//i.e. ake(features, array(...))
						if (is_array($value)) {
							if ($containedKeys[$key] === true) {
								//copy over whole array
								foreach ($value as $id) {
									$pre[$multiselectName][] = "{$key} {$id}";
								}
							}
							else {
								//copy over explicit values
								foreach ($value as $id) {
									if (in_array($id, $containedKeys[$key])) {
										$pre[$multiselectName][] = "{$key} {$id}";
									}
								}
							}
						}
						else {
							$pre[$multiselectName][] = "{$key} {$value}";
						}
					}
				}
				//param not distributed -> copy over as is
				$final[$key] = $value;
			}
		}
		
		$final = array_merge($final, $pre);
		return $final;
	}
	
	public function processPhotos($logger) {
		//for cronjob
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		
		$now = time();
		$processingTimeLimit = $opt['synchronization']['sr']['import']['car_photos_timeout'];
		$processingStartLimit = date("Y-m-d H:i:s", $now - $processingTimeLimit);
		
		$select = $this->db->select()
			->from(array('cp' => $this->tables['cars_photos']))
			->joinLeft(array('c' => $this->tables['cars']),
				'c.car_id = cp.car_id',
				array('c_id' => 'car_id', 'sr_car_id')
			)
			->where('cp.status = ?', 'PROCESSING')
			->where('cp.processing_start <= ?', $processingStartLimit)
			->order('cp.id ASC');
		$timedOut = $this->db->fetchAll($select);

		foreach ($timedOut as $photo) {
			try {
				$targetDir = $opt['photos']['path'] . DIRECTORY_SEPARATOR . $photo['c_id'];
				if (!file_exists($targetDir)) {
					if (!@mkdir($targetDir)) {
						throw new Exception("Directory could not be created: " . $targetDir);
					}
				}
				
				$this->db->update(
					$this->tables['cars_photos'],
					array(
						'processing_start' => new Zend_Db_Expr('NOW()')
					),
					"id = " . $photo['id']
				);
				
                
				$this->fetchPhoto(
					$opt['synchronization']['sr']['domain'] . "/" . $opt['synchronization']['sr']['import']['car_photos_path'] . "/" . $photo['sr_car_id'],
					$targetDir,
					$photo['filename_base'],
					$photo['filename_extension'],
					$opt['photos']['sizes']
				);
				
				$select = $this->db->select()
					->from($this->tables['cars_photos'])
					->where('id = ' . $photo['id']);
				$recheckPhoto = $this->db->fetchRow($select);
				
				if (empty($recheckPhoto)) {
					throw new Exception("Photo re-check failed (no DB results)");
				}
				
				if ($recheckPhoto['status'] == 'MARKED_FOR_DELETION') {
					$this->deletePhoto($recheckPhoto['id']);
				}
				else {
					$this->db->update(
						$this->tables['cars_photos'],
						array(
							'status' => 'READY',
							'processing_start' => new Zend_Db_Expr('NULL')
						),
						"id = " . $recheckPhoto['id']
					);
				}
			}
			catch (Exception $e) {
				$logger->log(PHP_EOL . PHP_EOL . date("Y-m-d H:i:s", time()) . " -- \$timedOut" . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);
			}
		}
		
		
		
		$select = $this->db->select()
			->from(array('cp' => $this->tables['cars_photos']))
			->joinLeft(array('c' => $this->tables['cars']),
				'c.car_id = cp.car_id',
				array('c_id' => 'car_id', 'sr_car_id')
			)
			->where('cp.status = ?', 'NO_FILE')
			->order('cp.id ASC');
		$noPhoto = $this->db->fetchAll($select);
		
		foreach ($noPhoto as $photo) {
			try {
				$targetDir = $opt['photos']['path'] . DIRECTORY_SEPARATOR . $photo['c_id'];
				if (!file_exists($targetDir)) {
					if (!@mkdir($targetDir)) {
						throw new Exception("Directory could not be created: " . $targetDir);
					}
				}
				
				$this->db->update(
					$this->tables['cars_photos'],
					array(
						'status' => 'PROCESSING',
						'processing_start' => new Zend_Db_Expr('NOW()')
					),
					"id = " . $photo['id']
				);
				
				$this->fetchPhoto(
					$opt['synchronization']['sr']['domain'] . "/" . $opt['synchronization']['sr']['import']['car_photos_path'] . "/" .  $photo['sr_car_id'],
					$targetDir,
					$photo['filename_base'],
					$photo['filename_extension'],
					$opt['photos']['sizes']
				);
				
				$select = $this->db->select()
					->from($this->tables['cars_photos'])
					->where('id = ' . $photo['id']);
				$recheckPhoto = $this->db->fetchRow($select);
				
				if (empty($recheckPhoto)) {
					throw new Exception("Photo recheck failed (no DB results)");
				}
				
				if ($recheckPhoto['status'] == 'MARKED_FOR_DELETION') {
					$this->deletePhoto($recheckPhoto['id']);
				}
				else {
					$this->db->update(
						$this->tables['cars_photos'],
						array(
							'status' => 'READY',
							'processing_start' => new Zend_Db_Expr('NULL')
						),
						"id = " . $recheckPhoto['id']
					);
				}
			}
			catch (Exception $e) {
				$logger->log(PHP_EOL . PHP_EOL . date("Y-m-d H:i:s", time()) . " -- \$timedOut" . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);
			}
		}
		
		
		$select = $this->db->select()
			->from($this->tables['cars_photos'])
			->where('status = ?', 'MARKED_FOR_DELETION')
			->order('id ASC');
		$forDeletion = $this->db->fetchAll($select);
		
		foreach ($forDeletion as $photo) {
			try {
				$this->deletePhoto($photo['id']);
			}
			catch (Exception $e) {
				$logger->log(PHP_EOL . PHP_EOL . date("Y-m-d H:i:s", time()) . " -- \$forDeletion" . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);
			}
		}
	}
    
	public function fetchPhoto($srcPath, $destPath, $name, $extension, $sizes) {

        error_log($srcPath);

		$filesToGet = array();
		$filesToGet[] = $name . "." . $extension;

		foreach ($sizes as $sizeName => $data) {
			$filename = $name . "_" . $sizeName . "." . $extension;
			$filesToGet[] = $filename;
		}

		foreach ($filesToGet as $filename) {
			$handle = @fopen($srcPath . "/" . $filename, "r");
			if (!$handle) {
				throw new Exception("Handle to file could not be opened: " . $srcPath . "/" . $filename);
			}

			$contents = stream_get_contents($handle);
			@fclose($handle);

			$result = @file_put_contents($destPath . DIRECTORY_SEPARATOR . $filename, $contents);
			if ($result === false) {
				throw new Exception("file_put_contents failed; " . $destPath . DIRECTORY_SEPARATOR . $filename);
			}
		}
	}
	
    public function processVipautoPhotos($logger) {
		//for cronjob
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();

		$now = time();
		$processingTimeLimit = $opt['synchronization']['sr']['import']['car_photos_timeout'];
		$processingStartLimit = date("Y-m-d H:i:s", $now - $processingTimeLimit);

		$select = $this->db->select()
			->from(array('cvp' => $this->tables['cars_vipauto_photos']))
			->joinLeft(array('c' => $this->tables['cars']),
				'c.car_id = cvp.car_id',
				array('c_id' => 'car_id', 'sr_car_id')
			)
			->where('cvp.status = ?', 'PROCESSING')
			->where('cvp.processing_start <= ?', $processingStartLimit)
			->order('cvp.id ASC');
		$timedOut = $this->db->fetchAll($select);

		foreach ($timedOut as $photo) {
			try {
				$targetDir = $opt['photos']['vipauto_path'] . DIRECTORY_SEPARATOR . $photo['c_id'];
				if (!file_exists($targetDir)) {
					if (!@mkdir($targetDir)) {
						throw new Exception("Directory could not be created: " . $targetDir);
					}
				}

				$this->db->update(
					$this->tables['cars_vipauto_photos'],
					array(
						'processing_start' => new Zend_Db_Expr('NOW()')
					),
					"id = " . $photo['id']
				);

				$this->fetchPhoto(
					$opt['synchronization']['sr']['domain'] . "/" . $opt['synchronization']['sr']['import']['car_vipauto_photos_path'] . "/" . $photo['sr_car_id'],
					$targetDir,
					$photo['filename_base'],
					$photo['filename_extension'],
					$opt['photos']['sizes']
				);

				$select = $this->db->select()
					->from($this->tables['cars_vipauto_photos'])
					->where('id = ' . $photo['id']);
				$recheckPhoto = $this->db->fetchRow($select);

				if (empty($recheckPhoto)) {
					throw new Exception("Photo re-check failed (no DB results)");
				}

				if ($recheckPhoto['status'] == 'MARKED_FOR_DELETION') {
					$this->deleteVipautoPhoto($recheckPhoto['id']);
				}
				else {
					$this->db->update(
						$this->tables['cars_vipauto_photos'],
						array(
							'status' => 'READY',
							'processing_start' => new Zend_Db_Expr('NULL')
						),
						"id = " . $recheckPhoto['id']
					);
				}
			}
			catch (Exception $e) {
				$logger->log(PHP_EOL . PHP_EOL . date("Y-m-d H:i:s", time()) . " -- \$timedOut" . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);
			}
		}



		$select = $this->db->select()
			->from(array('cvp' => $this->tables['cars_vipauto_photos']))
			->joinLeft(array('c' => $this->tables['cars']),
				'c.car_id = cvp.car_id',
				array('c_id' => 'car_id', 'sr_car_id')
			)
			->where('cvp.status = ?', 'NO_FILE')
			->order('cvp.id ASC');
		$noPhoto = $this->db->fetchAll($select);

		foreach ($noPhoto as $photo) {
			try {
				$targetDir = $opt['photos']['vipauto_path'] . DIRECTORY_SEPARATOR . $photo['c_id'];
				if (!file_exists($targetDir)) {

					if (!@mkdir($targetDir)) {
						throw new Exception("Directory could not be created: " . $targetDir);
					}
				}

				$this->db->update(
					$this->tables['cars_vipauto_photos'],
					array(
						'status' => 'PROCESSING',
						'processing_start' => new Zend_Db_Expr('NOW()')
					),
					"id = " . $photo['id']
				);

				$this->fetchPhoto(
					$opt['synchronization']['sr']['domain'] . "/" . $opt['synchronization']['sr']['import']['car_vipauto_photos_path'] . "/" .  $photo['sr_car_id'],
					$targetDir,
					$photo['filename_base'],
					$photo['filename_extension'],
					$opt['photos']['sizes']
				);

				$select = $this->db->select()
					->from($this->tables['cars_vipauto_photos'])
					->where('id = ' . $photo['id']);
				$recheckPhoto = $this->db->fetchRow($select);

				if (empty($recheckPhoto)) {
					throw new Exception("Photo recheck failed (no DB results)");
				}

				if ($recheckPhoto['status'] == 'MARKED_FOR_DELETION') {
					$this->deleteVipautoPhoto($recheckPhoto['id']);
				}
				else {
					$this->db->update(
						$this->tables['cars_vipauto_photos'],
						array(
							'status' => 'READY',
							'processing_start' => new Zend_Db_Expr('NULL')
						),
						"id = " . $recheckPhoto['id']
					);
				}
			}
			catch (Exception $e) {
				$logger->log(PHP_EOL . PHP_EOL . date("Y-m-d H:i:s", time()) . " -- \$timedOut" . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);
			}
		}


		$select = $this->db->select()
			->from($this->tables['cars_vipauto_photos'])
			->where('status = ?', 'MARKED_FOR_DELETION')
			->order('id ASC');
		$forDeletion = $this->db->fetchAll($select);

		foreach ($forDeletion as $photo) {
			try {
				$this->deleteVipautoPhoto($photo['id']);
			}
			catch (Exception $e) {
				$logger->log(PHP_EOL . PHP_EOL . date("Y-m-d H:i:s", time()) . " -- \$forDeletion" . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);
			}
		}
	}

	/* szukanie - GB */

	public function search($parameters = array(), $selectOnly = false, $nextPrev = false, $onlyVisible = true, $onlyToday = false, $carId = false, $count = false, $carDateFrom=null, $carDateTo=null) {
		$select = $this->db->select()
			->from(array('c' => $this->tables['cars']), 
                    array('car_id', 'sr_car_id', 
                        'price' => 'IFNULL(LEAST(price, promotion_price), price)', 
                        'price_currency', 
                        'price_type_key', 
                        'build_year', 
                        'first_registration_year', 
                        'odometer', 
                        'power', 
                        'power_unit', 
                        'cubic_capacity', 
                        'position', 'title', 
                        'description', 
                        'ownership_type', 
                        'leasing', 
                        'credit_collateral', 
                        'is_reserved', 
                        'is_reserved_hidden', 
                        'is_sold_hidden', 
                        'status',
                        'added_datetime',
                        'price_change_datetime',
                        'is_visible_datetime',
                        'auction_price',
                        'is_new_car' => new Zend_Db_Expr('CASE c.location_id WHEN '.self::NEW_CAR_LOCATION.' THEN 1 ELSE 0 END')))
			->joinLeft(array('mk' => $this->tables['car_makes']),
				'mk.id = c.make_id',
				array('make_name' => 'name')
			)
			->joinLeft(array('ct' => $this->tables['car_types']),
				'mk.type_id = ct.id',
				array()
			)
			->joinLeft(array('md' => $this->tables['car_models']),
				'md.id = c.model_id',
				array('model_name' => 'name')
			)
			->joinLeft(array('vc_i' => $this->tables['vehicle_categories']),
				'vc_i.id = c.vehicle_category_id',
				array('vc_key' => 'key')
			)
			->joinLeft(array('ph' => $this->tables['cars_photos']),
				'ph.car_id=c.car_id AND ph.ord=1 AND ph.status="READY"',
				array('filename_base', 'filename_extension')
			)
			->joinLeft(array('loc' => $this->tables['locations']),
				'c.location_id = loc.location_id',
				array('short_name', 'location_group_id', 'name')
			)
			->joinLeft(array('locgr' => $this->tables['location_groups']),
				'loc.location_group_id = locgr.id',
				array('address'))
			
			->order(array(new Zend_Db_Expr('(c.ownership_type = "OWN" AND c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC'), new Zend_Db_Expr('(c.ownership_type = "CLIENT" AND c.status = "ON_SITE") DESC'),  new Zend_Db_Expr('c.ownership_type = "OWN" DESC'), new Zend_Db_Expr('(c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC'), 'c.added_datetime DESC'))
			;
		
        if(Zend_Registry::isRegistered('carTaker') && Zend_Registry::get('carTaker'))
        {
            $select->joinLeft(array('em' => $this->tables['employees']),
				'em.sr_id = '.Zend_Registry::get('carTaker'),
				array('caretaker_first_name' => 'first_name', 'caretaker_last_name' => 'last_name', 'caretaker_email' => 'email', 'caretaker_phone' => 'phone', 'caretaker_visible' => 'visible')
			);
        }
        else
        {
            $select->joinLeft(array('em' => $this->tables['employees']),
				'em.sr_id = c.caretaker_sr_id',
				array('caretaker_first_name' => 'first_name', 'caretaker_last_name' => 'last_name', 'caretaker_email' => 'email', 'caretaker_phone' => 'phone', 'caretaker_visible' => 'visible')
			);
        }
            
		if ($onlyVisible) {
			$select->where('c.is_visible = 1');
		}
		
		if (count($parameters)) {
			$this->searchPrepareFromParameters($parameters, $select);
		} else {
			//tu jest puste zapytanie - cache i co tam jeszcze chcesz
		}

        if($carDateFrom && $carDateTo) {

            $select->where($this->db->quoteInto('c.is_visible_datetime<=?', $carDateTo) . ' AND (' . $this->db->quoteInto('c.price_change_datetime<=?', $carDateTo) . ' OR c.price_change_datetime IS NULL)')
            ->where($this->db->quoteInto('c.is_visible_datetime>=?', $carDateFrom) . ' OR ' . $this->db->quoteInto('c.price_change_datetime>=?', $carDateFrom));

        }

		if ($onlyToday) {
            
            $time = time();
            if($onlyToday !== true)
            {
                $time = strtotime($onlyToday);
                
                $select->where($this->db->quoteInto('c.is_visible_datetime<=?', date("Y-m-d H:i:s", $time)) . ' AND (' . $this->db->quoteInto('c.price_change_datetime<=?', date("Y-m-d H:i:s", $time))  . ' OR c.price_change_datetime IS NULL)' );
            }
             
            $dateMinusOne = date("Y-m-d H:i:s", mktime(0, 0, 0, date('m',$time), date('d',$time) - 1, date('Y',$time)));
            $whereString = $this->db->quoteInto('c.is_visible_datetime>=?', $dateMinusOne) . ' OR ' . $this->db->quoteInto('c.price_change_datetime>=?', $dateMinusOne);
			$select->where($whereString);
            
            if ($carId) {
                $select->reset('columns');
                $select->columns("c.car_id");
                $select->where('c.car_id = ?', $carId);
            
                
            }
            $select->reset('order');
            $select->order(array(
                new Zend_Db_Expr('IFNULL((c.added_datetime >= c.price_change_datetime),1) DESC'),
                new Zend_Db_Expr('(c.ownership_type = "OWN" AND c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC'), 
                new Zend_Db_Expr('c.ownership_type = "OWN" DESC'), 
                new Zend_Db_Expr('(c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC')
                ));
            
            
            
		}
        

		if ($nextPrev) {
			$return = array();
			if ($nextPrev > 1) {
				$select->limit(1, $nextPrev - 2);
				$prev = $this->db->fetchRow($select);
				$return['prev'] = (($prev and is_array($prev) and array_key_exists('car_id', $prev)) ? $prev : false);
			} else {
				$return['prev'] = false;
			}
			$select->limit(1, $nextPrev);
			$next = $this->db->fetchRow($select);
			$return['next'] = (($next and is_array($next) and array_key_exists('car_id', $next)) ? $next : false);
			return $return;
		}

        if($count) {
            $select->reset('columns');
            $select->reset('order');
            //$select->reset('group');
            $select->columns(array('c.car_id'));
        }
        

        //echo $select. '<br />';
		if ($selectOnly) {
			return $select;
		}
        if($count) {

            return $this->db->fetchOne($this->db->select()->from(array('a' => $select), array('count' => new Zend_Db_Expr('COUNT("*")'))));


        }
		return $this->db->fetchAll($select);
	}

	private function searchPrepareFromParameters(&$parameters, &$select) {

		foreach($parameters as $key => $value) {

			if ($value and ($key == 'price_min' or $key == 'price_max' or is_array($value))) {
				switch($key) {
					case 'ids' :
						if (count($value) > 0) {
							$select->where('c.car_id IN(?)', $value);
						}
						else {
							$select->where('0=1');
						}
						break;
					case 'makes' :
						$i = false;
						$whereString = '';
						foreach ($value as $make) {
							$whereString .= ($i ? ' OR ' : '') . $this->db->quoteInto('mk.name LIKE ?', $make);
							$i = true;
						}
						$select->where($whereString);
						break;
					case 'models' :
						$i = false;
						$whereString = '';
						foreach ($value as $model) {
							$whereString .= ($i ? ' OR ' : '') . $this->db->quoteInto('md.id=?', $model);
							$i = true;
						}
						$select->where($whereString);
						break;
					case 'price_min' :
						if (array_key_exists('search_by_price_gross', $parameters) && $parameters['search_by_price_gross'] == "1") {
							$select->where('IFNULL(LEAST(c.price_gross, c.promotion_price_gross), price_gross) >= ?', $value);
						}
						else {
							$select->where('IFNULL(LEAST(c.price, c.promotion_price), price) >= ?', $value);
						}
						break;
					case 'price_max' :
						if (array_key_exists('search_by_price_gross', $parameters) && $parameters['search_by_price_gross'] == "1") {
							$select->where('IFNULL(LEAST(c.price_gross, c.promotion_price_gross), price_gross) <= ?', $value);
						}
						else {
							$select->where('IFNULL(LEAST(c.price, c.promotion_price), price) <= ?', $value);
						}
						break;
					case 'build' :
						$i = false;
						$whereString = '';
						foreach($value as $buildYear) {
							$currentYear = date("Y");
							switch($buildYear) {
								case '1year':
									$whereString .= ($i ? ' OR ' : '') . 'c.build_year>=' . ($currentYear - 1);
									break;
								case '13years':
									$whereString .= ($i ? ' OR ' : '') . '(c.build_year<=' . ($currentYear - 1) . ' AND c.build_year>=' . ($currentYear - 3) . ')';
									break;
								case '35years':
									$whereString .= ($i ? ' OR ' : '') . '(c.build_year<=' . ($currentYear - 3) . ' AND c.build_year>=' . ($currentYear - 5) . ')';
									break;
								case '5moreyears':
									$whereString .= ($i ? ' OR ' : '') . 'c.build_year<=' . ($currentYear - 5);
									break;
								case 'classic':
									$whereString .= ($i ? ' OR ' : '') . 'c.build_year<=' . ($currentYear - 25);
									break;
							}
							if (!$i) {
								$i = true;
							}
						}
						$select->where($whereString);
						break;
					case 'odometer' :
						$whereString = '';
						$i = false;
						foreach($value as $distance) {
							switch($distance) {
								case '<20km':
									$whereString .= ($i ? ' OR ' : '') . 'c.odometer<=20000';
									break;
								case '<50km':
									$whereString .= ($i ? ' OR ' : '') . 'c.odometer<=50000';
									break;
								case '<100km':
									$whereString .= ($i ? ' OR ' : '') . 'c.odometer<=100000';
									break;
								case '<150km':
									$whereString .= ($i ? ' OR ' : '') . 'c.odometer<=150000';
									break;
								case '>=150km':
									$whereString .= ($i ? ' OR ' : '') . 'c.odometer>=150000';
									break;
							}
							if (!$i) {
								$i = true;
							}
						}
						$select->where($whereString);
						break;
					case 'power' :
						$i = false;
						$whereString = '';
						foreach($value as $power) {
							switch($power) {
								case ">300hp":
									$whereString .= ($i ? ' OR ' : '') . 'c.power>=300';
									break;
								case ">200hp":
									$whereString .= ($i ? ' OR ' : '') . 'c.power>=200';
									break;
							}
							if (!$i) {
								$i = true;
							}
						}
						$select->where($whereString);
						break;
					case 'gearboxes' :
						$select->join(array('gt' => $this->tables['gearbox_types']),
									'gt.id = c.gearbox_type_id',
									array());	
						if (count($value) > 0) {
							$select->where('gt.key IN(?)', $value);
						}
						break;
					case 'fuels' :
						$select->join(array('f' => $this->tables['fuel_types']),
							'f.id = c.fuel_type_id',
							array());
						if (count($value) > 0) {
							$select->where('f.key IN (?)', $value);
						}
						break;
					case 'features' :
						$index = 0;
                       
                        $featuresCount = count($value);
                        // max 2 el lakierowane to tez bezwypadkowy, po count aby bylo to lub to a nie and
                        if(in_array('no_accident', $value)) 
                                $value[] = 'max_2_elements_painted';
                        
                        $select->join(array('cf' => $this->tables['cars_features']),
										'cf.car_id = c.car_id',
										array())
                                ->join(array('vf' => $this->tables['vehicle_features']),
										'cf.feature_id = vf.id',
										array());;
						foreach($value as $feature) {
                            if($index == 0)
                            {
                                $where = 'vf.key = "'.$feature.'"';
                            }
                            else
                               $where .= ' OR vf.key = "'.$feature.'"';
                                
							$index++;
						}
                        $select->where('('.$where.')');   
                        $select->having('COUNT(DISTINCT vf.key) >= '.$featuresCount);
                        $select->group('c.car_id');
						break;
					case 'extras' :
						$index = 0;
                        $extrasCount = count($value);
                        
                        $select->join(array('ce' => $this->tables['cars_extras']),
										'ce.car_id = c.car_id',
										array())
                                ->join(array('ve' => $this->tables['vehicle_extras']),
										'ce.extra_id = ve.id',
										array());;
						foreach($value as $extra) {
                            
                            
                            if($index == 0)
                            {
                                $where = 've.key = "'.$extra.'"';
                            }
                            else
                               $where .= ' OR ve.key = "'.$extra.'"';
                            
                           
							$index++;
						}
                        $select->where('('.$where.')');   
                        $select->having('COUNT(DISTINCT ve.key) = '.$extrasCount);
                        $select->group('c.car_id');
						break;
					case 'types' :
						$select->where("mk.type_id IN(?)", $value);
						break;
					case 'categories' :
						$select->join(array('cat' => $this->tables['vehicle_categories']),
							'cat.id = c.vehicle_category_id',
							array());
						if (count($value) > 0) {
							$select->where('cat.key IN (?)', $value);
						}
						break;
					case 'origin' :
						if (count($value) > 0) {
							$select->where('origin_country IN (?)', $value);
						}
						break;
					case 'production' :
						if (count($value) > 0) {
							$select->where('production_country_id IN (?)', $value);
						}
						break;
					case 'origin_not' :
						if (count($value) > 0) {
							$select->where('origin_country NOT IN (?)', $value);
						}
						break;
					case 'production_not':
						if (count($value) > 0) {
							$select->where('production_country_id NOT IN (?)', $value);
						}
						break;
					case 'colour_key':
						if (count($value) > 0) {
							$select->where('colour_key IN (?)', $value);
						}
						break;
					case 'colour_key_not':
						if (count($value) > 0) {
							$select->where('colour_key NOT IN (?)', $value);
						}
						break;
					case 'door_count':
						//incoming: array("2_3", "5", "4_5") ; implode: 2_3_5_4_5 ; explode: array(2,3,4,5)
						$values = $value;
						if (count($value) > 0) {
							$values = implode("_", $value);
						}
						$values = explode("_", $values);
						if (count($values) > 0) {
							$select->where('door_count IN (?)', $values);
						}
						break;
                   
				}
			}
			elseif (is_array($value)) {
				switch($key) {
					case 'ids' :
						if (count($value) > 0) {
							$select->where('c.car_id IN(?)', $value);
						}
						else {
							$select->where('0=1');
						}
						break;
				}
			}
			elseif (!is_array($value) && !empty($value)) {
				switch($key) {
					case 'type' :
						$select->where("ct.slug IN(?)", $value);
						break;

                    case 'origin_country' :
                        $select->where("c.origin_country = ?", $value);
                        break;
                    case 'category' :
                        $select->join(array('cat' => $this->tables['vehicle_categories']),
                            'cat.id = c.vehicle_category_id',
                            array());

                        $select->where('cat.key = ?', $value);

                        break;

					case 'build_from' :
						$select->where('c.build_year>=?', $value);
						break;
					case 'build_to' :
						$select->where('c.build_year<=?', $value);
						break;
                    case 'odometer_from' :
                        $select->where('c.odometer>=?', $value);
                        break;
                    case 'odometer_to' :
                        $select->where('c.odometer<=?', $value);
                        break;
                    case 'cubic_capacity_from' :
                        if($value < 10) // jak ktos poda w litrach
                            $value = $value * 1000;
						$select->where('c.cubic_capacity>=?', $value);
						break;
					case 'cubic_capacity_to' :
                        if($value < 10) // jak ktos poda w litrach
                            $value = $value * 1000;

						$select->where('c.cubic_capacity<=?', $value);
						break;
					case 'query' :
						$strSearch = array(" ", "-", "/");
						$strReplace = array("", "", "");
						$positionForSearch = str_replace($strSearch, $strReplace, $parameters['query']);

						$query  = str_replace(' ', '%', $parameters['query']);
						
						$searchString = $this->db->quoteInto('c.position_for_search LIKE ? OR ', $positionForSearch . '%');
                        $searchString .= $this->db->quoteInto('c.position LIKE ? OR ', '%' .$positionForSearch . '%');
                        $searchString .= $this->db->quoteInto('c.registration_plate LIKE ? OR ', '%' . $parameters['query'] . '%');
                        $searchString .= $this->db->quoteInto('c.sr_car_id LIKE ? OR ', '%' . $parameters['query'] . '%');
                        $searchString .= $this->db->quoteInto('c.offer_id LIKE ? OR ', '%' . $parameters['query'] . '%');
						$searchString .= $this->db->quoteInto('md.name LIKE ? OR ', '%' . $parameters['query'] . '%');
						$searchString .= $this->db->quoteInto('mk.name LIKE ? OR ', $parameters['query']);
						$searchString .= $this->db->quoteInto('CONCAT(mk.name, " ", md.name) LIKE ? OR ', $parameters['query']);
                        $searchString .= $this->db->quoteInto('CONCAT(md.name, " ", c.build_year) LIKE ? OR ', $parameters['query']);
                        $searchString .= $this->db->quoteInto('CONCAT(mk.name, " ", md.name, " ", c.build_year) LIKE ? OR ', $parameters['query']);
						$searchString .= $this->db->quoteInto('CONCAT(mk.name, " ", md.name, " ", c.build_year, " ", c.title)  LIKE ? OR ', '%' . $query . '%');
						$searchString .= $this->db->quoteInto('c.title LIKE ? OR ', '%' . $query . '%');
                        $searchString .= $this->db->quoteInto('em.last_name LIKE ? OR ', '%' . $parameters['query'] . '%');
						$searchString .= $this->db->quoteInto('c.description LIKE ?', '%' . $query . '%');

						$select->where($searchString);


                        if ((int)$parameters['query'] > 0) {
                            $order = $select->getPart('order');
                            $select->reset('order');
                            $select->order(array(new Zend_Db_Expr('(c.position_for_search LIKE "'.$positionForSearch . '%'.'" OR c.position LIKE "'.'%' .$positionForSearch . '%") DESC')), array('c.position DESC'), $order);

                        }


						break;
					case 'order' :
					$select->reset('order');
						switch ($value) {
							case "1":
								$select->order('c.added_datetime DESC');
								break;
							case "2":
								$select->order('c.added_datetime ASC');
								break;
							case "3":
								$select->order('IFNULL(c.promotion_price, c.price) ASC');
								break;
							case "4":
								$select->order('IFNULL(c.promotion_price, c.price) DESC');
								break;
							case "5":
								$select->order('mk.name ASC');
								break;
							case "6":
								$select->order('mk.name DESC');
								break;
							case "7":
								$select->order('c.build_year ASC');
								break;
							case "8":
								$select->order('c.build_year DESC');							
								break;
							default:
								$select->order(array(new Zend_Db_Expr('(c.ownership_type = "OWN" AND c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC'), new Zend_Db_Expr('c.ownership_type = "OWN" DESC'), new Zend_Db_Expr('(c.status = "ON_SITE" AND ((c.position != "" AND c.position IS NOT NULL) OR c.position_id IS NOT NULL)) DESC'), 'c.added_datetime DESC'));
								break;
						}
						break;
					case 'for_exchange' :
						$select->where('c.ownership_type = ?', "OWN");
						break;
					case 'rental' :
						$select->where('c.rental = 1');
						break;
					case 'owner_email' :
						$select->where('c.owner_email = ?', $value);
						break;
					case 'location' :
                            $select->where('locgr.id = ' . (int)$value);
						break;
					case 'make' :
						$select->where('mk.slug=?', $value);
						break;
					case 'model' :
						$select->where('md.slug=?', $value);
						break;
					case 'on_site' :
						$select->where('c.status=?', "ON_SITE");
						break;
					case 'seat_count_min' :
						$select->where('c.seat_count>=' . (int)$value);
						break;
					case 'seat_count_max' :
						$select->where('c.seat_count<=' . (int)$value);
						break;
					case 'seat_count' :
						$values = explode("_", $value);
						$select->where('c.seat_count IN(?)', $values);
						break;
					case 'consumption_less_than' :
						$select->where('consumption_city IS NOT NULL AND consumption_city < ' . (double)$value);
						break;
					case 'displacement_min' :
						$select->where('c.cubic_capacity>=' . ((double)$value) * 1000);
						break;
					case 'displacement_max' :
						$select->where('c.cubic_capacity<=' . ((double)$value) * 1000);
						break;
					case 'promotions' :
						$select->where('promotion_price IS NOT NULL and promotion_price<price');
						break;
					case 'credit_collateral' :
						$select->where('c.credit_collateral = 1');
						break;
					case 'leasing_transfer' :
						$select->where('c.leasing = ?', 'y');
						break;
					case 'leasing' :
						/*$select->join(array('cf_leasing' => $this->tables['cars_features']),
								'cf_leasing.car_id = c.car_id',
								array())
							->join(array('vf_leasing' => $this->tables['vehicle_features']),
								'cf_leasing.feature_id = vf_leasing.id',
								array())
						;*/
						//$select->where('(vf_leasing.key IS NULL AND price_type_key = ' . $this->db->quote("netto") . ') OR vf_leasing.key = ?', "vat_invoice");
						
						$select->where('price_type_key = ' . $this->db->quote("netto"));
						
						
						$currentYear = date("Y");
						$select->where('c.build_year >= ' . (string)((int)$currentYear - 5));
						break;
					case 'invoice' :
						$select->where('price_type_key = ' . $this->db->quote("netto"));
						break;
					case 'new' :
						$timeBorder = time() - 60 * 60 *24 * 7; //last 7 days
						$select->where('added_datetime >= ?', date("Y-m-d H:i:s", $timeBorder));
						break;
					case 'last_2_years' :
						$yearBorder = date("Y") - 1;
						$select->where('c.build_year >= ' . $yearBorder);
						break;
					case 'premium' :
						$select->where('mk.special_flag = ?', "PREMIUM");
						break;
					case 'exclusive' :
						$select->where('mk.special_flag = ?', "EXCLUSIVE");
						break;
					case '4x4_pickup' :
						$select->join(array('ce_4x4' => $this->tables['cars_extras']),
								'ce_4x4.car_id = c.car_id',
								array())
							->join(array('ve_4x4' => $this->tables['vehicle_extras']),
								'ce_4x4.extra_id = ve_4x4.id',
								array())
							->join(array('cat_pickup' => $this->tables['vehicle_categories']),
								'cat_pickup.id = c.vehicle_category_id',
								array())
							->group('c.car_id')
							->where('NOT (cat_pickup.key NOT IN (' . $this->db->quote(array("pickup", "off-road", "suv")) . ') AND ve_4x4.key NOT IN(' . $this->db->quote(array("awd")) . '))')
						;
						break;
					case 'after_leasing_vindication' :
						$select->where('c.after_leasing = 1 OR c.after_vindication = 1');
						break;
					case 'price_type_key':
						$select->where('c.price_type_key = ?', $value);
						break;
                    
                    case 'no_new_car':

                        $select->where('c.location_id != ?', self::NEW_CAR_LOCATION);
                        break;
                    case '100_vat':

                        $select->where('c.100_vat = 1');
                        break;
                    case 'auction':

                        $select->where('c.auction_price IS NOT NULL');
                        break;
				}
			}
			elseif (!is_array($value)) {
				switch ($key) {
					case 'premium' :
						if ($value === "0") {
							$select->where('mk.special_flag IS NULL OR mk.special_flag != ?', "PREMIUM");
						}
						break;
					case 'exclusive' :
						if ($value === "0") {
							$select->where('mk.special_flag IS NULL OR mk.special_flag NOT IN(?)', "EXCLUSIVE");
						}
						break;
				}
			}
		}
	}
	/* koniec szukania */
	
	public function searchPrepareParameters($parameters, $defaults = array()) {
		$final = array();
		
		//pre-process parameters to group from mix & chaos to actual types ;)
		$pre = array(
			'features' => array(),
			'extras' => array(),
			'power' => array(),
			'fuels' => array(),
			'production' => array(),
			'origin' => array(),
			'production_not' => array(),
			'origin_not' => array(),
			'door_count' => array(),
			'colour_key' => array(),
			'colour_key_not' => array()
		);

        if(!empty($defaults))
            $pre = $defaults;
		
		foreach ($parameters as $key => $value) {

			if (!in_array($key, array("important_features", "engine"))) {
				$final[$key] = $value;
			}
			else {
				if (count($value) < 1) {
					continue;
				}
				if(!is_array($value)) {
                    $value = array($value);
                }
				foreach ($value as $composite) {
					$xpld = explode(" ", $composite);
					if (count($xpld) != 2) continue;
					$name = $xpld[0];
					$val = $xpld[1];
					
					if (array_key_exists($name, $pre) && is_array($pre[$name])) {
						$pre[$name][] = $val;
                        $pre[$name] = array_unique($pre[$name]);

					}
					else {
						$pre[$name] = $val;
					}


				}
			}
		}

		$final = array_merge($final, $pre);
		
		if ((isset($final['price_min']) && $final['price_min']) || (isset($final['price_max']) && $final['price_max'])) {
			$view = Zend_Layout::getMvcInstance()->getView();
			if ($view->language_row) {
				if (isset($final['price_min']) && $final['price_min']) {
					$final['price_min'] = round($final['price_min'] / $view->language_row['multiplier']);
				}
				if (isset($final['price_max']) && $final['price_max']) {
					$final['price_max'] = round($final['price_max'] / $view->language_row['multiplier']);
				}
			}
		}
		
		return $final;
	}

	public function synchronizePhotos($carId, $photos, $isArchived) {
		$photosTable = $this->tables['cars_photos'];
		if ($isArchived) {
			$photosTable = $this->tables['archive_cars_photos'];
		}
		
		//check which photos already present
		$select = $this->db->select()
			->from($photosTable, array('sr_photo_id'))
			->where('car_id = ' . (int)$carId)
			->order('ord ASC');
		$existingSrIds = $this->db->fetchCol($select);
		
		//check which photos incoming; add new where necessary
		$incomingSrIds = array();
		foreach ($photos as $item) {

            $forceSync = $item['force_sync'];

            unset($item['force_sync']);

			//store sr ids for later use
			$incomingSrIds[] = $item['sr_photo_id'];
			
			//add photo if necessary
			if (count($existingSrIds) == 0 || !in_array($item['sr_photo_id'], $existingSrIds)) {
				$this->db->insert(
					$photosTable,
					$item
				);
			}
			else if (in_array($item['sr_photo_id'], $existingSrIds)) {

                $updateData = array(
                    'ord' => $item['ord'],
                    'description' => $item['description']
                );

                    if($forceSync)
                        $updateData['status'] = 'NO_FILE';

				$this->db->update(
					$photosTable,
                    $updateData,
					"car_id = " . (int)$carId . " AND sr_photo_id = " . (int)$item['sr_photo_id']
				);
			}
		}
		
		//photos that are not included in incoming data need to be marked for deletion
		$where = "";
		if (count($incomingSrIds) > 0) {
			$where = " AND sr_photo_id NOT IN (" . implode(",", $incomingSrIds) . ")";
		}
		
		$this->db->update(
			$photosTable,
			array(
				'status' => 'MARKED_FOR_DELETION'
			),
			"car_id = " . (int)$carId . $where
		);
		
	}
    
    public function synchronizeVipautoPhotos($carId, $photos, $isArchived) {
		$photosTable = $this->tables['cars_vipauto_photos'];
		if ($isArchived) {
			$photosTable = $this->tables['archive_cars_vipauto_photos'];
		}
		
		//check which photos already present
		$select = $this->db->select()
			->from($photosTable, array('sr_photo_id'))
			->where('car_id = ' . (int)$carId)
			->order('ord ASC');
		$existingSrIds = $this->db->fetchCol($select);
		
		//check which photos incoming; add new where necessary
		$incomingSrIds = array();
		foreach ($photos as $item) {
			//store sr ids for later use
			$incomingSrIds[] = $item['sr_photo_id'];
			
			//add photo if necessary
			if (count($existingSrIds) == 0 || !in_array($item['sr_photo_id'], $existingSrIds)) {
				$this->db->insert(
					$photosTable,
					$item
				);
			}
			else if (in_array($item['sr_photo_id'], $existingSrIds)) {
				$this->db->update(
					$photosTable,
					array('ord' => $item['ord']),
					"car_id = " . (int)$carId . " AND sr_photo_id = " . (int)$item['sr_photo_id']
				);
			}
		}
		
		//photos that are not included in incoming data need to be marked for deletion
		$where = "";
		if (count($incomingSrIds) > 0) {
			$where = " AND sr_photo_id NOT IN (" . implode(",", $incomingSrIds) . ")";
		}
		
		$this->db->update(
			$photosTable,
			array(
				'status' => 'MARKED_FOR_DELETION'
			),
			"car_id = " . (int)$carId . $where
		);
		
	}
	
	public function updateFuelConsumption($data) {
		foreach ($data as $gearboxTypeId => $values) {
			if (
				array_key_exists('ids', $values)
				&& array_key_exists('consumption_city', $values)
				&& array_key_exists('consumption_motorway', $values)
				&& count($values['ids']) > 0
			) 
			{
				
				$this->db->update(
					$this->tables['cars'],
					array(
						'consumption_city' => $values['consumption_city'],
						'consumption_motorway' => $values['consumption_motorway']
					),
					"sr_car_id IN(" . $this->db->quote($values['ids']) . ")"
				);
			}
			else {
				continue;
			}
		}
	}
    
    public function isNewCar($id) {
		$id = (int)$id;
		$select = $this->db->select()
			->from(array('c' => $this->tables['cars']), array('car_id'))
			->where('car_id = ' . $id)
            ->where('location_id = '.self::NEW_CAR_LOCATION);
        
        $fetch = $this->db->fetchRow($select);
        
        if($fetch)
            return true;
        
        return false;
    }



    public function getCountries($assoc=true) {
        $select = $this->db->select()
            ->from($this->tables['countries'], array('id', 'name'));
        return $this->db->fetchPairs($select);
    }



		
}