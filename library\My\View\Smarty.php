<?php

class My_View_Smarty extends Zend_View_Abstract
{
	/**
	* Obiekt Smarty
	*
	* @var My_Smarty
	*/
	protected $_smarty = null;
 
	/**
	* Konstruktor
	*
	* @param array $config dane konfiguracyjne
	*/
	public function __construct($config = array())
	{
		$this->_smarty = new My_Smarty();
		$this->_smarty->setZendView($this);
 
		foreach ($config['params'] as $key => $value) {
			$this->_smarty->$key = $value;
		}
 
		$this->setScriptPath($config['scriptPath']);
 
		// Ścieżki do helperów
		foreach ($config['helperDirs'] as $path => $prefix) {
			$this->addHelperPath($path, $prefix);
		}
	}
 
	/**
	* Zwraca obiekt Smarty
	*
	* @return My_Smarty
	*/
	public function getEngine()
	{
		return $this->_smarty;
	}
 
	/**
	* Ustawia ścieżkę do szablonów
	*
	* @param string $path ścieżka
	*/
	public function setScriptPath($path)
	{
		if (null === $path) {
			return;
		}
 
		$this->_smarty->template_dir = $path;
	}
 
	/**
	* Pobiera ścieżki do szablonów
	*
	* @return array tablica ścieżek
	*/
	public function getScriptPaths() {
		return array($this->getScriptPath(null));
	}
 
	/**
	* Zwraca ściezkę do plików tpl
	*
	* @return string ścieżka
	*/
	public function getScriptPath($name) {
		return $this->_smarty->template_dir;
	}
 
	/**
	* Ustawienie parametru
	*
	* @param string $key klucz
	* @param mixed $value wartość
	*/
	public function setParam($key, $value)
	{
		$this->_smarty->$key = $value;
	}
 
	/**
	* Ustawia zmienną w widoku
	*
	* @param string $key nazwa zmiennej
	* @param mixed $value wartość zmiennej
	*/
	public function __set($key, $value)
	{
		$this->_smarty->assign($key, $value);
	}
 
	/**
	* Pobiera zmienną z widoku
	*
	* @param string $key nazwa zmiennej
	* @return mixed wartość zmiennej
	*/
	public function __get($key)
	{
		return $this->_smarty->get_template_vars($key);
	}
 
	/**
	* Sprawdzenie czy zmienna jest ustawiona w widoku
	*
	* @param string $key nazwa zmiennej
	* @return boolean czy zmienna jest ustawiona
	*/
	public function __isset($key)
	{
		return null === $this->_smarty->get_template_vars($key);
	}
 
	/**
	* Usunięcie zmiennej z widoku
	*
	* @param string $key nazwa zmiennej
	*/
	public function __unset($key)
	{
		$this->_smarty->clear_assign($key);
	}
 
	/**
	* Przypisywanie zmiennych do widoku
	*
	* @param string|array $var nazwa zmiennej lucz tablica par (klucz => wartość)
	* @param mixed $value wartość zmiennej
	*/
	public function assign($var, $value = null)
	{
		if (is_array($var)) {
			$this->_smarty->assign($var);
			return;
		}
 
		$this->_smarty->assign($var, $value);
	}
 
	/**
	* Usunięcie wszystkich przypisanych do widoku zmiennych
	*/
	public function clearVars()
	{
		$this->_smarty->clear_all_assign();
	}
 
	/**
	* Renderowanie szablonu
	*
	* @param string $name nazwa szablonu
	* @return string wyrenderowany szablon
	*/
	public function render($name)
	{
		if (file_exists($this->getScriptPath($name) . $name)) {
			return $this->_smarty->fetch($name);
		}
		return null;
	}
 
	/**
	* Rozszerzenie abstrakcyjnej metody klasy nadrzędnej
	*/
	protected function _run()
	{
	}
}