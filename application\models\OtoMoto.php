<?php

class Model_OtoMoto extends Model_Base {
	
	const CRON_JOB_TIMEOUT_SEC = 3000; //50 minutes
	const CRON_JOB_TIMEOUT_MARGIN = 300; //5 minutes
	
	protected $_client; //SOAP client
	protected $_debug=false;
	protected $_mode = 'beta'; // 'beta' || 'production'
	protected $_key; //shorthand for $_clientOptions[mode][webapi-key]
	protected $_config;
	protected $_countryCode = 1;
	protected $_clientOptions = array(
		'beta' => array(
			'endpoint'		=>	"http://beta.otomoto.pl/webapi/server.php",
			'wsdl'			=>	"http://beta.otomoto.pl/webapi/server.php?wsdl",
			'options'		=>	array(
				'trace'			=>	true,
				'soap_version'	=>	SOAP_1_1
			)
		),
		'production' => array(
			'endpoint'		=>	"http://otomoto.pl/webapi/server.php",
			'wsdl'			=>	"http://otomoto.pl/webapi/server.php?wsdl",
			'options'		=>	array(
				'soap_version' => SOAP_1_1
			)
		)
	);
	
	protected $_sid;
	protected $_dealer;
	
	protected $_allegroCategories;
	protected $_colours;
	protected $_countries;
	protected $_fuelTypes = array();
	protected $_gearboxTypes = array();
	protected $_makes = array();
	protected $_versions;
	protected $_vehicleCategory = array();
	protected $_vehicleExtras = array();
	protected $_vehicleFeatures = array();
	
	public static $betaAccount = array(
		'oa_id' => 1,
		'oa_login' => '<EMAIL>',
		'oa_password' => 'autoauto',
		'oa_webapi_key' => '56A7A4B88A8C682E98EBB3E8CBB11E9C',
		'oa_is_beta' => '1'
	);
	
	public static $mainAccount = array(
		'oa_id' => 1,
		'oa_login' => '<EMAIL>',
		'oa_password' => 'autoauto',
		'oa_webapi_key' => '5395C65D92460053E582B7B5373C8729',
		'oa_is_beta' => '2'
	);
	
	public function __construct($config, $debug=false) {
		try {
			parent::__construct();
			
			$this->_config = $config;
			$this->_debug = (bool)$debug;
			
			if ($config['oa_is_beta'] == '1') {
				$this->_mode = 'beta';
			}
			else if ($config['oa_is_beta'] == '2') {
				$this->_mode = 'production';
				//throw new Exception("Production mode not available yet; " . __METHOD__ . ", line " . (__LINE__ - 1));
			}
			else {
				throw new Exception("Invalid mode specified: '" . $config['oa_is_beta'] . "' in " . __METHOD__ . ", line " . (__LINE__ - 1));
			}
			
			$this->_key = $config['oa_webapi_key'];
			
			//clean up old tmp files
			foreach (glob(sys_get_temp_dir()."/wsdl*") as $filename) {
		    	@unlink($filename);
			}
			
			//PHP bug workaround ("Extra content at the end of the document")
			$tmpWsdlPath = tempnam(sys_get_temp_dir(), 'wsdl');
			@copy($this->_clientOptions[$this->_mode]['wsdl'], $tmpWsdlPath);
			//EndOfWorkaround
			
			$this->_client = new SoapClient($tmpWsdlPath, $this->_clientOptions[$this->_mode]['options']);
			@unlink($tmpWsdlPath);
			
			/*
			//client changed ^
			$this->_client = new Zend_Soap_Client(
				$tmpWsdlPath,
				$this->_clientOptions[$this->_mode]['options']
			);
			*/
		}
		catch (Exception $e) {
			//throw new Exception("Błąd przy inicjalizacji modułu otoMoto (wariant " . $this->_mode . "):<br />" . $e->getMessage() . "<br /><br />" . $e->getTraceAsString());
		}
		
	}
	
	public function clientCall($method, $args) {
		try {
			$result = call_user_func_array(array($this->_client, $method), $args);
			if ($this->_debug) {
				Zend_Debug::dump($result,"<h2>clientCall(\"$method\")</h2>");
				Zend_Registry::get('ZFDlogger')->debug($result);
			}
			return $result;
		}
		catch (Exception $e) {
			if ($this->_debug) {
				Zend_Debug::dump($e->getMessage(),'<h2>Exception!</h2>');
				Zend_Debug::dump($this->_client->__getLastRequest(),'<h2>Execption; lastRequest:</h2>');
				Zend_Debug::dump($this->_client->__getLastResponse(),'<h2>Execption; lastResponse:</h2>');
			}
			else {
				throw $e;
			}
		}
	}
	
	public static function getAccountById($accountId) {
		$base = new Model_Base();
		$select = $base->getDb()->select()
			->from($base->tables['otomoto_accounts'])
			->where('oa_id = ' . (int)$accountId);
		return $base->getDb()->fetchRow($select);
	}
	
	public function getAllegroCategories() {
		if (isset($this->_allegroCategories)) {
			return $this->_allegroCategories;
		}
		else {
			if (empty($this->_sid)) {
				//throw new Exception("Session id not set in " . __METHOD__ . ", line " . (__LINE__ - 1));
			}
			try {
				$params = array(
					'webapi-key'	=>	$this->_key
				);
				$this->_allegroCategories = $this->clientCall("getAllegroCategories", $params);
				return $this->_allegroCategories;
			} catch (Exception $e) {
				$this->_allegroCategories = null;
				throw $e;
			}
		}
	}
	
	public function getAllegroCategoriesAssoc($subtreeRootIds=null) {
		//subtreeRootId - only get given branch of tree
		$data = $this->getAllegroCategories();
		$ret = $this->getAllegroCategoriesAssocTree(array(), $data, $level=0, $subtreeRootIds);
		return $ret;
	}
	
	public function getAllegroCategoriesAssocTree($array, $data, $level, $subtreeRootIds=null) {
		foreach ($data as $category) {
			$separator = "--";
			$separatorText = "";
			for ($i=0; $i < $level; $i++) { 
				$separatorText .= $separator;
			}
			
			$boolInclude = ($subtreeRootIds === null || in_array($category->id, $subtreeRootIds)) ? true : false;
			if ($boolInclude) $array["" . $category->id] = $separatorText . $category->name;
			if (count($category->children) > 0) {
				$array = $this->getAllegroCategoriesAssocTree($array, $category->children, ($boolInclude) ? $level+1 : $level, $rootId = ($boolInclude) ? null : $subtreeRootIds);
			}
		}
		return $array;
	}
	
	public function getColours() {
		$params = array(
			'webapi-key'	=>	$this->_key
		);
		return $this->clientCall("getColours", $params);
	}
	
	public function getColoursAssoc() {
		$data = $this->getColours();
		$ret = array();
		foreach ($data as $colour) {
			$ret[$colour->key] = $colour->name;
		}
		asort($ret);
		return $ret;
	}
	
	public function getCountries() {
		if (isset($this->_countries)) {
			return $this->_countries;
		}
		else {
			try {
				$params = array(
					'webapi-key'	=>	$this->_key
				);
				$this->_countries = $this->clientCall("getCountries", $params);
				return $this->_countries;
			} catch (Exception $e) {
				$this->_countries = null;
				throw $e;
			}
		}
	}
	
	public function getCountriesAssoc() {
		$data = $this->getCountries();
		$ret = array();
		foreach ($data as $country) {
			$ret[$country->id] = $country->name;
		}
		setlocale(LC_ALL, 'pl_PL');
		asort($ret, SORT_LOCALE_STRING);
		return $ret;
	}
	
	public function getFuelTypes($type='CAR') {
		if (isset($this->_fuelTypes[$type])) {
			return $this->_fuelTypes[$type];
		}
		else {
			try {
				$params = array(
					'webapi-key'	=>	$this->_key,
					'object-type'	=>	$type
				);
				$this->_fuelTypes[$type] = $this->clientCall("getFuelTypes", $params);
				return $this->_fuelTypes[$type];
			} catch (Exception $e) {
				$this->_fuelTypes[$type] = null;
				throw $e;
			}
		}
	}
	
	public function getFuelTypesAssoc($type='CAR') {
		$data = $this->getFuelTypes();
		$ret = array();
		foreach ($data as $fType) {
			$ret[$fType->key] = $fType->name;
		}
		asort($ret);
		return $ret;
	}
	
	public function getGearboxTypes($type='CAR') {
		if (isset($this->_gearboxTypes[$type])) {
			return $this->_gearboxTypes[$type];
		}
		else {
			try {
				$params = array(
					'webapi-key'	=>	$this->_key,
					'object-type'	=>	$type
				);
				$this->_gearboxTypes[$type] = $this->clientCall("getGearboxTypes", $params);
				return $this->_gearboxTypes[$type];
			} catch (Exception $e) {
				$this->_gearboxTypes[$type] = null;
				throw $e;
			}
		}
	}
	
	public function getGearboxTypesAssoc($type='CAR') {
		$data = $this->getGearboxTypes();
		$ret = array();
		foreach ($data as $fType) {
			$ret[$fType->key] = $fType->name;
		}
		asort($ret);
		return $ret;
	}
	
	public function getMakes($type='CAR') {
		if (isset($this->_makes[$type])) {
			return $this->_makes[$type];
		}
		else {
			try {
				$params = array(
					'type'			=>	$type,
					'webapi-key'	=>	$this->_key
				);
				$this->_makes[$type] = $this->clientCall("getMakes", $params);
				return $this->_makes[$type];
			} catch (Exception $e) {
				$this->_makes[$type] = null;
				throw $e;
			}
		}
	}
	
	public function getMakesAssoc($type='CAR') {
		$data = $this->getMakes($type);
		$ret = array();
		foreach ($data as $make) {
			$ret[$make->id] = $make->name;
		}
		asort($ret);
		return $ret;
	}
	
	public function getModels($type='CAR', $makeId) {
		$params = array(
			'make-id'		=>	$makeId,
			'webapi-key'	=>	$this->_key,
			'country-code'	=>	$this->_countryCode,
			'type'			=>	$type,
		);
		return $this->clientCall("getModels", $params);
	}
	
	public function getModelsAssoc($type='CAR', $makeId) {
		$data = $this->getModels($type, $makeId);
		$ret = array();
		foreach ($data as $model) {
			$ret[$model->id] = $model->name;
		}
		asort($ret);
		return $ret;
	}
	
	public function getTypesAssoc() {
		return array(
			'CAR'		=>	'samochód',
			//'MOTORBIKE'	=>	'motocykl',
			'TRUCK'		=>	'ciężarówka'
		);
	}
	
	public function getVehicleCategories($type='CAR') {
		if (isset($this->_vehicleCategories[$type])) {
			return $this->_vehicleCategories[$type];
		}
		else {
			try {
				$params = array(
					'type'			=>	$type,
					'webapi-key'	=>	$this->_key
				);
				$this->_vehicleCategories[$type] = $this->clientCall("getVehicleCategories", $params);
				return $this->_vehicleCategories[$type];
			} catch (Exception $e) {
				$this->_vehicleCategories[$type] = null;
				throw $e;
			}
		}
	}
	
	public function getVehicleCategoriesAssoc($type='CAR') {
		$data = $this->getVehicleCategories($type);
		$ret = array();
		foreach ($data as $cat) {
			$ret[$cat->key] = $cat->name;
		}
		asort($ret);
		return $ret;
	}
	
	public function getVehicleExtras($type='CAR') {
		if (isset($this->_vehicleExtras[$type])) {
			return $this->_vehicleExtras[$type];
		}
		else {
			try {
				$params = array(
					'type'			=>	$type,
					'webapi-key'	=>	$this->_key
				);
				$this->_vehicleExtras[$type] = $this->clientCall("getVehicleExtras", $params);
				return $this->_vehicleExtras[$type];
			} catch (Exception $e) {
				$this->_vehicleExtras[$type] = null;
				throw $e;
			}
		}
	}
	
	public function getVehicleExtrasAssoc($type='CAR') {
		$data = $this->getVehicleExtras($type);
		$ret = array();
		foreach ($data as $extra) {
			$ret[$extra->key] = $extra->name;
		}
		asort($ret);
		return $ret;
	}
	
	public function getVehicleFeatures($type='CAR') {
		if (isset($this->_vehicleFeatures[$type])) {
			return $this->_vehicleFeatures[$type];
		}
		else {
			try {
				$params = array(
					'type'			=>	$type,
					'webapi-key'	=>	$this->_key
				);
				$this->_vehicleFeatures[$type] = $this->clientCall("getVehicleFeatures", $params);
				return $this->_vehicleFeatures[$type];
			} catch (Exception $e) {
				$this->_vehicleFeatures[$type] = null;
				throw $e;
			}
		}
	}
	
	public function getVehicleFeaturesAssoc($type='CAR') {
		$data = $this->getVehicleFeatures($type);
		$ret = array();
		foreach ($data as $feature) {
			$ret[$feature->key] = $feature->name;
		}
		asort($ret);
		return $ret;
	}
	
	public function getVersions($type='CAR', $makeId, $modelId) {
		$params = array(
			'make-id'		=>	$makeId,
			'model-id'		=>	$modelId,
			'webapi-key'	=>	$this->_key,
			'country-code'	=>	$this->_countryCode,
			'type'			=>	$type
		);
		return $this->clientCall("getVersions", $params);
	}
	
	public function getVersionsAssoc($type='CAR', $makeId, $modelId) {
		$data = $this->getVersions($type, $makeId, $modelId);
		$ret = array();
		foreach ($data as $ver) {
			$ret[$ver->id] = $ver->name;
		}
		asort($ret);
		return $ret;
	}
	
	public function login() {
		$params = array(
			"dealer-login"		=>	$this->_config['oa_login'],
			"dealer-password"	=>	$this->_config['oa_password'],
			"country-code"		=>	$this->_countryCode,
			"webapi-key"		=>	$this->_key
		);
	
		$result = $this->clientCall("doDealerLogin", $params);
		$this->_sid = $result['session-id'];
		$this->_dealer = $result['dealer'];
	}
	
	public function logout() {
		$params = array(
			'session-id'	=>	$this->_sid,
			'webapi-key'	=>	$this->_key
		);
		$result = $this->clientCall("doDealerLogout", $params);
		if ($result == "OK") {
			$this->_sid = null;
		}
	}
	
	public function test() {
		//Zend_Debug::dump($ft = $this->getFuelTypesAssoc($type='CAR'));
		
		//$colours = $this->getColoursAssoc();
		
		///$cats = $this->getVehicleCategoriesAssoc($type='CAR');
		
		//$makes = $this->getMakesAssoc($type='TRUCK');
		
		//$models = $this->getModelsAssoc($type='CAR', $makeId=442);
		
		//$versions = $this->getVersionsAssoc($type='CAR', $makeId=6, $modelId=627679);
		
		//Zend_Debug::Dump($extras = $this->getVehicleExtrasAssoc($type='CAR'));
		
		//Zend_Debug::dump($features = $this->getVehicleFeaturesAssoc($type='CAR'));
		
		//$gt = $this->getGearboxTypesAssoc($type='CAR');
		
		//$this->login();
		//Zend_Debug::dump($this->getAllegroCategories());
		//Zend_Debug::dump($this->getCountriesAssoc());
		//$this->logout();
		
		//Zend_Debug::dump($this->clientCall('doHello', array('test-hello-string' => 'raz-dwa-czy-czetery')),'<h2></h2>');
		
		//Zend_Debug::dump($this->getCountriesAssoc(),'<h2></h2>');
		
		//$this->clientCall("getCountries", array("webapi-key" => $this->_key));
		//Zend_Debug::dump($this->_client->__getLastRequest(),'<h2>lastReq:</h2>');
		
		$this->clientCall("getAllegroCategories", array("webapi-key" => $this->_key));
		//Zend_Debug::dump($this->_client->__getLastRequest(),'<h2>lastReq:</h2>');
	}
	
}