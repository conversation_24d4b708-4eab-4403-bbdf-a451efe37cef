<?php

class Form_SearchItemBySales<PERSON> extends My_Form {

	public function init() {
        $empl = new Model_Employees();
        $tr = Zend_Registry::get('Zend_Translate');
        
		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
				'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')),
				)
			)),
			new Zend_Form_Element_Hidden('title', array(
				'label'	=>	'SEARCH_NAME',
				'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 0, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Hidden('newsletter', array(
				'label'	=>	'NEWSLETTER_ON_OFF',
				'validators' => array(),
				'value' => 1
			)),
			new Zend_Form_Element_Text('first_name', array(
				'label'	=>	'FIRST_NAME',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),

			new Zend_Form_Element_Text('address', array(
				'label'	=>	'ADDRESS',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 255, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('zip_code', array(
				'label'	=>	'ZIP_CODE',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 32, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('city', array(
				'label'	=>	'CITY',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('country', array(
				'label'	=>	'COUNTRY',
				'value' => 'Polska',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 64, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('phone', array(
				'label'	=>	'PHONE',
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
             
            new Zend_Form_Element_Select('added_by_sr_id', array(
                    'label'	=>	'FAVOURITE_SALESMAN',
                    'multiOptions' => array('' => $tr->_('FAVOURITE_SALESMAN'). ' - '. strtolower($tr->_('ANY'))) + $empl->getAll(array('sr_assoc' => true)),
                    'required' => false
            )),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('save_by_salesman', array(
				'label' => 'SAVE'
			))
		));
		
		parent::init();
	}//init

}