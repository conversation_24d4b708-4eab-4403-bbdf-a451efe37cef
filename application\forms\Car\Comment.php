<?php

class Form_Car_Comment extends My_Form {

	public function init() {
		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
                'attribs' => array('class' => 'form-control'),
				'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8'))
				)
			)),
			new Zend_Form_Element_Text('name', array(
				'label'	=>	'FIRST_NAME_LAST_NAME',
                'attribs' => array('class' => 'form-control'),
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Text('phone', array(
				'label'	=>	'PHONE',
                'attribs' => array('class' => 'form-control'),
				'filters' => array(new Zend_Filter_StripTags()),
				'validators' => array(new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Textarea('message', array(
				'label'	=>	'OFFER_MESSAGE',
                'attribs' => array('class' => 'form-control'),
				'filters' => array(new Zend_Filter_StripTags()),
				'attribs' => array('rows' => 3, 'cols' => 40)
			)),
            new Zend_Form_Element_Captcha('captcha', array(
                'label' => 'CAPTCHA',
                'captcha' => new My_Captcha_ReCaptcha(array(
                    'siteKey'  => '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-',
                    'secretKey' => '6Lfx7A0UAAAAAL41t8hFSPCzuh6VQGhcpXttwrJd'
                )),
            )),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label'	=>	'TEST_DRIVE_SUBMIT'
			)),
		));
		
		if (Zend_Auth::getInstance()->hasIdentity()) {
			$this->removeElement('captcha');
		}
		
		parent::init();
	}//init

}