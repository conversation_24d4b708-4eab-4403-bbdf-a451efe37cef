<div id="breadcrumb">
    <?php if(isset($this->filterArray) && count($this->filterArray) > 0): ?>
        <div id="search_filters">
        <?php foreach($this->filterArray as $filterElement): ?>

                <div class="search_filter">

                    <span>

                        <?php if(in_array($filterElement['title'],$this->filterArrayIncludeTitle) || $filterElement['value'] == ""): ?>
                            <?= $this->translate->_($filterElement['title']); ?><? if ($filterElement['value'] != "") {echo ":";} ?>
                        <?php endif;?>
                        <?= $filterElement['value']; ?>
                        <span>
                            <?php
                            $queryStringArray = $this->queryStringArray;
                            unset($queryStringArray[$filterElement['appendName']]);
                            ?>
                    <a href="<?= $this->url() . '?' . http_build_query($queryStringArray) ?>"></a>
                </div>

        <?php endforeach; ?>
            <div class="clear"></div>
        </div>
    <?php else: ?>
        <strong><?= $this->translate->_('YOU_ARE_HERE') ?>:</strong> <a href="<?= $this->url(array('language' => $this->language), 'home', true) ?>"><?= $this->translate->_('HOME') ?></a>
        <?php if (is_array($this->breadcrumb)): ?>
            <?php foreach ($this->breadcrumb as $key => $value): ?>
            &raquo; <a href="<?= $key ?>"><?= $this->escape($value) ?></a>
            <?php endforeach ?>
        <?php endif; ?>
    <?php endif;?>
	
	<?php if ($this->car): ?>
		<div id="car_hint" class="<? if ($this->car['ocassion'] == 1 || $this->car['high_commission'] == 1) {echo 'y';} elseif ($this->car['ownership_type'] == "OWN") {echo 'r';} ?>">
			<?php if ($this->car['is_reserved_hidden'] || $this->car['is_sold_hidden']): ?>
				<span class="smaller">&bull;</span>
			<?php endif ?>
			&bull;
		</div>
	<?php endif ?>
</div>