<?php


if($this->queryStringArray['list_view'] == 'block') {
    $rowClass = 'row block ';
} else {
    $rowClass = 'list ';
}
$template = 'car-item-'.$this->queryStringArray['list_view'].'.phtml';

?>


<?php if ($this->paginator): ?>
    <div class="cars <?=$rowClass?>pt-3">

        <?php $index = 1;
        foreach ($this->paginator as $car):

            $getString = ($this->hash ? $this->hash . "&amp;perPage=" . $this->perPage : "?perPage=" . $this->perPage);
            $carLink = $this->domain . $this->carLink($car, null, $noMarkup=true) . $getString;

            if($this->car['auction_price'] > 0 && isset($this->auctionSearch) && $this->auctionSearch) {
                if($this->hash || $this->perPage ) {
                    $carLink .= "&amp;auction=1";
                    $getString .= "&amp;auction=1";
                } else {
                    $carLink .= "?auction=1";
                    $getString .= "?auction=1";
                }
            }

            ?>

            <?= $this->partial($template,
            array(
                'car' => $car,
                'translate' => $this->translate,
                'language' => $this->language,
                'language_row' => $this->language_row,
                'hash' => $this->hash,
                'page' => $this->page,
                'perPage' => $this->perPage,
                'domain' => $this->domain,
                'searchParameters' => $this->searchParameters,
                'siteVariant' => $this->siteVariant,
                'isFavourite' => in_array($car['car_id'], $this->favouritesIds),
                'auctionSearch' => $this->auctionSearch,
                'queryStringArray' => $this->queryStringArray,
                'carLink' =>  $carLink,
                'getString' =>  $getString,

            ))

            ?>
            <?php $index++; endforeach; ?>

    </div>

    <?php echo $this->paginationControl($this->paginator, 'Sliding', 'paginator-bootstrap.phtml', array('prevString' => '&laquo;', 'nextString' => '&raquo;', 'getString' => ($this->hash ? '?hash=' . $this->hash : ''), 'carListShowMax' => $this->pagingMaxResults, 'carListShowDefault' => $this->pagingDefaultResults, 'translate' => $this->translate, 'queryStringArray' => $this->queryStringArray)); ?>

    <?php

    if (count($this->paginator) == 0) : ?>
        <div class="no_offer">
            <span class="orange-color big"><?= $this->translate->_('NO_OFFER') ?></span>
        </div>
    <? endif; ?>

<?php endif ?>

