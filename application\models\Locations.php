<?php

class Model_Locations extends Model_Base {
	
    const NEW_CAR_LOCATION_GROUP = 5;
	public function getLocationGroups($assoc=true) {
		$cols = $assoc ? array('id', 'address') : array('*');
		$select = $this->db->select()
			->from($this->tables['location_groups'], $cols)
            ->where('is_visible = 1')
			->order('id ASC');
       
		return $assoc ? $this->db->fetchPairs($select) : $this->db->fetchAll($select);
	}
	
	public function getLocationGroupsWithLocations($onlyIds=null) {
		$select = $this->db->select()
			->from($this->tables['location_groups'])
            ->where('is_visible = 1')
			->order('position DESC');
		if (is_array($onlyIds)) {
			$select->where('id IN(?)', $onlyIds);
		}

		$groups = $this->db->fetchAll($select);
		
		foreach ($groups as $key => $value) {
			$locations = $this->getLocations($groups[$key]['id']);
			$groups[$key]['locations'] = $locations;
		}

		return $groups;
	}
	
	public function getLocation($id, $withLocationGroup=false) {
		$id = (int)$id;
		$select = $this->db->select()
			->from($this->tables['locations'])
			->where('location_id = ' . $id)
			->where('is_active = 1');
		$location = $this->db->fetchRow($select);

		if(!$location)
			return false;
		
		if ($withLocationGroup) {
			$select = $this->db->select()
				->from($this->tables['location_groups'])
				->where('id = ' . $location['location_group_id']);
			$location['location_group'] = $this->db->fetchRow($select);
		}
		
		return $location;
	}
	
	public function getLocations($groupId=null, $assoc=false) {
		$cols = $assoc ? array('id', 'name') : array('*');
		$select = $this->db->select()
			->from($this->tables['locations'])
            ->where('is_visible = 1')
			->where('is_active = 1')
			->order('name ASC');
		if ((int)$groupId > 0) {
			$select->where('location_group_id = ' . (int)$groupId);
		}
		return $assoc ? $this->db->fetchPairs($select) : $this->db->fetchAll($select);
	}

    public function addLocation($data) {
        $this->db->insert(
            $this->tables['locations'],
            $data
        );
    }


    public function editLocation($id, $data) {
        $this->db->update(
            $this->tables['locations'],
            $data,
            "location_id = " . (int)$id
        );
    }
	
}