<html>
<head>
<title>Docs For Class TCPDFBarcode</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">com-tecnick-tcpdf</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_com-tecnick-tcpdf.html" class="menu">class tree: com-tecnick-tcpdf</a> ]
		  [ <a href="../elementindex_com-tecnick-tcpdf.html" class="menu">index: com-tecnick-tcpdf</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_com-tecnick-tcpdf.html">com-tecnick-tcpdf</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../com-tecnick-tcpdf/_2dbarcodes.php.html">		2dbarcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_barcodes.php.html">		barcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_htmlcolors.php.html">		htmlcolors.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_qrcode.php.html">		qrcode.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_tcpdf.php.html">		tcpdf.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_config---tcpdf_config.php.html">		tcpdf_config.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_unicode_data.php.html">		unicode_data.php
		</a><br>
	  </div><br />
      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../com-tecnick-tcpdf/QRcode.html">QRcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF.html">TCPDF</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF2DBarcode.html">TCPDF2DBarcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDFBarcode.html">TCPDFBarcode</a><br />
	  </div>
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Class: TCPDFBarcode</h1>
Source Location: /barcodes.php<br /><br />


<table width="100%" border="0">
<tr><td valign="top">

<h3><a href="#class_details">Class Overview</a></h3>
<pre></pre><br />
<div class="description">PHP class to creates array representations for common 1D barcodes to be used with TCPDF (http://www.tcpdf.org).<br /></div><br /><br />
<h4>Author(s):</h4>
<ul>
          <li>Nicola Asuni</li>
                              </ul>




        
                
<h4>Version:</h4>
<ul>
  <li>1.0.008</li>
</ul>

        
</td>

<td valign="top">
<h3><a href="#class_vars">Variables</a></h3>
<ul>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#var$barcode_array">$barcode_array</a></li>
  </ul>
</td>


<td valign="top">
<h3><a href="#class_methods">Methods</a></h3>
<ul>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#method__construct">__construct</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_c128">barcode_c128</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_codabar">barcode_codabar</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_code11">barcode_code11</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_code39">barcode_code39</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_code93">barcode_code93</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_eanext">barcode_eanext</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_eanupc">barcode_eanupc</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_i25">barcode_i25</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_imb">barcode_imb</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_msi">barcode_msi</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_pharmacode">barcode_pharmacode</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_pharmacode2t">barcode_pharmacode2t</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_postnet">barcode_postnet</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_rms4cc">barcode_rms4cc</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbarcode_s25">barcode_s25</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodbinseq_to_array">binseq_to_array</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodchecksum_code39">checksum_code39</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodchecksum_code93">checksum_code93</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodchecksum_s25">checksum_s25</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methoddec_to_hex">dec_to_hex</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodencode_code39_ext">encode_code39_ext</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodgetBarcodeArray">getBarcodeArray</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodhex_to_dec">hex_to_dec</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodimb_crc11fcs">imb_crc11fcs</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodimb_reverse_us">imb_reverse_us</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodimb_tables">imb_tables</a></li>
    <li><a href="../com-tecnick-tcpdf/TCPDFBarcode.html#methodsetBarcode">setBarcode</a></li>
  </ul>
</td>

</tr></table>
<hr />

<table width="100%" border="0"><tr>






</tr></table>
<hr />

<a name="class_details"></a>
<h3>Class Details</h3>
<div class="tags">
[line 62]<br />
PHP class to creates array representations for common 1D barcodes to be used with TCPDF (http://www.tcpdf.org).<br /><br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Nicola Asuni</td>
  </tr>
  <tr>
    <td><b>version:</b>&nbsp;&nbsp;</td><td>1.0.008</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.tcpdf.org">http://www.tcpdf.org</a></td>
  </tr>
  <tr>
    <td><b>name:</b>&nbsp;&nbsp;</td><td>TCPDFBarcode</td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a></td>
  </tr>
</table>
</div>
</div><br /><br />
<div class="top">[ <a href="#top">Top</a> ]</div><br />

<hr />
<a name="class_vars"></a>
<h3>Class Variables</h3>
<div class="tags">
	<a name="var$barcode_array"></a>
	<p></p>
	<h4>$barcode_array = <span class="value"></span></h4>
	<p>[line 68]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>var:</b>&nbsp;&nbsp;</td><td>representation of barcode.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
</div><br />

<hr />
<a name="class_methods"></a>
<h3>Class Methods</h3>
<div class="tags">

  <hr />
	<a name="method__construct"></a>
	<h3>constructor __construct <span class="smalllinenumber">[line 84]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>TCPDFBarcode __construct(
string
$code, string
$type)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		This is the class constructor.<br /><br /><p>Return an array representations for common 1D barcodes:<ul><li>$arrcode['code'] code to be printed on text label</li><li>$arrcode['maxh'] max bar height</li><li>$arrcode['maxw'] max bar width</li><li>$arrcode['bcode'][$k] single bar or space in $k position</li><li>$arrcode['bcode'][$k]['t'] bar type: true = bar, false = space.</li><li>$arrcode['bcode'][$k]['w'] bar width in units.</li><li>$arrcode['bcode'][$k]['h'] bar height in units.</li><li>$arrcode['bcode'][$k]['p'] bar top position (0 = top, 1 = middle)</li></ul></p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to print</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$type</b>&nbsp;&nbsp;</td>
        <td>type of barcode: <ul><li>C39 : CODE 39 - ANSI MH10.8M-1983 - USD-3 - 3 of 9.</li><li>C39+ : CODE 39 with checksum</li><li>C39E : CODE 39 EXTENDED</li><li>C39E+ : CODE 39 EXTENDED + CHECKSUM</li><li>C93 : CODE 93 - USS-93</li><li>S25 : Standard 2 of 5</li><li>S25+ : Standard 2 of 5 + CHECKSUM</li><li>I25 : Interleaved 2 of 5</li><li>I25+ : Interleaved 2 of 5 + CHECKSUM</li><li>C128A : CODE 128 A</li><li>C128B : CODE 128 B</li><li>C128C : CODE 128 C</li><li>EAN2 : 2-Digits UPC-Based Extention</li><li>EAN5 : 5-Digits UPC-Based Extention</li><li>EAN8 : EAN 8</li><li>EAN13 : EAN 13</li><li>UPCA : UPC-A</li><li>UPCE : UPC-E</li><li>MSI : MSI (Variation of Plessey code)</li><li>MSI+ : MSI + CHECKSUM (modulo 11)</li><li>POSTNET : POSTNET</li><li>PLANET : PLANET</li><li>RMS4CC : RMS4CC (Royal Mail 4-state Customer Code) - CBC (Customer Bar Code)</li><li>KIX : KIX (Klant index - Customer index)</li><li>IMB: Intelligent Mail Barcode - Onecode - USPS-B-3200</li><li>CODABAR : CODABAR</li><li>CODE11 : CODE 11</li><li>PHARMA : PHARMACODE</li><li>PHARMA2T : PHARMACODE TWO-TRACKS</li></ul></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_c128"></a>
	<h3>method barcode_c128 <span class="smalllinenumber">[line 799]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_c128(
string
$code, [string
$type = 'B'])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		C128 barcodes.<br /><br /><p>Very capable code, excellent density, high reliability; in very wide use world-wide</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$type</b>&nbsp;&nbsp;</td>
        <td>barcode type: A, B or C</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_codabar"></a>
	<h3>method barcode_codabar <span class="smalllinenumber">[line 1487]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_codabar(
string
$code)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		CODABAR barcodes.<br /><br /><p>Older code often used in library systems, sometimes in blood banks</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_code11"></a>
	<h3>method barcode_code11 <span class="smalllinenumber">[line 1543]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_code11(
string
$code)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		CODE11 barcodes.<br /><br /><p>Used primarily for labeling telecommunications equipment</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_code39"></a>
	<h3>method barcode_code39 <span class="smalllinenumber">[line 237]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_code39(
string
$code, [
$extended = false], [boolean
$checksum = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		CODE 39 - ANSI MH10.8M-1983 - USD-3 - 3 of 9.<br /><br /><p>General-purpose code in very wide use world-wide</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$checksum</b>&nbsp;&nbsp;</td>
        <td>if true add a checksum to the code</td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>$extended</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_code93"></a>
	<h3>method barcode_code93 <span class="smalllinenumber">[line 406]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_code93(
string
$code, boolean
$checksum)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		CODE 93 - USS-93<br /><br /><p>Compact code similar to Code 39</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$checksum</b>&nbsp;&nbsp;</td>
        <td>if true add a checksum to the code</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_eanext"></a>
	<h3>method barcode_eanext <span class="smalllinenumber">[line 1188]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_eanext(
string
$code, [string
$len = 5])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		UPC-Based Extentions<br /><br /><p>2-Digit Ext.: Used to indicate magazines and newspaper issue numbers  5-Digit Ext.: Used to mark suggested retail price of books</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$len</b>&nbsp;&nbsp;</td>
        <td>barcode type: 2 = 2-Digit, 5 = 5-Digit</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_eanupc"></a>
	<h3>method barcode_eanupc <span class="smalllinenumber">[line 995]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_eanupc(
string
$code, [string
$len = 13])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		EAN13 and UPC-A barcodes.<br /><br /><p>EAN13: European Article Numbering international retail product code  UPC-A: Universal product code seen on almost all retail products in the USA and Canada  UPC-E: Short version of UPC symbol</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$len</b>&nbsp;&nbsp;</td>
        <td>barcode type: 6 = UPC-E, 8 = EAN8, 13 = EAN13, 12 = UPC-A</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_i25"></a>
	<h3>method barcode_i25 <span class="smalllinenumber">[line 735]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_i25(
string
$code, [boolean
$checksum = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Interleaved 2 of 5 barcodes.<br /><br /><p>Compact numeric code, widely used in industry, air cargo  Contains digits (0 to 9) and encodes the data in the width of both bars and spaces.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$checksum</b>&nbsp;&nbsp;</td>
        <td>if true add a checksum to the code</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_imb"></a>
	<h3>method barcode_imb <span class="smalllinenumber">[line 1724]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_imb(
string
$code)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		IMB - Intelligent Mail Barcode - Onecode - USPS-B-3200  (requires PHP bcmath extension)  Intelligent Mail barcode is a 65-bar code for use on mail in the United States.<br /><br /><p>The fields are described as follows:<ul><li>The Barcode Identifier shall be assigned by USPS to encode the presort identification that is currently printed in human readable form on the optional endorsement line (OEL) as well as for future USPS use. This shall be two digits, with the second digit in the range of 0–4. The allowable encoding ranges shall be 00–04, 10–14, 20–24, 30–34, 40–44, 50–54, 60–64, 70–74, 80–84, and 90–94.</li><li>The Service Type Identifier shall be assigned by USPS for any combination of services requested on the mailpiece. The allowable encoding range shall be 000http://it2.php.net/manual/en/function.dechex.php–999. Each 3-digit value shall correspond to a particular mail class with a particular combination of service(s). Each service program, such as OneCode Confirm and OneCode ACS, shall provide the list of Service Type Identifier values.</li><li>The Mailer or Customer Identifier shall be assigned by USPS as a unique, 6 or 9 digit number that identifies a business entity. The allowable encoding range for the 6 digit Mailer ID shall be 000000- 899999, while the allowable encoding range for the 9 digit Mailer ID shall be *********-*********.</li><li>The Serial or Sequence Number shall be assigned by the mailer for uniquely identifying and tracking mailpieces. The allowable encoding range shall be *********–********* when used with a 6 digit Mailer ID and 000000-999999 when used with a 9 digit Mailer ID. e. The Delivery Point ZIP Code shall be assigned by the mailer for routing the mailpiece. This shall replace POSTNET for routing the mailpiece to its final delivery point. The length may be 0, 5, 9, or 11 digits. The allowable encoding ranges shall be no ZIP Code, 00000–99999,  *********–*********, and ***********–***********.</li></ul></p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to print, separate the ZIP (routing code) from the rest using a minus char '-' (BarcodeID_ServiceTypeID_MailerID_SerialNumber-RoutingCode)</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_msi"></a>
	<h3>method barcode_msi <span class="smalllinenumber">[line 605]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_msi(
string
$code, [boolean
$checksum = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		MSI.<br /><br /><p>Variation of Plessey code, with similar applications  Contains digits (0 to 9) and encodes the data only in the width of bars.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$checksum</b>&nbsp;&nbsp;</td>
        <td>if true add a checksum to the code (modulo 11)</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_pharmacode"></a>
	<h3>method barcode_pharmacode <span class="smalllinenumber">[line 1635]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_pharmacode(
string
$code)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Pharmacode<br /><br /><p>Contains digits (0 to 9)</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_pharmacode2t"></a>
	<h3>method barcode_pharmacode2t <span class="smalllinenumber">[line 1661]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_pharmacode2t(
string
$code)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Pharmacode two-track<br /><br /><p>Contains digits (0 to 9)</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_postnet"></a>
	<h3>method barcode_postnet <span class="smalllinenumber">[line 1263]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_postnet(
string
$code, [boolean
$planet = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		POSTNET and PLANET barcodes.<br /><br /><p>Used by U.S. Postal Service for automated mail sorting</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>zip code to represent. Must be a string containing a zip code of the form DDDDD or DDDDD-DDDD.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$planet</b>&nbsp;&nbsp;</td>
        <td>if true print the PLANET barcode, otherwise print POSTNET</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_rms4cc"></a>
	<h3>method barcode_rms4cc <span class="smalllinenumber">[line 1336]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_rms4cc(
string
$code, [boolean
$kix = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		RMS4CC - CBC - KIX  RMS4CC (Royal Mail 4-state Customer Code) - CBC (Customer Bar Code) - KIX (Klant index - Customer index)  RM4SCC is the name of the barcode symbology used by the Royal Mail for its Cleanmail service.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to print</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$kix</b>&nbsp;&nbsp;</td>
        <td>if true prints the KIX variation (doesn't use the start and end symbols, and the checksum) - in this case the house number must be sufficed with an X and placed at the end of the code.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbarcode_s25"></a>
	<h3>method barcode_s25 <span class="smalllinenumber">[line 664]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array barcode_s25(
string
$code, [boolean
$checksum = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Standard 2 of 5 barcodes.<br /><br /><p>Used in airline ticket marking, photofinishing  Contains digits (0 to 9) and encodes the data only in the width of bars.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$checksum</b>&nbsp;&nbsp;</td>
        <td>if true add a checksum to the code</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodbinseq_to_array"></a>
	<h3>method binseq_to_array <span class="smalllinenumber">[line 705]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array binseq_to_array(
string
$seq, 
$bararray)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert binary barcode sequence to TCPDF barcode array<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>barcode representation.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$seq</b>&nbsp;&nbsp;</td>
        <td>barcode as binary sequence  òparam array $bararray TCPDF barcode array to fill up</td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>$bararray</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodchecksum_code39"></a>
	<h3>method checksum_code39 <span class="smalllinenumber">[line 382]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>char checksum_code39(
string
$code)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Calculate CODE 39 checksum (modulo 43).<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>checksum.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodchecksum_code93"></a>
	<h3>method checksum_code93 <span class="smalllinenumber">[line 534]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string checksum_code93(
string
$code)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Calculate CODE 93 checksum (modulo 47).<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>checksum code.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodchecksum_s25"></a>
	<h3>method checksum_s25 <span class="smalllinenumber">[line 579]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int checksum_s25(
string
$code)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Checksum for standard 2 of 5 barcodes.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>checksum.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to process.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methoddec_to_hex"></a>
	<h3>method dec_to_hex <span class="smalllinenumber">[line 1848]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string dec_to_hex(
string
$number)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert large integer number to hexadecimal representation.<br /><br /><p>(requires PHP bcmath extension)</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>hexadecimal representation</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$number</b>&nbsp;&nbsp;</td>
        <td>number to convert specified as a string</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodencode_code39_ext"></a>
	<h3>method encode_code39_ext <span class="smalllinenumber">[line 331]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>encoded encode_code39_ext(
string
$code)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Encode a string to be used for CODE 39 Extended mode.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>string.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to represent.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetBarcodeArray"></a>
	<h3>method getBarcodeArray <span class="smalllinenumber">[line 92]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array getBarcodeArray(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return an array representations of barcode.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodhex_to_dec"></a>
	<h3>method hex_to_dec <span class="smalllinenumber">[line 1872]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string hex_to_dec(
string
$hex)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert large hexadecimal number to decimal representation (string).<br /><br /><p>(requires PHP bcmath extension)</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>hexadecimal representation</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$hex</b>&nbsp;&nbsp;</td>
        <td>hexadecimal number to convert specified as a string</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodimb_crc11fcs"></a>
	<h3>method imb_crc11fcs <span class="smalllinenumber">[line 1889]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int imb_crc11fcs(
string
$code_arr)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Intelligent Mail Barcode calculation of Frame Check Sequence<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>11 bit Frame Check Sequence as integer (decimal base)</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code_arr</b>&nbsp;&nbsp;</td>
        <td>array of hexadecimal values (13 bytes holding 102 bits right justified).</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodimb_reverse_us"></a>
	<h3>method imb_reverse_us <span class="smalllinenumber">[line 1925]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int imb_reverse_us(
int
$num)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Reverse unsigned short value<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>reversed value</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$num</b>&nbsp;&nbsp;</td>
        <td>value to reversr</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodimb_tables"></a>
	<h3>method imb_tables <span class="smalllinenumber">[line 1942]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array imb_tables(
int
$n, int
$size)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		generate Nof13 tables used for Intelligent Mail Barcode<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>requested table</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$n</b>&nbsp;&nbsp;</td>
        <td>is the type of table: 2 for 2of13 table, 5 for 5of13table</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$size</b>&nbsp;&nbsp;</td>
        <td>size of table (78 for n=2 and 1287 for n=5)</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetBarcode"></a>
	<h3>method setBarcode <span class="smalllinenumber">[line 102]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>array setBarcode(
string
$code, string
$type)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the barcode.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$code</b>&nbsp;&nbsp;</td>
        <td>code to print</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$type</b>&nbsp;&nbsp;</td>
        <td>type of barcode: <ul><li>C39 : CODE 39 - ANSI MH10.8M-1983 - USD-3 - 3 of 9.</li><li>C39+ : CODE 39 with checksum</li><li>C39E : CODE 39 EXTENDED</li><li>C39E+ : CODE 39 EXTENDED + CHECKSUM</li><li>C93 : CODE 93 - USS-93</li><li>S25 : Standard 2 of 5</li><li>S25+ : Standard 2 of 5 + CHECKSUM</li><li>I25 : Interleaved 2 of 5</li><li>I25+ : Interleaved 2 of 5 + CHECKSUM</li><li>C128A : CODE 128 A</li><li>C128B : CODE 128 B</li><li>C128C : CODE 128 C</li><li>EAN2 : 2-Digits UPC-Based Extention</li><li>EAN5 : 5-Digits UPC-Based Extention</li><li>EAN8 : EAN 8</li><li>EAN13 : EAN 13</li><li>UPCA : UPC-A</li><li>UPCE : UPC-E</li><li>MSI : MSI (Variation of Plessey code)</li><li>MSI+ : MSI + CHECKSUM (modulo 11)</li><li>POSTNET : POSTNET</li><li>PLANET : PLANET</li><li>RMS4CC : RMS4CC (Royal Mail 4-state Customer Code) - CBC (Customer Bar Code)</li><li>KIX : KIX (Klant index - Customer index)</li><li>IMB: Intelligent Mail Barcode - Onecode - USPS-B-3200</li><li>CODABAR : CODABAR</li><li>CODE11 : CODE 11</li><li>PHARMA : PHARMACODE</li><li>PHARMA2T : PHARMACODE TWO-TRACKS</li></ul></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
</div><br />


        <div class="credit">
		    <hr />
		    Documentation generated on Sun, 28 Mar 2010 22:22:39 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>