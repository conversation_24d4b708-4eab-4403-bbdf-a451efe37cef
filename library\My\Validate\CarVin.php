<?php

class My_Validate_CarVin extends Zend_Validate_Abstract {
	
	const VIN_INVALID_CHARACTERS = 'vinInvalidCharacters';
	const VIN_INVALID_CHARACTERS_UNKNOWN = 'vinInvalidCharactersUnknown';
	const VIN_INVALID_LENGTH = 'vinInvalidLength';
	
	protected $_messageTemplates = array(
		self::VIN_INVALID_CHARACTERS => "Znaki 'i', 'o' oraz 'q' są niedozwolone",
		self::VIN_INVALID_CHARACTERS_UNKNOWN => "Pole zawiera niedozwolone znaki",
		self::VIN_INVALID_LENGTH => 'Ni<PERSON>rawidłowa długość'
	);
	
	public function isValid($data, $context=null) {
		if (strlen($data) != 17) {
			$this->_error(self::VIN_INVALID_LENGTH);
			return false;
		}
		
		$test = strtoupper($data);
		$testFiltered = Zend_Filter::filterStatic($test, 'Alnum');
		
		if ($test != $testFiltered) {
			$this->_error(self::VIN_INVALID_CHARACTERS_UNKNOWN);
			return false;
		}
		
		if (strpos($testFiltered, "I") !== false || strpos($testFiltered, "O") !== false || strpos($testFiltered, "Q") !== false) {
			$this->_error(self::VIN_INVALID_CHARACTERS);
			return false;
		}
		return true;
	}
	
}