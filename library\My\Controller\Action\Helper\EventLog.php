<?php

class My_Controller_Action_Helper_EventLog extends Zend_Controller_Action_Helper_Abstract {
	
	public function log($data, $level=Zend_Log::INFO) {
		$loggerArray = array(
			'account_id' => Zend_Auth::getInstance()->hasIdentity() ? Zend_Auth::getInstance()->getIdentity()->id : new Zend_Db_Expr('NULL'),
			'ip' => $this->getRequest()->getServer('REMOTE_ADDR'),
			'controller' => $this->getRequest()->getControllerName(),
			'action' => $this->getRequest()->getActionName(),
			'operation' => null,
			'time' => new Zend_Db_Expr('NOW()'),
			'outcome' => null,
			'additional' => null
		);
		
		$data = array_merge($loggerArray, $data);
		
		$logger = Zend_Registry::get('eventLogger');
		$logger->log($data, $level);
	}
	
}