<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta http-equiv="Content-Language" content="<?= $this->language ?>" />
        <?php if($this->metaDescription): ?>
        <meta name="description" content="<?= $this->metaDescription ?>" />
        <?php endif ; ?>
		<title><?= $this->siteTitle ?></title>
		<? /* $this->render('el/header_includes.phtml') */ ?>
    <?= $this->render('_new-elements/_header-tag-includes.phtml') ?>
        <!-- Google Tag Manager -->
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-NKNWTWB');</script>
        <!-- End Google Tag Manager -->
	</head>



	<body<?php if ($this->htmlBodyClass) :?> class="<?= $this->htmlBodyClass ?>"<?php endif; ?> data-layout="layout_front">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NKNWTWB"
                      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
		<div id="outer_container">
			<div id="fx_right">
				<div id="fx_right_content">flash</div>
			</div>

			<div id="inner_container">
				<div id="header">
					<? /*$this->render('el/top_menu.phtml')*/ ?>
          <?= $this->render('_new-elements/main-menu.phtml') ?>

				</div>
				<div class="clear"></div>
				<? /* $this->render('el/main_menu.phtml') */ ?>

				<div id="front_breadcrumb_replacement"></div>

				<?= $this->render('el/show_messages.phtml') ?>

				<div id="fx">
					<div id="fx_left">
						<?php if ($this->carTypesWithCounts && count($this->carTypesWithCounts) > 0): ?>
							<table id="front_other_categories">
								<tr>

									<td class="">

										<?=

                                        $this->partial(
											'type_boxes.phtml',
											array(
												'view' => $this,
												'catsWithCounts' => $this->carTypesWithCounts,
											)
										) ?>
									</td>
								</tr>
							</table>
						<?php endif ?>
            <?php if ($this->makesWithCounts && count($this->makesWithCounts) > 0): ?>
							<div id="front_makes">

								<?php
									$count = count($this->makesWithCounts);
									$cols = 5;
									$rows = $count / $cols;
									$mod = $count % $cols;
									if ($mod > 0) $rows++;

									$makesRows = array();
									for ($i=0; $i < $cols; $i++) {
										$makesRows[$i] = array();
									}
									$i = 0;

                                    foreach ($this->makesWithCounts as $index => $item) {
										$makesRows[$i % $rows][] = array($index => $item);
										$i++;
									}

								?>
								<table cellspacing="0" cellpadding="0">
									<?php $r=0; foreach ($makesRows as $row): ?>
										<tr>
											<?php $i=0; foreach ($row as $item): ?>
												<td>

													<?= $this->partial(
															'make_boxes.phtml',
															array(
																'view' => $this,
																'makesWithCounts' => $item,
																'types' => array(1),
																'nonArrayFilters' => array()
															)
														)
													?>
												</td>
											<?php $i++; endforeach ?>
										</tr>
									<?php $r++; endforeach ?>


								</table>

							</div>
						<?php endif ?>

						<?php if ($this->exclusiveMakesWithCounts && count($this->exclusiveMakesWithCounts) > 0): ?>
							<table id="front_exclusive">
								<tr>
									<td class="left">
										<h2><a href="<?= $this->url(array('language' => $this->language), 'list', true) ?>?addName[]=types&amp;addValue_0[]=1&amp;addName[]=exclusive&amp;addValue_1=1"><?= $this->translate->_('SEARCH_EXCLUSIVE') ?> (<?= (int)$this->exclusiveMakesWithCountsTotalCount ?>)</a></h2>
									</td>
									<td class="right">
										<?= $this->partial(
											'make_boxes.phtml',
											array(
												'view' => $this,
												'makesWithCounts' => $this->exclusiveMakesWithCounts,
												'types' => array(1),
												'nonArrayFilters' => array()
											)
										) ?>
									</td>
								</tr>
							</table>
						<?php endif ?>



					</div>
					<div id="front_search" class="search">
						<div id="front_search_content">
							<h3><?= $this->translate->_('FIND_CAR') ?></h3>
							<?php
								$form = new Form_Search(array('variant' => 'front'));
                                $form->getElement('submit')->setLabel('SEARCH');
								echo $form;
							?>

							<script type="text/javascript" charset="utf-8">
								var autoautoLang = "<?= $this->language ?>";

								var multiselectOptions = {
									checkAllText: "<?= $this->escape($this->translate->_('MULTISELECT_CHECK_ALL')) ?>",
									uncheckAllText: "<?= $this->escape($this->translate->_('MULTISELECT_UNCHECK_ALL')) ?>",
									selectedText: "<?= $this->escape($this->translate->_('MULTISELECT_SELECTED_TEXT')) ?>",
									minWidth: 270
								};

                                var multiselectOptions2 = {
                                    checkAllText: "<?= $this->escape($this->translate->_('MULTISELECT_CHECK_ALL')) ?>",
                                    uncheckAllText: "<?= $this->escape($this->translate->_('MULTISELECT_UNCHECK_ALL')) ?>",
                                    selectedText: function(selectedCount,length,selected){
                                        if(selectedCount == length) {
                                            return "<?= $this->escape($this->translate->_('ANY2')) ?>";
                                        } else if(selectedCount == 1) {
                                            return $(selected[0]).attr('title');
                                        } else {
                                            return "<?= $this->escape($this->translate->_('MULTISELECT_SELECTED_TEXT')) ?>".replace("#",selectedCount);
                                        }
                                    },

                                    minWidth: 270
                                };

								$(document).ready(function(){
									$("#front_search .form_label").hide();

									$('#categories').multiselect($.extend({}, multiselectOptions, {classes: "for_categories", noneSelectedText: "<?= $this->escape($this->translate->_('CATEGORY')) ?>"}));
									$('#important_features').multiselect($.extend({}, multiselectOptions, {minWidth: 110,noneSelectedText: "<?= $this->escape($this->translate->_('IMPORTANT_FEATURES')) ?>"}));
									$('#engine').multiselect($.extend({}, multiselectOptions, {minWidth: 110, classes: 'engine', noneSelectedText: "<?= $this->escape($this->translate->_('ENGINE')) ?>"}));
									$('#gearboxes').multiselect($.extend({}, multiselectOptions2, {minWidth: 110, noneSelectedText: "<?= $this->escape($this->translate->_('GEARBOX')) ?>"}));
									$('#build').multiselect($.extend({}, multiselectOptions, {noneSelectedText: "<?= $this->escape($this->translate->_('PRODUCTION_YEAR')) ?>"}));
									$('#odometer').multiselect($.extend({}, multiselectOptions, {minWidth: 110,noneSelectedText: "<?= $this->escape($this->translate->_('ODOMETER')) ?>"}));

									$("#front_search form").submit(function(){
										$(this).find("option[selected='']").removeAttr('selected');
									});

                                    var fixedDisabledFeaturesIndexStart = 13;
                                    var fixedDisabledFeaturesIndexStop = 18;
									var divideModulo = [18, 18, 19];
									$(".for_important_features ul.ui-multiselect-checkboxes").append('<div class="clear" />');
									var lastModulo = 0;
									for (var i in divideModulo) {
										$(".for_important_features ul.ui-multiselect-checkboxes>li:lt("+divideModulo[i]+")").wrapAll('<div class="features_column" />');
									}
                                    $(".for_important_features ul.ui-multiselect-checkboxes li").slice(fixedDisabledFeaturesIndexStart, fixedDisabledFeaturesIndexStop).find('input').attr('checked', 'checked').attr('disabled', 'disabled').siblings('span').css('color', '#f67812');


									/*var buildFrom = $(".for_build_from").outerHtml();
									var buildTo =  $(".for_build_to").outerHtml();

									$(".for_build_from, .for_build_to").remove();
									$(".for_build div.ui-multiselect-menu").append(buildFrom, buildTo);
									$(".for_build_from input, .for_build_to input").css('color', '#333');
									$(".for_build_from, .for_build_to").css({'width': '115px', 'float': 'left'});
									$(".for_build_from").css({'padding-right': '27px'});
									$(".for_build_from input, .for_build_to input").css({'width': '105px'});*/


                                    /*var cubicCapacityFrom = $(".for_cubic_capacity_from").outerHtml();
									var cubicCapacityTo =  $(".for_cubic_capacity_to").outerHtml();

									$(".for_cubic_capacity_from, .for_cubic_capacity_to").remove();
									$(".for_engine div.ui-multiselect-menu").append(cubicCapacityFrom, cubicCapacityTo);
									$(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css('color', '#333');
									$(".for_cubic_capacity_from, .for_cubic_capacity_to").css({'width': '115px', 'float': 'left'});
									$(".for_cubic_capacity_from").css({'padding-right': '27px'});
									$(".for_cubic_capacity_from input, .for_cubic_capacity_to input").css({'width': '105px'});*/


									$("#front_search .input_text input").compactInputs({
										getLabelFn: function(el) {
											return $(el).closest(".form_input").siblings(".form_label").find("label");
										}
									});

								});
								</script>
						</div>

                        <div class="have_car_found_bar">

                            <a href="<?= $this->url(array('language' => $this->language), 'haveCarFound', true) ?>">
                                <span class="for_icon">&nbsp;</span>
                                <?= $this->translate->_('HAVE_CAR_FOUND') ?>
                            </a>
                        </div>

                        <?php if($this->language == 'pl'): ?>
							<style>

								#front_search form {
									display: none;
								}
							</style>
                            <div class="aa-auction">

                                <a class="small" href="<?= $this->url(array('language' => $this->language), 'list', true)?>?addName[]=auction&addValue_0=1">
                                    <img src="/images/aa-auction.png" alt="Aukcje autoauto" />
                                </a>

                                <a class="big" href="<?= $this->url(array('language' => $this->language), 'list', true)?>?addName[]=auction&addValue_0=1">
                                    <img src="/images/autoauto_banner maly.jpg" alt="Bezpośredni od Właściciela" />
                                </a>

                                <script type="text/javascript">

                                    $(function(){
                                        $("#front_search h3").click(function() {
                                            $("#front_search form").show();
                                            $(".aa-auction .big").hide();
                                            $(".aa-auction .small").show();
                                        })
                                    })
                                </script>
                            </div>
                        <?php endif; ?>


                    </div>
				</div>



				<div class="carsCategories">
					<?php
					$params = array(
						0 => array('categories' => array('sedan','hatchback')),
						1 => array('categories' => array('suv','pickup')),
						2 => array('categories'  => array('van', 'combi')),
						3 => array('categories'  => array('coupe','cabrio')),
						4 => array('premium' => '1'),
						5 => array('exclusive' => '1'),

					);
					$i=0;
					foreach ($this->randomCars as $rc): ?>
						<?php if (isset($rc['car_id']) && !empty($rc['car_id'])): ?>
							<a href="<?= $this->url(array('language' => $this->language), 'list', true) . '?' . http_build_query($params[$i]) ?>" class="carsCategories--item" data-hover-text="szukaj" data-text="<?= $this->translate->_('CUSTOM_CATEGORY_DESCRIPTION_' . $rc['custom_category_description']) ?>" data-js="carsCategoriesItem" style="background-image: url('/images/cars/<?= $rc['car_id'] . '/' . $rc['filename_base'] . '_SM.' . $rc['filename_extension'] ?>')">
                  <span><?= $this->translate->_('CUSTOM_CATEGORY_DESCRIPTION_' . $rc['custom_category_description']) ?></span>

                  <span>
                    <?php if($this->siteVariant == 'autoauto.by') :?>
                        <?= $this->escape($this->carPrice($rc, $this->language_row, 'EUR')) ?>
                    <?php else: ?>
                      <?= $this->carPrice($rc, $this->language_row) ?>
                    <?php endif; ?>
                  </span>

              </a>
						<?php else: ?>
              <a href="<?= $this->url(array('language' => $this->language), 'list', true) . '?' . $params[$i] ?>" class="carsCategories--item" style="background-image: url('/images/video.jpg')">
                <span></span>
                <span><?= $this->translate->_('CUSTOM_CATEGORY_DESCRIPTION_' . $rc['custom_category_description']) ?></span>
              </a>
						<?php endif ?>
					<?php $i++; endforeach ?>
				</div>


        <!--
        <div id="random-cars">
					<?php
					$params = array(
						0 => 'addName[]=categories&amp;addValue_0[]=sedan&amp;addValue_0[]=hatchback',
						1 => 'addName[]=categories&amp;addValue_0[]=suv&amp;addValue_0[]=pickup',
						2 => 'addName[]=categories&amp;addValue_0[]=van&amp;addValue_0[]=combi',
						3 => 'addName[]=categories&amp;addValue_0[]=coupe&amp;addValue_0[]=cabrio',
						4 => 'addName[]=premium&amp;addValue_0=1',
						5 => 'addName[]=leasing&amp;addValue_0=1',

					);
					$i=0;
					foreach ($this->randomCars as $rc): ?>
						<?php if (isset($rc['car_id']) && !empty($rc['car_id'])): ?>
              <a href="<?= $this->url(array('language' => $this->language), 'list', true) . '?' . $params[$i] ?>" class="<?php if ($i==0) echo 'first'; elseif ($i==4) echo 'middle'; elseif ($i==5) echo 'last'; ?>">
								<?php if($i == 5): ?>
                  <span class="big-vat">VAT</span>
                  <span class="category"><?= $this->translate->_('CUSTOM_CATEGORY_DESCRIPTION_' . $rc['custom_category_description']) ?></span>
                  <span class="price">
                      <?= $this->translate->_('CARS_FROM_USA_PIECES') ?> <?= $rc['count']?>
                  </span>
                <?php else :?>
                  <img src="/images/cars/<?= $rc['car_id'] . '/' . $rc['filename_base'] . '_SM.' . $rc['filename_extension'] ?>" alt="">
                  <span class="category <?php if($i == 4): ?>PREMIUM<?php endif;?>"> <?= $this->translate->_('CUSTOM_CATEGORY_DESCRIPTION_' . $rc['custom_category_description']) ?></span>
                  <span class="price">
                  <?php if($this->siteVariant == 'autoauto.by') :?>
                      <?= $this->escape($this->carPrice($rc, $this->language_row, 'EUR')) ?>
                  <?php else: ?>
                    <?= $this->carPrice($rc, $this->language_row) ?>
                  <?php endif; ?>
                  </span>
                <?php endif;?>
							</a>
						<?php else: ?>
							<a href="<?= $this->url(array('language' => $this->language), 'list', true) . '?' . $params[$i] ?>" class="<?php if ($i==0) echo 'first'; elseif ($i==4) echo 'middle'; elseif ($i==5) echo 'last'; ?>">
								<img src="/images/video.jpg" style="width: 159px; height: 119px;" alt="">
								<span class="category"><?= $this->translate->_('CUSTOM_CATEGORY_DESCRIPTION_' . $rc['custom_category_description']) ?></span>
							</a>
						<?php endif ?>
					<?php $i++; endforeach ?>
					<div class="clear"></div>
				</div>
        -->
                <?php if($this->advertisingBarText): ?>

                    <div class="marquee-wrapper">

                        <div id="m1" class="marquee advertising_bar"><span><?= $this->advertisingBarText ?></span></div>
                    </div>

                <?php else: ?>
                    <div class="marquee-wrapper">
                        <div id="m1" class="marquee advertising_bar"><span>Najlepsze ceny w Polsce!     Auta z gwarancją!  SKUP SAMOCHODÓW ZA GOTÓWKĘ.  Zapraszamy 7 dni w tygodniu do 3 punktów w Warszawie. Zleć poszukiwanie auta przez naszą stronę w zakładce / Kup /. Sprzedaj swoje auto lub zamień na inne - od ręki. Zamieniamy małe na duże i odwrotnie - z dopłatą lub my dopłacimy. Dopasuj auto do swoich potrzeb. SKUP SAMOCHODÓW ZA GOTÓWKĘ </span></div>
                    </div>
                <?endif;?>

                <script type="text/javascript">
                    $('.marquee').marquee({
                        duration: 20000,
                        gap: 50,
                        delayBeforeStart: 0,
                        direction: 'left',
                        duplicated: true
                    });

                </script>


        <div class="greenSections">
          <div class="greenSections--item">

              <a class="blue-gradient" href="<?= $this->url(array('language' => $this->language), 'locations', true) ?>"><?= $this->translate->_('CONTACT_US') ?></a>
              <div class="greenSections--item__content">
                <?php if (is_array($this->randomEmployees) && $this->randomEmployees[0]): ?>
                <img id="random_employee" src="/images/employees/<?= $this->randomEmployees[0]['photo_basename'] ?>_S.<?= $this->randomEmployees[0]['photo_extension'] ?>" alt="<?= $this->escape($this->randomEmployees[0]['first_name'] . " " . $this->randomEmployees[0]['last_name']) ?>" />
                <div class="larger">
                  <?= $this->escape($this->randomEmployees[0]['first_name'] . " " . $this->randomEmployees[0]['last_name']) ?>
                </div>
                <div>
                  <a href="mailto:<?= $this->escape($this->randomEmployees[0]['email']) ?>"><?= $this->escape($this->randomEmployees[0]['email']) ?></a>
                </div>
                <div>
                  <?= $this->escape($this->randomEmployees[0]['phone']) ?>
                  <?php if (!empty($this->randomEmployees[0]['skype'])): ?>
                      <a class="skype" href="skype:<?= $this->escape($this->randomEmployees[0]['skype']) ?>?call"></a>


                  <?php endif ?>
                </div>
                <?php endif ?>
              </div>

              <a class="green-gradient" href="<?= $this->url(array('language' => $this->language), 'list', true) ?>" style="text-transform: uppercase"><?= $this->translate->_('CAR_NEWEST_OFFERS') ?></a>
              <div class="greenSections--item__content car-background" onclick="window.location.href='<?= $this->url(array('language' => $this->language), 'list', true) ?>'">
                <h3>Co dzień co najmniej 10 nowych aut</h3>
                <p>sprawdź ofertę ></p>
              </div>

          </div>

          <script src="https://maps.googleapis.com/maps/api/js?v=3&libraries=places"></script>
          <script>
            var geocoder;
            var googleMap;
            var marker;

            function initializeMap() {
              setTimeout(function() {
                geocoder = new google.maps.Geocoder();
                var myLatlng = new google.maps.LatLng(52.183400, 20.952342);
                var pointerAlKrakowska = new google.maps.LatLng(52.183400, 20.952342);
                var pointerOstrobramska = new google.maps.LatLng(52.232215, 21.117278);
                var pointerJanaPawla = new google.maps.LatLng(52.251833, 20.984389);

                var mapOptions = {
                  zoom: 11,
                  scrollwheel: false,
                  clickable: false,
                  disableDefaultUI: true,
                  center: myLatlng,
                  draggable: false,
                  styles: [
                    {
                      featureType: 'water',
                      stylers: [
                        { color: '#b5d2ea' },
                        { visibility: 'on' }
                      ]
                    },
                    {
                      featureType: 'landscape',
                      stylers: [ { color:'#f2f2f2' } ]
                    },
                    {
                      featureType: 'road',
                      stylers: [
                        { saturation: -100 },
                        { lightness: 45 }
                      ]
                    },
                    {
                      featureType: 'road.highway',
                      stylers: [ { visibility:'on' } ]
                    },
                    {
                      featureType: 'road.arterial',
                      elementType: 'labels.icon',
                      stylers:[ { visibility:'on' } ]
                    },
                    {
                      featureType: 'administrative',
                      elementType: 'labels.text.fill',
                      stylers: [ { color: '#444444' } ]
                    },
                    {
                      featureType: 'transit',
                      stylers:[ { visibility:'on' } ]
                    },{
                      featureType: 'poi',
                      stylers: [
                        { visibility: 'on' },
                        { color: '#dcecd6' }
                      ]
                    }
                  ]
                };

                googleMap = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);


                marker = new google.maps.Marker({
                    map: googleMap,
                    draggable:false,
                    animation: google.maps.Animation.DROP,
                    position: pointerAlKrakowska,
                    icon: window.temporaryHost + '/images/_new/marker-blue.png',
                    url: 'http://maps.google.com/maps?q=Al.+Krakowska+178,+Warszawa&amp;hl=<?= $this->language ?>&amp;ie=UTF8&amp;ll=52.1833,20.952344&amp;spn=0.009736,0.027466&amp;sll=52.232491,21.11392&amp;sspn=0.009725,0.027466&amp;z=16&amp;output=embed&amp;iframe=true&amp;width=900&amp;height=550'
                });

                  google.maps.event.addListener(marker, 'click', function() {
                      window.open(marker.url);
                  });
                  /*
                marker2 = new google.maps.Marker({
                    map: googleMap,
                    draggable:false,
                    animation: google.maps.Animation.DROP,
                    position: pointerOstrobramska,
                    icon: window.temporaryHost + '/images/_new/marker-orange.png',
                    url: 'http://maps.google.com/maps?q=Ostrobramska+73,+Warszawa&amp;hl=<?= $this->language ?>&amp;ie=UTF8&amp;ll=52.232491,21.11392&amp;spn=0.009725,0.027466&amp;sll=52.25178,20.98423&amp;sspn=0.009721,0.027466&amp;z=16&amp;output=embed&amp;iframe=true&amp;width=900&amp;height=550'
                });

                  google.maps.event.addListener(marker2, 'click', function() {
                      window.open(marker2.url);
                  });

                marker3 = new google.maps.Marker({
                    map: googleMap,
                    draggable:false,
                    animation: google.maps.Animation.DROP,
                    position: pointerJanaPawla,
                    icon: window.temporaryHost + '/images/_new/marker-green.png',
                    url: 'http://maps.google.com/maps?q=Jana+Paw%C5%82a+II+69,+Warszawa&amp;hl=<?= $this->language ?>&amp;ie=UTF8&amp;ll=52.25178,20.98423&amp;spn=0.009721,0.027466&amp;sll=52.232478,21.113877&amp;sspn=0.009725,0.027466&amp;z=16&amp;output=embed&amp;iframe=true&amp;width=900&amp;height=550'
                });

                  google.maps.event.addListener(marker3, 'click', function() {
                      window.open(marker3.url);
                  });
                  */

              }, 1000);

            }

            google.maps.event.addDomListener(window, 'load', initializeMap);

          </script>

          <div class="greenSections--item x2">
            <a href="#" class="blue-gradient"><?php //$this->translate->_('WHERE_WE_ARE') ?></a>
            <div class="greenSections--item__content">
              <div id="map-canvas"></div>
            </div>
          </div>

        </div>


				<?= $this->layout()->content ?>
				<div class="front-description">
                    <p>Od przeszło 15 lat skupujemy i sprzedajemy auta używane. Warszawa to miasto, na terenie którego prowadzimy naszą działalność, współpracując z szerokim gronem przedsiębiorców oraz klientów indywidualnych.
                    W przygotowanej przez nas ofercie sprzedażowej można znaleźć utrzymane w doskonałym stanie, luksusowe samochody używane takich marek, jak Land Rover, Jaguar czy Aston Martin. Wszystkie auta są poddane szczegółowym czynnościom kontrolnym i serwisowym- zyskujemy dzięki temu pewność, iż są one w pełni sprawne technicznie. Sprzedajemy również przeznaczone do codziennego użytku, osobowe samochody używane oraz auta terenowe. Używane pojazdy dostępne są w bardzo atrakcyjnych cenach.
                    Na stronie zamieściliśmy wygodną w obsłudze wyszukiwarkę, pozwalającą na określenie takich danych, jak rocznik pojazdu, cena, pojemność silnika czy rodzaj nadwozia. Korzystając z tego udogodnienia, można w wygodny sposób odnaleźć odpowiednie samochody używane (Sprzedaż). Warszawa to miasto, w którym do dyspozycji naszych klientów pozostają setki pojazdów najlepszych marek.  Zapewniamy kompleksową obsługę, pomagając w ubieganiu się o kredyty oraz leasingi. Współpracujemy także z firmami ubezpieczeniowymi, negocjując korzystne warunki współpracy dla osób, zakupujących nasze używane samochody. Luksusowe pojazdy, takie jak Lexus, czy Lincoln, dostępne są w niewiarygodnie korzystnych cenach.
                    Osoby, poszukujące uczciwego, godnego zaufania sprzedawcy, często odwiedzają nasze komisy samochodowe. Warszawa to miasto, w którym działamy w trzech, doskonale zlokalizowanych punktach. Sprzedając samochody luksusowe używane udostępniamy możliwość wykonania jazdy próbnej oraz dokładnej oceny stanu pojazdu. Nasi konsultanci zapewniają profesjonalne doradztwo oraz pomagają w dokonaniu rozsądnego wyboru auta.
                    Każdego dnia oferta naszej firmy uzupełniana jest o luksusowe auta używane oraz co najmniej dziesięć nowych samochodów osobowych. Zapewniamy także możliwość kupna samochodu za gotówkę od osób z Warszawy i okolic.
</p>
                </div>
				<?= $this->render('el/footer.phtml') ?>
			</div>
		</div>

        <script type="text/javascript">
        $(function () {

            setInterval(function(){

                if("rgb(255, 255, 255)" == $(".have_car_found_bar a").css('color'))
                    $(".have_car_found_bar a").css('color', 'rgb(246, 120, 18)');
                else if("rgb(246, 120, 18)" == $(".have_car_found_bar a").css('color'))
                    $(".have_car_found_bar a").css('color', 'rgb(255,255,255)');
                }
            ,1500);


        });

        </script>
		<script type="text/javascript">


            var _gaq = _gaq || [];
            _gaq.push(['_setAccount', 'UA-********-1']);
            _gaq.push(['_setDomainName', 'autoauto.pl']);
            _gaq.push(['_trackPageview']);

            (function() {
                var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
                ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
                var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
            })();

        </script>

        <script type="text/javascript">
            adroll_adv_id = "G4XRYOVPF5CGTCX5SSQN67";
            adroll_pix_id = "YQP7DP2BQFHYNCLGIBQLP5";
            (function () {
                var oldonload = window.onload;
                window.onload = function(){
                    __adroll_loaded=true;
                    var scr = document.createElement("script");
                    var host = (("https:" == document.location.protocol) ? "https://s.adroll.com" : "http://a.adroll.com");
                    scr.setAttribute('async', 'true');
                    scr.type = "text/javascript";
                    scr.src = host + "/j/roundtrip.js";
                    ((document.getElementsByTagName('head') || [null])[0] ||
                        document.getElementsByTagName('script')[0].parentNode).appendChild(scr);
                    if(oldonload){oldonload()}};
            }());
        </script>

        <!-- Kod tagu remarketingowego Google -->
        <!--------------------------------------------------
        Tagi remarketingowe nie mogą być wiązane z informacjami umożliwiającymi identyfikację osób ani umieszczane na stronach o tematyce należącej do kategorii kontrowersyjnych. Więcej informacji oraz instrukcje konfiguracji tagu znajdziesz tutaj: http://google.com/ads/remarketingsetup
        --------------------------------------------------->
        <script type="text/javascript">
            /* <![CDATA[ */
            var google_conversion_id = 976363198;
            var google_custom_params = window.google_tag_params;
            var google_remarketing_only = true;
            /* ]]> */
        </script>
        <script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
        </script>
        <noscript>
            <div style="display:inline;">
                <img height="1" width="1" style="border-style:none;" alt="" src="//googleads.g.doubleclick.net/pagead/viewthroughconversion/976363198/?value=0&amp;guid=ON&amp;script=0"/>
            </div>
        </noscript>

        <!-- Kod tagu remarketingowego Google -->
        <!--------------------------------------------------
        Tagi remarketingowe nie mogą być wiązane z informacjami umożliwiającymi identyfikację osób ani umieszczane na stronach o tematyce należącej do kategorii kontrowersyjnych. Więcej informacji oraz instrukcje konfiguracji tagu znajdziesz tutaj: http://google.com/ads/remarketingsetup
        --------------------------------------------------->
        <script type="text/javascript">
            /* <![CDATA[ */
            var google_conversion_id = 847200092;
            var google_custom_params = window.google_tag_params;
            var google_remarketing_only = true;
            /* ]]> */
        </script>
        <script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
        </script>
        <noscript>
            <div style="display:inline;">
                <img height="1" width="1" style="border-style:none;" alt="" src="//googleads.g.doubleclick.net/pagead/viewthroughconversion/847200092/?guid=ON&amp;script=0"/>
            </div>
        </noscript>


    </body>

</html>