<?php if ($this->pageCount >= 2):
	?>

    <nav aria-label="Page navigation example">
        <ul class="pagination justify-content-center">

            <?php if (isset($this->previous)): ?>
                <li class="page-item ">
                    <a class="page-link" href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => $this->previous))); ?>" tabindex="-1"><?= $this->prevString ?></a>
                </li>
            <?php endif; ?>
            <?php if (!in_array(1, $this->pagesInRange)): ?>
                <li class="page-item"><a class="page-link"  href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => 1))); ?>">1</a></li>
                <li class="page-item">...</li>
            <?php endif ?>
            <?php foreach ($this->pagesInRange as $page): ?>
                <?php if ($page != $this->current): ?>
                    <li class="page-item"><a class="page-link" href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => $page))) ?>"><?php echo $page;?></a></li>
                <?php else: ?>
                    <li class="page-item active"><a class="page-link" href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => $page))) ?>"><?php echo $page;?></a></li>
                <?php endif;?>
            <?php endforeach;?>

            <?php if (!in_array($this->pageCount, $this->pagesInRange)): ?>
                <li class="page-item">...</li>
                <li class="page-item"><a class="page-link"  href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => $this->pageCount))); ?>"><?= $this->pageCount ?></a></li>
            <?php endif ?>

            <?php if (isset($this->next)): ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo $this->url() .  '?'. http_build_query(array_merge($this->queryStringArray,array('page' => $this->next))); ?>"><?= $this->nextString ?></a>
                </li>
            <?php endif; ?>

            <?php if (isset($this->carListShowMax) && $this->carListShowMax): ?>
                <span><a href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => 1, 'perPage' => 'max'))); ?>"><?= str_replace("%items%", $this->carListShowMax, $this->translate->_('PAGINATOR_SHOW_ITEM_COUNT_PER_PAGE')) ?></a></span>
            <?php elseif (isset($this->carListShowDefault) && $this->carListShowDefault): ?>
                <span><a href="<?php echo $this->url() . '?'. http_build_query(array_merge($this->queryStringArray,array('page' => 1, 'perPage' => 'default'))) ?>"><?= str_replace("%items%", $this->carListShowDefault, $this->translate->_('PAGINATOR_SHOW_ITEM_COUNT_PER_PAGE')) ?></a></span>
            <?php endif ?>
        </ul>
    </nav>


<?php endif;?>
