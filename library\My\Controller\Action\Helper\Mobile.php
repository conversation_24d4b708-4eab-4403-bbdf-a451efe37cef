<?php

class My_Controller_Action_Helper_Mobile extends Zend_Controller_Action_Helper_Abstract {

    public function preDispatch() {

        $front = Zend_Controller_Front::getInstance();
        $bootstrap = $front->getParam('bootstrap');
        $options = $bootstrap->getOptions();
        $layoutFront = $front->getRequest()->getActionName() == 'index' && $front->getRequest()->getControllerName() == 'index';
        if($layoutFront)
            Zend_Layout::getMvcInstance()->setLayout('layout_front');

        /** @var $viewRenderer Zend_Controller_Action_Helper_ViewRenderer */
        $viewRenderer = $this->getActionController()->getHelper('ViewRenderer');

        /** @var $view Haml_View */
        $view = $viewRenderer->view;

        $view->isMobile = false;

        $detect = new Mobile_Detect();

        $layoutNamespace = new Zend_Session_Namespace('layout');

        $layout = $front->getRequest()->getParam('layout');

        if($layout)
            $layoutNamespace->layout = $layout;

        if ($layoutNamespace->layout == 'mobile' || ($detect->isMobile() && !$detect->isTablet() && !$layoutNamespace->layout)) {



            $viewRenderer->setViewSuffix('mobile.phtml');
            $script = $viewRenderer->getViewScript();

            if (!$view->getScriptPath($script)) {
                $viewRenderer->setViewSuffix('phtml');
            }
            else {
                $view->isMobile = true;
                $layoutNamespace->layout = 'mobile';

                if($layoutFront)
                    Zend_Layout::getMvcInstance()->setLayout('layout_front.mobile');
                else
                    Zend_Layout::getMvcInstance()->setLayout('layout.mobile');
            }
        }
    }


}