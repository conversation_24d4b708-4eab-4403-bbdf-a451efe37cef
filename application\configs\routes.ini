[production]


routes.sitemap.route = "sitemap.xml"
routes.sitemap.defaults.controller = "index"
routes.sitemap.defaults.action = "sitemap"

routes.home.route = ":language/"
routes.home.reqs.language = "[A-z][A-z]"
routes.home.defaults.controller = "index"
routes.home.defaults.action = "index"

routes.general.route = ":language/:controller/:action/*"
routes.general.reqs.language = "[A-z][A-z]"
routes.general.defaults.controller = "index"
routes.general.defaults.action = "index"

routes.register.route = ":language/register/*"
routes.register.reqs.language = "[A-z][A-z]"
routes.register.defaults.controller = "index"
routes.register.defaults.action = "register"

routes.activateUser.route = ":language/activate/:uid/:hash"
routes.activateUser.reqs.language = "[A-z][A-z]"
routes.activateUser.defaults.controller = "index"
routes.activateUser.defaults.action = "activate-user"

routes.login.route = ":language/login"
routes.login.reqs.language = "[A-z][A-z]"
routes.login.defaults.language = "pl"
routes.login.defaults.controller = "index"
routes.login.defaults.action = "login"

routes.logout.route = ":language/logout"
routes.logout.reqs.language = "[A-z][A-z]"
routes.logout.defaults.language = "pl"
routes.logout.defaults.controller = "index"
routes.logout.defaults.action = "logout"

routes.remindPassword.route = ":language/remind-password"
routes.remindPassword.reqs.language = "[A-z][A-z]"
routes.remindPassword.defaults.language = "pl"
routes.remindPassword.defaults.controller = "index"
routes.remindPassword.defaults.action = "login"

routes.remindPasswordInstruction.route = ":language/remind-password-instruction"
routes.remindPasswordInstruction.reqs.language = "[A-z][A-z]"
routes.remindPasswordInstruction.defaults.language = "pl"
routes.remindPasswordInstruction.defaults.controller = "index"
routes.remindPasswordInstruction.defaults.action = "login"
routes.remindPasswordInstruction.defaults.showRemindInstruction = "true"

routes.remindPasswordConfirm.route = ":language/remind-password-confirm/:uid/:hash"
routes.remindPasswordConfirm.reqs.language = "[A-z][A-z]"
routes.remindPasswordConfirm.defaults.language = "pl"
routes.remindPasswordConfirm.defaults.controller = "index"
routes.remindPasswordConfirm.defaults.action = "login"
routes.remindPasswordConfirm.defaults.confirmRemind = "true"

routes.aboutUs.route = ":language/about-us"
routes.aboutUs.reqs.language = "[A-z][A-z]"
routes.aboutUs.defaults.language = "pl"
routes.aboutUs.defaults.controller = "index"
routes.aboutUs.defaults.action = "about-us"

routes.aboutUsWork.route = ":language/about-us-work"
routes.aboutUsWork.reqs.language = "[A-z][A-z]"
routes.aboutUsWork.defaults.language = "pl"
routes.aboutUsWork.defaults.controller = "index"
routes.aboutUsWork.defaults.action = "about-us-work"

routes.cookiesPolicy.route = ":language/cookies-policy"
routes.cookiesPolicy.reqs.language = "[A-z][A-z]"
routes.cookiesPolicy.defaults.language = "pl"
routes.cookiesPolicy.defaults.controller = "index"
routes.cookiesPolicy.defaults.action = "cookies-policy"

routes.locations.route = ":language/contact"
routes.locations.reqs.language = "[A-z][A-z]"
routes.locations.defaults.language = "pl"
routes.locations.defaults.controller = "index"
routes.locations.defaults.action = "locations"

routes.locationDetails.route = ":language/contact/:id/:description"
routes.locationDetails.reqs.language = "[A-z][A-z]"
routes.locationDetails.defaults.language = "pl"
routes.locationDetails.defaults.controller = "index"
routes.locationDetails.defaults.action = "location-details"

routes.export.route = ":language/export"
routes.export.reqs.language = "[A-z][A-z]"
routes.export.defaults.language = "pl"
routes.export.defaults.controller = "index"
routes.export.defaults.action = "export"

routes.financing.route = ":language/financing"
routes.financing.reqs.language = "[A-z][A-z]"
routes.financing.defaults.language = "pl"
routes.financing.defaults.controller = "index"
routes.financing.defaults.action = "financing"

routes.service.route = ":language/service"
routes.service.reqs.language = "[A-z][A-z]"
routes.service.defaults.language = "pl"
routes.service.defaults.controller = "index"
routes.service.defaults.action = "service"

routes.showCar.route = ":language/show/:id/*"
routes.showCar.reqs.language = "[A-z][A-z]"
routes.showCar.defaults.language = "pl"
routes.showCar.defaults.controller = "cars"
routes.showCar.defaults.action = "show"

routes.showCarBySrId.route = ":language/show-sr/:id/*"
routes.showCarBySrId.reqs.language = "[A-z][A-z]"
routes.showCarBySrId.defaults.language = "pl"
routes.showCarBySrId.defaults.controller = "cars"
routes.showCarBySrId.defaults.action = "show-by-sr"

routes.showCarByOfferId.route = "show-offer-id/:id/*"
routes.showCarByOfferId.reqs.language = "[A-z][A-z]"
routes.showCarByOfferId.defaults.language = "pl"
routes.showCarByOfferId.defaults.controller = "cars"
routes.showCarByOfferId.defaults.action = "show-by-offer-id"

routes.showCarWithDescription.route = ":language/:id/:description"
routes.showCarWithDescription.reqs.language = "[A-z][A-z]"
routes.showCarWithDescription.defaults.language = "pl"
routes.showCarWithDescription.defaults.controller = "cars"
routes.showCarWithDescription.defaults.action = "show"
routes.showCarWithDescription.reqs.id = "\d+"

routes.showCarTestDrive.route = ":language/:id/:description/test-drive"
routes.showCarTestDrive.reqs.language = "[A-z][A-z]"
routes.showCarTestDrive.defaults.language = "pl"
routes.showCarTestDrive.defaults.controller = "cars"
routes.showCarTestDrive.defaults.action = "show"
routes.showCarTestDrive.reqs.id = "\d+"
routes.showCarTestDrive.defaults.testDrive = 1

routes.forceShowCarTestDrive.route = ":language/:id/:description/show-test-drive"
routes.forceShowCarTestDrive.reqs.language = "[A-z][A-z]"
routes.forceShowCarTestDrive.defaults.language = "pl"
routes.forceShowCarTestDrive.defaults.controller = "cars"
routes.forceShowCarTestDrive.defaults.action = "show"
routes.forceShowCarTestDrive.reqs.id = "\d+"
routes.forceShowCarTestDrive.defaults.forceShowTestDrive = 1

routes.forceShowCarContactCartaker.route = ":language/:id/:description/show-contact-cartaker"
routes.forceShowCarContactCartaker.reqs.language = "[A-z][A-z]"
routes.forceShowCarContactCartaker.defaults.language = "pl"
routes.forceShowCarContactCartaker.defaults.controller = "cars"
routes.forceShowCarContactCartaker.defaults.action = "show"
routes.forceShowCarContactCartaker.reqs.id = "\d+"
routes.forceShowCarContactCartaker.defaults.forceShowContactCartaker = 1

routes.showCarComment.route = ":language/:id/:description/comment"
routes.showCarComment.reqs.language = "[A-z][A-z]"
routes.showCarComment.defaults.language = "pl"
routes.showCarComment.defaults.controller = "cars"
routes.showCarComment.defaults.action = "show"
routes.showCarComment.reqs.id = "\d+"
routes.showCarComment.defaults.comment = 1

routes.showCarMakeOffer.route = ":language/:id/:description/make-offer"
routes.showCarMakeOffer.reqs.language = "[A-z][A-z]"
routes.showCarMakeOffer.defaults.language = "pl"
routes.showCarMakeOffer.defaults.controller = "cars"
routes.showCarMakeOffer.defaults.action = "show"
routes.showCarMakeOffer.reqs.id = "\d+"
routes.showCarMakeOffer.defaults.makeOffer = 1

routes.forceShowCarMakeOffer.route = ":language/:id/:description/show-make-offer"
routes.forceShowCarMakeOffer.reqs.language = "[A-z][A-z]"
routes.forceShowCarMakeOffer.defaults.language = "pl"
routes.forceShowCarMakeOffer.defaults.controller = "cars"
routes.forceShowCarMakeOffer.defaults.action = "show"
routes.forceShowCarMakeOffer.reqs.id = "\d+"
routes.forceShowCarMakeOffer.defaults.forceShowMakeOffer = 1

routes.showCarSendOffer.route = ":language/:id/:description/send-offer"
routes.showCarSendOffer.reqs.language = "[A-z][A-z]"
routes.showCarSendOffer.defaults.language = "pl"
routes.showCarSendOffer.defaults.controller = "cars"
routes.showCarSendOffer.defaults.action = "show"
routes.showCarSendOffer.reqs.id = "\d+"
routes.showCarSendOffer.defaults.sendOffer = 1

routes.forceShowCarSendOffer.route = ":language/:id/:description/show-send-offer"
routes.forceShowCarSendOffer.reqs.language = "[A-z][A-z]"
routes.forceShowCarSendOffer.defaults.controller = "cars"
routes.forceShowCarSendOffer.defaults.action = "show"
routes.forceShowCarSendOffer.reqs.id = "\d+"
routes.forceShowCarSendOffer.defaults.forceShowCarSendOffer = 1

routes.showCarMakeReservation.route = ":language/:id/:description/make-reservation"
routes.showCarMakeReservation.reqs.language = "[A-z][A-z]"
routes.showCarMakeReservation.defaults.language = "pl"
routes.showCarMakeReservation.defaults.controller = "cars"
routes.showCarMakeReservation.defaults.action = "show"
routes.showCarMakeReservation.reqs.id = "\d+"
routes.showCarMakeReservation.defaults.makeReservation = 1

routes.oldList.route = ":language/list/:page/*"
routes.oldList.reqs.language = "[A-z][A-z]"
routes.oldList.defaults.language = "pl"
routes.oldList.defaults.controller = "cars"
routes.oldList.defaults.action = "list"
routes.oldList.defaults.page = 1
routes.oldList.reqs.page = "\d+"

routes.getmodels.route = ":language/getmodels"
routes.getmodels.reqs.language = "[A-z][A-z]"
routes.getmodels.defaults.language = "pl"
routes.getmodels.defaults.controller = "cars"
routes.getmodels.defaults.action = "getmodels"

routes.getInstalments.route = ":language/instalments"
routes.getInstalments.reqs.language = "[A-z][A-z]"
routes.getInstalments.defaults.language = "pl"
routes.getInstalments.defaults.controller = "cars"
routes.getInstalments.defaults.action = "get-instalments"

routes.searchList.route = ":language/search-list"
routes.searchList.reqs.language = "[A-z][A-z]"
routes.searchList.defaults.controller = "cars"
routes.searchList.defaults.action = "search-list"


routes.editSearchList.route = ":language/search-edit/:hash"
routes.editSearchList.reqs.language = "[A-z][A-z]"
routes.editSearchList.defaults.language = "pl"
routes.editSearchList.defaults.controller = "cars"
routes.editSearchList.defaults.action = "search-edit"
routes.editSearchList.defaults.hash = 0
routes.editSearchList.reqs.hash = "\w+"

routes.favouriteList.route = ":language/favourite-list/:page/*"
routes.favouriteList.reqs.language = "[A-z][A-z]"
routes.favouriteList.defaults.controller = "cars"
routes.favouriteList.defaults.action = "favourite-list"
routes.favouriteList.defaults.page = 1
routes.favouriteList.reqs.page = "\d+"

routes.favouriteListSession.route = ":language/favourites/:page/*"
routes.favouriteListSession.reqs.language = "[A-z][A-z]"
routes.favouriteListSession.defaults.controller = "cars"
routes.favouriteListSession.defaults.action = "favourite-list-session"
routes.favouriteListSession.defaults.page = 1
routes.favouriteListSession.reqs.page = "\d+"

routes.myCars.route = ":language/my-cars/:page/*"
routes.myCars.reqs.language = "[A-z][A-z]"
routes.myCars.defaults.controller = "cars"
routes.myCars.defaults.action = "my-cars"
routes.myCars.defaults.page = "1"
routes.myCars.reqs.page = "\d+"

routes.cronNewsletter.route = "cron/newsletter"
routes.cronNewsletter.defaults.controller = "cron"
routes.cronNewsletter.defaults.action = "newsletter"

routes.myReservations.route = ":language/my-reservations/:page/*"
routes.myReservations.reqs.language = "[A-z][A-z]"
routes.myReservations.defaults.controller = "cars"
routes.myReservations.defaults.action = "my-reservations"
routes.myReservations.defaults.page = 1
routes.myReservations.reqs.page = "\d+"

routes.racingTeam.route = ":language/racing-team"
routes.racingTeam.reqs.language = "[A-z][A-z]"
routes.racingTeam.defaults.controller = "racing-team"
routes.racingTeam.defaults.action = "index"

routes.haveCarFound.route = ":language/have-car-found"
routes.haveCarFound.reqs.language = "[A-z][A-z]"
routes.haveCarFound.defaults.language = "pl"
routes.haveCarFound.defaults.controller = "cars"
routes.haveCarFound.defaults.action = "have-car-found"

routes.sellCar.route = ":language/sell/:page/*"
routes.sellCar.reqs.language = "[A-z][A-z]"
routes.sellCar.defaults.language = "pl"
routes.sellCar.defaults.controller = "index"
routes.sellCar.defaults.action = "sell-car"
routes.sellCar.defaults.page = 1
routes.sellCar.reqs.page = "\d+"

routes.exchangeCar.route = ":language/sell/exchange/:page/*"
routes.exchangeCar.reqs.language = "[A-z][A-z]"
routes.exchangeCar.defaults.language = "pl"
routes.exchangeCar.defaults.controller = "index"
routes.exchangeCar.defaults.action = "sell-car"
routes.exchangeCar.defaults.exchange = "true"
routes.exchangeCar.defaults.page = 1
routes.exchangeCar.reqs.page = "\d+"

routes.deleteSearchByHashInit.route = ":language/delete-search-by-hash-start/:hash"
routes.deleteSearchByHashInit.reqs.language = "[A-z][A-z]"
routes.deleteSearchByHashInit.defaults.language = "pl"
routes.deleteSearchByHashInit.defaults.controller = "cars"
routes.deleteSearchByHashInit.defaults.action = "have-car-found"
routes.deleteSearchByHashInit.defaults.isDeleteFromNL = 1

routes.deleteSearchByHash.route = ":language/delete-search-by-hash/:hash"
routes.deleteSearchByHash.reqs.language = "[A-z][A-z]"
routes.deleteSearchByHash.defaults.language = "pl"
routes.deleteSearchByHash.defaults.controller = "cars"
routes.deleteSearchByHash.defaults.action = "delete-search-by-hash"

routes.deleteSearchByMail.route = ":language/delete-search-by-mail"
routes.deleteSearchByMail.reqs.language = "[A-z][A-z]"
routes.deleteSearchByMail.defaults.language = "pl"
routes.deleteSearchByMail.defaults.controller = "cars"
routes.deleteSearchByMail.defaults.action = "delete-search-by-mail"

routes.newCars.route = ":language/new-car/:carId/"
routes.newCars.reqs.language = "[A-z][A-z]"
routes.newCars.defaults.language = "pl"
routes.newCars.defaults.controller = "index"
routes.newCars.defaults.action = "new-car"
routes.newCars.defaults.carId = 0
routes.newCars.reqs.carId = "\d+"

routes.sellExchangeCarList.route = ":language/sell-exchange-car-list/:page/*"
routes.sellExchangeCarList.reqs.language = "[A-z][A-z]"
routes.sellExchangeCarList.defaults.language = "pl"
routes.sellExchangeCarList.defaults.controller = "index"
routes.sellExchangeCarList.defaults.action = "sell-exchange-car-list"
routes.sellExchangeCarList.defaults.page = 1
routes.sellExchangeCarList.reqs.page = "\d+"

routes.list.route = ":language/list/:type/:make/:model"
routes.list.reqs.language = "[A-z][A-z]"
routes.list.reqs.type = "(osobowe|dostawcze|inne)"
routes.list.defaults.language = "pl"
routes.list.defaults.controller = "cars"
routes.list.defaults.action = "list"
routes.list.defaults.type = null
routes.list.defaults.make = null
routes.list.defaults.model = null



[staging : production]
[testing : production]
[development : production]