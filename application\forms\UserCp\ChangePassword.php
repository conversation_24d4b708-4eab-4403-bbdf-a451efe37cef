<?php

class Form_UserCp_ChangePassword extends My_Form {

	public function init() {
		$this->addElements(array(
			new Zend_Form_Element_Password('current_pass', array(
				'label'	=>	'CURRENT_PASSWORD',
				'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 6, 'max' => 64, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Password('new_pass_1', array(
				'label'	=>	'NEW_PASSWORD_1',
				'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 6, 'max' => 64, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Password('new_pass_2', array(
				'label'	=>	'NEW_PASSWORD_2',
				'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 6, 'max' => 64, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label' => 'CHANGE_PASSWORD_SUBMIT'
			)),
		));
		parent::init();
	}//init
	
	public function isValid($data) {
		$ret = parent::isValid($data);
		$isValid = true;
		
		$users = new Model_Users();
		
		if (!$users->isValidPassword(Zend_Auth::getInstance()->getIdentity()->id, $data['current_pass'])) {
			$isValid = false;
			$this->current_pass->addError('INVALID_CURRENT_PASSWORD');
		}
		
		if ($data['new_pass_1'] != $data['new_pass_2']) {
			$isValid = false;
			$errKey = "PASSWORDS_MUST_MATCH";
			$this->new_pass_1->addError($errKey);
			$this->new_pass_2->addError($errKey);
		}
		
		return $ret && $isValid;
	}

}