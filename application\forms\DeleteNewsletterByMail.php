<?php

class Form_DeleteNewsletterByMail extends My_Form {

	public function init() {
		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'required' => true,
				'validators' => array(new Zend_Validate_EmailAddress()),
				'attribs' => array('class' => 'bolder')
			)),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('submit', array(
				'label'	=>	'DELETE_BY_HASH_SUGGEST_EDIT_BUTTON'
			)),
            new Zend_Form_Element_Submit('submit2', array(
				'label'	=>	'DELETE'
			)),
		));
		
		parent::init();
	}//init

}