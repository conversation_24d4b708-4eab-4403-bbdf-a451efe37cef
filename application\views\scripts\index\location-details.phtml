<div id="contact">

    <div class="row py-4">
        <div class="col-lg-12">

            <h1 class="title"> <?= $this->location['location_group']['business_name'] . ' ' . $this->translate->_('LOCATION_POINT') . ' ' . $this->location['name'] ?> </h1>

        </div>

    </div>

    <div class="row">
        <div class="col-lg-3">
            <ul class="left-menu list-unstyled">
                <li><a href="<?= $this->url(array('language' => $this->language), 'list', true) ?>"><?= $this->translate->_('CAR') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language), 'financing', true) ?>"><?= $this->translate->_('FINANCING') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language), 'aboutUs', true) ?>"><?= $this->translate->_('COMPANY') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language), 'aboutUsWork', true) ?>"><?= $this->translate->_('ABOUT_US_WORK') ?></a></li>
                <li><a href="<?= $this->url(array('language' => $this->language), 'racingTeam', true) ?>"><?= $this->translate->_('RACING_TEAM') ?></a></li>
                <li><a<?= $this->leftMenu == 'contact' ? ' class="active"' :'' ?> href="<?= $this->url(array('language' => $this->language), 'locations', true) ?>"><?= $this->translate->_('CONTACT') ?></a></li>
            </ul>
        </div>
        <div class="col-lg-9">
            <div class="row pt-4">
                <div class="col-lg-12">



                    <div id="content_static" class="column_right_1">
                        <?php if (false && $this->html): ?>
                            <?= $this->html ?>
                        <?php else: ?>
                            <?php ob_start(); ?>

                            <div class="location-details mb-5">
                                <div>

                                    <h2 class="mb-4"><?= $this->location['location_group']['business_name'] . ' ' . $this->translate->_('LOCATION_POINT') . ' ' . $this->location['name'] ?>
                                    </h2>
                                    <p class="lower"><?= $this->location['location_group']['address'] ?></span></a></p>
                                    <p> <?= $this->location['location_group']['zip_code'] . " " . $this->location['location_group']['city'] ?></p>
                                </div>
                                <div class="details mt-auto">

                                    <?php if (!empty($this->location['phone'])): ?>
                                       <p><i class="fa fa-phone" aria-hidden="true"></i> <?= $this->location['phone'] ?></p>
                                        <?php endif ?>

                                    <?php if (!empty($this->location['email'])): ?>
                                      <p><a href="mailto:<?= $this->location['email'] ?>"><i class="fa fa-envelope" aria-hidden="true"></i> <?= $this->location['email'] ?></a></p>
                                        <?php endif ?>
                                </div>


                            </div>


                                <h3 class="mb-4"><?= $this->translate->_('CONSULTANTS') ?></h3>
                        <div class="row">
                                <?php foreach ($this->employees as $employee): ?>
                                <div class="col-lg-6">
                                    <div class="consultant mb-3 d-flex">
                                        <div class="image d-flex justify-content-center align-items-center">
                                            <?php if (!empty($employee['photo_basename'])): ?>
                                                <!-- /images/employees/ -->
                                                <a  class="d-flex justify-content-center align-items-center" href="/images/employees/<?= $employee['photo_basename'] . '_L.' . $employee['photo_extension'] ?>" rel="prettyPhoto[gallery]" title="<?= $this->escape($employee['first_name'] . ' ' . $employee['last_name']) ?>">
                                                    <img class="img-fluid" src="<?= $this->domain ?>/images/employees/<?= $employee['photo_basename'] . '_M.' . $employee['photo_extension'] ?>" alt="<?= $this->escape($employee['first_name'] . ' ' . $employee['last_name']) ?>" />
                                                </a>
                                            <?php else: ?>
                                                <div class="">
                                                    <i class="fa fa-user-o" aria-hidden="true"></i>
                                                </div>

                                            <?php endif ?>
                                        </div>



                                        <div class="data">
                                            <div class="name"><?= $employee['first_name'] . ' ' . $employee['last_name'] ?></div>
                                            <?php if (!empty($employee['position'])): ?>
                                                <div class="position"><?= $employee['position'] ?></div>

                                            <?php endif ?>

                                            <div class="details">
                                            <?php if (!empty($employee['phone'])): ?>
                                                <div class="phone"><i class="fa fa-phone" aria-hidden="true"></i> <?= $employee['phone'] ?></div>

                                            <?php endif ?>

                                            <?php if (!empty($employee['email'])): ?>
                                                <div class="position"><i class="fa fa-envelope" aria-hidden="true"></i> <a href="mailto:<?= $employee['email'] ?>"><?= $employee['email'] ?></a></div>

                                            <?php endif ?>

                                            <?php if (count($employee['languages'])): ?>
                                                        <div class="languages">
                                                <?= $this->translate->_('EMPLOYEE_SPEAKS_LANGUAGES') ?>:
                                               <?= implode($employee['languages'], ', ') ?>
                                                        </div>
                                            <?php endif ?>
                                            </div>
                                        </div>
                                        <div class="clear"></div>
                                    </div>
                                </div>
                                <?php endforeach ?>
                        </div>
                            </div>
                            <?php
                            $html = ob_get_flush();
                            $this->cache->save($html, 'location_' . $this->location['location_id'] . '_' . $this->language, $tags=array('location', $this->language, 'location_' . $this->location['location_id'], 'employee', 'translate'))
                            ?>
                        <?php endif ?>
                        <div class="clear"></div>
                    </div>


                </div>

            </div>

        </div>
    </div>

</div>



