<?php

class Form_SearchItemNoIdentity extends My_Form {

	public function init() {
		$cars = new Model_Cars_Cars();
		$view = Zend_Layout::getMvcInstance()->getView();
                $empl = new Model_Employees();
                $tr = Zend_Registry::get('Zend_Translate');






		$this->addElements(array(
			new Zend_Form_Element_Text('email', array(
				'label'	=>	'EMAIL',
				'type' => "email",
				'required' => true,
				'validators' => array(
					new Zend_Validate_EmailAddress(),
					new Zend_Validate_StringLength(array('min' => 2, 'max' => 128, 'encoding' => 'UTF-8')),
				),
				'attribs' => array('class' => 'email', 'placeholder' => 'Wpisz e-mail')
			)),
			new Zend_Form_Element_Hidden('title', array(
				'label'	=>	'SEARCH_NAME',
				'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 0, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Hidden('newsletter', array(
				'label'	=>	'NEWSLETTER_ON_OFF',
				'validators' => array(),
				'value' => 1
			)),
			new Zend_Form_Element_Captcha('captcha', array(
				'label' => 'CAPTCHA',
				'captcha' => new My_Captcha_Math(array(
					'timeout' => '180',
				)),
			)),
			/*
			new Zend_Form_Element_Captcha('captcha', array(
				'label' => 'CAPTCHA',
				'captcha' => new My_Captcha_ReCaptcha(array(
					'siteKey'  => '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-',
					'secretKey' => '6Lfx7A0UAAAAAL41t8hFSPCzuh6VQGhcpXttwrJd'
				)),
				'attribs' => array('class' => 'email')
			)),

			*/
		
            new Zend_Form_Element_Select('added_by_sr_id', array(
                    'label'	=>	'FAVOURITE_SALESMAN',
                    'multiOptions' => array('' => $tr->_('FAVOURITE_SALESMAN'). ' - '. strtolower($tr->_('ANY'))) + $empl->getAll(array('sr_assoc' => true)),
                    'required' => false,
            )),


			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('save', array(
				'label' => 'SAVE'
			))
		));
		parent::init();
		/*
            $this->setElementDecorators(array(
                'ViewHelper',
                'Errors',
                array('Description', array('tag' => 'p', 'class' => 'description')),
            ),array('captcha'),false);


            $this->captcha->removeDecorator('Label');
            $this->captcha->removeDecorator('HtmlTag');*/




	}

}