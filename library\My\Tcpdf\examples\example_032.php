<?php
//============================================================+
// File name   : example_032.php
// Begin       : 2008-06-09
// Last Update : 2009-09-30
// 
// Description : Example 032 for TCPDF class
//               EPS/AI image
// 
// Author: <PERSON>
// 
// (c) Copyright:
//               <PERSON>
//               Tecnick.com s.r.l.
//               Via <PERSON>, 11
//               09044 Quartucciu (CA)
//               ITALY
//               www.tecnick.com
//               <EMAIL>
//============================================================+

/**
 * Creates an example PDF TEST document using TCPDF
 * @package com.tecnick.tcpdf
 * @abstract TCPDF - Example: EPS/AI image
 * <AUTHOR>
 * @copyright 2004-2009 <PERSON>nick.com S.r.l (www.tecnick.com) <PERSON>, 11 - 09044 - <PERSON>ua<PERSON>ucci<PERSON> (CA) - ITALY - www.tecnick.com - <EMAIL>
 * @link http://tcpdf.org
 * @license http://www.gnu.org/copyleft/lesser.html LGPL
 * @since 2008-06-09
 */

require_once('../config/lang/eng.php');
require_once('../tcpdf.php');

// create new PDF document
$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false); 

// set document information
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Nicola Asuni');
$pdf->SetTitle('TCPDF Example 032');
$pdf->SetSubject('TCPDF Tutorial');
$pdf->SetKeywords('TCPDF, PDF, example, test, guide');

// disable header and footer
$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);

// set default monospaced font
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);

//set margins
$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);

//set auto page breaks
$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

//set image scale factor
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO); 

//set some language-dependent strings
$pdf->setLanguageArray($l); 

// ---------------------------------------------------------

// set font
$pdf->SetFont('helvetica', '', 14);

// Page 1: AI
$pdf->AddPage();
$pdf->ImageEps('../images/tiger.ai', 10, 50, 190);

// Page 2: EPS, with link
$pdf->AddPage();
$pdf->ImageEps('../images/bug.eps', 0, 25, 0, 240, "http://www.tcpdf.org", true, 'T', 'C');

// Page 3: AI
$pdf->AddPage();
$pdf->ImageEps('../images/pelican.ai', 15, 70, 180);

// ---------------------------------------------------------

//Close and output PDF document
$pdf->Output('example_032.pdf', 'I');

//============================================================+
// END OF FILE                                                 
//============================================================+
?>
