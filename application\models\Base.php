<?php
class Model_Base
{
    protected $db;
	protected $error_msg;
	protected $tables;
	protected $service_account_id;
    
	function __construct()  
	{
		$this->db = Zend_Db_Table_Abstract::getDefaultAdapter();
		
		$options = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		
		// Database tables
		$this->tables = array(
			'archive_cars'						=>	'aa_archive_cars',
			'archive_cars_descriptions'			=>	'aa_archive_cars_descriptions',
			'archive_cars_descriptions_values'	=>	'aa_archive_cars_descriptions_values',
			'archive_cars_extras'				=>	'aa_archive_cars_extras',
			'archive_cars_favourites'			=>	'aa_archive_cars_favourites',
			'archive_cars_features'				=>	'aa_archive_cars_features',
			'archive_cars_photos'				=>	'aa_archive_cars_photos',
            'archive_cars_vipauto_photos'		=>	'aa_archive_cars_vipauto_photos',
			'archive_cars_reservations'			=>	'aa_archive_cars_reservations',
			'archive_cars_videos'				=>	'aa_archive_cars_videos',
			'cars'								=>	'aa_cars',
			'cars_descriptions'					=>	'aa_cars_descriptions',
			'cars_descriptions_values'			=>	'aa_cars_descriptions_values',
			'cars_extras'						=>	'aa_cars_extras',
			'cars_favourites'					=>	'aa_cars_favourites',
			'cars_features'						=>	'aa_cars_features',
			'cars_fuel_consumption'				=>	'aa_cars_fuel_consumption',
            'cars_new'                          =>	'aa_cars_new',
            'cars_photos'                       =>	'aa_cars_photos',
			'cars_vipauto_photos'				=>	'aa_cars_vipauto_photos',
			'cars_reservations'					=>	'aa_cars_reservations',
			'cars_videos'						=>	'aa_cars_videos',
			'cars_video_providers'				=>	'aa_cars_video_providers',
			'car_makes'							=>	'aa_car_makes',
			'car_models'						=>	'aa_car_models',
            'cars_exchange_models'			    =>	'aa_cars_exchange_models',
			'car_model_versions'				=>	'aa_car_model_versions',
			'car_types'							=>	'aa_car_types',
			'colours'							=>	'aa_colours',
			'companies'							=>	'aa_companies',
			'countries'							=>	'aa_countries',
			'employees'							=>	'aa_employees',
			'employee_languages'				=>	'aa_employee_languages',
            'employees_financing_locations'		=>	'aa_employees_financing_locations',
			'employees_languages'				=>	'aa_employees_languages',
			'fuel_types'						=>	'aa_fuel_types',
			'gearbox_types'						=>	'aa_gearbox_types',
			'languages'							=>	'aa_languages',
			'locations'							=>	'aa_locations',
			'location_groups'					=>	'aa_location_groups',
			'log'								=>	'aa_log',
			'payments'							=>	'aa_payments',
			'translations'						=>	'aa_translations',
			'users'								=>	'aa_users',
            'users_cars_models_for_exchange'	=>	'aa_users_cars_models_for_exchange',
            'users_cars_sell_exchange'	        =>	'aa_users_cars_sell_exchange',
            'users_cars_buy'	                =>	'aa_users_cars_buy',
			'user_searches'						=>	'aa_user_searches',
			'vehicle_categories'				=>	'aa_vehicle_categories',
			'vehicle_descriptions'				=>	'aa_vehicle_descriptions',
			'vehicle_description_categories'	=>	'aa_vehicle_description_categories',
			'vehicle_extras'					=>	'aa_vehicle_extras',
			'vehicle_features'					=>	'aa_vehicle_features',
			'currency_values'					=>	'aa_currency_values',
            'advertising_bar'                   =>  'aa_advertising_bar'
		);		
	}
	
	public static function getDb() {
		return Zend_Db_Table_Abstract::getDefaultAdapter();
	}
}