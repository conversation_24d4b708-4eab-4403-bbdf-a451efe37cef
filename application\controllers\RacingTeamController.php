<?php

class RacingTeamController extends Zend_Controller_Action {
	
	public function init() {
		$this->view->breadcrumb = array(
			$this->view->url(array('language' => $this->view->language), 'racingTeam', true) => $this->view->translate->_('RACING_TEAM')
		);
	}
	
	public function indexAction() {
		$this->_redirect('/pl/racing-team/gallery');
		$this->view->containerClass = "with_photo";
	}
	
	public function galleryAction() {
		$this->view->breadcrumb += array(
			$this->view->url(array('language' => $this->view->language, 'controller' => 'racing-team', 'action' => 'gallery'), 'general', true) => $this->view->translate->_('GALLERY')
		);
	}
	
}