<?php

class Model_Users extends Model_Base {
	
	public function activate($userId, $hash) {
		$retVal = false;
		
		$select = $this->db->select()
			->from($this->tables['users'])
			->where('id = ' . (int)$userId)
			->where('activation_hash = ?', $hash);
		$user = $this->db->fetchRow($select);
		
		if (empty($user)) {
			return $retVal;
		}
		
		$this->db->update(
			$this->tables['users'],
			array(
				'activation_hash' => new Zend_Db_Expr('NULL')
			),
			"id = " . (int)$userId
		);
		
		$retVal = true;
		
		return $retVal;
	}
	
	public function addBySalesman($data, $srId, $doTransaction=false) {
		try {
			if ($doTransaction) $this->db->beginTransaction();

			$password = "";
			$charPool = '0123456789-=abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+[]{};:,./<>?';
			for($p = 0; $p < 6; $p++) {
				$password .= $charPool[mt_rand(0,strlen($charPool)-1)];
			}
			
			$addData = array(
				'email' => $data['email'],
				'first_name' => $data['first_name'],
				'last_name' => $data['last_name'],
				'address' => $data['address'],
				'zip_code' => $data['zip_code'],
				'city' => $data['city'],
				'country' => $data['country'],
				'phone' => $data['phone'],
				'added_by_sr_id' => $srId,
                'export_to_sr' => 1
			);
			
			$addData += array(
				'date_added' => new Zend_Db_Expr('NOW()'),
				'user_salt' => substr(md5(microtime()), 0, 8),
				'activation_hash' => new Zend_Db_Expr('NULL')
			);
			
			$salt = Zend_Registry::get('password_salt');
			$addData['password'] = sha1($salt . $password . $addData['user_salt']);
			
			//$addData = My_DbNullify::nullify($addData);
			
			$this->db->insert(
				$this->tables['users'],
				$addData
			);
			
			$userId = $this->db->lastInsertId($this->tables['users'], 'id');
			
			$req = Zend_Controller_Front::getInstance()->getRequest();
			$lang = $req->getParam('language');
					
			$translate = Zend_Registry::get('Zend_Translate');
			$msg = $translate->_('NEW_PASSWORD_EMAIL_BODY');
			$msg = str_replace("%password%", $password, $msg);
			
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($data['email'])
				->setBodyText($msg)
				->setSubject($translate->_('NEW_PASSWORD_EMAIL_SUBJECT'));
			$mail->send();

            $user = $this->getUser($userId);

            if ($user['export_to_sr'] == 1) {
                $exportSr = new Model_ImportExport_Export();
                try {
                    ob_start();//just in case
                    $success = $exportSr->sendClientEdit($user);
                    ob_end_clean();//just in case

                    if (!$success) {
                        throw new Exception("Success is false in " . __METHOD__ . ", line " . (__LINE__ - 1));
                    }


                }
                catch(Exception $e) {
                    try {
                        $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
                        $this->logger = new Zend_Log();
                        $this->logger->addWriter(new Zend_Log_Writer_Stream($opt['synchronization']['sr']['export']['logfile']));
                        $this->logger->log(PHP_EOL . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);


                    }
                    catch(Exception $e) {
                        try {
                            //$mail = new Zend_Mail("UTF-8");
                            //$mail->addTo('<EMAIL>')->setSubject('Error www_auto')->setBodyText($e->getMessage() . "\n\n" . $e->getTraceAsString());

                        }
                        catch (Exception $e) {
                            //give up

                        }
                    }
                }
            }
			
			if ($doTransaction) $this->db->commit();
			
			return $userId;
		}
		catch (Exception $e) {
			if ($doTransaction) $this->db->rollBack();
			throw $e;
		}
	}
	
	public function addQuick($data, $doTransaction=false) {
		try {
			if ($doTransaction) $this->db->beginTransaction();

			$password = "";
			$charPool = '0123456789-=abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+[]{};:,./<>?';
			for($p = 0; $p < 6; $p++) {
				$password .= $charPool[mt_rand(0,strlen($charPool)-1)];
			}
			
			$baseData = array(
				'first_name' => '',
				'last_name' => '',
                'company_name' => '',
				'address' => '',
				'zip_code' => '',
				'city' => '',
				'country' => '',
				'phone' => '',
			);
			
			$data = array_merge($baseData, $data);
			
			$addData = array(
				'email' => $data['email'],
				'first_name' => $data['first_name'],
				'last_name' => $data['last_name'],
				'address' => $data['address'],
				'zip_code' => $data['zip_code'],
				'city' => $data['city'],
                'company_name' => $data['company_name'],
				'country' => $data['country'],
				'phone' => $data['phone'],
                'export_to_sr' => 1, //zawsze eksportujemy
			);

            if(isset($data['added_by_sr_id']) && !empty($data['added_by_sr_id'])) {
                $addData['added_by_sr_id'] = $data['added_by_sr_id'];
            }
			
			$addData += array(
				'date_added' => new Zend_Db_Expr('NOW()'),
				'user_salt' => substr(md5(microtime()), 0, 8),
				'activation_hash' => new Zend_Db_Expr('NULL')
			);
			
			$salt = Zend_Registry::get('password_salt');
			$addData['password'] = sha1($salt . $password . $addData['user_salt']);
			
			//$addData = My_DbNullify::nullify($addData);
			
			$this->db->insert(
				$this->tables['users'],
				$addData
			);
			
			$userId = $this->db->lastInsertId($this->tables['users'], 'id');
			
			$req = Zend_Controller_Front::getInstance()->getRequest();
			$lang = $req->getParam('language');
					
			$translate = Zend_Registry::get('Zend_Translate');
			$msg = $translate->_('NEW_PASSWORD_EMAIL_BODY');
			$msg = str_replace("%password%", $password, $msg);
			
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($data['email'])
				->setBodyText($msg)
				->setSubject($translate->_('NEW_PASSWORD_EMAIL_SUBJECT'));
                        
            $mail->send();

            $user = $this->getUser($userId);

            if ($user['export_to_sr'] == 1) {
                $exportSr = new Model_ImportExport_Export();
                try {
                    ob_start();//just in case
                    $success = $exportSr->sendClientEdit($user);
                    ob_end_clean();//just in case

                    if (!$success) {
                        throw new Exception("Success is false in " . __METHOD__ . ", line " . (__LINE__ - 1));
                    }

                }
                catch(Exception $e) {
                    try {
                        $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
                        $this->logger = new Zend_Log();
                        $this->logger->addWriter(new Zend_Log_Writer_Stream($opt['synchronization']['sr']['export']['logfile']));
                        $this->logger->log(PHP_EOL . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);

                    }
                    catch(Exception $e) {
                        try {
                            //$mail = new Zend_Mail("UTF-8");
                            //$mail->addTo('<EMAIL>')->setSubject('Error www_auto')->setBodyText($e->getMessage() . "\n\n" . $e->getTraceAsString());
                        }
                        catch (Exception $e) {
                            //give up
                        }
                    }
                }
            }
			
			if ($doTransaction) $this->db->commit();
			
			return $userId;
		}
		catch (Exception $e) {
			if ($doTransaction) $this->db->rollBack();
			throw $e;
		}
	}
	
	public function changePassword($userId, $data) {
		$user = $this->getUser((int)$userId);
		if (empty($user)) {
			throw new Exception("User not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		
		$salt = Zend_Registry::get('password_salt');
		$userSalt = $user['user_salt'];
		
		$newPassword = sha1($salt . $data['new_pass_1'] . $userSalt);
		
		$this->db->update(
			$this->tables['users'],
			array('password' => $newPassword),
			"id = " . (int)$user['id']
		);
	}

	public function delFavourite($carId, $userId) {
		$carId = (int)$carId;
		try {
			$select = $this->db->select()
				->from($this->tables['cars_favourites'])
				->where('car_id=?', $carId)
                ->where('user_id = ?', $userId);

			$favouriteCheck = $this->db->fetchRow($select);
			if (!$favouriteCheck or !array_key_exists('id', $favouriteCheck) or $favouriteCheck['user_id'] != $userId) {
				return false;
			} else {
				$this->db->delete($this->tables['cars_favourites'], $this->db->quoteInto('id=?', $favouriteCheck['id']));
			}
			return $favouriteId;
		}
		catch (Exception $e) {
			throw $e;
		}
	}

    public function delFavourites($userId) {
        try {

                return $this->db->delete($this->tables['cars_favourites'], $this->db->quoteInto('user_id=?', $userId));
        }
        catch (Exception $e) {
            throw $e;
        }
    }
	
	public function deleteSearchByHash($hash) {
		try {
			if (empty($hash)) {
				return false;
			}
			
			$this->db->update(
				$this->tables['user_searches'],
				array('newsletter' => 0, 'is_deleted' => 1),
				$this->db->quoteInto('hash = ?', $hash)
			);
			
			return true;
			
		} catch (Exception $e) {
			return false;
		}
	}
	
	public function deleteSearchesByEmail($email) {
		try {
			if (empty($email)) {
				return false;
			}
			
			$select = $this->db->select()
				->from(array('u' => $this->tables['users']), array('id'))
				->where('email = ?', $email);
			$userId = $this->db->fetchOne($select);
			
			if (empty($userId)) {
				return false;
			}
			
			$this->db->update(
				$this->tables['user_searches'],
				array('newsletter' => 0, 'is_deleted' => 1),
				"user_id = " . (int)$userId
			);
			
			return true;
		}
		catch (Exception $e) {
			return false;
		}
	}

	public function delSearch($searchId, $userId) {
		$searchId = (int)$searchId;
		try {
			$select = $this->db->select()
				->from($this->tables['user_searches'])
				->where('id=?', $searchId);

			$searchCheck = $this->db->fetchRow($select);
			if (!$searchCheck or !array_key_exists('id', $searchCheck) or $searchCheck['user_id'] != $userId) {
				return false;
			} else {
				$this->db->delete($this->tables['user_searches'], $this->db->quoteInto('id=?', $searchCheck['id']));
			}
			return $searchId;
		}
		catch (Exception $e) {
			throw $e;
		}
	}

	public function edit($id, $data, $doExport=true) {
		$columns = array('company_name', 'first_name', 'last_name', 'nip_or_pesel', 'regon', 'id_document', 'id_type', 'address', 'zip_code', 'city', 'country', 'phone', 'is_deleted');
		$updateData = array();
		foreach ($columns as $key) {
			if (array_key_exists($key, $data)) {
				$updateData[$key] = empty($data[$key]) && !is_numeric($data[$key]) ? new Zend_Db_Expr('NULL') : $data[$key];
			}
		}

        // zawsze eksportujemy
        $updateData['export_to_sr'] = 1;

		$this->db->update(
			$this->tables['users'],
			$updateData,
			"id = " . (int)$id
		);
		
		
		$user = $this->getUser((int)$id);
		
		//identity may be missing if edit triggered by sys2 merge clients operation;
		//otherwise update identity for editing user
		if (Zend_Auth::getInstance()->hasIdentity()) {
			$newIdentity = new stdClass();
			foreach (Zend_Auth::getInstance()->getIdentity() as $key => $value) {
				if (array_key_exists($key, $user)) {
					$newIdentity->{$key} = $user[$key];
				}
			}
			$newIdentity->role = "user";
			
			$auth = Zend_Auth::getInstance();
			$auth->setStorage(new Zend_Auth_Storage_Session($namespace = "Zend_Auth_WWWAuto"));
			$auth->clearIdentity();
			$storage = $auth->getStorage();
			$storage->write($newIdentity);
		}
		
		if ($doExport && $user['export_to_sr'] == 1) {		
			$exportSr = new Model_ImportExport_Export();
			try {
				ob_start();//just in case
				$success = $exportSr->sendClientEdit($user);
				ob_end_clean();//just in case
				
				if (!$success) {
					throw new Exception("Success is false in " . __METHOD__ . ", line " . (__LINE__ - 1));
				}
				
				return true;
			}
			catch(Exception $e) {
				try {
					$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
					$this->logger = new Zend_Log();
					$this->logger->addWriter(new Zend_Log_Writer_Stream($opt['synchronization']['sr']['export']['logfile']));
					$this->logger->log(PHP_EOL . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);
					
					return false;
				}
				catch(Exception $e) {
					try {
						//$mail = new Zend_Mail("UTF-8");
						//$mail->addTo('<EMAIL>')->setSubject('Error www_auto')->setBodyText($e->getMessage() . "\n\n" . $e->getTraceAsString());
						return false;
					}
					catch (Exception $e) {
						//give up
						return false;
					}
				}
			}
		}
	}

	public function editSearch($search_id, $data, $hash=null) {
		$search = null;
		if ($search_id) {
			$search = $this->getUserStoredSearch(Zend_Auth::getInstance()->getIdentity()->id, $search_id);
		}
		else if ($hash) {
			$search = $this->getUserStoredSearch(null, null, $hash);
		}
		
		if (empty($search) || empty($search['id'])) {
			throw new Exception("Search not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}

		$searchData = array('newsletter' => $data['newsletter'], 'title' => $data['title']);
		$data['expire'] = 0;
		switch($data['expire']) {
			case 1:
				$searchData['expire_date'] = date("Y-m-d", mktime(0, 0, 0, date('m'), date('d') + 7, date('Y')));
				break;
			case 2:
				$searchData['expire_date'] = date("Y-m-d", mktime(0, 0, 0, date('m') + 1, date('d'), date('Y')));
				break;
			case 3:
				$searchData['expire_date'] = date("Y-m-d", mktime(0, 0, 0, date('m'), date('d'), date('Y') + 1));
				break;
			case 0:
			default:
				$searchData['expire_date'] = null;
		}
		$searchData = My_DbNullify::nullify($searchData);
		
		$cars = new Model_Cars_Cars();
		$searchParameters = $data;
		unset($searchParameters['title']);
		unset($searchParameters['newsletter']);
		$searchString = serialize($cars->searchPrepareParameters($searchParameters));
		
		$searchData['search_data'] = $searchString;
		
		$this->db->update(
			$this->tables['user_searches'],
			$searchData,
			"id = " . $search['id']
		);

		return true;
	}
	
	public function getClientsForSys2($options) {
		$select = $this->db->select()
			->from(array('u' => $this->tables['users']))
			->joinLeft(array('us' => $this->tables['user_searches']),
				'us.user_id = u.id',
				array()
		);
		if ((int)$options['added_by_sr_id']) {
			$select->where("u.added_by_sr_id = " . (int)$options['added_by_sr_id']);
		}
		if ((int)$options['for_salesman_sr_id']) {
			$select->where("u.added_by_sr_id = " . (int)$options['added_by_sr_id'] . " OR us.added_by_sr_id = " . (int)$options['for_salesman_sr_id']);
		}
		
		$ret = array();
		$ret['users'] = $this->db->fetchAll($select);
		
		$select = $this->db->select()
			->from(array('us' => $this->tables['user_searches']))
			->join(array('u' => $this->tables['users']),
				'us.user_id = u.id',
				array()
		);
		if ((int)$options['added_by_sr_id']) {
			$select->where("u.added_by_sr_id = " . (int)$options['added_by_sr_id']);
		}
		if ((int)$options['for_salesman_sr_id']) {
			$select->where("u.added_by_sr_id = " . (int)$options['added_by_sr_id'] . " OR us.added_by_sr_id = " . (int)$options['for_salesman_sr_id']);
		}
		
		$ret['searches'] = $this->db->fetchAll($select);
		
		return $ret;
	}

	public function getSearchesWithCleanup($date=null, $searchId=null, $carId=null, $carDateFrom=null, $carDateTo=null) {
		if (!$date) {
			$date = date("Y-m-d");
		}

		//Czyszczenie przeterminowanych wyszukań
		$this->db->update(
			$this->tables['user_searches'],
			array('newsletter' => 0),
			$this->db->quoteInto('expire_date IS NOT NULL and expire_date<?', $date)
		);

		$select = $this->db->select()
			->from(array('us' => $this->tables['user_searches']))
			->joinLeft(array('u' => $this->tables['users']),
				'us.user_id = u.id',
				array('first_name', 'last_name', 'email', 'phone')
			)
			->where('newsletter=?', 1)
            ->where('us.is_deleted = 0')
			->order(array("id DESC", "expire_date ASC"));
			
		if ((int)$searchId > 0) {
			$select->where('us.id = ' . (int)$searchId);
		}


		$searches = $this->db->fetchAll($select);

		$cars = new Model_Cars_Cars();

		$onlyToday = true;
		if ((int)$searchId > 0 || ($carDateFrom && $carDateTo)) {
			$onlyToday = false;
		}

		$userCarsArray = array();
		foreach($searches as $userSearch) {
			$userCarsArray[] = array(
				'first_name' => $userSearch['first_name'],
				'last_name' => $userSearch['last_name'],
				'email' => $userSearch['email'],
                'phone' => $userSearch['phone'],
				'search_name' => $userSearch['title'],
				'employee_sr_id' => $userSearch['added_by_sr_id'],
				'hash' => $userSearch['hash'],
                'language' => $userSearch['language'],
                'added_datetime' => $userSearch['added_datetime'],
				'cars' => $cars->search(unserialize($userSearch['search_data']), false, false, true, $onlyToday,$carId,false,$carDateFrom,$carDateTo)
			);
		}

		return $userCarsArray;
	}

	public function getUser($id) {
		$select = $this->db->select()
			->from($this->tables['users'])
			->where('id = ' . (int)$id);
		return $this->db->fetchRow($select);
	}

    public function getUserByEmail($email) {
		$select = $this->db->select()
			->from($this->tables['users'])
			->where('email = ?' , $email);
		return $this->db->fetchRow($select);
	}
	public function getUserStoredSearch($user_id=null, $search_id=null, $hash=null, $email=null) {
		$select = $this->db->select()
			->from(array("us" => $this->tables['user_searches']))
            ->where('us.is_deleted = 0');

		if ($search_id) {
			$select->where('id=?', $search_id);
			if ($user_id) {
				$select->where('user_id = ?', $user_id);
			}
			return $this->db->fetchRow($select);
		} elseif ($hash) {
			$select->where('hash=?', $hash);
			return $this->db->fetchRow($select);
		} elseif($email) {
			$select
            
            ->join(array("u" => $this->tables['users']), 'u.id = us.user_id')
            ->where('email = ?', $email);
			return $this->db->fetchAll($select);
		}
        else {
			$select->where('user_id = ?', $user_id);
			return $this->db->fetchAll($select);
		}
	}

	public function getUserFavourites($userId, $returnSelect=false) {
		$select = $this->db->select()
			->from(array('cf' => $this->tables['cars_favourites']))
			->joinLeft(array('c' => $this->tables['cars']),
				'cf.car_id = c.car_id',
				array('car_id', 'sr_car_id', 'price' => 'IFNULL(LEAST(price, promotion_price), price)', 'price_currency', 'price_type_key', 'build_year', 'first_registration_year', 'odometer', 'power', 'power_unit', 'cubic_capacity', 'power_unit', 'position', 'title', 'description', 'ownership_type', 'leasing', 'credit_collateral', 'is_reserved', 'is_reserved_hidden', 'is_sold_hidden', 'status','is_new_car' => new Zend_Db_Expr('CASE c.location_id WHEN '.  Model_Cars_Cars::NEW_CAR_LOCATION.' THEN 1 ELSE 0 END'))
			)
			->joinLeft(array('mk' => $this->tables['car_makes']),
				'mk.id = c.make_id',
				array('make_name' => 'name')
			)
			->joinLeft(array('md' => $this->tables['car_models']),
				'md.id = c.model_id',
				array('model_name' => 'name')
			)
			->joinLeft(array('ph' => $this->tables['cars_photos']),
				'ph.car_id=c.car_id AND ord=1',
				array('filename_base', 'filename_extension'))
			->joinLeft(array('loc' => $this->tables['locations']),
				'c.location_id = loc.location_id',
				array('short_name', 'location_group_id', 'name')
			)
			->joinLeft(array('locgr' => $this->tables['location_groups']),
				'loc.location_group_id = locgr.id',
				array('address')
			)
			->joinLeft(array('em' => $this->tables['employees']),
				'em.sr_id = c.caretaker_sr_id',
				array('caretaker_first_name' => 'first_name', 'caretaker_last_name' => 'last_name', 'caretaker_email' => 'email', 'caretaker_phone' => 'phone', 'caretaker_visible' => 'visible')
			)
			->where('user_id=?', $userId);

		return $returnSelect ? $select : $this->db->fetchAll($select);
	}

	public function isComplete($id) {
		$id = (int)$id;
		$user = $this->getUser($id);
		if (empty($user)) {
			throw new Exception("User empty in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		else {
			return 
			!empty($user['last_name']) &&
			!empty($user['address']) &&
			!empty($user['zip_code']) &&
			!empty($user['city']) &&
			(!empty($user['nip_or_pesel']) || !empty($user['id_document']));
		}
	}

	public function isSearchStored($searchParameters, $userId) {
		$searchString = serialize($searchParameters);

		$select = $this->db->select()
				->from($this->tables['user_searches'])
				->where('user_id=?', $userId)
				->where('search_data=?', $searchString);

		$searchCheck = $this->db->fetchRow($select);

		if ($searchCheck and is_array($searchCheck)) {
			return $searchCheck['id'];
		} else {
			return false;
		}
	}

	public function isValidPassword($userId, $password) {
		$user = $this->getUser((int)$userId);
		if (empty($user)) {
			throw new Exception("User not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		
		$salt = Zend_Registry::get('password_salt');
		$hashed = sha1($salt . $password . $user['user_salt']);
		
		return $hashed == $user['password'];
	}
	
	public function login($data) {
		$email = $data['email'];
		$pass = isset($data['password']) ? $data['password'] : false;
		
		$auth = Zend_Auth::getInstance();
		$auth->setStorage(new Zend_Auth_Storage_Session($namespace = "Zend_Auth_WWWAuto"));
		
		$authAdapter = new Zend_Auth_Adapter_DbTable($this->db);
		
		
        if($pass)
        {
            $authAdapter->setTableName($this->tables['users'])
                ->setIdentityColumn('email')
                ->setCredentialColumn('password');

            $authAdapter->setIdentity($email)
                ->setCredential($pass)
                ->setCredentialTreatment(
                    'sha1(concat(' . Zend_Db_Table_Abstract::getDefaultAdapter()->quoteInto('?', Zend_Registry::get('password_salt')) . ', ?, user_salt))'
            );
        }
        else
        {
            $authAdapter->setTableName($this->tables['users'])
			->setIdentityColumn('email')
			->setCredentialColumn('is_deleted');
		
            $authAdapter->setIdentity($email)
                ->setCredential('0');
            //	->setCredentialTreatment(
            //		'sha1(concat(' . Zend_Db_Table_Abstract::getDefaultAdapter()->quoteInto('?', Zend_Registry::get('password_salt')) . ', ?, user_salt))'
            //);
            
        }
		$result = $auth->authenticate($authAdapter);
				
		if ($result->isValid())
		{
			$resultRow = $authAdapter->getResultRowObject(array(
				'id',
				'email',
				'first_name',
				'last_name',
                'company_name',
				'nip_or_pesel',
				'regon',
				'id_document',
				'id_type',
				'address',
				'zip_code',
				'city',
				'country',
				'phone',
				'is_deleted',
				'activation_hash'
			));
			$resultRow->role = "user"; //other roles available only through sys2->www2 login mechanism
			if($pass)
                $resultRow->passwordPassed = true;
            else
                $resultRow->passwordPassed = false;
            
			$storage = $auth->getStorage();
			$storage->write($resultRow);
			
			// Check if user is enabled and activated
			if ($auth->getIdentity()->is_deleted != 0 || !empty($auth->getIdentity()->activation_hash))
			{
				$auth->clearIdentity();
				return false;
			}
			else
			{
				return true;
			}				
		}//isValid
		else {
			return false;
		}
	}
	
	public function logout() {
		$auth = Zend_Auth::getInstance();
		$auth->clearIdentity();
	}
	
	public function register($data, $doTransaction=true) {
		try {
			if ($doTransaction) $this->db->beginTransaction();
			
			
			
                        $data['password'] = "";
			$charPool = '0123456789-=abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+[]{};:,./<>?';
			for($p = 0; $p < 6; $p++) {
				$data['password'] .= $charPool[mt_rand(0,strlen($charPool)-1)];
			}
                        
                        unset($data['captcha']);
			$password = $data['password'];
                        
			$data += array(
				'date_added' => new Zend_Db_Expr('NOW()'),
				'user_salt' => substr(md5(microtime()), 0, 8),
				//'activation_hash' => md5(microtime() . "zmyłka") //aktywacja usunieta 29.06.2011 - niepotrzebna
			);
			
			$salt = Zend_Registry::get('password_salt');
			$data['password'] = sha1($salt . $data['password'] . $data['user_salt']);
			unset($data['password_repeat']);
			
			$data = My_DbNullify::nullify($data);
			
			$this->db->insert(
				$this->tables['users'],
				$data
			);
			
			$userId = $this->db->lastInsertId($this->tables['users'], 'id');
			
			/*
				//aktywacja usunieta 29.06.2011
			$req = Zend_Controller_Front::getInstance()->getRequest();
			$lang = $req->getParam('language');
			
			$view = Zend_Layout::getMvcInstance()->getView();
			$link = $view->url(array('language' => $lang, 'uid' => $userId, 'hash' => $data['activation_hash']), 'activateUser', true);
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$domain = $opt['general']['domain'];
			$link = $domain . $link;
			
			$translate = Zend_Registry::get('Zend_Translate');
			$msg = $translate->_('REGISTRATION_CONFIRMATION_EMAIL_BODY');
			$msg = str_replace("%link%", $link, $msg);
			
			$mail = new Zend_Mail($charset="UTF-8");
			$mail->addTo($data['email'])
				->setBodyText($msg)
				->setSubject($translate->_('REGISTRATION_CONFIRMATION_EMAIL_SUBJECT'));
			$mail->send();
			*/
			
			if ($doTransaction) $this->db->commit();
			
			$this->login(array(
				'email' => $data['email'],
				'password' => $password
			));
		}
		catch (Exception $e) {
			if ($doTransaction) $this->db->rollBack();
			throw $e;
		}
	}
	
	public function resetPassword($userId, $hash) {
		$select = $this->db->select()
			->from($this->tables['users'])
			->where('id = ' . (int)$userId)
			->where('remind_password_hash = ?', $hash);
		$user = $this->db->fetchRow($select);
		
		if (empty($user)) {
			return false;
		}
		
		$random = substr(md5(time() . $userId), 0, 8);
		$salt = Zend_Registry::get('password_salt');
		$userSalt = $user['user_salt'];
		
		$newPass = sha1($salt . $random . $userSalt);
		
		$this->db->update(
			$this->tables['users'],
			array('password' => $newPass, 'remind_password_hash' => new Zend_Db_Expr('NULL')),
			"id = " . $user['id']
		);
		
		$req = Zend_Controller_Front::getInstance()->getRequest();
		$lang = $req->getParam('language');
		
		$translate = Zend_Registry::get('Zend_Translate');
		$msg = $translate->_('NEW_PASSWORD_EMAIL_BODY');
		$msg = str_replace("%password%", $random, $msg);
		
		$mail = new Zend_Mail($charset="UTF-8");
		$mail->addTo($user['email'])
			->setBodyText($msg)
			->setSubject($translate->_('NEW_PASSWORD_EMAIL_SUBJECT'));
		$mail->send();
		
		return true;
	}
	
	public function resetPasswordStart($data) {
		$email = $data['email'];
		$select = $this->db->select()
			->from($this->tables['users'])
			->where('email = ?', $email);
		$user = $this->db->fetchRow($select);
		
		if (empty($user)) {
			throw new Exception("User not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}
		
		$hash = md5(time() . $email);
		$this->db->update(
			$this->tables['users'],
			array('remind_password_hash' => $hash),
			"id = " . $user['id']
		);
		
		$req = Zend_Controller_Front::getInstance()->getRequest();
		$lang = $req->getParam('language');
		
		$view = Zend_Layout::getMvcInstance()->getView();
		$link = $view->url(array('language' => $lang, 'uid' => $user['id'], 'hash' => $hash), 'remindPasswordConfirm', true).'?show_password=1';
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$domain = Zend_Registry::get('siteDomain');
		$link = $domain . $link;
		
		$translate = Zend_Registry::get('Zend_Translate');
		$msg = $translate->_('RESET_PASSWORD_EMAIL_BODY');
		$msg = str_replace("%link%", $link, $msg);
		//$msg = "Wiadomość. Należałoby pociągnąć z translate z jakimiś znacznikami zmiennej z hashem: " . $userId . " / " . $data['activation_hash'];
		
		$mail = new Zend_Mail($charset="UTF-8");
		$mail->addTo($data['email'])
			->setBodyText($msg)
			->setSubject($translate->_('RESET_PASSWORD_EMAIL_SUBJECT'));
		$mail->send();
	}

	public function saveFavourite($carId, $userId) {
		$select = $this->db->select()
			->from($this->tables['users'])
			->where('id = ?', $userId);
		$user = $this->db->fetchRow($select);
		
		if (empty($user)) {
			throw new Exception("User not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}

		$select = $this->db->select()
			->from($this->tables['cars'])
			->where('car_id = ?', $carId);
		$car = $this->db->fetchRow($select);
		
		if (empty($car)) {
			throw new Exception("Car not found in " . __METHOD__ . ", line " . (__LINE__ - 1));
		}

		$select = $this->db->select()
			->from($this->tables['cars_favourites'])
			->where('car_id = ?', $carId)
			->where('user_id = ?', $userId);
		$carCheck = $this->db->fetchRow($select);

		if (!$carCheck or !is_array($carCheck)) {
			$favouriteData = array('user_id' => $userId, 'car_id' => $carId);
			$favouriteData = My_DbNullify::nullify($favouriteData);

			$this->db->insert(
				$this->tables['cars_favourites'],
				$favouriteData
			);	
			return $this->db->lastInsertId($this->tables['cars_favourites'], 'id');
		} else {
			return $carCheck['id'];
		}

	}

    public function getFavouritesIds() {

        if (Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity() &&  Zend_Auth::getInstance()->getIdentity()->role == 'user') {
            $select = $this->db->select()
                ->from($this->tables['cars_favourites'], array('car_id'))
                ->where('user_id = ?', Zend_Auth::getInstance()->getIdentity()->id);
            return $this->db->fetchCol($select);
        } else {
            $favouriteNamespace = new Zend_Session_Namespace('favourite_cars');
            if (!isset($favouriteNamespace->cars) || !is_array($favouriteNamespace->cars)) {
                $favouriteNamespace->cars = array();
            }
            return $favouriteNamespace->cars;
        }



    }

	public function saveSearch($data, $searchParameters, $userId) {
		$searchString = serialize($searchParameters);
		
		$hash = sha1(serialize($data) . serialize($searchParameters) . serialize($userId));
		
		if ($userId === null) {
			//user not logged in - check existence and add new if necessary
			$select = $this->db->select()
				->from($this->tables['users'], array('id'))
				->where('email = ?', $data['email']);
			$userId = $this->db->fetchOne($select);
			
			if (!$userId) {
				$userId = $this->addQuick($data, $trans=false);
			}
		}

		$select = $this->db->select()
				->from($this->tables['user_searches'])
				->where('user_id=?', $userId)
                ->where('is_deleted=0')
				->where('search_data=?', $searchString);

		$searchCheck = $this->db->fetchRow($select);

		if ($searchCheck && is_array($searchCheck)) {
			return false;
		}

		try {
			$data['expire'] = 0;//infinite; na zyczenie RM 2011.07.04
			$searchData = array('user_id' => $userId, 'search_data' => $searchString, 'title' => $data['title'], 'newsletter' => $data['newsletter'], 'added_by_sr_id' => ($data['added_by_sr_id'] ? $data['added_by_sr_id'] : new Zend_Db_Expr('NULL')));
			switch($data['expire']) {
				case 1:
					$searchData['expire_date'] = date("Y-m-d", mktime(0, 0, 0, date('m'), date('d') + 7, date('Y')));
					break;
				case 2:
					$searchData['expire_date'] = date("Y-m-d", mktime(0, 0, 0, date('m') + 1, date('d'), date('Y')));
					break;
				case 3:
					$searchData['expire_date'] = date("Y-m-d", mktime(0, 0, 0, date('m'), date('d'), date('Y') + 1));
					break;
				case 0:
				default:
					$searchData['expire_date'] = null;
			}
			$searchData['added_datetime'] = new Zend_Db_Expr('NOW()');
            $searchData['language'] = Zend_Registry::get('translate_language');
            
			$searchData = My_DbNullify::nullify($searchData);

			$this->db->insert(
				$this->tables['user_searches'],
				$searchData + array('hash' => $hash)
			);	
			return $this->db->lastInsertId($this->tables['user_searches'], 'id');
		}
		catch (Exception $e) {
			throw $e;
		}
	}

	public function saveSearchBySalesman($data, $searchParameters) {
		$searchString = serialize($searchParameters);
		
		$hash = sha1(serialize($data) . serialize($searchParameters));

		$srId = null;
		if (Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->sr_id) {
			$srId = (int)Zend_Auth::getInstance()->getIdentity()->sr_id;
		}
			
		try {
			$this->db->beginTransaction();
			
			$select = $this->db->select()
				->from($this->tables['users'], array('id'))
				->where('email = ?', $data['email']);
			$userId = $this->db->fetchOne($select);
			
			if (!$userId) {
				$userId = $this->addBySalesman($data, $srId, $trans=false);
			}
	
			$select = $this->db->select()
					->from($this->tables['user_searches'])
					->where('user_id=?', $userId)
					->where('search_data=?', $searchString);
	
			$searchCheck = $this->db->fetchRow($select);
	
			if ($searchCheck and is_array($searchCheck)) {
				return false;
			}
			$data['expire'] = 0;//infinite; na zyczenie RM 2011.07.04
			$searchData = array('user_id' => $userId, 'search_data' => $searchString, 'title' => $data['title'], 'newsletter' => $data['newsletter']);
			switch($data['expire']) {
				case 1:
					$searchData['expire_date'] = date("Y-m-d", mktime(0, 0, 0, date('m'), date('d') + 7, date('Y')));
					break;
				case 2:
					$searchData['expire_date'] = date("Y-m-d", mktime(0, 0, 0, date('m') + 1, date('d'), date('Y')));
					break;
				case 3:
					$searchData['expire_date'] = date("Y-m-d", mktime(0, 0, 0, date('m'), date('d'), date('Y') + 1));
					break;
				case 0:
				default:
					$searchData['expire_date'] = null;
			}
			$searchData['added_datetime'] = new Zend_Db_Expr('NOW()');
			$searchData['added_by_sr_id'] = $srId;
            $searchData['language'] = Zend_Registry::get('translate_language');
			$searchData = My_DbNullify::nullify($searchData);

			$this->db->insert(
				$this->tables['user_searches'],
				$searchData + array('hash' => $hash)
			);
			
			$searchId = $this->db->lastInsertId($this->tables['user_searches'], 'id');
			
			$this->db->commit();
			
			return $searchId;
		}
		catch (Exception $e) {
			$this->db->rollBack();
			throw $e;
		}
	}

}