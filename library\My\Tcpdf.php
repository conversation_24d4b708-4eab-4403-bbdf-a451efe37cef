<?php

require_once('Tcpdf/config/lang/eng.php');
require_once('Tcpdf/tcpdf.php');

class My_Tcpdf {
	
	public function generateCarDetails($htmlContent) {
		$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, "iso-8859-1"); 
		
		$pdf->SetCreator(PDF_CREATOR);
		$pdf->SetAuthor("AutoAuto");
		$pdf->SetTitle("Auto");
		$pdf->SetSubject("Auto");
		
		$pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, '', '');
		$pdf->setHeaderFont(array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
		$pdf->setFooterFont(array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
		$pdf->Set<PERSON>argins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
		$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
		$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
		
		$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
		$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
		$pdf->AliasNbPages();
		$pdf->AddPage();
		
		$pdf->SetFont("dejavusans", "", 22);
		$pdf->writeHTML($htmlContent, true, 0, true, true);
		
		$pdf->lastPage();
		$pdf->Output("auto.pdf", "I", "I");
		
		return;
	}
	
	public function generateCarList($htmlContent) {
		$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, "iso-8859-1"); 
		
		$pdf->SetCreator(PDF_CREATOR);
		$pdf->SetAuthor("AutoAuto");
		$pdf->SetTitle("List");
		$pdf->SetSubject("List");
		
		$pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, '', '');
		$pdf->setHeaderFont(array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
		$pdf->setFooterFont(array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
		$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
		$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
		$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
		
		$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
		$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
		$pdf->AliasNbPages();
		$pdf->AddPage();
		
		$pdf->SetFont("dejavusans", "", 8);
		$pdf->writeHTML($htmlContent, true, 0, true, true);
		
		$pdf->lastPage();
		$pdf->Output("list.pdf", "I", "I");
		
		return;
	}
    
    public function generateReservationFromWww($htmlContent, $fileName, $number, $couponHtml=null) {
		$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, "utf-8"); 
		
		$pdf->SetCreator(PDF_CREATOR);
		$pdf->SetAuthor("AutoAuto");
		$pdf->SetTitle("PRZEDWSTĘPNA UMOWA SPRZEDAŻY");
		$pdf->SetSubject("PRZEDWSTĘPNA UMOWA SPRZEDAŻY");
		
		$pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, '', 'PRZEDWSTEPNA UMOWA SPRZEDAZY');
		$pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
		$pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
		$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
		$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
		$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
		
		$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
		$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
		$pdf->setPrintFooter(false);
		$pdf->AliasNbPages();
		$pdf->AddPage();
		
		$pdf->SetFont("dejavusans", "", 9);
		$pdf->writeHTML($htmlContent, true, 0, true, true);
		
		if ($couponHtml) {
			$pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, '', '                                              KUPON');
			$pdf->addPage();
			$pdf->writeHTML($couponHtml, true, 0, true, true);
		}
		
		$pdf->lastPage();
		$pdf->Output($fileName, "I");
		
		return "PUS.WWW.05.2011";
	  
	}//reservation
}