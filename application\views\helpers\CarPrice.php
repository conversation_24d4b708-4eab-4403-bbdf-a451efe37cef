<?php

class Zend_View_Helper_CarPrice extends Zend_View_Helper_Abstract {
	public function carPrice($car, $language_row, $currency=false, $reducedInfo = false, $priceTypeKey=false, $customPrice = false) {
        
        $language_row['multiplier'] = 1;
        $language_row['currency'] = 'PLN';
        $language_row['symbol_placement'] = 'APPEND';
        $tr = Zend_Registry::get('Zend_Translate');
        if (isset($car['is_new_car']) && $car['is_new_car'] == 1)
        {
            if($currency)
                return '';

            return $tr->_('DEALERS_AUCTION');
        }
		if ($car['price_currency'] != 'PLN') {
			return $this->view->currency($car['price']) . " " . $car['price_currency'];
		}
		else {

            if($customPrice) {
			    $price = $customPrice;
            } else {
                $price = $car['price'] ;

                if($priceTypeKey !== false) {
                    if($priceTypeKey == 'brutto' && $car['price_type_key'] = 'netto') {
                        $price = $price * 1.23;
                    }
                } else {
                    $priceTypeKey = $car['price_type_key'];
                }

            }
			if($currency)
			{
				$cv = new Model_CurrencyValues();
				$exchange  = $cv->getCurrentCurrencyValue($currency);
		
				if($exchange)
					$price /= $exchange;
				else 
					return false;
			}

			$ret = $this->view->currency($price  * $language_row['multiplier']) ;
			if ($language_row['symbol_placement'] == 'PREPEND') {
				$ret = $language_row['currency'] . " " . $ret;
			}
			else {
				if($currency)
				{
					if($currency == 'EUR')
					$ret = "€ " . $ret;
				else 
					$ret .= " " . $currency;
				}
				else
					$ret .= " " . $language_row['currency'];
			}
			$ret .= " " . $this->view->translate->_($priceTypeKey);

            if($reducedInfo) {
                $priceChangeDatetime = new DateTime($car['price_change_datetime']);
                if($priceChangeDatetime > new DateTime('-1 month')) {



                    $price = $car['price'];

                    if($car['promotion_price'] == $car['price']) {
                        $priceLast = $car['promotion_price_last'];
                    } else {
                        $priceLast = $car['price_last'];
                    }
                    if($price <  $priceLast && (($price / $priceLast) < 0.95)) {


                        $reducedPrice = ($priceLast - $price);

                        $ret .= sprintf('<div class="reduced">%s %s %s</div>',
                            $tr->_('REDUCED_BY'), $this->view->currency($reducedPrice),$language_row['currency']);
                    }
                }
            }


			return $ret;
		}
	}
}