
<div id="top_menu" data-layout="el/top_menu">

    <div id="top_menu_left">
        <div class="item" id="top_menu_search">
            <form method="post" action="<?= $this->url(array('language' => $this->language), 'list', true); ?>">
                <input type="submit" name="" value="" id="find_car_quick_submit">
                <input type="text" name="query" value="" id="find_car_quick_value" />

            </form>
        </div>
        <div id="top_slogan">
            <?php
            $str = $this->translate->_('TOP_SLOGAN');
            $strParts = explode("%", $str);
            $i = 1;
            if (count($strParts) == 3) { ?>
                <span class="blue">1500</span>
                <?= $strParts[0] ?>
                <span class="red">7</span>
                <?= $strParts[1] ?>
                <span class="green">3</span>
                <?= $strParts[2] ?>

            <?php }	?>
        </div>
    </div>
    <div id="top_menu_right">
        <?php $cache = Zend_Registry::get('Cache'); ?>





        <div id="top_menu_languages">
            <?php foreach ($this->getEnabledLanguages() as $key => $value): ?>
                <div class="language language_<?= $key ?><? if ($key == $this->language):?> active<? endif; ?>">

                        <a href="<?= $this->url(array('language' => $key), null, false) ?><?php echo (!empty($this->changeLanguageData['queryString']) ? (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash']."&" : "?" ) . $this->changeLanguageData['queryString'] : (!empty($this->changeLanguageData['hash']) ? "?hash=".$this->changeLanguageData['hash'] : "" ) ) ?>">&nbsp;</a>


                </div>
            <?php endforeach ?>
        </div>
        <div class="float_right">
            <?php if (!empty($this->identity)): ?>
                <?php if ($this->identity->role == "user"): ?>
                    <div class="item favourites">
                        <a href="<?= $this->url(array('language' => $this->language), 'favouriteList', true) ?>">
                            <img src="/images/heart3.png?ver=3" />
                            <span id="favourites-count"><?= $this->favouritesCount ?></span>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="item favourites">
                        <a href="<?= $this->url(array('language' => $this->language), 'favouriteListSession', true) ?>">
                            <img src="/images/heart3.png?ver=3" />
                            <span id="favourites-count"><?= $this->favouritesCount ?></span>
                        </a>
                    </div>
                <?php endif ?>
                <?php if ($this->identity->role == "user"): ?>
                    <div class="item"  id="top_menu_account">
                        <a href="<?= $this->url(array('language' => $this->language, 'controller' => 'user-cp'), 'general', true) ?>"><?= $this->translate->_('USER_CP') ?></a>
                    </div>
                <?php endif ?>
                <div class="item"  id="top_menu_logout">
                    <a href="<?= $this->url(array('language' => $this->language), 'logout', true) ?>"><?= $this->translate->_('LOG_OUT') ?></a>
                </div>
            <?php else: ?>
                <div class="item favourites">
                    <a href="<?= $this->url(array('language' => $this->language), 'favouriteListSession', true) ?>">
                        <img src="/images/heart3.png?ver=2" />
                        <span id="favourites-count"><?= $this->favouritesCount ?></span>
                    </a>
                </div>

            <?php endif ?>
            <?php if (empty($this->identity)): ?>
                <div class="item" id="top_menu_login">
                    <a href="<?= $this->url(array('language' => $this->language), 'login', true) ?>"><?= $this->translate->_('LOG_IN') ?></a>
                </div>
            <?endif;?>
        </div>
     </div>

</div>
