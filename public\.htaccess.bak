SetEnv APPLICATION_ENV testing

RewriteEngine On
RewriteCond %{HTTPS} !=on [OR]
RewriteCond %{HTTP_HOST} !^www\. [NC]
RewriteRule ^ https://www.%{HTTP_HOST#www.}%{REQUEST_URI} [R=301,L]

#RewriteCond %{HTTP_HOST} !^www\.
#RewriteRule ^(.*)$ http://www.%{HTTP_HOST}/$1 [R=301,L]

RedirectMatch 301 ^(.+)/$ $1

RewriteCond %{ENV:REDIRECT_STATUS} ^$
RewriteRule ^index.php$ /$1 [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,9}./([^/]+/)*index.php
RewriteRule ^index.php(.*)$ $1 [R=301,L]

RewriteRule ^pl$ / [R=301,L]
RewriteRule ^index/index/language/pl$ / [R=301,L]
RewriteRule ^index/index/language$ / [R=301,L]
RewriteRule ^index/index$ / [R=301,L]
RewriteRule ^index$ / [R=301,L]

RewriteCond %{REQUEST_FILENAME} -s [OR]
RewriteCond %{REQUEST_FILENAME} -l [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^.*$ - [NC,L]
RewriteRule ^.*$ index.php [NC,L]
