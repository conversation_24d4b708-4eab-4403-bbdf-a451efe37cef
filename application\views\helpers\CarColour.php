<?php

class Zend_View_Helper_CarColour extends Zend_View_Helper_Abstract {
	
	public function carColour($carData) {
		$view = Zend_Layout::getMvcInstance()->getView();
		$ret = "";
		
		if (!empty($carData['colour_key'])) {
			$trKey = 'COLOR_' . $carData['colour_key'];
			$trVal = $view->translate->_($trKey);
			$ret .= $trVal;
		}
		else {
			$ret .= $carData['colour'];
		}
		
		if ($carData['colour_metallic'] == 'y') {
			if (!empty($ret)) $ret .= " ";
			$ret .= "metallic";
		}
		return $ret;
	}
	
}