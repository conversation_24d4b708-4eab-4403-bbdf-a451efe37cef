<?php

class My_Plugin_Auth extends Zend_Controller_Plugin_Abstract {
	
	public function preDispatch(Zend_Controller_Request_Abstract $request) {
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$view = Zend_Layout::getMvcInstance()->getView();
		$view->domain = Zend_Registry::get('siteDomain');
		
		$controller_name = $request->getControllerName();
		$action_name = $request->getActionName();
		$role = 'guest';
        
        if(!isset($_COOKIE['cookie']))
        {
            setcookie('cookie',1, strtotime("+3 year"),'/');
            $view->cookie = false;
        }
        else
            $view->cookie = true;


		/*
		
		$db = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getResource('db');
		$masterSwitch = $db->fetchOne("SELECT `value` FROM `sr_misc_settings` WHERE `key` = 'system_master_switch'");
		if ($masterSwitch != 'on') {
			
			if ($controller_name == 'login' && in_array($action_name, array('index', 'process'))) {}
			else {
				if (Zend_Auth::getInstance()->hasIdentity()) {
					$userId = Zend_Auth::getInstance()->getIdentity()->u_user_id;
					$usersAllowedImploded = $db->fetchOne("SELECT `value` FROM `sr_misc_settings` WHERE `key` = 'can_browse_when_system_off'");
					$usersAllowed = explode(',', $usersAllowedImploded);
					if (!in_array($userId, $usersAllowed)) {
						echo "Przerwa techniczna";
						exit;
					}
				}
				else {
					echo "Przerwa techniczna";
					exit;
				}
			}
		}
		
		*/
		
		$acl = new My_Acl();
		Zend_Registry::set('Acl', $acl);
		
		$view = Zend_Layout::getMvcInstance()->getView();
		$auth = Zend_Auth::getInstance();
		$auth->setStorage(new Zend_Auth_Storage_Session($namespace = "Zend_Auth_WWWAuto"));
		
		if (Zend_Auth::getInstance()->hasIdentity())
		{
			$identity = Zend_Auth::getInstance()->getIdentity();
			$view->identity = $identity;
			
			$role = $identity->role;
		}
		
		if (!$acl->has($controller_name)) {
			throw new Zend_Controller_Dispatcher_Exception('404 - page not found', 404);
			return null;
		}
		
		if(!$acl->isAllowed($role, $controller_name, $action_name)) {
			//echo "not allowed"; exit;
			$redirector = Zend_Controller_Action_HelperBroker::getStaticHelper('Redirector');
			$view = Zend_Layout::getMvcInstance()->getView();
			//$dbgInfo = APPLICATION_ENV == 'development' ? ": " . $role . "/" . $controller_name . "/" . $action_name : "";
			Zend_Controller_Action_HelperBroker::getStaticHelper('FlashMessenger')->addMessage($view->translate->_('NO_PERMISSIONS_TO_SHOW_PAGE'));
			
			if (Zend_Auth::getInstance()->hasIdentity()) {
				$redirector->gotoUrl($view->url(array('language' => $view->language), 'home', true));
			}
			else {
				Zend_Controller_Action_HelperBroker::getStaticHelper('SetRedirBack')->direct('login');
				$request->setControllerName('index')
			      ->setActionName('login');
				$redirector->gotoUrl($view->url(array('language' => $view->language), 'login', true));
			}
	    }
	    else {
	    	//pass some data to the layout
	    	$view->controller_name = $controller_name;
	    	$view->action_name = $action_name;
    	}
		
	}

    public function postDispatch(Zend_Controller_Request_Abstract $request) {

        $view = Zend_Layout::getMvcInstance()->getView();

#        if (Zend_Auth::getInstance()->hasIdentity() && Zend_Auth::getInstance()->getIdentity()->role == "user") {
#            $users = new Model_Users();
#            $userFavourites = $users->getUserFavourites(Zend_Auth::getInstance()->getIdentity()->id, $returnSelect=false);
#            $view->favouritesCount = count($userFavourites);
#        } else {
#
#            $favNS = new Zend_Session_Namespace('favourite_cars');
#            $view->favouritesCount = count($favNS->cars);
#        }


    }

	
}