<?php

class My_Application_Resource_Cache extends Zend_Application_Resource_ResourceAbstract
{
	/**   * Default registry key
	*/
	const DEFAULT_REGISTRY_KEY = 'App_Cache';
	
	/**
	* Inititalize cache resource
	*/
	public function init ()
	{
		die("use zf's built-in cachemanager instead");
		$caches = $this->getOptions();
		foreach ($caches as $cacheIniName => $options) {
			/// create cache instance
			$cache = Zend_Cache::factory(
				$options['frontend']['adapter'],
				$options['backend']['adapter'],        
				$options['frontend']['params'],
				$options['backend']['params']
			);
			
			/// use as default database metadata cache
			if (isset($options['isDefaultMetadataCache']) && true === (bool) $options['isDefaultMetadataCache']) {
				Zend_Db_Table_Abstract::setDefaultMetadataCache($cache);
			}
			
			/// use as default translate cache
			if (isset($options['isDefaultTranslateCache']) && true === (bool) $options['isDefaultTranslateCache']) {
				Zend_Translate::setCache($cache);
			}
			
			/// use as default locale cache
			if (isset($options['isDefaultLocaleCache']) && true === (bool) $options['isDefaultLocaleCache']) {
				Zend_Locale::setCache($cache);
			}
			
			/// add to registry
			$key = (isset($options['registry_key']) && !is_numeric($options['registry_key'])) ? $options['registry_key'] : self::DEFAULT_REGISTRY_KEY;
			Zend_Registry::set($key, $cache);
		}
	}
}