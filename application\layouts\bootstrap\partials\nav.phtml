<?php if(!isset($this->activeMenu)) {

    $this->activeMenu = null;
}
?>
<div class="navbar navbar-top-aa hidden-md-down">
    <div class="container">

        <div class="d-flex align-items-center flex-wrap">
            <div class="mr-auto">
                <span class="slogan mr-auto">
                    <span class="orange-color"><?= $this->allCount ?></span> AUT <span
                        class="orange-color">CZYNNE</span> 7 DNI W TYGODNIU
                 </span>

            </div>
            <div class="mr-5 ">
                <span class="fb-like"><i class="fa fa-facebook mr-3" aria-hidden="true"></i> polub nas</span>
            </div>
            <div>
                <form action="<?= $this->url(array('language' => $this->language), 'list', true) ?>"
                      class="form-inline search-form-top">
                    <input type="text" class="form-control search-input" name="query" placeholder="szukaj">
                    <button type="submit" class="btn btn-search-input"><i class="fa fa-search" aria-hidden="true"></i>
                    </button>
                </form>
            </div>

        </div>
    </div>
</div>

<nav class="navbar navbar-inverse bg-primary py-md-3 py-lg-0 navbar-menu-aa sticky-top navbar-toggleable-md">
    <div class="container">

        <button class="navbar-toggler navbar-toggler-aa" type="button" data-toggle="collapse"
                data-target="#navbarsExampleContainer" aria-controls="navbarsExampleContainer" aria-expanded="false"
                aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <a class="navbar-brand" href="/">
            <img src="/images/logo_autoauto_pl.png"/>
        </a>


        <div class="collapse navbar-collapse mr-4" id="navbarsExampleContainer">
            <ul class="navbar-nav ml-auto top-menu">
                <li class="nav-item<?= $this->menuActive == 'buyCar' ? ' active' : '' ?>">
                    <a class="nav-link active"
                       href="<?= $this->url(array('language' => $this->language), 'list', true) ?>">
                        <?php
                        $text = explode(" ", strtolower($this->translate->_('BUY_CAR')));
                        echo $text[0] . ' <span class="orange-color">' . $text[1] . '</span>';
                        ?>
                    </a>
                </li>


                <li class="nav-item<?= $this->menuActive == 'financeCar' ? ' active' : '' ?>">
                    <a class="nav-link"
                       href=" <?= $this->url(array('language' => $this->language), 'financing', true) ?>">
                        <?php
                        $text = explode(" ", strtolower($this->translate->_('FINANCE_CAR')));
                        echo $text[0] . ' <span class="orange-color">' . $text[1] . '</span>';
                        ?>
                    </a>
                </li>


                <li class="nav-item<?= $this->menuActive == 'sellCar' ? ' active' : '' ?>">
                    <a class="nav-link"
                       href="<?= $this->url(array('language' => $this->language), 'sellCar', true) ?>">
                        <?php
                        $text = explode(" ", strtolower($this->translate->_('SELL_CAR')));
                        echo $text[0] . ' <span class="orange-color">' . $text[1] . '</span>';
                        ?>
                    </a>
                </li>


                <li class="nav-item<?= $this->menuActive == 'contactCar' ? ' active' : '' ?>">
                    <a class="nav-link"
                       href="<?= $this->url(array('language' => $this->language), 'locations', true) ?>">
                        <?= strtolower($this->translate->_('CONTACT')) ?>

                    </a>
                </li>
            </ul>
        </div>

        <div class="d-flex align-items-center hidden-md-down">

            <div class="mr-4">
                <i class="fa aa-heart" aria-hidden="true"></i>
            </div>
            <div class="mr-4">
                <div class="btn-group">
                    <button class="btn btn-secondary btn-language-switcher btn-sm dropdown-toggle" type="button"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <?= strtoupper($this->language) ?>
                    </button>
                    <div class="dropdown-menu">

                        <?php foreach ($this->getEnabledLanguages() as $key => $value): ?>
                            <a href="<?= $this->url(array('language' => $key), 'home', true) ?><?php echo(!empty($this->changeLanguageData['queryString']) ? (!empty($this->changeLanguageData['hash']) ? "?hash=" . $this->changeLanguageData['hash'] . "&" : "?") . $this->changeLanguageData['queryString'] : (!empty($this->changeLanguageData['hash']) ? "?hash=" . $this->changeLanguageData['hash'] : "")) ?>"
                               class="<?= $key ?><? if ($key == $this->language): ?> active<? endif; ?>">
                                <button class="dropdown-item" type="button"><?= strtoupper($key) ?></button>
                            </a>

                        <?php endforeach ?>


                    </div>
                </div>

            </div>

            <div class="">
                <button class="navbar-toggler-aa shortcut-menu-toggler collapsed" type="button" data-toggle="collapse"
                        data-target="#shortcutMenu" aria-controls="shortcutMenu"
                        aria-expanded="false"
                        aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
            </div>
        </div>


    </div>

</nav>