<?php

class Zend_View_Helper_Price extends Zend_View_Helper_Abstract {
	public function price($value, $language_row, $price_type=null) {
		
		$ret = "";
		$ret = $this->view->currency($value * $language_row['multiplier']);
		if ($language_row['symbol_placement'] == 'PREPEND') {
			$ret = $language_row['currency'] . " " . $ret;
		}
		else {
			$ret .= " " . $language_row['currency'];
		}
		if ($price_type !== null) {
			$ret .= " " . $this->view->translate->_($price_type);
		}
		return $ret;
	}
}