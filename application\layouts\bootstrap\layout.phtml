<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <?php if($this->metaDescription): ?>
        <meta name="description" content="<?= $this->metaDescription ?>" />
    <?php endif ; ?>
    <link rel="icon" href="../../favicon.ico">

    <link href="https://fonts.googleapis.com/css?family=Lato:300,300i,400,400i,700,700i&amp;subset=latin-ext"
          rel="stylesheet">


    <title><?= $this->siteTitle ?></title>
    <? /* $this->render('el/header_includes.phtml') */ ?>

    <!-- Bootstrap core CSS -->

    <link href="/assets/css/vendor/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/vendor/font-awesome/css/font-awesome.min.css">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/css/select2.min.css" rel="stylesheet"/>
    <link href="https://code.jquery.com/ui/1.11.4/themes/ui-lightness/jquery-ui.css" rel="stylesheet">
    <link href="/assets/css/vendor/jquery.bxslider.min.css" rel="stylesheet">
    <link href="/assets/css/style.css?v=20200615" rel="stylesheet">
    <link href="/assets/css/vendor/lightbox.min.css" rel="stylesheet">

</head>

<body>

<?= $this->render('partials/nav.phtml') ?>

<?= $this->render('partials/shortcut-menu.phtml') ?>

<?php
if(isset($this->topSearchForm)) {

    echo $this->render('partials/search.phtml');
}

?>



<div class="container">

    <?= $this->layout()->content ?>

</div>

<div class="container">


    <?= $this->render('partials/footer.phtml') ?>

</div>
<script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>

<!-- Bootstrap core JavaScript
================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="https://code.jquery.com/jquery-3.2.1.min.js" crossorigin="anonymous"></script>
<script
        src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"
        integrity="sha256-VazP97ZCwtekAsvgPBSUwPFKdrwD3unUfSGVYrahUqU="
        crossorigin="anonymous"></script>
<script>window.jQuery || document.write('<script src="/assets/js/vendor/jquery.min.js"><\/script>')</script>
<script src="/assets/js/vendor/tether.min.js"></script>
<script src="/assets/js/vendor/bootstrap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>
<script src="/assets/js/vendor/jquery.bxslider.min.js"></script>
<script src="/assets/js/vendor/lightbox.min.js"></script>
<script src="/assets/js/app.js"></script>

<script src="http://www.google.com/recaptcha/api.js?onload=CaptchaCallback&render=explicit"
        async defer>
</script>

<script type="text/javascript">
    var CaptchaCallback = function() {

        $( ".g-recaptcha" ).each(function( index ) {
            grecaptcha.render(this, {'sitekey' : '6Lfx7A0UAAAAAMFhGYFf0HlqfHjtdPk4jljAHlG-'});
        });

    };
</script>

<script type="text/javascript">

    $(function () {

        $('#make').on("change", function (e) {
            console.log("change");

            var makeIds= 0
            if($(this).data('ids') == 1) {
                makeIds = 1;
            }


            $.ajax({
                type: 'GET',
                url: '<?= $this->url(array(), 'getmodels', true); ?>',
                data: {makes: [$('#make').val()], 'make_ids': makeIds},
                dataType: 'json',
                async: false,
                success: function (data) {

                    $("#model").empty();
                    $("#model").append('<option value=""></option>');
                    for (k in data) {
                        $("#model").append('<option value="' + k + '">' + data[k] + '</option>');
                    }

                    $("#model").prop('disabled', false);
                    $("#model").trigger('change');

                },
                error: function () {
                }
            });


        });

        $('.select2-multiselect').on('select2:close', function (evt) {
            var uldiv = $(this).siblings('span.select2').find('ul')
            var count = $(this).select2('data').length

            uldiv.find('li.select2-selection__choice').remove();
            var li = $("<li></li>")

            li.html(count + " <?= $this->translate->_('SELECTED') ?>");
            if (count == 0) {
                uldiv.find('.select2-search').show();
            }
            else {
                uldiv.find('.select2-search').hide();
                uldiv.append(li);
            }

        });

    });
</script>

<?= $this->inlineScript() ?>
<div id="overlay"></div>
</body>
</html>
