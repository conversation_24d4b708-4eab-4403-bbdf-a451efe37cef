<?php

class My_Controller_Action_Helper_SetRedirBack extends Zend_Controller_Action_Helper_Abstract {
	
	public function direct($sessionKey, $urlOrRouteName=null, $routeOpts=null, $reset=false) {
		$ns = new Zend_Session_Namespace('My_Controller_Action_Helper_RedirectBack');
		if (!isset($ns->urls) || !is_array($ns->urls)) {
			$ns->urls = array();
		}
		
		if ($urlOrRouteName == null) {
			//set return point to current request uri
			$ns->urls[$sessionKey] = Zend_Controller_Front::getInstance()->getRequest()->getRequestUri();
			return;
		}
		
		if ($routeOpts !== null) {
			if (!array_key_exists('language', $routeOpts) || empty($routeOpts['language'])) {
				//auto-add language if missing
				$routeOpts['language'] = Zend_Controller_Front::getInstance()->getRequest()->getParam('language');
			}
			//construct uri from named route
			$urlHelper = new Zend_View_Helper_Url();
			$ns->urls[$sessionKey] = $urlHelper->url($routeOpts, $urlOrRouteName, $reset);
		}
		else {
			//copy string as-is
			$ns->urls[$sessionKey] = $urlOrRouteName;
		}
	}
	
	public function clearData() {
		$ns = new Zend_Session_Namespace('My_Controller_Action_Helper_RedirectBack');
		$ns->urls = null;
	}
	
}