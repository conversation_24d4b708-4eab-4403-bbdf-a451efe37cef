<?php

class Model_Companies extends Model_Base {
	
	public function getCompany($id) {
		$select = $this->db->select()
			->from($this->tables['companies'])
			->where('id = ?', $id);
        
		return $this->db->fetchRow($select);
	}

    public function addCompany($data) {
        $this->db->insert(
            $this->tables['companies'],
            $data
        );
    }


    public function editCompany($id, $data) {
        $this->db->update(
            $this->tables['companies'],
            $data,
            "id = " . (int)$id
        );
    }
	
}