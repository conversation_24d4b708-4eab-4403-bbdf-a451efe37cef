<html>
<head>
<title>Docs for page tcpdf_config.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">com-tecnick-tcpdf</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_com-tecnick-tcpdf.html" class="menu">class tree: com-tecnick-tcpdf</a> ]
		  [ <a href="../elementindex_com-tecnick-tcpdf.html" class="menu">index: com-tecnick-tcpdf</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_com-tecnick-tcpdf.html">com-tecnick-tcpdf</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../com-tecnick-tcpdf/_2dbarcodes.php.html">		2dbarcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_barcodes.php.html">		barcodes.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_htmlcolors.php.html">		htmlcolors.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_qrcode.php.html">		qrcode.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_tcpdf.php.html">		tcpdf.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_config---tcpdf_config.php.html">		tcpdf_config.php
		</a><br>
			<a href="../com-tecnick-tcpdf/_unicode_data.php.html">		unicode_data.php
		</a><br>
	  </div><br />
      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../com-tecnick-tcpdf/QRcode.html">QRcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF.html">TCPDF</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDF2DBarcode.html">TCPDF2DBarcode</a><br />
	    		<a href="../com-tecnick-tcpdf/TCPDFBarcode.html">TCPDFBarcode</a><br />
	  </div>
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: tcpdf_config.php</h1>
Source Location: /config/tcpdf_config.php<br /><br />

<br>
<br>


<h2>Page Details:</h2>
Configuration file for TCPDF.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Nicola Asuni</td>
  </tr>
  <tr>
    <td><b>version:</b>&nbsp;&nbsp;</td><td>4.0.014</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2004-2008 Nicola Asuni - Tecnick.com S.r.l (www.tecnick.com) Via Della Pace, 11 - 09044 - Quartucciu (CA) - ITALY - www.tecnick.com - <EMAIL></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://tcpdf.sourceforge.net">http://tcpdf.sourceforge.net</a></td>
  </tr>
  <tr>
    <td><b>since:</b>&nbsp;&nbsp;</td><td>2004-10-27</td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="http://www.gnu.org/copyleft/lesser.html">LGPL</a></td>
  </tr>
</table>
</div>
<br /><br />
<br /><br />
<br /><br />
  <hr />
	<a name="defineHEAD_MAGNIFICATION"></a>
	<h3>HEAD_MAGNIFICATION <span class="smalllinenumber">[line 211]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>HEAD_MAGNIFICATION = 1.1</code>
    </td></tr></table>
    </td></tr></table>

    magnification factor for titles<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_BLANK_IMAGE"></a>
	<h3>K_BLANK_IMAGE <span class="smalllinenumber">[line 101]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_BLANK_IMAGE = K_PATH_IMAGES.'_blank.png'</code>
    </td></tr></table>
    </td></tr></table>

    blank image<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_CELL_HEIGHT_RATIO"></a>
	<h3>K_CELL_HEIGHT_RATIO <span class="smalllinenumber">[line 216]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_CELL_HEIGHT_RATIO = 1.25</code>
    </td></tr></table>
    </td></tr></table>

    height of cell repect font height<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_PATH_CACHE"></a>
	<h3>K_PATH_CACHE <span class="smalllinenumber">[line 86]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_PATH_CACHE = K_PATH_MAIN.'cache/'</code>
    </td></tr></table>
    </td></tr></table>

    cache directory for temporary files (full path)<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_PATH_FONTS"></a>
	<h3>K_PATH_FONTS <span class="smalllinenumber">[line 81]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_PATH_FONTS = K_PATH_MAIN.'fonts/'</code>
    </td></tr></table>
    </td></tr></table>

    path for PDF fonts<br /><br /><p>use K_PATH_MAIN.'fonts/old/' for old non-UTF8 fonts</p><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_PATH_IMAGES"></a>
	<h3>K_PATH_IMAGES <span class="smalllinenumber">[line 96]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_PATH_IMAGES = K_PATH_MAIN.'images/'</code>
    </td></tr></table>
    </td></tr></table>

    images directory<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_PATH_MAIN"></a>
	<h3>K_PATH_MAIN <span class="smalllinenumber">[line 58]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_PATH_MAIN = $k_path_main</code>
    </td></tr></table>
    </td></tr></table>

    Installation path (/var/www/tcpdf/).<br /><br /><p>By default it is automatically calculated but you can also set it as a fixed string to improve performances.</p><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_PATH_URL"></a>
	<h3>K_PATH_URL <span class="smalllinenumber">[line 75]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_PATH_URL = $k_path_url</code>
    </td></tr></table>
    </td></tr></table>

    URL path to tcpdf installation folder (http://localhost/tcpdf/).<br /><br /><p>By default it is automatically calculated but you can also set it as a fixed string to improve performances.</p><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_PATH_URL_CACHE"></a>
	<h3>K_PATH_URL_CACHE <span class="smalllinenumber">[line 91]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_PATH_URL_CACHE = K_PATH_URL.'cache/'</code>
    </td></tr></table>
    </td></tr></table>

    cache directory for temporary files (url path)<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_SMALL_RATIO"></a>
	<h3>K_SMALL_RATIO <span class="smalllinenumber">[line 226]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_SMALL_RATIO = 2/3</code>
    </td></tr></table>
    </td></tr></table>

    reduction factor for small font<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="defineK_TITLE_MAGNIFICATION"></a>
	<h3>K_TITLE_MAGNIFICATION <span class="smalllinenumber">[line 221]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>K_TITLE_MAGNIFICATION = 1.3</code>
    </td></tr></table>
    </td></tr></table>

    title magnification respect main font size<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_AUTHOR"></a>
	<h3>PDF_AUTHOR <span class="smalllinenumber">[line 121]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_AUTHOR = 'TCPDF'</code>
    </td></tr></table>
    </td></tr></table>

    document author<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_CREATOR"></a>
	<h3>PDF_CREATOR <span class="smalllinenumber">[line 116]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_CREATOR = 'TCPDF'</code>
    </td></tr></table>
    </td></tr></table>

    document creator<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_FONT_MONOSPACED"></a>
	<h3>PDF_FONT_MONOSPACED <span class="smalllinenumber">[line 201]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_FONT_MONOSPACED = 'courier'</code>
    </td></tr></table>
    </td></tr></table>

    default monospaced font name<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_FONT_NAME_DATA"></a>
	<h3>PDF_FONT_NAME_DATA <span class="smalllinenumber">[line 191]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_FONT_NAME_DATA = 'helvetica'</code>
    </td></tr></table>
    </td></tr></table>

    default data font name<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_FONT_NAME_MAIN"></a>
	<h3>PDF_FONT_NAME_MAIN <span class="smalllinenumber">[line 181]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_FONT_NAME_MAIN = 'helvetica'</code>
    </td></tr></table>
    </td></tr></table>

    default main font name<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_FONT_SIZE_DATA"></a>
	<h3>PDF_FONT_SIZE_DATA <span class="smalllinenumber">[line 196]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_FONT_SIZE_DATA = 8</code>
    </td></tr></table>
    </td></tr></table>

    default data font size<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_FONT_SIZE_MAIN"></a>
	<h3>PDF_FONT_SIZE_MAIN <span class="smalllinenumber">[line 186]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_FONT_SIZE_MAIN = 10</code>
    </td></tr></table>
    </td></tr></table>

    default main font size<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_HEADER_LOGO"></a>
	<h3>PDF_HEADER_LOGO <span class="smalllinenumber">[line 136]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_HEADER_LOGO = 'tcpdf_logo.jpg'</code>
    </td></tr></table>
    </td></tr></table>

    image logo<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_HEADER_LOGO_WIDTH"></a>
	<h3>PDF_HEADER_LOGO_WIDTH <span class="smalllinenumber">[line 141]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_HEADER_LOGO_WIDTH = 30</code>
    </td></tr></table>
    </td></tr></table>

    header logo image width [mm]<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_HEADER_STRING"></a>
	<h3>PDF_HEADER_STRING <span class="smalllinenumber">[line 131]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_HEADER_STRING = &quot;by Nicola Asuni - Tecnick.com\nwww.tcpdf.org&quot;</code>
    </td></tr></table>
    </td></tr></table>

    header description string<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_HEADER_TITLE"></a>
	<h3>PDF_HEADER_TITLE <span class="smalllinenumber">[line 126]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_HEADER_TITLE = 'TCPDF Example'</code>
    </td></tr></table>
    </td></tr></table>

    header title<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_IMAGE_SCALE_RATIO"></a>
	<h3>PDF_IMAGE_SCALE_RATIO <span class="smalllinenumber">[line 206]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_IMAGE_SCALE_RATIO = 1</code>
    </td></tr></table>
    </td></tr></table>

    ratio used to adjust the conversion of pixels to user units<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_MARGIN_BOTTOM"></a>
	<h3>PDF_MARGIN_BOTTOM <span class="smalllinenumber">[line 166]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_MARGIN_BOTTOM = 25</code>
    </td></tr></table>
    </td></tr></table>

    bottom margin<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_MARGIN_FOOTER"></a>
	<h3>PDF_MARGIN_FOOTER <span class="smalllinenumber">[line 156]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_MARGIN_FOOTER = 10</code>
    </td></tr></table>
    </td></tr></table>

    footer margin<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_MARGIN_HEADER"></a>
	<h3>PDF_MARGIN_HEADER <span class="smalllinenumber">[line 151]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_MARGIN_HEADER = 5</code>
    </td></tr></table>
    </td></tr></table>

    header margin<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_MARGIN_LEFT"></a>
	<h3>PDF_MARGIN_LEFT <span class="smalllinenumber">[line 171]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_MARGIN_LEFT = 15</code>
    </td></tr></table>
    </td></tr></table>

    left margin<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_MARGIN_RIGHT"></a>
	<h3>PDF_MARGIN_RIGHT <span class="smalllinenumber">[line 176]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_MARGIN_RIGHT = 15</code>
    </td></tr></table>
    </td></tr></table>

    right margin<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_MARGIN_TOP"></a>
	<h3>PDF_MARGIN_TOP <span class="smalllinenumber">[line 161]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_MARGIN_TOP = 27</code>
    </td></tr></table>
    </td></tr></table>

    top margin<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_PAGE_FORMAT"></a>
	<h3>PDF_PAGE_FORMAT <span class="smalllinenumber">[line 106]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_PAGE_FORMAT = 'A4'</code>
    </td></tr></table>
    </td></tr></table>

    page format<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_PAGE_ORIENTATION"></a>
	<h3>PDF_PAGE_ORIENTATION <span class="smalllinenumber">[line 111]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_PAGE_ORIENTATION = 'P'</code>
    </td></tr></table>
    </td></tr></table>

    page orientation (P=portrait, L=landscape)<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
  <hr />
	<a name="definePDF_UNIT"></a>
	<h3>PDF_UNIT <span class="smalllinenumber">[line 146]</span></h3>
	<div class="tags">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>PDF_UNIT = 'mm'</code>
    </td></tr></table>
    </td></tr></table>

    document unit of measure [pt=point, mm=millimeter, cm=centimeter, in=inch]<br /><br />    <br />
		</div>
	<div class="top">[ <a href="#top">Top</a> ]</div><br /><br />
<br />

        <div class="credit">
		    <hr />
		    Documentation generated on Sun, 28 Mar 2010 22:22:43 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>