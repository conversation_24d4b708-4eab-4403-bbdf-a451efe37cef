<?php

class Zend_View_Helper_EngineData extends Zend_View_Helper_Abstract {
	
	public function engineData($carData) {
        $cars = new Model_Cars_Cars();
        $gearboxTypes = $cars->getGearboxTypesByName();
		$view = Zend_Layout::getMvcInstance()->getView();
		$ret = "";
		$ret .= $view->translate->_($carData['fuel_key']);
		$ret .= " " . sprintf("%01.1f", $carData['cubic_capacity'] / 1000.0);
        $ret .= " / " . $carData['power'] . " " . $view->translate->_($carData['power_unit']);
        //$ret .= " / ".$view->translate->_('box')." " .$gearboxTypes[$carData['gearbox_type_id']];
        $ret .= " / ".$gearboxTypes[$carData['gearbox_type_id']];
		//$ret .= " (" . $carData['power'] . " " . $view->translate->_($carData['power_unit']) . ")";
		return $ret;
	}
	
}