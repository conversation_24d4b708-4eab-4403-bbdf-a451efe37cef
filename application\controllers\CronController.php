<?php

class CronController extends Zend_Controller_Action {
	
	public function init() {
        set_time_limit(0);
		$now = date("Y-m-d", time());
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$secretPass = md5($opt['cron']['secretPass'] . $now);
		//if ($secretPass != $this->_request->getParam('pass')) exit;
		
		$this->_helper->viewRenderer->setNoRender(true);
		$this->_helper->layout->disableLayout();
		
		$this->logger = new Zend_Log();
		$this->logger->addWriter(new Zend_Log_Writer_Stream($opt['cron']['logfile']));
	}
	
	public function cleanCacheAction() {
		$adm = new Model_SrAdmin();
		$adm->cleanCacheAll();
		$this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);
		echo ".";
	}
	
	public function deleteAllCarPhotosAction() {
		My_Utils::rrmdir(APPLICATION_PATH . '/../public/images/cars');
	}
	
	public function resetDbAction() {
		return;
		if (APPLICATION_ENV != 'development') exit;
		
		$sql = file_get_contents(APPLICATION_PATH . '/../baza.sql');
		$db = Zend_Db_Table_Abstract::getDefaultAdapter();
		$db->exec($sql);
	}
	
	public function carPhotosAction() {
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$this->logger = new Zend_Log();
		$this->logger->addWriter(new Zend_Log_Writer_Stream($opt['synchronization']['sr']['import']['logfile']));
		
		$this->logger->log(PHP_EOL . PHP_EOL . date("Y-m-d H:i:s") . "  --  carPhotosAction started" . PHP_EOL, Zend_Log::INFO);
		
		try {
			$cars = new Model_Cars_Cars();
			$cars->processPhotos($this->logger);
			$this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);
		}
		catch (Exception $e) {
			$this->_helper->eventLog->log(array('outcome' => 'fail'), Zend_Log::INFO);
			$this->logger->log(PHP_EOL . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);
		}
		exit;
	}
    
    public function carVipautoPhotosAction() {
		$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
		$this->logger = new Zend_Log();
		$this->logger->addWriter(new Zend_Log_Writer_Stream($opt['synchronization']['sr']['import']['logfile']));
		
		$this->logger->log(PHP_EOL . PHP_EOL . date("Y-m-d H:i:s") . "  --  carVipautoPhotosAction started" . PHP_EOL, Zend_Log::INFO);
		
		try {
			$cars = new Model_Cars_Cars();
			$cars->processVipautoPhotos($this->logger);
			$this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);
		}
		catch (Exception $e) {
			$this->_helper->eventLog->log(array('outcome' => 'fail'), Zend_Log::INFO);
			$this->logger->log(PHP_EOL . PHP_EOL . $e->getMessage() . PHP_EOL . $e->getTraceAsString(), Zend_Log::ERR);
		}
		exit;
	}
	
	public function expireReservationsAction() {
		$ct = new Model_Cars_Transfers();
		$ct->expireReservations();
		$this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);
	}

	public function newsletterAction() {
		//try {
			$users = new Model_Users();

            $carDateTo = date('Y-m-d H:i').":00";
            $carDateFrom = date('Y-m-d H:i:s', strtotime($carDateTo)-299);

			//Tablica [id_usera] => array(samochody dodane dzis lub ze zmienioną dzis cena które spełniaja kryteria wyszukiwania zapisane przez usera)
			$searches =   $users->getSearchesWithCleanup(null,null,null,$carDateFrom,$carDateTo);



			
			$opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
			$domain = Zend_Registry::get('siteDomain');
			
			$cssContent = file_get_contents(APPLICATION_PATH . '/../public/files/global.css');
			$cssContent = str_replace('url(/', 'url(' . $domain . '/', $cssContent);
            
            $translations = new Model_Translations();
            $cache = Zend_Registry::get('Cache');
                    
            $enabled_languages = null;
            if (!$enabled_languages = $cache->load('enabled_languages')) {
                $enabled_languages = $translations->getEnabledLanguages();

                $cache->save(
                    $enabled_languages,
                    'enabled_languages',
                    $tags=array('translate')
                );
            }

            
			$employees = new Model_Employees();
            $employeesMials = array();
            $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
                        
			foreach($searches as $searchUser) {
				$msg = false;
				if (count($searchUser['cars'])) {

                    $employee = null;
                    if (!empty($searchUser['employee_sr_id'])) {
                        $employee = $employees->getBySrId($searchUser['employee_sr_id']);

                    }


                    $cars = $searchUser['cars'];
                    $carsPriceChangeCount = 0;
                    $carsCount = count($cars);
                    foreach($cars as $car) {
                        $mail = new Zend_Mail($charset="UTF-8");

                        if(DateTime::createFromFormat('Y-m-d H:i:s',$car['price_change_datetime']) > DateTime::createFromFormat('Y-m-d H:i:s',$car['is_visible_datetime']))
                            $carsPriceChangeCount++;

                        if ($employee) {
                            $this->view->employee = $employee;


                            $mail->setFrom(
                                $opt['resources']['mail']['defaultFrom']['email'],
                                $employee['first_name'] . ' ' . $employee['last_name']
                            );

                            $mail->setReplyTo(
                                $employee['email'],
                                $employee['first_name'] . ' ' . $employee['last_name']
                            );


                        } else {
                            $mail->setFrom(
                                $opt['resources']['mail']['defaultFrom']['email'],
                                'AutoAuto.pl'
                            );
                        }

                        $this->view->css = $cssContent;
                        $searchUser['cars'] = array($car);
                        $this->view->user = $searchUser;

                        $language = $searchUser['language'];


                        $translate = null;
                        $cacheId = 'translate_' . $language;
                        if (!$translate = $cache->load($cacheId)) {

                            $translate = new Zend_Translate(
                                'array',
                                $translations->getTranslations($language),
                                $language
                            );


                            $cache->save(
                                $translate,
                                $cacheId,
                                $tags=array('translate', $language)
                            );
                        }

                        Zend_Registry::set('Zend_Translate', $translate);
                        Zend_Registry::set('translate_language', $language);
                        Zend_Form::setDefaultTranslator($translate);


                        $this->view->translate = $translate;
                        $this->view->language = $language;
                        $this->view->language_row = $enabled_languages[$language];
                        $this->view->singleCar = true;

                        $html = $this->view->render('newsletter_content.phtml');

                        $emogrifier = new My_Emogrifier($html);
                        $html = $emogrifier->emogrify();

                        $text = $this->view->translate->_('NEWSLETTER_GREETING') . "\n".
                            $this->view->translate->_('NEWSLETTER_HEADING') .':'.  $searchUser['search_name']
                            . "\n\n\n". $this->view->translate->_('MAIL_HTML_MESSAGE_ALERT');

                        $firstCar = $searchUser['cars'][0];

                        $subject = $firstCar['make_name'] . ' '. $firstCar['model_name']. ' '.
                            $firstCar['build_year']. ' '. $this->view->CarPrice($firstCar,$this->view->language_row);

                        $mail->addTo($searchUser['email'])
                            ->setBodyText($text)
                            ->setBodyHtml($html)
                            ->setSubject($subject);

                            $mail->send();


                    }

                    if($employee && ($carsCount > $carsPriceChangeCount)) {

                        $employeesMials[$searchUser['employee_sr_id']]['employee'] = $employee;
                        $employeesMials[$searchUser['employee_sr_id']]['searchUsers'][] = $searchUser;

                    }


				}


			}
                foreach($employeesMials as $key => $value)
                {
                    $employee = $value['employee'];




                    if(!empty($employee['email']))
                    {
                        $this->view->searchUsers = $value['searchUsers'];

                        $html = $this->view->render('employees_mail_info.phtml');

                        $mail = new Zend_Mail($charset="UTF-8");

                        $mail->setFrom($opt['resources']['mail']['defaultFrom']['email']);

                        $mail->addTo($employee['email'])
                                ->setBodyHtml($html)
                                ->setSubject("Mailing do Twoich klientów autoauto.pl");
                        
                        //echo $html."<br /><br /><br />";

                        $mail->send();
                    }
                }
			$this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);
		/*} catch (Exception $e) {
			
		}*/
		exit;
	}
	
	public function testAction() {
		echo "test";
		$this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);
	}
	
	public function currencyImportAction() {
		$cv = new Model_CurrencyValues();
		$cv->importCurrencyList();
		$this->_helper->eventLog->log(array('outcome' => 'ok'), Zend_Log::INFO);
	}


    public function generateSlugsAction() {

        $slugify = new \Cocur\Slugify\Slugify();

        $db = Model_Cars_Cars::getDb();

        $select = $db->select()
            ->from('aa_car_makes');

        foreach($db->fetchAll($select) as $make) {

            $db->update(
                'aa_car_makes',
                array('slug' => $slugify->slugify($make['name'])),
            'id = ' .$make['id']
            );
        }

        $select = $db->select()
            ->from('aa_car_models');

        foreach($db->fetchAll($select) as $model) {

            $db->update(
                'aa_car_models',
                array('slug' => $slugify->slugify($model['name'])),
                'id = ' .$model['id']
            );
        }


    }
	
}