<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta http-equiv="Content-Language" content="<?= $this->language ?>" />
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title><?php if (is_array($this->breadcrumb) && count($this->breadcrumb) > 0): ?><?php end($this->breadcrumb); $lastValue = current($this->breadcrumb);reset($this->breadcrumb);?><?= $this->escape($lastValue) ?> - <?php endif ?><?= $this->siteTitle ?></title>
        <?= $this->render('el/header_includes.phtml') ?>
	</head>
	
	<body<?php if ($this->htmlBodyClass) :?> class="<?= $this->htmlBodyClass ?>"<?php endif; ?>>
		<div id="outer_container">
			<div id="inner_container">
				<div id="header">
					<?= $this->render('el/top_menu.mobile.phtml') ?>
                    <div class="clear"></div>
					
				</div>
				<div class="clear"></div>
				<?= $this->render('el/main_menu.mobile.phtml') ?>

				
				<?= $this->render('el/breadcrumb.phtml') ?>
				
				<?= $this->render('el/show_messages.phtml') ?>

                <div id="left_search" class="column_left_1">
                    <?= $this->render('left_search.mobile.phtml') ?>
                </div>
		
				<?= $this->layout()->content ?>

				<div class="clear"></div>
				
				<?= $this->render('el/footer.mobile.phtml') ?>
			</div>
		</div>

        <script type="text/javascript">

            var _gaq = _gaq || [];
            _gaq.push(['_setAccount', 'UA-********-1']);
            _gaq.push(['_setDomainName', 'autoauto.pl']);
            _gaq.push(['_trackPageview']);

            (function() {
                var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
                ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
                var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
            })();

        </script>

        <script type="text/javascript">
            adroll_adv_id = "G4XRYOVPF5CGTCX5SSQN67";
            adroll_pix_id = "YQP7DP2BQFHYNCLGIBQLP5";
            (function () {
                var oldonload = window.onload;
                window.onload = function(){
                    __adroll_loaded=true;
                    var scr = document.createElement("script");
                    var host = (("https:" == document.location.protocol) ? "https://s.adroll.com" : "http://a.adroll.com");
                    scr.setAttribute('async', 'true');
                    scr.type = "text/javascript";
                    scr.src = host + "/j/roundtrip.js";
                    ((document.getElementsByTagName('head') || [null])[0] ||
                        document.getElementsByTagName('script')[0].parentNode).appendChild(scr);
                    if(oldonload){oldonload()}};
            }());
        </script>

        <!-- Kod tagu remarketingowego Google -->
        <!--------------------------------------------------
        Tagi remarketingowe nie mogą być wiązane z informacjami umożliwiającymi identyfikację osób ani umieszczane na stronach o tematyce należącej do kategorii kontrowersyjnych. Więcej informacji oraz instrukcje konfiguracji tagu znajdziesz tutaj: http://google.com/ads/remarketingsetup
        --------------------------------------------------->
        <script type="text/javascript">
            /* <![CDATA[ */
            var google_conversion_id = 976363198;
            var google_custom_params = window.google_tag_params;
            var google_remarketing_only = true;
            /* ]]> */
        </script>
        <script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
        </script>
        <noscript>
            <div style="display:inline;">
                <img height="1" width="1" style="border-style:none;" alt="" src="//googleads.g.doubleclick.net/pagead/viewthroughconversion/976363198/?value=0&amp;guid=ON&amp;script=0"/>
            </div>
        </noscript>

    </body>
	
</html>