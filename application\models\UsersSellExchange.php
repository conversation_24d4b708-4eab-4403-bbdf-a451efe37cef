<?php

class Model_UsersSellExchange extends Model_Base {
	

	public function add($data, $doTransaction=true) {

        $usersModel = new Model_Users();

        if (!isset($data['user_id'])) {
            //user not logged in - check existence and add new if necessary
            $select = $this->db->select()
                ->from($this->tables['users'], array('id'))
                ->where('email = ?', $data['email']);
            $data['user_id'] = $this->db->fetchOne($select);

            if (!$data['user_id']) {

                $data['user_id']= $usersModel->addQuick($data, $trans=false);
            }
        }

        // dla pewnosci export do sys2
        $user = $usersModel->getUser($data['user_id']);
        $usersModel->edit($data['user_id'], array('email' => $user['email']));

		try {
			if ($doTransaction) $this->db->beginTransaction();

			$addData = array(
				'user_id' => $data['user_id'],
				'added_datetime' => new Zend_Db_Expr('NOW()'),
				'updated_datetime' => new Zend_Db_Expr('NOW()'),
				'model_id' => $data['model'],
				'build_year' => $data['build_year'],
                'engine' => $data['engine'],
				'odometer' => $data['odometer'],
				'color' => $data['color'],
                'origin' => $data['origin'],
                'no_accident' => $data['no_accident'] == 1 ? 'y' : 'n',
                'repaired_elements' => $data['repaired_elements'],
                'comments' => $data['comments'],
                'email' => $data['email'],
                'phone' => $data['phone'],
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'is_for_exchange' => $data['is_for_exchange'],

			);

			$this->db->insert(
				$this->tables['users_cars_sell_exchange'],
				$addData
			);

			
			$id = $this->db->lastInsertId($this->tables['users_cars_sell_exchange'], 'id');

            if(!empty($data['photo_tmp_file_path'])) {

                $pathinfo = pathinfo($data['photo_tmp_file_path']);

                $opt = Zend_Controller_Front::getInstance()->getParam('bootstrap')->getOptions();
                $photoDir = $opt['userSellExchange']['path'];
                $sizes = $opt['photos']['sizes'];

                $filename = $id;
                $extension = $pathinfo['extension'];

                @copy($data['photo_tmp_file_path'], $photoDir . DIRECTORY_SEPARATOR . $filename.'.'.$extension);

                $imgProc = new My_ImageResize();

                foreach ($sizes as $sizeName => $sizeConfig) {
                    $imgProc->process(
                        $data['photo_tmp_file_path'],
                        $filename . "_{$sizeName}." . $extension,
                        $photoDir,
                        array($sizeConfig['width'], $sizeConfig['height']),
                        $overlay=null,
                        $strict=true
                    );
                }


                @unlink($data['photo_tmp_file_path']);

                $data['photo'] = $filename.'.'.$extension;

                $this->db->update(
                    $this->tables['users_cars_sell_exchange'],
                    array('photo' => $data['photo']),
                    'id = '. $id
                );

            }

            if(isset($data['exchange_models']) && is_array($data['exchange_models'])) {
                foreach($data['exchange_models'] as $model) {

                    $this->db->insert(
                        $this->tables['users_cars_models_for_exchange'],
                        array('user_car_exchange_id' => $id, 'model_id' => $model)
                    );

                }

            }

			if ($doTransaction) $this->db->commit();
			
			return $id;
		}
		catch (Exception $e) {
			if ($doTransaction) $this->db->rollBack();

            if(!empty($data['photo_tmp_file_path'])) {

                @unlink($data['photo_tmp_file_path']);
            }
			throw $e;
		}
	}

    public function get($id) {

        $select = $this->db->select()
            ->from(array('ucse' => $this->tables['users_cars_sell_exchange']))
            ->joinLeft(array('md' => $this->tables['car_models']),
                'md.id = ucse.model_id',
                array('md.sr_model_id')
            )
            ->where('ucse.id = ?', $id);

        $result = $this->db->fetchRow($select);

        if($result['is_for_exchange'] == 1) {

            $select = $this->db->select()
                ->from(array('ucmfe' => $this->tables['users_cars_models_for_exchange']), array('model_id' => 'md.sr_model_id'))
                ->joinLeft(array('md' => $this->tables['car_models']),
                    'md.id = ucmfe.model_id',
                    null
                )
                ->where('ucmfe.user_car_exchange_id = ?', $result['id']);

            $result['exchange_models'] =  $this->db->fetchCol($select);
        }

        return $result;


    }

    public function update($id, $data, $doTransaction=true) {

        try {
            if ($doTransaction) $this->db->beginTransaction();

            $newData = array();

            $fields = array(
                'user_id',
                'added_datetime' ,
                'updated_datetime',
                'model_id',
                'build_year',
                'engine',
                'odometer',
                'color',
                'origin',
                'no_accident',
                'repaired_elements',
                'comments',
                'caretaker_sr_id',
                'photo',
                'email',
                'phone',
                'first_name',
                'last_name',
                'is_for_exchange',
                'is_visible',
            );


            foreach ($fields as $field) {
                if (isset($data[$field]))
                    $newData[$field] = $data[$field];
            }


            $this->db->update(
                $this->tables['users_cars_sell_exchange'],
                $newData, 'id = '. $id
            );




            if ($doTransaction) $this->db->commit();

            return $id;
        }
        catch (Exception $e) {
            if ($doTransaction) $this->db->rollBack();

            throw $e;
        }
    }

    public function search($parameters = array()) {
        $select = $this->db->select()
            ->from(array('ucse' => $this->tables['users_cars_sell_exchange']),
                array('id',
                    'build_year',
                    'engine',
                    'odometer',
                    'color',
                    'origin',
                    'no_accident',
                    'repaired_elements',
                    'comments',
                    'photo',
                    'is_for_exchange',
                    'added_datetime',
                    'updated_datetime',
                    'exchange_makes' => new Zend_Db_Expr('GROUP_CONCAT(CONCAT(mk2.name, " ", md2.name) SEPARATOR ", ")')))

            ->joinLeft(array('md' => $this->tables['car_models']),
                'md.id = ucse.model_id',
                array('model_name' => 'name')
            )
            ->joinLeft(array('mk' => $this->tables['car_makes']),
                'mk.id = md.make_id',
                array('make_name' => 'name')
            )->joinLeft(array('em' => $this->tables['employees']),
                'em.sr_id = ucse.caretaker_sr_id',
                array('caretaker_first_name' => 'first_name', 'caretaker_last_name' => 'last_name', 'caretaker_email' => 'email', 'caretaker_phone' => 'phone', 'caretaker_visible' => 'visible')
            )->joinLeft(array('ucmfe' => $this->tables['users_cars_models_for_exchange']),
                'ucmfe.user_car_exchange_id = ucse.id',
                null
            ) ->joinLeft(array('md2' => $this->tables['car_models']),
                'md2.id = ucmfe.model_id',
                null
            )
            ->joinLeft(array('mk2' => $this->tables['car_makes']),
                'mk2.id = md2.make_id',
                null
            )


            ->where('ucse.is_visible = 1')
            ->order(array('ucse.added_datetime DESC'))
            ->group('ucse.id');

            if(isset($parameters['is_for_exchange'])) {

                $select->where('ucse.is_for_exchange = ?', $parameters['is_for_exchange']);
            }


        //echo $select. '<br />';

        return $select;


    }

}