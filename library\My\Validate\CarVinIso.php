<?php

class My_Validate_CarVinIso extends Zend_Validate_Abstract {
	
	const VIN_INVALID = 'vinInvalid';
	
	protected $_messageTemplates = array(
		self::VIN_INVALID => "'%value%' nie jest prawidłową wartością VIN"
	);
	
	protected function _isVin($vin) {
		$this->_setValue($vin);
		$vin = strtoupper($vin);
		
		if (strlen($vin) != 17) {
			return false;
		}
		
		if ($vin == "00000000000000000") {
			return false;
		}
		
		$value = array(
			'A' => 1,
			'B' => 2,
			'C' => 3,
			'D' => 4,
			'E' => 5,
			'F' => 6,
			'G' => 7,
			'H' => 8,
			'J' => 1,
			'K' => 2,
			'L' => 3,
			'M' => 4,
			'N' => 5,
			'P' => 7,
			'R' => 9,
			'S' => 2,
			'T' => 3,
			'U' => 4,
			'V' => 5,
			'W' => 6,
			'X' => 7,
			'Y' => 8,
			'Z' => 9
		);
		
		$weight = array (
			0 => 8,
			1 => 7,
			2 => 6,
			3 => 5,
			4 => 4,
			5 => 3,
			6 => 2,
			7 => 10,
			8 => 0,
			9 => 9,
			10 => 8,
			11 => 7,
			12 => 6,
			13 => 5,
			14 => 4,
			15 => 3,
			16 => 2
		);
		
		$char = str_split($vin);
		$total = 0;
		
		for ($i = 0; $i < 17; $i++) {
			if (is_numeric($char[$i])) {
				$total += ($char[$i] * $weight[$i]);
			} elseif (array_key_exists($char[$i], $value)) {
				$total += ($value[$char[$i]] * $weight[$i]);
			} else {
				return false;
			}
		}
		
		$mod = $total % 11;
		
		if ($mod == 10) {
			$checkDigit = 'X';
		} else {
			$checkDigit = $mod;
		}
		
		if ($char[8] == $checkDigit) { 
			return true;
		} else {
			return false;
		}
	}

	public function isValid($data, $context=null) {
		if ($this->_isVin($data)) return true;
		else {
			$this->_error(self::VIN_INVALID);
			return false;
		}
	}
	
}