<?php

class Zend_View_Helper_CarLink extends Zend_View_Helper_Abstract {
	
	public function carLink($carData, $title, $onlyHref=false) {
		$ret = "";
		if (!$onlyHref) {
			$ret .= '<a href="';
		}

		$ret .= $this->view->url(array('language' => $this->view->language, 'id' => $carData['car_id'], 'description' => $this->view->carPermalink($carData)), 'showCarWithDescription', true);
		
		if (!$onlyHref) {
			$ret .= '">';
			$ret .= $title;
			$ret .= '</a>';
		}
				
		return $ret;
	}
	
}