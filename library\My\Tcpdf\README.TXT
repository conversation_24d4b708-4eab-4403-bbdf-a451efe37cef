TCPDF - README
============================================================

IF YOU'D LIKE TO SUPPORT TCPDF, PLEASE CONSIDER MAKING A 
DONATION: 
http://sourceforge.net/donate/index.php?group_id=128076

------------------------------------------------------------

Name: TCPDF
Version: 4.9.001
Release date: 2010-03-28
Author:	<PERSON> (c) 2001-2010:
	Nicola <PERSON>.com s.r.l.
	<PERSON>, 11
	09044 Quartucciu (CA)
	ITALY
	www.tecnick.com
	
URLs:
	http://www.tcpdf.org
	http://www.sourceforge.net/projects/tcpdf
	
Description:
	TCPDF is a PHP class for generating PDF files on-the-fly without requiring external extensions.
	TCPDF has been originally derived from the Public Domain FPDF class by <PERSON> (http://www.fpdf.org).
	
Main Features:
//  * no external libraries are required for the basic functions;
// 	* supports all ISO page formats;
// 	* supports custom page formats, margins and units of measure;
// 	* supports UTF-8 Unicode and Right-To-Left languages;
// 	* supports TrueTypeUnicode, OpenTypeUnicode, TrueType, OpenType, Type1 and CID-0 fonts;
// 	* supports document encryption;
// 	* includes methods to publish some XHTML code, including forms;
// 	* includes graphic (geometric) and transformation methods;
// 	* includes Javascript and Forms support;
// 	* includes a method to print various barcode formats: CODE 39, ANSI MH10.8M-1983, USD-3, 3 of 9, CODE 93, USS-93, Standard 2 of 5, Interleaved 2 of 5, CODE 128 A/B/C, 2 and 5 Digits UPC-Based Extention, EAN 8, EAN 13, UPC-A, UPC-E, MSI, POSTNET, PLANET, RMS4CC (Royal Mail 4-state Customer Code), CBC (Customer Bar Code), KIX (Klant index - Customer index), Intelligent Mail Barcode, Onecode, USPS-B-3200, CODABAR, CODE 11, PHARMACODE, PHARMACODE TWO-TRACKS, QR-Code;
// 	* includes methods to set Bookmarks and print a Table of Content;
// 	* includes methods to move and delete pages;
// 	* includes methods for automatic page header and footer management;
// 	* supports automatic page break;
// 	* supports automatic page numbering and page groups;
// 	* supports automatic line break and text justification;
// 	* supports JPEG and PNG images natively, all images supported by GD (GD, GD2, GD2PART, GIF, JPEG, PNG, BMP, XBM, XPM) and all images supported via ImagMagick (http://www.imagemagick.org/www/formats.html)
// 	* supports stroke and clipping mode for text;
// 	* supports clipping masks;
// 	* supports Grayscale, RGB, CMYK, Spot Colors and Transparencies;
// 	* supports several annotations, including links, text and file attachments;
// 	* supports page compression (requires zlib extension);
//  * supports text hyphenation.
//  * supports transactions to UNDO commands.
//  * supports signature certifications.

Installation (full instructions on http://www.tcpdf.org):
	1. copy the folder on your Web server
	2. set your installation path and other parameters on the config/tcpdf_config.php
	3. call the examples/example_001.php page with your browser to see an example

Source Code Documentation:
	doc/index.html
	
For Additional Documentation:
	http://www.tcpdf.org

License
	Copyright (C) 2002-2010 Nicola Asuni - Tecnick.com S.r.l.
	
	This program is free software: you can redistribute it and/or modify
	it under the terms of the GNU Lesser General Public License as published by
	the Free Software Foundation, either version 2.1 of the License, or
	(at your option) any later version.
	
	This program is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU Lesser General Public License for more details.
	
	You should have received a copy of the GNU Lesser General Public License
	along with this program.  If not, see <http://www.gnu.org/licenses/>.
	
	See LICENSE.TXT file for more information.
	
============================================================
