<?php

class My_Acl extends Zend_Acl {
	
	public function __construct($options=null) {
		
		$this->myAddResources(array(
			array('cron', null),
			array('error', null),
			array('import', null),
			array('index', null),
			array('login', null),
			array('cars', null),
			array('test', null),
			array('user-cp', null),
			array('racing-team', null),
			array('car-static', null)
		));
		
		$this->addRole('guest');
		$this->addRole('user', 'guest');
		$this->addRole('salesman', 'guest');
		
		//$this->deny('guest');
		
		//import/export, cronjobs, error controller
		$this->allow('guest', 'cron');
		$this->allow('guest', 'import');
		$this->allow('guest', 'error'); $this->allow('guest', 'test');
		
		//regular actions
		$this->allow('guest', 'index');
		$this->allow('guest', 'racing-team');
		$this->allow('guest', 'cars', 'index');
		$this->allow('guest', 'cars', 'new-list');
		$this->allow('guest', 'cars', 'delete-search-by-hash');
		$this->allow('guest', 'cars', 'delete-search-by-mail');
		$this->allow('guest', 'cars', 'favourite-list-session'); //session-based favourites
		$this->allow('guest', 'cars', 'getmodels');
		$this->allow('guest', 'cars', 'get-instalments');
		$this->allow('guest', 'cars', 'have-car-found');
		$this->allow('guest', 'cars', 'list');
		$this->allow('guest', 'cars', 'payment-failed');
		$this->allow('guest', 'cars', 'payment-ok');
		$this->allow('guest', 'cars', 'payment-status-changed');
		$this->allow('guest', 'cars', 'show');
		$this->allow('guest', 'cars', 'show-by-sr');
        $this->allow('guest', 'cars', 'show-by-offer-id');
        $this->allow('guest', 'cars', 'search-list');
		$this->allow('guest', 'cars', 'search-edit');
        
        $this->allow('guest', 'cars', 'contract-preview');
		
		$this->deny('guest', 'index', 'logout');
		
		$this->allow('user', 'index', 'logout');
		$this->allow('salesman', 'index', 'logout');
		$this->allow('user', 'cars', 'my-cars');
		$this->allow('user', 'cars', 'my-reservations');
		$this->allow('user', 'cars', 'favourite-list'); //db-based favourites
		$this->allow('user', 'user-cp');
		
	}
	
	protected function myAddResources($resources) {
		foreach ($resources as $resource) {
			$this->addResource(new Zend_Acl_Resource($resource[0]), $resource[1]);
		}
	}

}