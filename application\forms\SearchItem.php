<?php

class Form_SearchItem extends My_Form {

	public function init() {
		$cars = new Model_Cars_Cars();
		$view = Zend_Layout::getMvcInstance()->getView();
        $empl = new Model_Employees();
        $tr = Zend_Registry::get('Zend_Translate');

		$this->addElements(array(
			new Zend_Form_Element_Hidden('title', array(
				'label'	=>	'SEARCH_NAME',
				'required' => true,
				'validators' => array(new Zend_Validate_StringLength(array('min' => 0, 'max' => 128, 'encoding' => 'UTF-8')))
			)),
			new Zend_Form_Element_Hidden('newsletter', array(
				'label'	=>	'NEWSLETTER_ON_OFF',
                'value' => 1,
				'validators' => array()
			)),
            new Zend_Form_Element_Select('added_by_sr_id', array(
                    'label'	=>	'FAVOURITE_SALESMAN',
                    'multiOptions' => array('' => $tr->_('FAVOURITE_SALESMAN'). ' - '. strtolower($tr->_('ANY'))) + $empl->getAll(array('sr_assoc' => true)),
                    'required' => false
            )),
			new Zend_Form_Element_Hash('csrf', array(
				'label'	=>	'',
				'salt' => 'csrf_foo_' . get_class($this)
			)),
			new Zend_Form_Element_Submit('save', array(
				'label' => 'SAVE'
			))
          
		));
		
		parent::init();
	}

}