<?php

class Model_AdvertisingBar extends Model_Base {
	
	public function getAdvertisingText($lang) {
		$select = $this->db->select()
			->from($this->tables['advertising_bar'], array('text'))
			->where('language = ?', $lang);
		return $this->db->fetchOne($select);
	}
    
    public function editAdvertisingText($data) {
        
        $this->db->delete($this->tables['advertising_bar']);
        
        foreach($data as $d)
        {
            $this->db->insert(
                $this->tables['advertising_bar'],
                array(
                    'language'		=>	$d['ab_language'],
                    'text'	=>	$d['ab_text'],
                    'is_vipauto'		=>	$d['ab_is_vipauto']
                )
            );
        }
		return $this->db->lastInsertId($this->tables['car_makes'], 'id');
	}
    
}