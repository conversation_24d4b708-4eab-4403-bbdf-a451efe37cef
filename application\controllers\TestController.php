<?php

class TestController extends Zend_Controller_Action {
	
	public function init() {
		if (APPLICATION_ENV != 'development') exit;
		
		$this->_helper->layout->setLayout('jqrequest');
		$this->_helper->viewRenderer->setNoRender(true);
		$this->db = Zend_Db_Table_Abstract::getDefaultAdapter();
	}
	
	public function t1Action() {
		$imp = new Model_ImportExport_Import();
		$imp->processCarPhotos(array('car_id' => 62, 'photos' => array(array('cp_id' => 2), array('cp_id' => 3))));
	}
	
	public function t2Action() {
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, $debug=false);
		$models = $om->getVersionsAssoc($type='CAR', 5, 743061);
		Zend_Debug::dump($models,'<h2></h2>');
	}
	
	public function t3Action() {
		$ct = new Model_Cars_Transfers();
		$payment = array(
			'id' => 4,
			'date_added' => '2011-05-26 14:15:52',
			'user_id' => 5,
			'car_id' => 1,
			'company_id' => 6,
			'pos_id' => 123,
			'ts' => '123',
			'sig' => '123qwe',
			'amount' => 1567.98,
			'status' => 1,
			'date_completed' => null
		);
		$status = array(
			'status' => 1,
			'amount' => 156798
		);
		$ct->updateReservationsOnPaymentStatusChange($payment, $status);
	}
	
	public function indexAction() {
		echo "test/index";
	}
	
	public function fillInColoursAction() {
		$cars = new Model_Cars_Cars();
		$cars->addColoursFromOtoMoto();
	}
	
	public function fillInCountriesAction() {
		$cars = new Model_Cars_Cars();
		$cars->addCountriesFromOtoMoto();
	}
	
	public function fillInFuelTypesAction() {
		$cars = new Model_Cars_Cars();
		$cars->addFuelTypesFromOtoMoto();
	}
	
	public function fillInGearboxTypesAction() {
		die("Do przegladniecia - usuniete type_id z gearbox_types");
		$cars = new Model_Cars_Cars();
		$cars->addGearboxTypesFromOtoMoto();
	}
	
	public function fillInMakesAction() {
		set_time_limit(1200);
		
		$cars = new Model_Cars_Cars();
		$cars->addCarMakesFromOtoMoto();
	}
	
	public function fillInModelsAction() {
		set_time_limit(1200);
		
		$cars = new Model_Cars_Cars();
		$cars->addCarModelsFromOtoMoto();
	}
	
	public function fillInModelVersionsAction() {
		set_time_limit(1200);
		
		$cars = new Model_Cars_Cars();
		$cars->addCarModelVersionsFromOtoMoto();
	}
	
	public function fillInVehicleCategoriesAction() {
		set_time_limit(1200);
		
		$cars = new Model_Cars_Cars();
		$cars->addVehicleCategoriesFromOtoMoto();
	}
	
	public function fillInVehicleExtrasAction() {
		set_time_limit(1200);
		
		$cars = new Model_Cars_Cars();
		$cars->addVehicleExtrasFromOtoMoto();
	}
	
	public function fillInVehicleFeaturesAction() {
		set_time_limit(1200);
		
		$cars = new Model_Cars_Cars();
		$cars->addVehicleFeaturesFromOtoMoto();
	}
	
	public function testAction() {
		$om = new Model_OtoMoto(Model_OtoMoto::$mainAccount, true);
		$om->getVehicleExtrasAssoc('CAR');
	}
	
}