<?php

class My_Validate_PESEL extends Zend_Validate_Abstract {

	const PESEL_NUMBER_INVALID = 'peselNumberInvalid';
	
	protected $_messageTemplates = array(
		self::PESEL_NUMBER_INVALID => "peselNumberInvalid",
	);
	
	public function isValid($value) {
		if (!preg_match('/^[0-9]{11}$/',$value)) {
			$this->_error(self::PESEL_NUMBER_INVALID);
			return false;
		}
		
		$arrSteps = array(1, 3, 7, 9, 1, 3, 7, 9, 1, 3); // tablica z odpowiednimi wagami
		$intSum = 0;
		for ($i = 0; $i < 10; $i++) {
			$intSum += $arrSteps[$i] * $value[$i]; //mnożymy każdy ze znaków przez wagę i sumujemy wszystko
		}
		$int = 10 - $intSum % 10; //obliczamy sumę kontrolną
		$intControlNr = ($int == 10)?0:$int;
		if ($intControlNr == $value[10]) {
			return true;
		}
		
		$this->_error(self::PESEL_NUMBER_INVALID);
		return false;
	}	
}